import { BoosterAnimator } from "./BoosterAnimator";

export class MultipleBoosterAnimator implements BoosterAnimator {
    private animators: BoosterAnimator[];

    public constructor() {
        this.animators = [];
    }

    public addAnimator(...animators: BoosterAnimator[]): this {
        this.animators.push(...animators);
        return this;
    }

    public play(doneCallback: () => void): void {
        const playAnimation = (index: number, animators: BoosterAnimator[]) => {
            if (index >= animators.length) {
                doneCallback();
                return;
            }
            const animator = animators[index];
            animator.play(() => playAnimation(index + 1, animators));
        };
        playAnimation(0, this.animators);
    }
}