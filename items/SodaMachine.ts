import assert = require('assert');
import {
    Cartable,
    Collidable,
    Consumable,
    Destroyable, Pickable,
    Pullable,
    Serializable,
    Valuable,
} from "../components/components";
import { Entity } from "./Entity";

const { ccclass, disallowMultiple, menu, property } = cc._decorator;

@ccclass
@disallowMultiple
@menu('engine/items/SodaMachine')
export class SodaMachine extends Entity {
    @property(cc.String)
    public readonly entityId: string = '';

    @property(cc.Node)
    private readonly view: cc.Node | null = null;

    public onLoad(): void {
        assert(this.checkComponents([Collidable, Pullable, Cartable], [Pickable]));
        assert(this.view !== null);

        this.addComponent(Consumable);
        this.addComponent(Destroyable);
        this.addComponent(Valuable).setValue(100);
        this.addComponent(Serializable);
    }
}