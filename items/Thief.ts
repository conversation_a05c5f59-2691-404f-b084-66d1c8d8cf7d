import assert = require('assert');
import {
    Animatable,
    Blinkable,
    Collidable,
    Flowable, Pickable,
    Pullable,
    Serializable,
    Valuable,
} from "../components/components";
import { EntityManager } from '../manager/EntityManager';
import { Miner } from '../miners/Miner';
import { AnimationUpdater, TransformHelper } from '../utils/Utils';
import { Entity } from "./Entity";
import { EntityState, NullEntityState } from './EntityState';
import * as ee from "../../libraries/ee/index";
import {LevelManager} from "../../manager/level/LevelManager";

const { ccclass, disallowMultiple, menu, property } = cc._decorator;

type State = EntityState;
type Context = Thief;

enum StateId {
    Pulled = 'pulled',
    Appearing = 'appearing',
    Disappearing = 'disappearing',
    Stealing = 'stealing',
    Ready = 'ready',
    Waiting = 'waiting',
}

class PulledState implements State {
    public get id(): string {
        return StateId.Pulled;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.context.playPulledAnimation();
    }

    public update(delta: number, manager: EntityManager): void { }
    public exit(): void { }
    public serialize(): any { }
    public deserialize(data: any): void { }
}

/** The thief is about to appear. */
class AppearingState implements State {
    private readonly speed = 120;

    public get id(): string {
        return StateId.Appearing;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.context.playAppearingAnimation();
        const pickable = this.context.getComponent(Pickable);
        pickable.active = true;
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        if (levelManager.isPickUpActive) {
            pickable.blinkGreen();
        }
    }

    public update(delta: number, manager: EntityManager): void {
        const distance = delta * this.speed;
        this.context.node.x += distance;
    }

    public exit(): void { }
    public serialize(): any { }
    public deserialize(data: any): void { }
}

/** The thief is about to disappear. */
class DisappearingState implements State {
    private readonly speed = 480;

    public get id(): string {
        return StateId.Disappearing;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.context.playDisappearingAnimation();
    }

    public update(delta: number, manager: EntityManager): void {
        const view = manager.getView().node;
        const helper = new TransformHelper(view, this.context.node.parent);
        const dst = helper.convertTo(cc.v2(view.width * -0.6, 0)).x;

        const distance = delta * this.speed;
        this.context.node.x -= distance;
        if (this.context.node.x <= dst) {
            this.context.state = new WaitingState(this.context);
        }
    }

    public exit(): void { }
    public serialize(): any { }
    public deserialize(data: any): void { }
}

class WaitingState implements State {
    private readonly duration = 10;
    private elapsedDuration = 0;

    public get id(): string {
        return StateId.Waiting;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void { }

    public update(delta: number, manager: EntityManager): void {
        this.elapsedDuration += delta;
        if (this.elapsedDuration >= this.duration) {
            this.context.state = new AppearingState(this.context);
        }
    }

    public exit(): void { }

    public serialize(): any {
        return {
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

/** The thief is about to steal. */
class StealingState implements State {
    private duration = 0;
    private elapsedDuration = 0;

    public get id(): string {
        return StateId.Stealing;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.duration = this.context.playStealingAnimation();
        this.context.getComponent(Pickable).active = false;
    }

    public update(delta: number, manager: EntityManager): void {
        this.elapsedDuration += delta;
        if (this.elapsedDuration >= this.duration) {
            this.context.state = new ReadyState();
        }
    }

    public exit(): void { }

    public serialize(): any {
        return {
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

/** Whether the item has finished playing stealing animation. */
class ReadyState implements State {
    public get id(): string {
        return StateId.Ready;
    }

    public constructor() { }
    public enter(): void { }
    public update(delta: number, manager: EntityManager): void { }
    public exit(): void { }
    public serialize(): any { }
    public deserialize(data: any): void { }
}

@ccclass
@disallowMultiple
@menu('engine/items/Thief')
export class Thief extends Entity {
    @property(cc.String)
    public readonly entityId: string = '';

    @property({ type: cc.Node, visible: true })
    private readonly _view: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _mask: cc.Node | null = null;

    private get view(): cc.Node {
        if (this._view === null) {
            throw Error('Item not registered.');
        }
        return this._view;
    }

    private get mask(): cc.Node {
        if (this._mask === null) {
            throw Error('Item not registered.');
        }
        return this._mask;
    }

    private _animationUpdater: AnimationUpdater | null = null;
    private _maskUpdater: AnimationUpdater | null = null;

    private get animation(): AnimationUpdater {
        if (this._animationUpdater === null) {
            this._animationUpdater = new AnimationUpdater(this.view.getComponent(sp.Skeleton));
        }
        return this._animationUpdater;
    }

    private get maskAnimation(): AnimationUpdater {
        if (this._maskUpdater === null) {
            this._maskUpdater = new AnimationUpdater(this.mask.getComponent(sp.Skeleton));
        }
        return this._maskUpdater;
    }

    private _state: State;

    public get state(): State {
        return this._state;
    }

    public set state(value: State) {
        if (this._state !== value) {
            this._state.exit();
            this._state = value;
            this._state.enter();
        }
    }

    /** The target miner that is being stolen. */
    private _target: Miner | null = null;

    public get target(): Miner | null {
        return this._target;
    }

    public constructor() {
        super();
        this._state = new NullEntityState();
    }

    public onLoad(): void {
        assert(this.checkComponents([Collidable, Pullable], [Blinkable, Pickable]));
        assert(this._view !== null);

        this.addComponent(Valuable)
            .setValue(500);

        this.addComponent(Animatable)
            .onAnimated((delta, manager) => {
                this.state.update(delta, manager);
                this.animation.update(delta);
                this.maskAnimation.update(delta);
            });

        this.getComponent(Pullable)
            .onPulled(() => this.state = new PulledState(this));

        this.addComponent(Serializable)
            .onSerialized(manager => ({
                state: this.state.id,
                data: this.state.serialize(),
                target: (this.target ? manager.getComponentId(this.target) : undefined),
                time: this.animation.time,
            }))
            .onDeserialized((_data, manager) => {
                const data = _data as {
                    state?: string,
                    data: any,
                    target?: string,
                    time?: number,
                };
                const states: { [key: string]: () => State } = {
                    [StateId.Pulled       /**/]: () => new PulledState(this),
                    [StateId.Appearing    /**/]: () => new AppearingState(this),
                    [StateId.Disappearing /**/]: () => new DisappearingState(this),
                    [StateId.Stealing     /**/]: () => new StealingState(this),
                    [StateId.Ready        /**/]: () => new ReadyState(),
                    [StateId.Waiting      /**/]: () => new WaitingState(this),
                };
                this.state = states[data.state || StateId.Appearing]();
                this.state.deserialize(data.data);
                this._target = manager.findComponentById(Miner, data.target || '');
                this.animation.time = data.time || 0;
            });

        this.addComponent(Flowable)
            .onBegin(() => {
                this.state = new AppearingState(this);
            });
    }

    public playPulledAnimation(): void {
        this.animation.playAnimation('arrested', true);
    }

    public playPulledAnimation2(): void {
        this.animation.playAnimation('arrested_2', true);
    }

    public playAppearingAnimation(): void {
        this.animation.playAnimation('appear', true);
        this.maskAnimation.playAnimation('appear', true);
    }

    public playDisappearingAnimation(): void {
        this.animation.playAnimation('disappear', true);
    }

    public playStealingAnimation(): number {
        return this.animation.playAnimation('robbery', true);
    }

    public isAppearing(): boolean {
        return this.state.id === StateId.Appearing;
    }

    public isReady(): boolean {
        return this.state.id === StateId.Ready;
    }

    public isDisappearing(): boolean {
        return this.state.id === StateId.Disappearing;
    }

    /** Switches to stealing state. */
    public steal(target: Miner): void {
        this._target = target;
        this.state = new StealingState(this);
    }

    /** Switches to disappear state. */
    public disappear(): void {
        this._target = null;
        this.state = new DisappearingState(this);
    }
}