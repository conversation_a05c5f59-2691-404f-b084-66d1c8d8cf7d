import assert = require('assert');
import {
    Animatable,
    Eventable,
    Flowable,
    Serializable,
} from '../components/components';
import { EntityManager } from '../manager/EntityManager';
import { AnimationUpdater } from '../utils/Utils';
import { Entity } from "./Entity";
import { EntityState, NullEntityState } from './EntityState';

const { ccclass, disallowMultiple, menu, property } = cc._decorator;

type State = EntityState;
type Context = AntEffect;

enum StateId {
    Attack = 'attack',
}

class SerializableState implements State {
    private state: State;

    public constructor(context: Context, data: {
        id?: string,
        data?: any,
    }) {
        const states: { [key: string]: () => State } = {
            [StateId.Attack]: () => new AttackState(context),
        };
        this.state = states[data.id || StateId.Attack]();
        this.state.deserialize(data.data || {});
    }

    public get id(): string {
        return this.state.id;
    }

    public enter(): void {
        this.state.enter();
    }

    public update(delta: number, manager: EntityManager): void {
        this.state.update(delta, manager);
    }

    public exit(): void {
        this.state.exit();
    }

    public serialize(): any {
        return this.state.serialize();
    }

    public deserialize(data: any): void {
        this.state.deserialize(data);
    }
}

class AttackState implements State {
    private duration = 0;
    private elapsedDuration = 0;

    public get id(): string {
        return StateId.Attack;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.duration = this.context.playAttackAnimation();
    }

    public update(delta: number, manager: EntityManager): void {
        this.elapsedDuration += delta;
        if (this.elapsedDuration >= this.duration) {
            this.context.kill(true);
        }
    }

    public exit(): void { }

    public serialize(): any {
        return {
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

@ccclass
@disallowMultiple
@menu('engine/items/AntEffect')
export class AntEffect extends Entity {
    @property(cc.String)
    public readonly entityId: string = '';

    @property({ type: sp.Skeleton, visible: true })
    private readonly _animation: sp.Skeleton | null = null;

    private _state: State;

    public get state(): State {
        return this._state;
    }

    public set state(value: State) {
        if (this._state !== value) {
            this._state.exit();
            this._state = new NullEntityState();
            this.getComponent(Eventable)
                .dispatch([`send`, `state`, `${this.instanceId}`], {
                    id: value.id,
                    data: value.serialize(),
                });
        }
    }

    public constructor() {
        super();
        this._state = new NullEntityState();
    }

    public onLoad(): void {
        assert(this._animation !== null);

        this.addComponent(Animatable)
            .onAnimated((delta, manager) => {
                this.state.update(delta, manager);
                this.animation.update(delta);
            });

        this.addComponent(Serializable)
            .onSerialized(manager => ({
                state: {
                    id: this.state.id,
                    data: this.state.serialize(),
                },
                time: this.animation.time,
            }))
            .onDeserialized((_data, manager) => {
                const data = _data as {
                    state?: {
                        id?: string,
                        data?: any,
                    },
                    time?: number,
                };
                this._state.exit();
                this._state = new SerializableState(this, data.state || {});
                this._state.enter();
                this.animation.time = data.time || 0;
            });

        this.addComponent(Eventable);

        this.addComponent(Flowable)
            .onBegin(() => {
                this.getComponent(Eventable)
                    .addListener([`receive`, `state`, `${this.instanceId}`], (paths, _data) => {
                        assert(paths.length === 0);
                        const data = _data as {
                            id?: string,
                            data?: any,
                        };
                        this._state = new SerializableState(this, data || {});
                        this._state.enter();
                    });
                this.state = new AttackState(this);
            })
            .onEnd(() => {
                this.getComponent(Eventable)
                    .removeListener([`receive`, `state`, `${this.instanceId}`]);
            });
    }

    private _animationUpdater: AnimationUpdater | null = null;

    private get animation(): AnimationUpdater {
        if (this._animationUpdater === null) {
            if (this._animation === null) {
                throw Error('Item not registered.');
            }
            this._animationUpdater = new AnimationUpdater(this._animation);
        }
        return this._animationUpdater;
    }

    public playAttackAnimation(): number {
        return this.animation.playAnimation('miner-ant-bites', false);
    }
}
