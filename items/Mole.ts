import assert = require('assert');

import {
    Animatable,
    Cartable,
    Collidable,
    Destroyable,
    Flowable, Pickable,
    Pullable,
    Serializable,
    Valuable,
} from "../components/components";
import { EntityManager } from '../manager/EntityManager';
import { AnimationUpdater } from '../utils/Utils';
import { Entity } from "./Entity";
import { EntityState, NullEntityState } from './EntityState';
import {Stone} from "./Stone";
import {Tnt} from "./Tnt";
import {Bag} from "./Bag";
import {Gold} from "./Gold";

type State = EntityState;
type Context = Mole;

enum StateId {
    IsPulled = 'isPulled',
    Appearing = 'appearing',
    Appeared = 'appeared',
    Disappearing = 'disappearing',
    Disappeared = 'disappeared',
}

class IsPulledState implements State {
    public get id(): string {
        return StateId.IsPulled;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.context.playPulledAnimation();
    }

    public update(delta: number, manager: EntityManager): void { }

    public exit(): void { }

    public serialize(): any { }

    public deserialize(data: any): void { }
}

class AppearingState implements State {
    private elapsedDuration: number = 0;

    public get id(): string {
        return StateId.Appearing;
    }

    public constructor(private readonly context: Context) {
    }

    public enter(): void {
        this.context.playStandUpAnimation();
    }

    public update(delta: number, manager: EntityManager): void {
        const speed = this.context.distance / 0.5;
        const distance = delta * speed;
        this.context.node.y += distance;

        this.elapsedDuration += delta;
        if (this.elapsedDuration >= 0.5) {
            this.context.setPullable(true);
            this.context.state = new AppearedState(this.context);
        }
    }

    public exit(): void { }

    public serialize(): any {
        return {
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

class AppearedState implements State {
    private elapsedDuration: number = 0;

    public get id(): string {
        return StateId.Appeared;
    }

    public constructor(private readonly context: Context) {
    }

    public enter(): void {
        this.context.playIdleAnimation();
    }

    public update(delta: number, manager: EntityManager): void {
        this.elapsedDuration += delta;
        if (this.elapsedDuration >= 5) {
            this.context.setPullable(false);
            this.context.state = new DisappearingState(this.context);
        }
    }

    public exit(): void { }

    public serialize(): any {
        return {
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

class DisappearingState implements State {
    private elapsedDuration: number = 0;

    public get id(): string {
        return StateId.Disappearing;
    }

    public constructor(private readonly context: Context) {
    }

    public enter(): void {
        this.context.playGoDownAnimation();
    }

    public update(delta: number, manager: EntityManager): void {
        const speed = -this.context.distance / 0.5;
        const distance = delta * speed;
        this.context.node.y += distance;

        this.elapsedDuration += delta;
        if (this.elapsedDuration >= 0.5) {
            this.context.clearItem();
            this.context.state = new DisappearedState(this.context);
        }
    }

    public exit(): void { }

    public serialize(): any {
        return {
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

class DisappearedState implements State {
    private elapsedDuration: number = 0;

    public get id(): string {
        return StateId.Disappeared;
    }

    public constructor(private readonly context: Context) {
    }

    public enter(): void {
        this.context.playStandUpAnimation();
    }

    public update(delta: number, manager: EntityManager): void {
        this.elapsedDuration += delta;
        if (this.elapsedDuration >= 0.5) {
            const entity = this.context.bringItem();
            manager.addEntity(entity);
            this.context.state = new AppearingState(this.context);
        }
    }

    public exit(): void { }

    public serialize(): any {
        return {
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

const enum Item {
    Stone = 0,
    TNT,
    Bag,
    Gold,
}

const { ccclass, menu, property } = cc._decorator;

@ccclass
@menu('engine/items/Mole')
export class Mole extends Entity {
    @property(cc.String)
    public readonly entityId: string = '';

    @property({ type: sp.Skeleton, visible: true })
    private readonly _body: sp.Skeleton | null = null;

    @property({ type: sp.Skeleton, visible: true })
    private readonly _maskBody: sp.Skeleton | null = null;

    @property({ type: sp.Skeleton, visible: true })
    private readonly _hand: sp.Skeleton | null = null;

    @property({ type: sp.Skeleton, visible: true })
    private readonly _maskHand: sp.Skeleton | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _item: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _appearedPosition: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _disappearedPosition: cc.Node | null = null;

    @property({ type: cc.Prefab, visible: true })
    private readonly _list: cc.Prefab[] = [];

    @property({ type: cc.Mask, visible: true })
    private readonly _mask: cc.Mask | null = null;

    private get body(): sp.Skeleton {
        if (this._body === null) {
            throw Error('Item not registered.');
        }
        return this._body;
    }

    private get maskBody(): sp.Skeleton {
        if (this._maskBody === null) {
            throw Error('Item not registered.');
        }
        return this._maskBody;
    }

    private get hand(): sp.Skeleton {
        if (this._hand === null) {
            throw Error('Item not registered.');
        }
        return this._hand;
    }

    private get maskHand(): sp.Skeleton {
        if (this._maskHand === null) {
            throw Error('Item not registered.');
        }
        return this._maskHand;
    }

    private get item(): cc.Node {
        if (this._item === null) {
            throw Error('Item not registered.');
        }
        return this._item;
    }

    private get appearedPosition(): cc.Node {
        if (this._appearedPosition === null) {
            throw Error('Item not registered.');
        }
        return this._appearedPosition;
    }

    private get disappearedPosition(): cc.Node {
        if (this._disappearedPosition === null) {
            throw Error('Item not registered.');
        }
        return this._disappearedPosition;
    }

    private get list(): cc.Prefab[] {
        return this._list;
    }

    private currentItem: cc.Node | null = null;
    private lastItem = -1;

    private _state: State;

    public get state(): State {
        return this._state;
    }

    public set state(value: State) {
        if (this._state !== value) {
            this._state.exit();
            this._state = value;
            this._state.enter();
        }
    }

    public constructor() {
        super();
        this._state = new NullEntityState();
    }

    private _bodyAnimationUpdater: AnimationUpdater | null = null;
    private _maskBodyAnimationUpdater: AnimationUpdater | null = null;

    private get bodyAnimation(): AnimationUpdater {
        if (this._bodyAnimationUpdater === null) {
            this._bodyAnimationUpdater = new AnimationUpdater(this.body);
        }
        return this._bodyAnimationUpdater;
    }
    private get maskBodyAnimation(): AnimationUpdater {
        if (this._maskBodyAnimationUpdater === null) {
            this._maskBodyAnimationUpdater = new AnimationUpdater(this.maskBody);
        }
        return this._maskBodyAnimationUpdater;
    }

    private _handAnimationUpdater: AnimationUpdater | null = null;
    private _maskHandAnimationUpdater: AnimationUpdater | null = null;

    private get handAnimation(): AnimationUpdater {
        if (this._handAnimationUpdater === null) {
            this._handAnimationUpdater = new AnimationUpdater(this.maskHand);
        }
        return this._handAnimationUpdater;
    }

    private get maskHandAnimation(): AnimationUpdater {
        if (this._maskHandAnimationUpdater === null) {
            this._maskHandAnimationUpdater = new AnimationUpdater(this.hand);
        }
        return this._maskHandAnimationUpdater;
    }

    public playPulledAnimation(): void {
        this.bodyAnimation.playAnimation('shaky', true);
        this.handAnimation.playAnimation('shaky', true);
    }

    public playIdleAnimation(): void {
        this.bodyAnimation.playAnimation('day_stand', true);
        this.handAnimation.playAnimation('day_stand', true);
        this.maskBodyAnimation.playAnimation('day_stand', true);
        this.maskHandAnimation.playAnimation('day_stand', true);
    }

    public playStandUpAnimation(): void {
        this.bodyAnimation.playAnimation('day_go_up', true);
        this.handAnimation.playAnimation('day_go_up', true);
        this.maskBodyAnimation.playAnimation('day_go_up', true);
        this.maskHandAnimation.playAnimation('day_go_up', true);
    }

    public playGoDownAnimation(): void {
        this.bodyAnimation.playAnimation('day_get_down', true);
        this.handAnimation.playAnimation('day_get_down', true);
        this.maskBodyAnimation.playAnimation('day_get_down', true);
        this.maskHandAnimation.playAnimation('day_get_down', true);
    }

    protected onLoad(): void {
        assert(this.checkComponents([Collidable, Pullable, Valuable], [Pickable]));
        assert(this._body !== null);
        assert(this._hand !== null);
        assert(this._item !== null);
        assert(this._appearedPosition !== null);
        assert(this._disappearedPosition !== null);

        this.getComponent(Pullable)
            .onPulled(() => {
                this.state = new IsPulledState(this);
            });

        this.addComponent(Destroyable);

        this.addComponent(Animatable)
            .onAnimated((delta, manager) => {
                this.state.update(delta, manager);
                this.bodyAnimation.update(delta);
                this.handAnimation.update(delta);
                this.maskBodyAnimation.update(delta);
                this.maskHandAnimation.update(delta);
            });
        this.addComponent(Serializable)
            .onSerialized(manager => ({
                state: this.state.id,
                data: this.state.serialize(),
                time: this.bodyAnimation.time,
            }))
            .onDeserialized((_data, manager) => {
                const data = _data as {
                    state: string,
                    data: any,
                    time?: number,
                };
                const states: { [key: string]: () => State } = {
                    [StateId.IsPulled  /*    */]: () => new IsPulledState(this),
                    [StateId.Appearing  /*   */]: () => new AppearingState(this),
                    [StateId.Appeared  /*    */]: () => new AppearedState(this),
                    [StateId.Disappearing  /**/]: () => new DisappearingState(this),
                    [StateId.Disappeared   /**/]: () => new DisappearedState(this),
                };
                this.state = states[data.state]();
                this.state.deserialize(data.data);
                this.bodyAnimation.time = data.time || 0;
                this.handAnimation.time = data.time || 0;
            });

        this.addComponent(Flowable)
            .onBegin(() => {
                this.state = new DisappearingState(this);
            });
    }

    public get distance(): number {
        return this.appearedPosition.y - this.disappearedPosition.y;
    }

    public isStandingUp(): boolean {
        return this.state.id === StateId.Appeared;
    }

    public isSitDown(): boolean {
        return this.state.id === StateId.Disappeared;
    }

    public setPullable(canPull: boolean): void {
        this.getComponent(Pullable).setPullable(canPull);
        this.setCurrentItemPullable(canPull);
    }

    public setCurrentItemPullable(canPull: boolean): void {
        if (this.currentItem !== null) {
            const pullable = this.currentItem.getComponent(Pullable);
            if (pullable !== null) {
                pullable.setPullable(canPull);
            }
        }
    }

    public standUp(): void {
        this.state = new AppearingState(this);
    }

    public sitDown(): void {
        this.state = new DisappearingState(this);
    }

    public getCurrentItem(): cc.Node | null {
        return this.currentItem;
    }

    public releaseItem(): cc.Node | null {
        if (this.currentItem !== null) {
            this.currentItem.removeFromParent(false);
            return this.currentItem;
        }
        return null;
    }

    public resetItem(): void {
        this.currentItem = null;
    }

    public clearItem(): void {
        if (this.currentItem !== null) {
            const entity = this.currentItem.getComponent(Entity);
            entity.kill();
        }
    }

    public itemIsPulled(): boolean {
        if (this.currentItem !== null) {
            const pullable = this.currentItem.getComponent(Pullable);
            if (pullable === null) {
                return false;
            }

            if (pullable.isPulled()) {
                return true;
            }
        }
        return false;
    }

    public itemIsCarted(): boolean {
        if (this.currentItem !== null) {
            const cartable = this.currentItem.getComponent(Cartable);
            if (cartable === null) {
                return false;
            }

            if (cartable.isCarted()) {
                return true;
            }
        }
        return false;
    }

    private getRandomType(): number {
        const ratio = [
            [Item.Stone, 0.4],
            [Item.TNT, 0.25],
            [Item.Bag, 0.2],
            [Item.Gold, 0.15],
        ];

        let randNum = Math.random();
        let weight = 0;

        for (let i = 0; i < ratio.length; i++) {
            if (i === this.lastItem) {
                continue;
            }
            weight += ratio[i][1];
        }

        for (let i = 0; i < ratio.length; i++) {
            if (i === this.lastItem) {
                continue;
            }

            const rate = ratio[i][1] / weight;
            if (randNum <= rate) {
                return i;
            }

            randNum -= rate;
        }

        return 0;
    }

    public bringItem(): Entity {
        const type = this.getRandomType();
        this.lastItem = type;
        const node = cc.instantiate(this.list[type]);
        node.active = true;
        this.item.addChild(node);
        this.currentItem = node;
        this.setCurrentItemPullable(false);
        this.setMoleToCurrentItem(node)
        return node.getComponent(Entity);
    }

    private setMoleToCurrentItem(item: cc.Node) {
        switch (this.lastItem) {
            case Item.Stone:
                const stone = this.currentItem.getComponent(Stone);
                stone.mole = this;
                break;
            case Item.TNT:
                const tnt = this.currentItem.getComponent(Tnt);
                tnt.mole = this;
                break;
            case Item.Bag:
                const bag = this.currentItem.getComponent(Bag);
                bag.mole = this;
                break;
            case Item.Gold:
                const gold = this.currentItem.getComponent(Gold);
                gold.mole = this;
                break;
            default:
                assert(false);
        }
    }

    public isDisappeared(): boolean {
        return this.state.id === StateId.Disappeared;
    }
}
