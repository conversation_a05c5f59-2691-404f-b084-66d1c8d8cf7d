import assert = require('assert');
import {
    Flowable,
    Pullable,
    Serializable,
    Valuable,
} from '../components/components';
import { EntityManager } from '../manager/EntityManager';
import { Entity } from "./Entity";
import { EntityState, NullEntityState } from './EntityState';

const { ccclass, disallowMultiple, menu, property } = cc._decorator;

type State = EntityState;
type Context = Boat;

enum StateId {
    Alive = 'alive',
    Pulled = 'pulled',
}

class AliveState implements State {
    public get id(): string {
        return StateId.Alive;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.context.playAliveAnimation();
    }

    public update(delta: number, manager: EntityManager): void { }
    public exit(): void { }
    public serialize(): any { }
    public deserialize(data: any): void { }
}

class PulledState implements State {
    public get id(): string {
        return StateId.Pulled;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.context.playPulledAnimation();
    }

    public update(delta: number, manager: EntityManager): void { }
    public exit(): void { }
    public serialize(): any { }
    public deserialize(data: any): void { }
}

@ccclass
@disallowMultiple
@menu('engine/items/Boat')
export class Boat extends Entity {
    @property(cc.String)
    public readonly entityId: string = '';

    @property({ type: cc.Node, visible: true })
    private readonly _view: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _pullView: cc.Node | null = null;

    private get view(): cc.Node {
        if (this._view === null) {
            throw Error('Item not registered.');
        }
        return this._view;
    }

    private get pullView(): cc.Node {
        if (this._pullView === null) {
            throw Error('Item not registered.');
        }
        return this._pullView;
    }

    private _state: State;

    public get state(): State {
        return this._state;
    }

    public set state(value: State) {
        if (this._state !== value) {
            this._state.exit();
            this._state = value;
            this._state.enter();
        }
    }

    public constructor() {
        super();
        this._state = new NullEntityState();
    }

    public onLoad(): void {
        assert(this.checkComponents([Pullable]));
        assert(this._view !== null);
        assert(this._pullView !== null);

        this.getComponent(Pullable)
            .onPulled(() => this.state = new PulledState(this))
            .onReleased(() => this.state = new AliveState(this));

        this.addComponent(Valuable).setValue(300);

        this.addComponent(Serializable)
            .onSerialized(manager => ({
                state: this.state.id,
                data: this.state.serialize(),
            }))
            .onDeserialized((_data, manager) => {
                const data = _data as {
                    state?: string,
                    data: any,
                };
                const states: { [key: string]: () => State } = {
                    [StateId.Alive  /**/]: () => new AliveState(this),
                    [StateId.Pulled /**/]: () => new PulledState(this),
                };
                this.state = states[data.state || StateId.Alive]();
                this.state.deserialize(data.data);
            });

        this.addComponent(Flowable)
            .onBegin(() => {
                this.state = new AliveState(this);
            });
    }

    public playAliveAnimation(): void {
        this.view.active = true;
        this.pullView.active = false;
    }

    public playPulledAnimation(): void {
        this.view.active = false;
        this.pullView.active = true;
    }
}
