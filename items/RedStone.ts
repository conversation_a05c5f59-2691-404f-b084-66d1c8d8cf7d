import assert = require('assert');

import {
    Animatable,
    Collidable,
    Consumable,
    Destroyable,
    Explosible, Pickable,
    Pullable,
    Valuable,
} from "../components/components";
import { Animator } from '../utils/Animator';
import { EffectBoom } from './EffectBoom';
import { Entity } from "./Entity";

const { ccclass, menu, property } = cc._decorator;

const enum State {
    Black = 0,
    Red,
}

@ccclass
@menu('engine/items/RedStone')
export class RedStone extends Entity {
    @property(cc.String)
    public readonly entityId: string = '';

    @property({ type: cc.Node, visible: true })
    private readonly _view: cc.Node | null = null;

    @property(cc.Prefab)
    private readonly _effectPrefab: cc.Prefab | null = null;

    private get view(): cc.Node {
        if (this._view === null) {
            throw Error('Item not registered.');
        }
        return this._view;
    }

    private get effectPrefab(): cc.Prefab {
        if (this._effectPrefab === null) {
            throw Error('Item not registered.');
        }
        return this._effectPrefab;
    }

    private animator: Animator;
    private state = State.Black;

    public constructor() {
        super();
        this.animator = new Animator();
        this.animator.addAction(cc.callFunc(() => this.animateRedBlack()));
    }

    public onLoad(): void {
        assert(this.checkComponents([Collidable, Pullable], [Pickable]));
        assert(this._view !== null);
        assert(this._effectPrefab !== null);

        this.getComponent(Pullable)
            .onPulled(() => {
                this.animator.removeAllActions();
                const animation = this.view.getComponent(sp.Skeleton);
                animation.setAnimation(0, 'black', true);
            });

        this.addComponent(Destroyable);

        this.addComponent(Valuable).setValue(20);

        this.addComponent(Animatable)
            .onAnimated(delta => {
                this.animator.animate(delta);
            });

        this.addComponent(Consumable).onConsumed(() => {
            const explosible = this.getComponent(Explosible);
            if (explosible.isExplosible()) {
                explosible.explode();
            } else {
                this.kill();
            }
        });

        const radius = 160;
        this.addComponent(Explosible)
            .setExplosive(true)
            .setRadius(radius)
            .setExplosion(() => {
                const node = cc.instantiate(this.effectPrefab);
                const effect = node.getComponent(EffectBoom);
                effect.setRadius(500);
                return effect;
            })
            .setExplosible(false);
    }

    public isRed(): boolean {
        return this.state === State.Red;
    }

    private animateRedBlack(): void {
        const animation = this.view.getComponent(sp.Skeleton);
        const explosion = this.getComponent(Explosible);

        const blackToRedDuration = animation.findAnimation('black_red').duration;
        const redToBlackDuration = animation.findAnimation('red_black').duration;
        const interval = 6;

        this.animator.addAction(cc.repeatForever(
            cc.sequence(
                // Black -> Red
                cc.delayTime(interval),
                cc.callFunc(() => {
                    animation.setAnimation(0, 'black_red', false);
                }),
                cc.delayTime(blackToRedDuration),
                cc.callFunc(() => {
                    animation.setAnimation(0, 'red', true);
                    this.state = State.Red;
                    explosion.setExplosible(true);
                }),

                // Red -> Black
                cc.delayTime(interval),
                cc.callFunc(() => {
                    animation.setAnimation(0, 'red_black', false);
                }),
                cc.delayTime(redToBlackDuration),
                cc.callFunc(() => {
                    animation.setAnimation(0, 'black', true);
                    this.state = State.Black;
                    explosion.setExplosible(false);
                })
            )
        ));
    }
}