import assert = require('assert');
import {
    Animatable,
    Blinkable,
    Collidable,
    Consumable,
    Destroyable,
    Flowable, Pickable,
    Pullable,
    Serializable,
    Valuable,
} from "../components/components";
import { EntityManager } from '../manager/EntityManager';
import { AnimationUpdater, DirectionHelper } from '../utils/Utils';
import { Entity } from "./Entity";
import { EntityState, NullEntityState } from './EntityState';

const { ccclass, menu, property } = cc._decorator;

type State = EntityState;
type Context = Hedgehog;

enum StateId {
    Walking = 'walking',
    Rolling = 'rolling',
    Pulled = 'pulled',
}

abstract class AliveState implements State {
    private readonly runningDuration = 5.0;
    private readonly runningSpeed = 80;

    public abstract id: string;

    public constructor(
        protected readonly context: Context,
        protected runningElapsedDuration: number) { }

    public enter(): void { }

    public update(delta: number, manager: EntityManager): void {
        const helper = new DirectionHelper(this.context.node);
        const timeDelta = Math.min(delta, this.runningDuration - this.runningElapsedDuration);
        const distance = timeDelta * this.runningSpeed;
        const signedDistance = helper.getDistance(distance);
        this.context.node.x += signedDistance;

        this.runningElapsedDuration += timeDelta;
        if (this.runningElapsedDuration >= this.runningDuration) {
            helper.changeDirection();
            this.runningElapsedDuration = 0;
        }
    }

    public exit(): void { }

    public serialize(): any {
        return {
            runningElapsed: this.runningElapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            runningElapsed?: number,
        };
        this.runningElapsedDuration = data.runningElapsed || 0;
    }
}

class WalkingState extends AliveState {
    private readonly duration = 6.0;
    private elapsedDuration = 0;

    public get id(): string {
        return StateId.Walking;
    }

    public constructor(context: Context, runningElapsedDuration: number) {
        super(context, runningElapsedDuration);
    }

    public enter(): void {
        this.context.playWalkingAnimation();
        this.context.getComponent(Valuable).setValue(200);
        this.context.getComponent(Pullable).setWeight(1);
    }

    public update(delta: number, manager: EntityManager): void {
        super.update(delta, manager);
        this.elapsedDuration += delta;
        if (this.elapsedDuration >= this.duration) {
            this.context.state = new RollingState(this.context, this.runningElapsedDuration);
        }
    }

    public exit(): void { }

    public serialize(): any {
        const data = super.serialize();
        return {
            ...data,
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        super.deserialize(_data);
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

class RollingState extends AliveState {
    private readonly duration = 6.0;
    private elapsedDuration = 0;

    public get id(): string {
        return StateId.Rolling;
    }

    public constructor(context: Context, runningElapsedDuration: number) {
        super(context, runningElapsedDuration);
    }

    public enter(): void {
        this.context.playRollingAnimation();
        this.context.getComponent(Valuable).setValue(0);
        this.context.getComponent(Pullable).setWeight(3);
    }

    public update(delta: number, manager: EntityManager): void {
        super.update(delta, manager);
        this.elapsedDuration += delta;
        if (this.elapsedDuration >= this.duration) {
            this.context.state = new WalkingState(this.context, this.runningElapsedDuration);
        }
    }

    public exit(): void { }

    public serialize(): any {
        const data = super.serialize();
        return {
            ...data,
            elapsed: this.elapsedDuration,
        };
    }

    public deserialize(_data: any): void {
        super.deserialize(_data);
        const data = _data as {
            elapsed?: number,
        };
        this.elapsedDuration = data.elapsed || 0;
    }
}

class PulledState implements State {
    public get id(): string {
        return StateId.Pulled;
    }

    public constructor(private readonly context: Context) { }

    public enter(): void {
        this.context.playPulledAnimation();
    }

    public update(delta: number, manager: EntityManager): void { }
    public exit(): void { }
    public serialize(): any { }
    public deserialize(data: any): void { }
}

@ccclass
@menu('engine/items/Hedgehog')
export class Hedgehog extends Entity {
    @property(cc.String)
    public readonly entityId: string = '';

    @property({ type: cc.Node, visible: true })
    private readonly _view: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _mask: cc.Node | null = null;

    private get view(): cc.Node {
        if (this._view === null) {
            throw Error('Item not registered.');
        }
        return this._view;
    }

    private get mask(): cc.Node {
        if (this._mask === null) {
            throw Error('Item not registered.');
        }
        return this._mask;
    }

    private _animationUpdater: AnimationUpdater | null = null;
    private _maskUpdater: AnimationUpdater | null = null;

    private get animation(): AnimationUpdater {
        if (this._animationUpdater === null) {
            this._animationUpdater = new AnimationUpdater(this.view.getComponent(sp.Skeleton));
        }
        return this._animationUpdater;
    }

    private get maskAnimation(): AnimationUpdater {
        if (this._maskUpdater === null) {
            this._maskUpdater = new AnimationUpdater(this.mask.getComponent(sp.Skeleton));
        }
        return this._maskUpdater;
    }

    private _state: State;
    private _isRolling = false;

    public get state(): State {
        return this._state;
    }

    public set state(value: State) {
        if (this._state !== value) {
            this._state.exit();
            this._state = value;
            this._state.enter();
        }
    }

    public constructor() {
        super();
        this._state = new NullEntityState();
    }

    public onLoad(): void {
        assert(this.checkComponents([Collidable, Pullable], [Blinkable, Pickable]));
        assert(this._view !== null);

        this.addComponent(Destroyable);

        this.getComponent(Pullable)
            .onPulled(() => this.state = new PulledState(this));

        this.addComponent(Animatable)
            .onAnimated((delta, manager) => {
                this.state.update(delta, manager);
                this.animation.update(delta);
                this.maskAnimation.update(delta);
            });

        this.addComponent(Valuable);
        this.addComponent(Consumable);

        this.addComponent(Serializable)
            .onSerialized(manager => ({
                state: this.state.id,
                data: this.state.serialize(),
                time: this.animation.time,
            }))
            .onDeserialized((_data, manager) => {
                const data = _data as {
                    state?: string,
                    data: any,
                    time?: number,
                };
                const states: { [key: string]: () => State } = {
                    [StateId.Walking]: () => new WalkingState(this, 0),
                    [StateId.Rolling]: () => new RollingState(this, 0),
                    [StateId.Pulled]: () => new PulledState(this),
                };
                this.state = states[data.state || StateId.Rolling]();
                this.state.deserialize(data.data);
                this.animation.time = data.time || 0;
            });

        this.addComponent(Flowable)
            .onBegin(() => {
                this.state = new WalkingState(this, 0);
            });
    }

    /** Plays the pulled animation. */
    public playPulledAnimation(): void {
        this.animation.playAnimation(this.isRolling() ? 'pull' : 'stand', true);
    }

    /** Plays the walking animation. */
    public playWalkingAnimation(): void {
        this._isRolling = false;
        this.animation.playAnimation('walk', true);
        this.maskAnimation.playAnimation('walk', true);
    }

    /** Plays the rolling animation. */
    public playRollingAnimation(): void {
        this._isRolling = true;
        this.animation.playAnimation('roll', true);
        this.maskAnimation.playAnimation('roll', true);
    }

    /** Checks whether this item is rolling. */
    public isRolling(): boolean {
        return this._isRolling;
    }
}
