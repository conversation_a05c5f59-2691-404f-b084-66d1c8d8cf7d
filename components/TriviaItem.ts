import { EntityManager } from '../manager/EntityManager';
import { EntityComponent } from './EntityComponent';

const { ccclass, disallowMultiple, menu, property } = cc._decorator;

@ccclass
@disallowMultiple
@menu('engine/components/Trivia Item')
export class TriviaItem extends EntityComponent {
    public serialize(manager: EntityManager): any {
        return {};
    }

    public deserialize(data: any, manager: EntityManager): void {
        // Do nothing.
    }
}