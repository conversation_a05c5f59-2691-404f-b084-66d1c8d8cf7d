import { Entity } from '../../items/Entity';
import { EntityManager } from "../../manager/EntityManager";
import { Miner } from "../Miner";

/** Used when the miner is about to capture an item. */
export interface MinerCaptureStrategy {
    /** Returns whether the strategy's state should change to capturing state. */
    capture(miner: Miner, entity: <PERSON><PERSON><PERSON>, manager: EntityManager): boolean;

    /** Updates the strategy. */
    update(miner: Miner, delta: number): void;

    /** Checks whether the strategy is done. */
    isDone(miner: Miner): boolean;
}

/** Does not capture anything. */
export class MinerNeverCaptureStrategy {
    public capture(miner: Miner, entity: <PERSON><PERSON><PERSON>, manager: EntityManager): boolean {
        return false;
    }
    public update(miner: Miner, delta: number): void { }
    public isDone(miner: Miner): boolean { return true; }
}