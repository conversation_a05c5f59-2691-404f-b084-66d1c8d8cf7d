import { MathUtils } from "../../math/math";
import { Animator } from "../../utils/Utils";
import { Miner } from "../Miner";

export interface MinerMoveStrategy {
    /** Moves to the specified position. */
    move(miner: Miner, position: number): boolean;

    /** Stops moving. */
    stop(miner: Miner): void;

    /** Updates the strategy. */
    update(miner: Miner, delta: number): void;
}

/** Cannot move this miner. */
export class ImmovableMinerMoveStrategy implements MinerMoveStrategy {
    public move(miner: Miner, position: number): boolean { return false; }
    public stop(miner: Miner): void { }
    public update(miner: Miner, delta: number): void { }
}

export class LinearMinerMoveStrategy implements MinerMoveStrategy {
    private position?: number;

    public constructor() {
    }

    public move(miner: Miner, position: number): boolean {
        this.position = position;
        return true;
    }

    public stop(miner: Miner): void {
        this.position = undefined;
    }

    public update(miner: Miner, delta: number): void {
        if (this.position !== undefined) {
            // Update position.
            const engine = miner.speedEngine;
            const speed = engine.getSpeed();
            const distance = speed * delta;

            const node = miner.node;
            let x = node.x;
            if (x < this.position) {
                // Move right.
                x += distance;
                if (x > this.position) {
                    x = this.position;
                    this.stop(miner);
                }
            } else {
                // Move left.
                x -= distance;
                if (x < this.position) {
                    x = this.position;
                    this.stop(miner);
                }
            }
            node.x = x;
        }
    }
}

/** Effective when using boat. */
export class FloatingMinerMoveStrategy implements MinerMoveStrategy {
    /** Direction sign, must be -1 or +1. */
    private direction: number;

    private dirtyDirection = true;

    public constructor(
        private readonly leftLimit: number,
        private readonly rightLimit: number,
        private readonly speed: number) {
        this.direction = MathUtils.getRandom(0, 1) < 0.5 ? +1 : -1;
    }

    public move(miner: Miner, position: number): boolean {
        // Can't control.
        return true;
    }

    public stop(miner: Miner): void {
        // Can't stop.
    }

    public update(miner: Miner, delta: number): void {
        if (this.dirtyDirection) {
            this.dirtyDirection = false;
            this.changeDirection(miner, this.direction);
        }

        const distance = this.speed * delta;
        const node = miner.node;
        let x = node.x + this.direction * distance;
        if (x < this.leftLimit) {
            x = this.leftLimit;
            this.direction = -this.direction;
            this.changeDirection(miner, this.direction);
        } else if (x > this.rightLimit) {
            x = this.rightLimit;
            this.direction = -this.direction;
            this.changeDirection(miner, this.direction);
        }
        node.x = x;
    }

    public changeDirection(miner: Miner, direction: number): void {
    }
}

export class InstantMinerMoveStrategy implements MinerMoveStrategy {
    private readonly animator: Animator;

    public constructor() {
        this.animator = new Animator();
    }

    public move(miner: Miner, position: number): boolean {
        if (this.animator.getActions().length > 0) {
            return false;
        }
        this.animator.removeAllActions();

        // Consequence jumps.
        const engine = miner.speedEngine;
        const speed = engine.getSpeed();

        const node = miner.node;
        const x = node.x;
        const delta = position - x;
        const height = 80;
        const duration = Math.abs(delta) / (speed * 10);
        const jumps = Math.ceil(Math.abs(delta) / (height * 5));
        // this.animator.
        // addAction(cc.targetedAction(context.node, cc.jumpBy(duration, cc.v2(delta, 0), height, jumps)));

        // Teleport.
        const hide = cc.scaleTo(0.2, 0.0).easing(cc.easeBackOut());
        const show = cc.scaleTo(0.2, 1.0).easing(cc.easeBackIn());
        this.animator.addAction(
            cc.sequence(
                cc.spawn(
                    cc.targetedAction(node, hide.clone()),
                    cc.targetedAction(miner.claw.node, hide.clone()),
                ),
                cc.delayTime(0.2),
                cc.targetedAction(node, cc.moveBy(0, cc.v2(delta, 0))),
                cc.spawn(
                    cc.targetedAction(node, show.clone()),
                    cc.targetedAction(miner.claw.node, show.clone()),
                ),
            ),
        );
        return true;
    }

    public stop(miner: Miner): void {
        this.animator.removeAllActions();
    }

    public update(miner: Miner, delta: number): void {
        this.animator.animate(delta);
    }
}