import assert = require('assert');
import { CollisionListener } from '../../activities/CollisionActivity';
import { Entity } from '../../items/Entity';
import { EntityManager } from "../../manager/EntityManager";
import { TimeHolder } from '../../utils/Global';
import { Miner } from "../Miner";
import { MinerCaptureStrategy, MinerNeverCaptureStrategy } from "./MinerCaptureStrategy";
import { MinerDigStrategy, NullMinerDigStrategy } from "./MinerDigStrategy";
import { ImmovableMinerMoveStrategy, MinerMoveStrategy } from "./MinerMoveStrategy";
import { MinerPullStrategy, NullMinerPullStrategy } from "./MinerPullStrategy";
import { MinerStrategy } from "./MinerStrategy";
import { MinerSwingStrategy, NullMinerSwingStrategy } from "./MinerSwingStrategy";
import { ImmediateMinerTakeStrategy, MinerTakeStrategy } from "./MinerTakeStrategy";
import { MinerThrowStrategy, NullMinerThrowStrategy } from "./MinerThrowStrategy";

/**
 * All available states in this strategy.
 */
enum State {
    /** The miner is about to swing. */
    Idle,

    /** The current miner's claw is swinging. */
    Swinging,

    /** The current miner's claw is digging. */
    Digging,

    Capturing,

    /** The current miner's claw is being pulled. */
    Pulling,

    /** The current miner is taking the claw's item. */
    Taking,
}

export class DefaultMinerStrategy implements MinerStrategy, CollisionListener {
    private state: State;
    private freely: boolean;

    public swingStrategy: MinerSwingStrategy;
    public digStrategy: MinerDigStrategy;
    public captureStrategy: MinerCaptureStrategy;
    public pullStrategy: MinerPullStrategy;
    public takeStrategy: MinerTakeStrategy;
    public throwStrategy: MinerThrowStrategy;
    public moveStrategy: MinerMoveStrategy;

    public constructor(private readonly miner: Miner) {
        // Initial state.
        this.state = State.Idle;
        this.freely = false;

        // Default strategies.
        this.swingStrategy = new NullMinerSwingStrategy();
        this.digStrategy = new NullMinerDigStrategy();
        this.captureStrategy = new MinerNeverCaptureStrategy();
        this.pullStrategy = new NullMinerPullStrategy();
        this.takeStrategy = new ImmediateMinerTakeStrategy();
        this.throwStrategy = new NullMinerThrowStrategy();
        this.moveStrategy = new ImmovableMinerMoveStrategy();
    }

    public get isSwinging(): boolean {
        return this.state === State.Swinging;
    }

    public dig(): boolean {
        if (this.state === State.Swinging) {
            // Perform simple state changing.
            this.state = State.Digging;
            if (!this.freely) {
                this.moveStrategy.stop(this.miner);
            }
            this.digStrategy.dig(this.miner);
            return true;
        }
        return false;
    }

    public throwItem(entity: Entity): boolean {
        return this.throwStrategy.throwItem(this.miner, entity);
    }

    public move(position: number): boolean {
        return (this.freely || this.state === State.Swinging) && this.moveStrategy.move(this.miner, position);
    }

    public pickItem(entity: Entity) {
        this.state = State.Taking;
        this.takeStrategy.take(this.miner, entity);
    }

    public captureItem(entity: Entity, manager: EntityManager): void {
        if (this.captureStrategy.capture(this.miner, entity, manager)) {
            this.state = State.Capturing;
        }
    }

    /** Sets whether to move freely when digging or pulling item. */
    public setFreely(freely: boolean): void {
        this.freely = freely;
        this.moveStrategy.stop(this.miner);
    }

    public processUpdate(delta: number, manager: EntityManager): void {
        this.throwStrategy.update(this.miner, delta);
        if (this.freely || this.state === State.Swinging) {
            this.moveStrategy.update(this.miner, delta);
        }
        if (this.state === State.Idle) {
            this.state = State.Swinging;
            this.swingStrategy.swing(this.miner);
            return;
        }
        if (this.state === State.Swinging) {
            this.swingStrategy.update(this.miner, delta);
            return;
        }
        if (this.state === State.Digging) {
            this.digStrategy.update(this.miner, delta);
            return;
        }
        if (this.state === State.Capturing) {
            this.captureStrategy.update(this.miner, delta);
            if (this.captureStrategy.isDone(this.miner)) {
                this.state = State.Pulling;
                this.pullStrategy.pull(this.miner);
            }
            return;
        }
        if (this.state === State.Pulling) {
            this.pullStrategy.update(this.miner, delta);
            if (this.pullStrategy.isDone(this.miner)) {
                this.state = State.Taking;
                const entity = this.miner.claw.releaseItem();
                this.pullStrategy.finish(this.miner);
                this.takeStrategy.take(this.miner, entity);
            }
            return;
        }
        if (this.state === State.Taking) {
            this.takeStrategy.update(this.miner, delta);
            if (this.takeStrategy.isDone(this.miner)) {
                this.state = State.Idle;
                cc.log("Pulled time: " + TimeHolder.digTimeInMilis);
                const duration = Date.now() - TimeHolder.digTimeInMilis;
                cc.log("Pulled duration: " + duration);
            }
            return;
        }
    }

    public test(lhs: Entity, rhs: Entity, manager: EntityManager): boolean {
        const claw = this.miner.claw;
        if (lhs !== claw) {
            return false;
        }
        if (this.state !== State.Digging) {
            return false;
        }
        return true;
    }

    public process(lhs: Entity, rhs: Entity, manager: EntityManager): void {
        assert(this.test(lhs, rhs, manager));
        if (this.captureStrategy.capture(this.miner, rhs, manager)) {
            this.state = State.Capturing;
        }
    }

    public serialize(): any {
        return {
            state: this.state,
        };
    }

    public deserialize(_data: any): void {
        const data = _data as {
            state?: State,
        };
        this.state = data.state || State.Idle;
    }
}