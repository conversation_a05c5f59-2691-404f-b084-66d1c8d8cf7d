import { Entity } from '../../items/Entity';

export enum RopeType {
    Normal,
    Titanium,
}

export enum RopeDirection {
    Left,
    Right,
}

/**
 * Represents the rope display.
 * Rope is an item that connects claw and miner.
 */
export abstract class Rope extends Entity {
    /** Gets or sets the type of the rope. */
    public abstract type: RopeType;

    /** Gets or sets the rope's swing direction. */
    public abstract direction: RopeDirection;

    /** Gets or sets the rope angle. */
    public abstract angle: number;

    /** Gets or sets the length of this rope. */
    public abstract length: number;
}

/** Null state for Rope. */
export class NullRope extends Rope {
    public get entityId(): string { return 'null_rope'; }
    public type: RopeType = RopeType.Normal;
    public direction = RopeDirection.Left;
    public angle: number = 0;
    public length: number = 0;
}