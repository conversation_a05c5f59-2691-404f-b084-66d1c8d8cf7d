import assert = require('assert');
import { CollisionListener } from "../../activities/activities";
import { Collidable } from "../../components/components";
import { Barricade, Entity } from "../../items/items";
import { EntityManager } from "../../manager/EntityManager";
import { Miner } from "../../miners/miners";
import { TransformHelper } from "../../utils/Utils";

type Listener = () => void;

/** Resolves collisions between Barricade and Miner. */
export class BarricadeCollisionListener implements CollisionListener {
    private listener?: Listener;

    public constructor(private readonly miner: Miner) {
    }

    public setListener(listener: Listener): this {
        this.listener = listener;
        return this;
    }

    public test(lhs: Entity, rhs: Entity, manager: EntityManager): boolean {
        if (!(lhs instanceof Miner)) {
            return false;
        }
        if (!(rhs instanceof Barricade)) {
            return false;
        }
        const miner = lhs as Miner;
        if (this.miner !== miner) {
            return false;
        }
        return true;
    }

    public process(lhs: <PERSON><PERSON><PERSON>, rhs: <PERSON><PERSON><PERSON>, manager: EntityManager): void {
        assert(this.test(lhs, rhs, manager));

        const miner = this.miner;
        const barricade = rhs as Barricade;
        const view = manager.getView().node;

        // Test AABB only.
        const minerAabb = miner.getComponent(Collidable).getShape(view).getAABB();
        const barricadeAabb = barricade.getComponent(Collidable).getShape(view).getAABB();

        const helper = new TransformHelper(miner.node, view);

        const leftDelta = barricadeAabb.xMax - minerAabb.xMin;
        const rightDelta = minerAabb.xMax - barricadeAabb.xMin;
        if (leftDelta < rightDelta) {
            // Barricade is on the left side.
            // Checks if barricade is actually collides.
            const delta = leftDelta;
            if (delta > 1e-3) {
                const target = helper.convertTo(cc.Vec2.ZERO).x + delta;
                miner.move(target);
                this.listener && this.listener();
            }
        } else {
            // Barricade is on the right side.
            // Checks if barricade is actually collides.
            const delta = rightDelta;
            if (delta > 1e-3) {
                const target = helper.convertTo(cc.Vec2.ZERO).x - delta;
                miner.move(target);
                this.listener && this.listener();
            }
        }
    }
}