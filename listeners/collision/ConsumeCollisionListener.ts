import assert = require('assert');
import { CollisionListener } from "../../activities/activities";
import {
    Cartable,
    Consumable,
    Consumer,
    Pullable,
} from "../../components/components";
import { Entity } from '../../items/Entity';
import { EntityManager } from "../../manager/EntityManager";
import { TransformHelper } from "../../utils/Utils";

/** Resolves collisions between Consumer and Consumable. */
export class ConsumeCollisionListener implements CollisionListener {
    public test(lhs: Entity, rhs: Entity, manager: EntityManager): boolean {
        const consumer = lhs.getComponent(Consumer);
        const consumable = rhs.getComponent(Consumable);
        if (consumer === null || consumable === null) {
            return false;
        }
        if (!consumer.canTarget()) {
            return false;
        }
        if (!consumer.isAlive() || !consumable.isAlive()) {
            return false;
        }
        {
            const pullable = consumer.getComponent(Pullable);
            if (pullable !== null && pullable.isPulled()) {
                return false;
            }

            const cartable = consumer.getComponent(Cartable);
            if (cartable !== null && cartable.isCarted()) {
                return false;
            }
        }
        {
            // Check whether consumable is being pulled.
            const pullable = consumable.getComponent(Pullable);
            if (pullable !== null && pullable.isPulled()) {
                return false;
            }
        }
        {
            // Don't target if target is targeting another Consumable entity.
            const item = consumable.getComponent(Consumer);
            if (item !== null && item.isTargeting()) {
                return false;
            }
        }
        return true;
    }

    public process(lhs: Entity, rhs: Entity, manager: EntityManager): void {
        assert(this.test(lhs, rhs, manager));
        const consumer = lhs.getComponent(Consumer);
        const consumable = rhs.getComponent(Consumable);

        // Make consumer on top.
        consumer.node.zIndex = Math.max(consumer.node.zIndex, consumable.node.zIndex + 1);

        const helper = new TransformHelper(consumable.node, consumer.node.parent);
        const destination = helper.convertTo(cc.Vec2.ZERO);
        const delta = destination.sub(consumer.node.position);
        consumer.target(consumable, delta);
    }
}