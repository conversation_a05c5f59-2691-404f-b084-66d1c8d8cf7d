import assert = require('assert');
import { CollisionListener } from '../../activities/activities';
import {
    Boomerang,
    Entity,
    Stranger<PERSON>oat,
} from '../../items/items';
import { EntityManager } from "../../manager/EntityManager";

/** Resolves collisions between Boomerang and StrangerBoat. */
export class BoomerangBoatCollisionListener implements CollisionListener {
    public test(lhs: <PERSON><PERSON>ty, rhs: <PERSON><PERSON><PERSON>, manager: EntityManager): boolean {
        if (!(lhs instanceof Boomerang)) {
            return false;
        }
        if (!(rhs instanceof StrangerBoat)) {
            return false;
        }
        const boomerang = lhs as Boomerang;
        if (!boomerang.isReturning()) {
            return false;
        }
        return true;
    }

    public process(lhs: <PERSON><PERSON><PERSON>, rhs: <PERSON><PERSON><PERSON>, manager: EntityManager): void {
        assert(this.test(lhs, rhs, manager));
        const boomerang = lhs as Boomerang;
        const boat = rhs as StrangerBoat;
        boomerang.kill();
        boat.grabBoomerang();
    }
}