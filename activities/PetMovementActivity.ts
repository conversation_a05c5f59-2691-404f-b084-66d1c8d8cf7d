/** Manages pet movement. */
export interface PetMovementActivity {
    /**
     * Sets the movement offset, i.e. maximum distance from the miner.
     * @param offset The desired offset.
     * @return Instance to this for method chaining.
     */
    setOffset(offset: number): this;

    /**
     * Sets the target, i.e. miner's position.
     * @param target The horizontal position.
     * @return Instance to this for method chaining.
     */
    setTarget(target: number): this;

    update(delta: number): void;

    stop(): void;
}

export class NullPetMovementActivity implements PetMovementActivity {
    public setOffset(offset: number): this { return this; }
    public setTarget(target: number): this { return this; }
    public update(delta: number): void { }
    public stop(): void { }
}