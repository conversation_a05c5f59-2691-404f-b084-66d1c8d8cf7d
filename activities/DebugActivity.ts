import assert = require('assert');
import { Collidable } from '../components/components';
import { EntityManager } from '../manager/EntityManager';
import { LazyPolygon, MultiPolygon, Polygon } from '../math/math';
import { Activity } from './Activity';

const drawPolygon = (graphics: cc.Graphics, shape: Polygon | LazyPolygon) => {
    const vertices = shape.getVertices();
    if (vertices.length === 0) {
        return;
    }
    graphics.moveTo(vertices[0].x, vertices[0].y);
    vertices.forEach(vertex => graphics.lineTo(vertex.x, vertex.y));
    graphics.close();
    graphics.stroke();
};

const drawMultiPolygon = (graphics: cc.Graphics, shape: MultiPolygon) => {
    shape.getPolygons().forEach(polygon => drawPolygon(graphics, polygon));
};

/** Draws all colliable objects using the specified graphics object. */
export class DebugActivity implements Activity {
    public constructor(private readonly graphics: cc.Graphics) {
    }

    public processUpdate(delta: number, manager: EntityManager): void {
        this.graphics.clear();
        const components = manager.findComponents(Collidable);
        components.forEach(component => {
            const shape = component.getShape(this.graphics.node);
            if (shape instanceof Polygon || shape instanceof LazyPolygon) {
                drawPolygon(this.graphics, shape);
            } else if (shape instanceof MultiPolygon) {
                drawMultiPolygon(this.graphics, shape);
            }
        });
    }
}
