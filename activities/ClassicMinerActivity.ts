/*
 FIXME.
import { CharacterAni<PERSON>, Miner, Character } from "../miners/miners";
import { EntityManager } from "../manager/manager";
import { Pullable } from "../components/components";
import { Entity } from "../items/items";
import { Animator } from "../utils/Animator";

export class ClassicMinerActivity extends DefaultMinerActivity {
    private animator: Animator;
    private stopped: boolean;

    public constructor(miner: Miner) {
        super(miner);
        this.animator = new Animator();
        this.stopped = true;
    };

    private getCharacter(): Character {
        return this.getMiner().getCharacter();
    };

    public swing(delay?: number): void {
        super.swing(delay);
        if (delay === undefined) {
            this.getCharacter().playAnimation(CharacterAnimation.Wait, true);
        }
    };

    public wait(): void {
        super.wait();
        this.animator.removeAllActions();
    };

    public dig(): boolean {
        if (!super.dig()) {
            return false;
        };
        this.getCharacter().playAnimation(CharacterAnimation.Dig, false);
        return true;
    };

    public pull(entity?: Entity): boolean {
        this.getMiner().getClaw().releaseItem(); // release old item.
        // Used for heavy entities.
        let oldPosition: cc.Vec2 | undefined;
        let oldParent: cc.Node | undefined;
        if (entity !== undefined) {
            oldPosition = entity.node.position;
            oldParent = entity.node.parent;
        }
        if (!super.pull(entity)) {
            return false;
        }
        if (entity !== undefined) {
            this.getCharacter().playAnimation(CharacterAnimation.Pull, true);
        }
        return true;
    };

    private showPoint(item: cc.Node): boolean {
        if (item !== undefined) {
            let component = item.getComponent(Pullable);
            let value = component.getValue();
            // this.moneyChangeActivity!.addMoney(value);
            // this.coreloopManager!.addMoney(value);
            return true;
        }
        return false;
    }

    public take(): boolean {
        if (!super.take()) {
            this.getMiner().getCharacter().playAnimation(CharacterAnimation.Wait, true);
            return false;
        } else {

        }
        return true;
    };

    public processUpdate(delta: number, manager: EntityManager): void {
        this.animator.animate(delta);

        const minerView = this.getMiner().node;
        const oldX = minerView.x;
        super.processUpdate(delta, manager);
        const newX = minerView.x;

        const moving = (oldX !== newX);
        if (moving) {
            this.stopped = false;
        } else {
            if (!this.stopped) {
                this.getCharacter().playAnimation(CharacterAnimation.Wait, true);
                this.stopped = true;
            }
        }
    };
};
*/