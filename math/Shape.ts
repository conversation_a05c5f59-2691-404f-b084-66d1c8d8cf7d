export interface Shape {
    /** Gets AABB. */
    getAABB(): cc.Rect;

    /**
     * Transforms this shape and returns a new copy.
     * @return The transformed shape.
     */
    transform(transform: cc.AffineTransform): Shape;

    /**
     * Tests whether this shape collides with another shape.
     * @param shape The shape to test collision with.
     * @return True if there is a collision, false otherwise.
     */
    collides(shape: Shape): boolean;

    getSquareMinDistanceToPoint(point: cc.Vec2): number;
}

/** Null state for Shape. */
export class NullShape {
    public getAABB(): cc.Rect { return cc.rect(); }
    public transform(transform: cc.AffineTransform): Shape { return new NullShape(); }
    public collides(shape: Shape): boolean { return false; }
    public getSquareMinDistanceToPoint(point: cc.Vec2): number { return Number.MAX_VALUE; }
}