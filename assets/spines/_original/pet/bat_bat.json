{"skeleton": {"hash": "jEUhJBOThecg3nELHwfik5h9s2Q", "spine": "3.6.50", "width": 157.48, "height": 92.41, "images": ""}, "bones": [{"name": "root_0.29", "scaleX": -0.29, "scaleY": 0.29}, {"name": "body", "parent": "root_0.29", "length": 44.36, "rotation": -76.25, "x": 15.3, "y": 395.37}, {"name": "body2", "parent": "body", "length": 44.36, "rotation": 10.42, "x": 44.36, "color": "abe323ff"}, {"name": "head", "parent": "body", "length": 29.64, "rotation": 174.7, "x": 0.38, "y": -9.01}, {"name": "earL", "parent": "head", "length": 25.56, "rotation": -33.92, "x": 79.04, "y": -79.65}, {"name": "earL2", "parent": "earL", "length": 25.56, "rotation": 13.29, "x": 27.39, "y": -0.7}, {"name": "earL3", "parent": "earL2", "length": 25.56, "rotation": -17.29, "x": 26.55, "y": -2.63}, {"name": "earR", "parent": "head", "length": 27.38, "rotation": 15.62, "x": 95.44, "y": 34.97}, {"name": "earR2", "parent": "earR", "length": 27.38, "rotation": -24.24, "x": 27, "y": -0.15}, {"name": "earR3", "parent": "earR2", "length": 27.38, "rotation": 17.89, "x": 28.44, "y": 2.86}, {"name": "eye", "parent": "head", "rotation": -5.32, "x": 60.96, "y": -7.45}, {"name": "head2", "parent": "head", "rotation": -5.32, "x": 38.61, "y": 54.8}, {"name": "head3", "parent": "head", "rotation": -5.32, "x": 13.82, "y": -86.77}, {"name": "head4", "parent": "head", "rotation": -5.32, "x": 34.93, "y": 1.64, "color": "abe323ff"}, {"name": "head5", "parent": "head", "x": 101.1, "y": 10.33}, {"name": "head6", "parent": "head", "x": 88.17, "y": -61.36}, {"name": "head7", "parent": "head", "x": 152.42, "y": -32.6}, {"name": "legL", "parent": "body", "length": 31.33, "rotation": 20.06, "x": 85.34, "y": 90.09}, {"name": "legR", "parent": "body", "length": 24.19, "rotation": -70.78, "x": 72.88, "y": -65.92}, {"name": "wingL", "parent": "body", "length": 64.1, "rotation": 120.75, "x": 17.83, "y": 50.58, "scaleX": 0.783}, {"name": "wingL2", "parent": "wingL", "length": 73.06, "rotation": 29.95, "x": 77.55, "y": 1.25}, {"name": "wingL3", "parent": "wingL2", "length": 90.47, "rotation": -40.41, "x": 80.83, "y": -2.41, "color": "abe323ff"}, {"name": "wingL4", "parent": "wingL3", "length": 99.57, "rotation": -22.99, "x": 94.31, "y": -3.08, "color": "abe323ff"}, {"name": "wingL5", "parent": "wingL2", "length": 53.64, "rotation": -117.56, "x": 57.95, "y": -62.64}, {"name": "wingR", "parent": "body", "length": 68.79, "rotation": -142.15, "x": -0.37, "y": -35.8, "scaleX": 0.77}, {"name": "wingR2", "parent": "wingR", "length": 78.82, "rotation": -26.94, "x": 73.28, "y": -1.17}, {"name": "wingR3", "parent": "wingR2", "length": 85.64, "rotation": 37.47, "x": 92.33, "y": 2.78, "color": "abe323ff"}, {"name": "wingR4", "parent": "wingR3", "length": 100.21, "rotation": 25.04, "x": 93.52, "y": 2.84}, {"name": "wingR5", "parent": "wingR2", "length": 45.09, "rotation": 113.98, "x": 74.2, "y": 49.95}], "slots": [{"name": "wingR2", "bone": "wingR", "attachment": "wingR"}, {"name": "wingR", "bone": "wingR"}, {"name": "wingL2", "bone": "wingL", "attachment": "wingL"}, {"name": "body2", "bone": "body", "attachment": "body"}, {"name": "legL", "bone": "legL", "attachment": "legL"}, {"name": "legR", "bone": "legR", "attachment": "legR"}, {"name": "eyeBack", "bone": "head7", "attachment": "eyeBack"}, {"name": "eye", "bone": "eye", "attachment": "eye"}, {"name": "head", "bone": "head", "attachment": "head"}], "transform": [{"name": "eye", "order": 6, "bones": ["eye"], "target": "head4", "rotateMix": 0.15, "translateMix": 0.15, "scaleMix": 0.15, "shearMix": 0.15}, {"name": "head4", "order": 2, "bones": ["head3"], "target": "head4", "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2}, {"name": "head5", "order": 3, "bones": ["head2"], "target": "head4", "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2}, {"name": "head6", "order": 4, "bones": ["head5"], "target": "head4", "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1}, {"name": "head7", "order": 5, "bones": ["head6"], "target": "head4", "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1}, {"name": "legL", "order": 8, "bones": ["legL"], "target": "body2", "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5}, {"name": "legR", "order": 7, "bones": ["legR"], "target": "body2", "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5}, {"name": "wingL3", "order": 0, "bones": ["wingL5"], "target": "wingL3", "rotation": -75.9, "x": 24, "y": -70, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5}, {"name": "wingR3", "order": 1, "bones": ["wingR5"], "target": "wingR3", "rotation": 80.2, "x": 20, "y": 60, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5}], "skins": {"default": {"body2": {"body": {"type": "mesh", "uvs": [0, 0.29827, 0, 0.48307, 0.0337, 0.64939, 0.12187, 0.81043, 0.2453, 0.93451, 0.43423, 1, 0.67607, 1, 0.78943, 0.96355, 0.87759, 0.89491, 0.94813, 0.79987, 1, 0.68635, 1, 0.55171, 1, 0.39067, 0.9582, 0.25075, 0.85996, 0.11875, 0.76675, 0.03955, 0.63072, 0, 0.44683, 0, 0.27301, 0.05275, 0.15462, 0.12667, 0.06897, 0.20323, 0.06141, 0.43027, 0.16721, 0.51739, 0.32088, 0.55963, 0.42164, 0.64939, 0.51233, 0.72067, 0.55515, 0.70747, 0.56271, 0.62299, 0.56271, 0.49891, 0.67859, 0.40387, 0.77179, 0.27979, 0.23271, 0.72067, 0.4292, 0.76291, 0.535, 0.88435, 0.6937, 0.79987, 0.64584, 0.67315, 0.79194, 0.56491, 0.84485, 0.47251], "triangles": [27, 25, 24, 31, 24, 32, 3, 31, 4, 4, 31, 32, 10, 36, 11, 26, 27, 35, 26, 25, 27, 32, 24, 25, 34, 35, 36, 9, 34, 36, 26, 35, 34, 10, 9, 36, 33, 25, 26, 33, 26, 34, 32, 25, 33, 8, 34, 9, 5, 4, 32, 7, 34, 8, 33, 5, 32, 6, 33, 34, 6, 34, 7, 5, 33, 6, 3, 2, 31, 36, 37, 11, 30, 15, 14, 30, 14, 13, 30, 29, 16, 30, 16, 15, 21, 0, 20, 37, 30, 13, 37, 13, 12, 29, 30, 37, 1, 0, 21, 29, 28, 17, 29, 17, 16, 18, 17, 28, 23, 22, 19, 18, 23, 19, 20, 19, 22, 21, 20, 22, 37, 12, 11, 28, 23, 18, 36, 29, 37, 28, 29, 36, 35, 27, 28, 24, 23, 28, 22, 2, 1, 22, 1, 21, 27, 24, 28, 36, 35, 28, 31, 22, 23, 31, 23, 24, 2, 22, 31], "vertices": [1, 1, -27.91, -40.24, 1, 2, 1, -7.25, -50.57, 0.82, 2, -59.9, -40.4, 0.18, 2, 1, 13.32, -55.92, 0.55, 2, -40.64, -49.38, 0.45, 2, 1, 36.49, -54.59, 0.19, 2, -17.61, -52.27, 0.81, 2, 1, 57.6, -47.06, 0.12, 2, 4.5, -48.68, 0.88, 2, 1, 75.99, -28.59, 0.06, 2, 25.93, -33.83, 0.94, 1, 2, 44.99, -8.53, 1, 2, 1, 92.72, 15.07, 0.00202, 2, 50.29, 6.07, 0.99798, 2, 1, 90.21, 29.24, 0.02999, 2, 50.38, 20.46, 0.97001, 2, 1, 83.72, 42.81, 0.10566, 2, 46.45, 34.99, 0.89434, 2, 1, 74.06, 55.24, 0.22118, 2, 39.2, 48.95, 0.77882, 2, 1, 59.01, 62.76, 0.39223, 2, 25.76, 59.08, 0.60777, 2, 1, 41.01, 71.77, 0.60675, 2, 9.68, 71.19, 0.39325, 2, 1, 22.91, 74.69, 0.76, 2, -7.58, 77.34, 0.24, 2, 1, 2.4, 70.56, 0.93, 2, -28.51, 76.99, 0.07, 1, 1, -11.91, 64.07, 1, 2, 1, -24.31, 50.34, 0.97696, 2, -58.43, 61.93, 0.02304, 2, 1, -35.08, 28.79, 0.99922, 2, -72.92, 42.68, 0.00078, 1, 1, -39.36, 5.48, 1, 1, 1, -38.04, -12.53, 1, 1, 1, -34.49, -26.84, 1, 2, 1, -9.55, -40.42, 0.99934, 2, -60.34, -30, 0.00066, 2, 1, 6.39, -32.89, 0.9, 2, -43.3, -25.48, 0.1, 2, 1, 20.11, -17.25, 0.85, 2, -26.97, -12.58, 0.15, 2, 1, 36.05, -10.46, 0.64, 2, -10.07, -8.79, 0.36, 2, 1, 49.33, -3.82, 0.11178, 2, 4.2, -4.66, 0.88822, 2, 1, 50.36, 1.93, 0.01159, 2, 6.25, 0.82, 0.98841, 2, 1, 41.36, 7.54, 0.6, 2, -1.59, 7.96, 0.4, 2, 1, 27.49, 14.48, 0.95, 2, -13.97, 17.29, 0.05, 2, 1, 23.65, 33.37, 0.9, 2, -14.33, 36.56, 0.1, 2, 1, 15.24, 51.23, 0.91, 2, -19.38, 55.65, 0.09, 2, 1, 32.95, -36.58, 0.45, 2, -17.84, -33.92, 0.55, 2, 1, 49.18, -15.92, 0.22, 2, 1.86, -16.53, 0.78, 2, 1, 68.96, -10.31, 0.02684, 2, 22.33, -14.59, 0.97316, 2, 1, 68.81, 13, 0.01834, 2, 26.4, 8.37, 0.98166, 2, 1, 51.84, 14.48, 0.19, 2, 9.97, 12.89, 0.81, 2, 1, 48.3, 37.65, 0.53, 2, 10.68, 36.32, 0.47, 2, 1, 41.07, 49.01, 0.63, 2, 5.63, 48.8, 0.37], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 30, 4, 62, 62, 64, 64, 66, 66, 12, 14, 68, 68, 70, 70, 72, 72, 74, 74, 26], "width": 131, "height": 125}}, "eye": {"eye": {"x": 3.52, "y": -1.21, "rotation": -105.95, "width": 80, "height": 39}}, "eyeBack": {"eyeBack": {"x": -97.64, "y": 20.47, "rotation": -111.27, "width": 105, "height": 64}}, "head": {"head": {"type": "mesh", "uvs": [0.00116, 0.3044, 0.00251, 0.15269, 0.04228, 0.14027, 0.12503, 0.24207, 0.21538, 0.29248, 0.30305, 0.34139, 0.37577, 0.40843, 0.49612, 0.37615, 0.60143, 0.38111, 0.65409, 0.24704, 0.78949, 0.13531, 0.8622, 0, 0.8905, 0, 0.9299, 0, 1, 0.14772, 1, 0.26442, 1, 0.37367, 1, 0.49285, 0.94996, 0.56733, 0.89229, 0.61451, 0.92488, 0.67907, 0.92488, 0.76845, 0.89229, 0.84791, 0.82459, 0.91743, 0.73432, 0.96212, 0.61898, 1, 0.48358, 1, 0.37827, 0.97453, 0.30054, 0.94226, 0.22532, 0.86777, 0.22282, 0.81314, 0.24037, 0.74611, 0.14509, 0.7039, 0.06987, 0.62941, 0.02473, 0.54499, 0, 0.43574, 0.23805, 0.66663, 0.24758, 0.59951, 0.27829, 0.50407, 0.3196, 0.42437, 0.69563, 0.39815, 0.75282, 0.3971, 0.83755, 0.51876, 0.31435, 0.78305, 0.40119, 0.79668, 0.47639, 0.75683, 0.54311, 0.74529, 0.62043, 0.76417, 0.70516, 0.73585, 0.80577, 0.66873, 0.82695, 0.32683, 0.83225, 0.20413, 0.8725, 0.10554, 0.78955, 0.45881, 0.16395, 0.48205, 0.1523, 0.37717, 0.09617, 0.29956, 0.36723, 0.61436, 0.45566, 0.67673, 0.53204, 0.66876, 0.59635, 0.5865, 0.73034, 0.53874, 0.7692, 0.62233, 0.39939, 0.52149, 0.55883, 0.50291, 0.31229, 0.84789, 0.53204, 0.85717, 0.38867, 0.88902, 0.49318, 0.93944, 0.57223, 0.95005, 0.64861, 0.89432, 0.7156, 0.824, 0.82547, 0.75766, 0.2721, 0.65815, 0.29715, 0.60566], "triangles": [44, 45, 66, 66, 45, 46, 66, 46, 47, 66, 47, 70, 45, 44, 58, 47, 46, 59, 45, 59, 46, 67, 44, 66, 5, 55, 4, 35, 56, 55, 55, 56, 4, 56, 3, 4, 0, 1, 56, 56, 1, 3, 3, 1, 2, 56, 35, 0, 54, 35, 55, 54, 34, 35, 33, 34, 54, 50, 9, 51, 51, 10, 52, 14, 52, 13, 52, 12, 13, 52, 11, 12, 10, 11, 52, 51, 52, 14, 15, 51, 14, 9, 10, 51, 50, 51, 15, 50, 15, 16, 62, 60, 61, 60, 64, 61, 59, 58, 63, 58, 57, 63, 59, 64, 60, 59, 63, 64, 45, 58, 59, 43, 57, 58, 47, 59, 60, 60, 48, 47, 47, 48, 71, 62, 61, 42, 23, 71, 72, 48, 49, 72, 49, 62, 19, 62, 42, 19, 19, 42, 18, 23, 72, 22, 22, 72, 21, 49, 19, 20, 72, 20, 21, 72, 49, 20, 71, 48, 72, 48, 62, 49, 18, 42, 17, 62, 48, 60, 17, 42, 16, 53, 50, 16, 9, 40, 8, 64, 8, 40, 53, 40, 41, 41, 40, 9, 53, 61, 40, 53, 41, 50, 64, 7, 8, 61, 64, 40, 61, 53, 42, 50, 41, 9, 16, 42, 53, 57, 74, 63, 63, 7, 64, 63, 6, 7, 55, 39, 54, 74, 38, 63, 37, 38, 74, 37, 54, 38, 39, 55, 5, 38, 39, 63, 39, 5, 6, 38, 54, 39, 39, 6, 63, 33, 54, 37, 73, 37, 74, 57, 73, 74, 44, 43, 58, 27, 28, 67, 30, 31, 43, 37, 32, 33, 32, 36, 31, 28, 29, 65, 29, 30, 65, 36, 32, 37, 65, 30, 43, 43, 31, 36, 28, 65, 67, 43, 36, 73, 65, 43, 44, 36, 37, 73, 67, 65, 44, 43, 73, 57, 27, 67, 68, 26, 27, 68, 70, 71, 24, 25, 70, 24, 25, 26, 69, 26, 68, 69, 25, 69, 70, 68, 66, 69, 69, 66, 70, 24, 71, 23, 70, 47, 71, 68, 67, 66], "vertices": [5, 11, 125.61, 30.39, 0.00036, 14, 59.26, 53.78, 0.00127, 7, 76.03, 17.48, 0.10245, 8, 46.92, 17.26, 0.23494, 9, 20.75, 17.48, 0.66098, 4, 14, 89.45, 43.82, 0.00334, 7, 104.34, 2.67, 0.03543, 8, 74.91, 2.74, 0.17227, 9, 48.94, 2.8, 0.78896, 4, 14, 88.01, 35.22, 0.0116, 7, 101.51, -5.62, 0.00232, 8, 72.17, -5.48, 0.15134, 9, 46.14, -5.47, 0.83474, 6, 14, 63.16, 26.48, 0.05825, 15, 81.41, 101.1, 0.00331, 10, 108.59, 54.08, 0.00168, 7, 75.48, -10.3, 0.00046, 8, 46.38, -10, 0.23305, 9, 20.21, -10.09, 0.70326, 6, 14, 48.48, 11.62, 0.19034, 15, 66.73, 86.24, 0.02525, 10, 95.35, 37.92, 0.01063, 7, 58.4, -22.74, 0.00076, 8, 29.84, -22.05, 0.30948, 9, 3.32, -22.39, 0.46353, 6, 14, 33.55, -3.75, 0.38338, 15, 51.8, 70.87, 0.07768, 10, 81.91, 21.23, 0.02651, 7, 40.9, -35.54, 0.00073, 8, 13.14, -34.67, 0.2823, 9, -13.89, -35.16, 0.22942, 3, 14, 12.09, -13.54, 0.71955, 15, 30.34, 61.09, 0.2204, 10, 61.44, 9.5, 0.06005, 3, 14, 9.39, -38.7, 0.49969, 15, 27.64, 35.92, 0.42862, 10, 61.09, -15.81, 0.07169, 5, 3, 108.58, -47.65, 0, 14, 0.69, -58.26, 0.27166, 15, 18.94, 16.37, 0.65895, 12, 88.47, 31.39, 0.00278, 10, 54.24, -36.09, 0.0666, 8, 3, 131.62, -69.16, 2e-05, 14, 23.72, -79.77, 0.07223, 15, 41.98, -5.14, 0.27845, 12, 113.41, 12.11, 0.01253, 10, 79.18, -55.37, 0.02676, 4, 35.19, 37.19, 0.09396, 5, 9.5, 37.04, 0.25336, 6, -15.93, 37.16, 0.26269, 8, 3, 141.17, -103.7, 0, 14, 33.28, -114.31, 0.02643, 15, 51.53, -39.69, 0.11553, 12, 126.13, -21.4, 0.00104, 10, 91.9, -88.88, 0.0117, 4, 62.39, 13.85, 0.05671, 5, 36.54, 13.83, 0.23765, 6, 11.25, 13.83, 0.55093, 6, 14, 53.83, -137.39, 0.00455, 15, 72.08, -62.76, 0.02346, 10, 114.5, -109.94, 0.00239, 4, 92.32, 6.17, 0.02576, 5, 66.68, 6.14, 0.16273, 6, 41.11, 6.17, 0.78111, 5, 15, 70.31, -67.6, 0.00209, 10, 113.19, -114.93, 0, 4, 93.56, 1.17, 0.02385, 5, 67.99, 1.1, 0.12331, 6, 42.34, 1.17, 0.85076, 5, 15, 67.66, -75.21, 0.00076, 12, 145.48, -55.26, 0.0003, 4, 95.6, -6.62, 0.04286, 5, 70.03, -6.77, 0.12329, 6, 44.37, -6.6, 0.83278, 5, 15, 33.63, -78.63, 0, 12, 111.92, -61.83, 0.00348, 4, 69.27, -28.45, 0.14136, 5, 43.37, -28.63, 0.19386, 6, 18.11, -28.4, 0.66129, 6, 15, 11.06, -69.23, 0.00038, 12, 88.57, -54.57, 0.02873, 10, 54.35, -122.05, 0.00043, 4, 45.3, -33.25, 0.29974, 5, 19.41, -33.26, 0.25089, 6, -5.82, -33.21, 0.41983, 7, 3, 80.43, -123.65, 1e-05, 15, -9.21, -59.64, 0.0036, 12, 67.5, -46.89, 0.10801, 10, 33.27, -114.37, 0.00296, 4, 23.12, -36.6, 0.44659, 5, -2.56, -36.49, 0.23638, 6, -27.99, -36.58, 0.20245, 7, 3, 58.39, -113.85, 0.00118, 15, -31.25, -49.83, 0.01793, 12, 44.64, -39.17, 0.27325, 10, 10.42, -106.65, 0.01212, 4, -0.64, -40.76, 0.46902, 5, -26.14, -40.63, 0.15792, 6, -51.76, -40.75, 0.06859, 8, 3, 48.11, -99.09, 0.00781, 14, -59.78, -109.7, 2e-05, 15, -41.53, -35.08, 0.06253, 12, 33.04, -25.44, 0.48012, 10, -1.19, -92.92, 0.03001, 4, -17.4, -34.25, 0.3335, 5, -42.88, -34.19, 0.07308, 6, -68.53, -34.25, 0.01293, 5, 3, 42.57, -85.32, 0.03735, 14, -65.32, -95.92, 0.0002, 15, -47.07, -21.3, 0.15916, 12, 26.25, -12.23, 0.73804, 10, -7.99, -79.71, 0.06525, 5, 3, 27.84, -86.68, 0.03414, 14, -80.05, -97.29, 2e-05, 15, -61.8, -22.66, 0.06525, 12, 11.71, -14.96, 0.86985, 10, -22.53, -82.44, 0.03074, 4, 3, 10.77, -80.03, 0.08871, 15, -78.88, -16.02, 0.01873, 12, -5.91, -9.93, 0.88076, 10, -40.15, -77.41, 0.0118, 4, 3, -2.01, -67.96, 0.21441, 15, -91.66, -3.94, 0.00406, 12, -19.76, 0.91, 0.77875, 10, -53.99, -66.57, 0.00278, 5, 3, -10.31, -49.98, 0.39833, 11, -40.48, -99.95, 0.00585, 15, -99.95, 14.03, 0.00053, 12, -29.69, 18.04, 0.59488, 10, -63.92, -49.44, 0.0004, 4, 3, -12.2, -29.58, 0.58318, 11, -44.26, -79.81, 0.03816, 12, -33.46, 38.18, 0.37861, 10, -67.69, -29.3, 4e-05, 4, 3, -10.94, -4.95, 0.67877, 11, -45.29, -55.17, 0.12927, 12, -34.5, 62.82, 0.19193, 10, -68.73, -4.66, 4e-05, 4, 3, -0.97, 20.67, 0.63097, 11, -37.74, -28.74, 0.29795, 12, -26.94, 89.25, 0.071, 10, -61.18, 21.77, 8e-05, 4, 3, 11.65, 38.69, 0.46477, 11, -26.85, -9.62, 0.517, 12, -16.05, 108.37, 0.01716, 10, -50.29, 40.89, 0.00107, 5, 3, 23.54, 51, 0.26572, 11, -16.15, 3.74, 0.72461, 14, -84.35, 40.39, 5e-05, 12, -5.35, 121.73, 0.00145, 10, -39.59, 54.25, 0.00818, 4, 3, 43.31, 59.69, 0.11364, 11, 2.73, 14.22, 0.85479, 14, -64.59, 49.08, 0.00071, 10, -20.71, 64.73, 0.03087, 4, 3, 53.93, 56.1, 0.04187, 11, 13.63, 11.64, 0.86607, 14, -53.97, 45.49, 0.01911, 10, -9.8, 62.15, 0.07295, 7, 3, 65.44, 47.8, 0.03824, 11, 25.87, 4.44, 0.74166, 14, -42.45, 37.19, 0.07578, 15, -24.2, 111.81, 6e-05, 12, 36.67, 122.43, 0, 10, 2.43, 54.95, 0.12894, 13, 26.09, 48.78, 0.01531, 7, 3, 80.48, 61.53, 0.0089, 11, 39.57, 19.5, 0.45821, 14, -27.41, 50.92, 0.01797, 10, 16.14, 70.01, 0.07009, 7, -10.63, 28.71, 0.31259, 8, -37.67, 28.42, 0.12159, 9, -65.25, 28.62, 0.01065, 7, 3, 99.74, 69.57, 0.00176, 11, 58, 29.29, 0.24322, 14, -8.15, 58.96, 0.00069, 10, 34.57, 79.8, 0.0318, 7, 9.66, 33.64, 0.41988, 8, -17.48, 33.03, 0.23567, 9, -44.95, 33.45, 0.06698, 7, 3, 118.4, 72.7, 7e-05, 11, 76.29, 34.15, 0.08392, 14, 10.51, 62.09, 5e-05, 10, 52.86, 84.66, 0.00901, 7, 28.74, 33.78, 0.38193, 8, 1.07, 33, 0.31975, 9, -26.03, 33.54, 0.20527, 6, 11, 98.85, 35.36, 0.0177, 14, 33.08, 61.21, 8e-05, 10, 75.41, 85.87, 0.00133, 7, 51.16, 29.19, 0.23982, 8, 22.66, 28.59, 0.30706, 9, -3.89, 29.03, 0.43401, 7, 3, 80.79, 42.33, 0.04002, 11, 41.66, 0.41, 0.56289, 14, -27.1, 31.72, 0.22472, 15, -8.85, 106.34, 0.00158, 12, 52.46, 118.4, 0, 10, 18.23, 50.92, 0.15247, 13, 41.89, 44.76, 0.01831, 6, 3, 97.74, 31.34, 0.00895, 11, 59.56, -8.95, 0.26241, 14, -10.15, 20.74, 0.54133, 15, 8.1, 95.36, 0.01459, 12, 70.36, 109.04, 0.00011, 10, 36.12, 41.56, 0.17261, 5, 3, 108.88, 22.62, 0.00127, 11, 71.46, -16.6, 0.08915, 14, 0.99, 12.02, 0.78956, 15, 19.24, 86.64, 0.03347, 10, 48.02, 33.91, 0.08655, 4, 11, 84.87, -29.15, 0.02056, 14, 13.18, -1.72, 0.83247, 15, 31.43, 72.9, 0.09134, 10, 61.43, 21.36, 0.05563, 5, 3, 98.39, -64.2, 4e-05, 14, -9.5, -74.81, 0.12062, 15, 8.75, -0.18, 0.80632, 12, 79.86, 13.97, 0.02628, 10, 45.63, -53.51, 0.04675, 5, 3, 93.15, -76.47, 0.00011, 14, -14.74, -87.07, 0.04699, 15, 3.51, -12.45, 0.81996, 12, 75.79, 1.27, 0.09742, 10, 41.55, -66.21, 0.03552, 5, 3, 64.9, -82.08, 0.00931, 14, -42.99, -92.69, 0.00075, 15, -24.74, -18.06, 0.39606, 12, 48.18, -6.94, 0.54346, 10, 13.94, -74.42, 0.05043, 7, 3, 52.94, 36.55, 0.10362, 11, 14.46, -7.92, 0.55751, 14, -54.96, 25.94, 0.10514, 15, -36.71, 100.57, 0.00176, 12, 25.26, 110.07, 0.00135, 10, -8.98, 42.59, 0.15902, 13, 14.68, 36.43, 0.07161, 7, 3, 43.94, 21.13, 0.18353, 11, 6.93, -24.11, 0.24207, 14, -63.95, 10.53, 0.0315, 15, -45.71, 85.15, 0.00404, 12, 17.73, 93.88, 0.01287, 10, -16.51, 26.4, 0.176, 13, 7.15, 20.24, 0.35, 7, 3, 46.01, 3.94, 0.15217, 11, 10.59, -41.03, 0.09616, 14, -61.88, -6.66, 0.05812, 15, -43.63, 67.96, 0.01717, 12, 21.39, 76.96, 0.01378, 10, -12.84, 9.48, 0.26259, 13, 10.82, 3.32, 0.4, 7, 3, 43.3, -9.54, 0.16735, 11, 9.15, -54.7, 0.03035, 14, -64.59, -20.14, 0.02312, 15, -46.34, 54.48, 0.06333, 12, 19.94, 63.29, 0.05556, 10, -14.29, -4.19, 0.26029, 13, 9.37, -10.35, 0.4, 7, 3, 34, -22.76, 0.22408, 11, 1.11, -68.73, 0.03164, 14, -73.89, -33.37, 0.00406, 15, -55.64, 41.26, 0.05518, 12, 11.91, 49.26, 0.15449, 10, -22.32, -18.22, 0.18055, 13, 1.34, -24.38, 0.35, 7, 3, 33.17, -40.89, 0.16635, 11, 1.97, -86.86, 0.00531, 14, -74.72, -51.5, 0.00654, 15, -56.47, 23.13, 0.13034, 12, 12.77, 31.13, 0.4096, 10, -21.47, -36.35, 0.16932, 13, 2.19, -42.52, 0.11255, 7, 3, 38.59, -64.92, 0.13733, 11, 9.59, -110.28, 5e-05, 14, -69.3, -75.52, 0.0052, 15, -51.06, -0.9, 0.13681, 12, 20.39, 7.71, 0.57152, 10, -13.85, -59.77, 0.12871, 13, 9.81, -65.93, 0.02039, 8, 3, 102.59, -94.85, 7e-05, 14, -5.3, -105.46, 0.01943, 15, 12.95, -30.83, 0.28102, 12, 86.89, -16.16, 0.05943, 10, 52.66, -83.64, 0.01764, 4, 25.43, -0.33, 0.11714, 5, -0.22, -0.36, 0.28239, 6, -25.68, -0.33, 0.22289, 8, 3, 124.98, -106.56, 2e-05, 14, 17.09, -117.17, 0.00341, 15, 35.34, -42.55, 0.10645, 12, 110.27, -25.74, 0.01703, 10, 76.04, -93.22, 0.0056, 4, 50.55, 2.45, 0.12052, 5, 24.68, 2.4, 0.28467, 6, -0.58, 2.44, 0.46229, 7, 3, 140.56, -121.53, 0, 15, 50.92, -57.51, 0.02799, 12, 127.17, -39.2, 0.00139, 10, 92.95, -106.68, 0.00075, 4, 71.83, -1.27, 0.06316, 5, 45.99, -1.33, 0.20168, 6, 20.67, -1.27, 0.70502, 5, 3, 81.26, -78.83, 0.00143, 14, -26.63, -89.44, 0.01238, 15, -8.39, -14.81, 0.6639, 12, 64.16, -2.19, 0.28265, 10, 29.93, -69.67, 0.03964, 8, 3, 121.63, 41.96, 0.00111, 11, 82.36, 3.84, 0.07763, 14, 13.74, 31.35, 0.29224, 15, 31.99, 105.98, 0.00189, 10, 58.93, 54.35, 0.0641, 7, 26.92, 2.69, 0.11721, 8, -0.56, 2.62, 0.28886, 9, -27.79, 2.68, 0.15695, 7, 11, 104.13, 2.64, 0.01792, 14, 35.31, 28.14, 0.13073, 15, 53.56, 102.77, 6e-05, 10, 80.7, 53.15, 0.02139, 7, 47.94, -4.09, 0.11748, 8, 19.68, -3.9, 0.33244, 9, -7.02, -3.96, 0.37997, 5, 14, 53.9, 35.3, 0.0407, 10, 98.54, 62, 0.00364, 7, 67.7, -0.02, 0.0651, 8, 38.74, 0.1, 0.2675, 9, 12.48, 0.11, 0.62306, 7, 3, 81.27, 14, 0.03772, 11, 44.76, -27.75, 0.09596, 14, -26.62, 3.39, 0.37961, 15, -8.38, 78.02, 0.06225, 12, 55.56, 90.24, 0.00501, 10, 21.32, 22.76, 0.37667, 13, 44.99, 16.6, 0.0428, 7, 3, 62.84, 1.91, 0.10956, 11, 27.54, -41.49, 0.0782, 14, -45.05, -8.7, 0.21973, 15, -26.8, 65.93, 0.02359, 12, 38.33, 76.49, 0.00332, 10, 4.1, 9.02, 0.39038, 13, 27.76, 2.85, 0.17521, 7, 3, 58.74, -13.13, 0.12813, 11, 24.85, -56.85, 0.01134, 14, -49.15, -23.74, 0.06216, 15, -30.91, 50.89, 0.19308, 12, 35.64, 61.14, 0.0657, 10, 1.41, -6.34, 0.36683, 13, 25.07, -12.5, 0.17276, 7, 3, 69.87, -31.57, 0.05452, 11, 37.64, -74.18, 0.00526, 14, -38.02, -42.18, 0.12843, 15, -19.77, 32.45, 0.29828, 12, 48.44, 43.81, 0.10797, 10, 14.21, -23.67, 0.36374, 13, 37.87, -29.83, 0.04181, 6, 3, 67.31, -61.18, 0.05029, 11, 37.84, -103.9, 0.00338, 14, -40.58, -71.79, 0.12201, 15, -22.33, 2.84, 0.30736, 12, 48.64, 14.09, 0.20657, 10, 14.4, -53.39, 0.31039, 7, 3, 50.18, -61.49, 0.11036, 11, 20.82, -105.79, 9e-05, 14, -57.71, -72.1, 0.03395, 15, -39.46, 2.53, 0.20759, 12, 31.61, 12.2, 0.42185, 10, -2.62, -55.28, 0.20114, 13, 21.04, -61.44, 0.02502, 6, 3, 96.64, 1.01, 0.0143, 11, 61.28, -39.25, 0.08138, 14, -11.25, -9.6, 0.38566, 15, 7, 65.03, 0.14022, 12, 72.07, 78.74, 0.02573, 10, 37.84, 11.26, 0.35271, 6, 3, 88.54, -30.63, 0.02504, 11, 56.15, -71.5, 0.01814, 14, -19.35, -41.23, 0.22459, 15, -1.1, 33.39, 0.28708, 12, 66.94, 46.48, 0.09923, 10, 32.71, -21, 0.34591, 7, 3, 40.7, 41.76, 0.25902, 11, 1.8, -3.87, 0.59716, 14, -67.19, 31.15, 0.03109, 15, -48.94, 105.78, 5e-05, 12, 12.59, 114.12, 0, 10, -21.64, 46.64, 0.09036, 13, 2.02, 40.48, 0.02232, 7, 3, 22.74, 0.88, 0.22548, 11, -12.29, -46.24, 0.11022, 14, -85.15, -9.73, 0.00264, 15, -66.9, 64.9, 0.01537, 12, -1.49, 71.75, 0.05809, 10, -35.73, 4.27, 0.13819, 13, -12.07, -1.89, 0.45, 7, 3, 27.22, 30.37, 0.39112, 11, -10.57, -16.46, 0.4165, 14, -80.67, 19.76, 0.00228, 15, -62.42, 94.39, 6e-05, 12, 0.22, 101.53, 0.00524, 10, -34.01, 34.05, 0.0348, 13, -10.35, 27.89, 0.15, 7, 3, 9.89, 14.35, 0.48923, 11, -26.34, -34.02, 0.22183, 14, -98, 3.74, 9e-05, 15, -79.75, 78.36, 0.00066, 12, -15.54, 83.97, 0.02598, 10, -49.78, 16.49, 0.0122, 13, -26.11, 10.33, 0.25, 6, 3, 2.04, 0.18, 0.55092, 11, -32.84, -48.86, 0.09632, 15, -87.6, 64.2, 0.00229, 12, -22.04, 69.13, 0.08837, 10, -56.28, 1.65, 0.0121, 13, -32.62, -4.51, 0.25, 6, 3, 7.06, -18.41, 0.49424, 11, -26.11, -66.9, 0.02732, 15, -82.58, 45.61, 0.00783, 12, -15.32, 51.09, 0.19823, 10, -49.55, -16.39, 0.02239, 13, -25.89, -22.55, 0.25, 7, 3, 15.56, -36.31, 0.40197, 11, -15.99, -83.94, 0.00427, 14, -92.33, -46.92, 3e-05, 15, -74.08, 27.7, 0.02401, 12, -5.19, 34.05, 0.37575, 10, -39.43, -33.43, 0.04397, 13, -15.77, -39.59, 0.15, 6, 3, 20.15, -62.03, 0.27912, 11, -9.04, -109.12, 1e-05, 14, -87.74, -72.64, 0.0003, 15, -69.5, 1.99, 0.06867, 12, 1.76, 8.87, 0.56587, 10, -32.48, -58.61, 0.08603, 7, 3, 79.91, 35.25, 0.04508, 11, 41.44, -6.71, 0.41027, 14, -27.98, 24.65, 0.27114, 15, -9.74, 99.27, 0.01585, 12, 52.23, 111.28, 0.00015, 10, 18, 43.8, 0.23484, 13, 41.66, 37.64, 0.02266, 6, 3, 90.92, 24.16, 0.01745, 11, 53.43, -16.74, 0.19613, 14, -16.97, 13.55, 0.43211, 15, 1.28, 88.18, 0.05133, 12, 64.23, 101.25, 0.00383, 10, 29.99, 33.77, 0.29914], "hull": 36, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 2, 0, 0, 70, 28, 30, 30, 32, 32, 34, 62, 72, 72, 74, 74, 76, 76, 78, 78, 12, 16, 80, 80, 82, 84, 38, 62, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 38, 18, 100, 100, 102, 102, 104, 22, 24, 24, 26, 104, 24, 82, 106, 106, 84, 100, 106, 74, 108, 108, 110, 110, 112, 112, 2, 6, 8, 8, 10, 114, 116, 116, 90, 92, 118, 118, 120, 120, 122, 122, 124, 124, 96, 72, 86, 114, 126, 126, 128, 128, 120, 86, 130, 88, 132, 132, 94, 130, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 98, 98, 124, 122, 128, 86, 146, 74, 148, 148, 114, 146, 148, 148, 126], "width": 203, "height": 205}}, "legL": {"legL": {"x": 32.29, "y": -11, "rotation": 48.92, "width": 60, "height": 82}}, "legR": {"legR": {"x": 27.3, "y": 4.39, "rotation": 98.47, "width": 39, "height": 77}}, "wingL2": {"wingL": {"type": "mesh", "uvs": [0, 0.99776, 0, 0.92001, 0.2057, 0.68918, 0.23431, 0.45834, 0.13288, 0.38886, 0.19009, 0.35076, 0.25512, 0.33732, 0.5152, 0.1401, 0.87151, 0.01235, 1, 0, 0.92093, 0.17147, 0.8273, 0.32611, 0.79869, 0.44713, 0.76488, 0.61074, 0.64784, 0.6959, 0.53861, 0.8102, 0.33574, 0.87295, 0.15368, 1, 0.29459, 0.48536, 0.31763, 0.42399, 0.33648, 0.3572, 0.53129, 0.48716, 0.32354, 0.39283, 0.59622, 0.33554, 0.4098, 0.65322, 0.30246, 0.45367, 0.47892, 0.5756, 0.56271, 0.41316, 0.60251, 0.23446, 0.26946, 0.71459], "triangles": [15, 24, 14, 14, 24, 26, 26, 21, 14, 14, 21, 13, 13, 21, 12, 12, 21, 27, 26, 19, 21, 21, 22, 27, 27, 23, 12, 12, 23, 11, 23, 28, 11, 11, 28, 10, 10, 28, 8, 28, 7, 8, 10, 8, 9, 19, 22, 21, 3, 5, 6, 19, 6, 22, 27, 22, 23, 22, 20, 23, 22, 6, 20, 28, 23, 20, 20, 6, 7, 28, 20, 7, 16, 24, 15, 16, 29, 24, 24, 29, 18, 29, 2, 18, 2, 3, 18, 24, 18, 26, 18, 25, 26, 25, 19, 26, 18, 3, 25, 4, 5, 3, 25, 3, 19, 19, 3, 6, 17, 29, 16, 17, 2, 29, 2, 17, 1, 17, 0, 1], "vertices": [1, 19, -30.57, 7.54, 1, 1, 19, -11.59, 19.72, 1, 2, 19, 70.19, 12.8, 0.47756, 20, -0.61, 13.68, 0.52244, 2, 20, 64.74, 9.83, 0.96953, 21, -20.18, -1.11, 0.03047, 2, 20, 83.14, 35.38, 0.43465, 21, -22.74, 30.28, 0.56535, 2, 20, 94.53, 22, 0.28875, 21, -5.39, 27.48, 0.71125, 2, 20, 99.07, 6.4, 0.03771, 21, 8.18, 18.54, 0.96229, 2, 21, 91.97, 10.44, 0.82457, 22, -7.42, 11.53, 0.17543, 1, 22, 86.33, 9.42, 1, 2, 22, 116.29, -0.04, 1, 23, 25.24, 177.54, 0, 2, 22, 79.14, -36.47, 0.96005, 23, 32.47, 126.01, 0.03995, 3, 21, 105.54, -80.77, 0.00065, 22, 40.69, -67.14, 0.6448, 23, 34.28, 76.86, 0.35455, 3, 21, 75.89, -99.03, 0.00074, 22, 20.51, -95.53, 0.25122, 23, 45.12, 43.76, 0.74804, 3, 20, 27.95, -121, 0.00011, 22, -5.68, -134.39, 0.00015, 23, 60.81, -0.39, 0.99974, 3, 19, 126.61, -78.66, 0.02568, 20, 2.6, -93.73, 0.16068, 23, 47.98, -35.34, 0.81363, 3, 19, 85.15, -73.72, 0.18533, 20, -30.85, -68.75, 0.37896, 23, 40.86, -76.48, 0.43571, 3, 19, 43.63, -41.78, 0.80978, 20, -50.88, -20.35, 0.12277, 23, 6.78, -116.25, 0.06745, 1, 19, -10.41, -23.89, 1, 1, 20, 62.09, 1.66, 1, 3, 20, 75.19, -5.87, 0.53445, 21, -2.06, -6.29, 0.45155, 23, -63.23, -10.42, 0.014, 1, 21, 9.21, 7.75, 1, 4, 20, 60.05, -62.64, 0.02613, 21, 23.22, -59.33, 0.04668, 22, -43.47, -79.54, 0.00625, 23, -5.76, 1.8, 0.92093, 2, 21, 2.85, -0.17, 0.9996, 23, -68.04, -4.22, 0.0004, 3, 21, 65.21, -41.61, 0.28752, 22, -11.73, -46.84, 0.32561, 23, -13.2, 46.77, 0.38688, 3, 19, 105.51, -23.47, 0.03263, 20, 11.87, -35.38, 0.64358, 23, -8.24, -53.5, 0.32379, 3, 20, 67.62, -1.52, 0.97496, 21, -10.64, -7.88, 0.02217, 23, -63.68, -19.14, 0.00287, 4, 19, 133, -25.79, 0.0018, 20, 34.54, -51.12, 0.33154, 21, -3.68, -67.09, 0.00624, 23, -4.47, -26.17, 0.66041, 4, 20, 81.26, -69.28, 0.00359, 21, 43.67, -50.63, 0.21738, 22, -28.04, -63.55, 0.11051, 23, -9.45, 23.72, 0.66853, 3, 21, 87.06, -23.24, 0.18965, 22, 1.2, -21.39, 0.70991, 23, -25.98, 72.29, 0.10044, 3, 19, 72.53, -4.11, 0.40332, 20, -7.03, -2.14, 0.58444, 23, -29.31, -85.41, 0.01224], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 6, 36, 40, 12, 38, 42, 42, 26, 38, 44, 44, 40, 44, 46, 46, 22, 36, 48, 48, 30, 36, 50, 50, 38, 50, 52, 52, 28, 44, 54, 54, 24, 40, 56, 56, 20, 32, 58, 58, 4], "width": 243, "height": 282}}, "wingR2": {"wingR": {"type": "mesh", "uvs": [1, 1, 0.9302, 1, 0.73273, 0.92797, 0.61442, 0.88481, 0.41306, 0.73718, 0.2747, 0.4245, 0.14764, 0.28684, 0, 0.12689, 0, 0.07534, 0.1374, 0.01441, 0.33877, 0, 0.49437, 0.04253, 0.6042, 0.11518, 0.64425, 0.08237, 0.70031, 0.09877, 0.67285, 0.18782, 0.65912, 0.27687, 0.76438, 0.58619, 1, 0.81867, 0.5364, 0.17659, 0.57416, 0.2258, 0.5959, 0.27267, 0.62793, 0.34297, 0.61649, 0.61246, 0.4975, 0.45779, 0.4197, 0.29141, 0.34648, 0.1719, 0.73205, 0.67104], "triangles": [3, 4, 23, 4, 24, 23, 4, 5, 24, 5, 25, 24, 21, 24, 25, 5, 6, 26, 7, 9, 6, 6, 9, 26, 9, 10, 26, 7, 8, 9, 21, 25, 20, 5, 26, 25, 25, 19, 20, 25, 26, 19, 20, 12, 15, 20, 19, 12, 12, 13, 15, 15, 13, 14, 26, 11, 19, 19, 11, 12, 26, 10, 11, 2, 3, 27, 3, 23, 27, 27, 17, 18, 27, 23, 17, 17, 23, 22, 23, 24, 22, 22, 16, 17, 24, 21, 22, 22, 21, 16, 15, 16, 20, 16, 21, 20, 1, 18, 0, 1, 2, 18, 18, 2, 27], "vertices": [1, 24, -28.61, 4.17, 1, 1, 24, -8.07, 16.06, 1, 3, 24, 56.02, 39.37, 0.79705, 25, -33.77, 28.32, 0.1537, 28, 21.35, 108.23, 0.04925, 3, 24, 94.41, 53.34, 0.17211, 25, -5.86, 58.17, 0.49424, 28, 36.06, 70.11, 0.33365, 2, 27, -16.31, 118.75, 0.00035, 28, 50.56, -1.15, 0.99965, 3, 26, 90.19, 70.36, 0.11583, 27, 25.56, 62.59, 0.50942, 28, 30.53, -68.27, 0.37475, 3, 26, 138.54, 63.29, 0.00215, 27, 66.38, 35.72, 0.96468, 28, 33.37, -117.06, 0.03316, 1, 27, 113.81, 4.49, 1, 1, 27, 112.99, -4.03, 1, 1, 27, 65.53, -9.63, 1, 2, 26, 93.25, -3.33, 0.25262, 27, -2.85, -5.48, 0.74738, 1, 26, 41.03, -14.39, 1, 2, 25, 103.21, -8.45, 0.08247, 26, 1.81, -15.53, 0.91753, 2, 25, 100.37, -22.83, 0.35382, 26, -9.2, -25.22, 0.64618, 2, 25, 87.71, -37.34, 0.47466, 26, -28.07, -29.03, 0.52534, 2, 25, 80.39, -21.47, 0.59924, 26, -24.22, -11.98, 0.40076, 2, 25, 70.53, -9.5, 0.98207, 26, -24.77, 3.52, 0.01793, 2, 24, 75.15, -15.11, 0.15816, 25, 7.98, -11.58, 0.84184, 1, 24, -13.52, -21.88, 1, 3, 25, 107.2, 16.44, 0.00285, 26, 20.12, 1.8, 0.98806, 28, -50.76, -13.48, 0.00909, 3, 25, 93.36, 10.12, 0.0692, 26, 5.29, 5.2, 0.90322, 28, -50.42, 1.73, 0.02758, 3, 25, 82.81, 8.15, 0.51439, 26, -4.28, 10.05, 0.43999, 28, -47.6, 12.08, 0.04562, 3, 25, 67.1, 5.36, 0.94786, 26, -18.45, 17.4, 0.01819, 28, -43.26, 27.44, 0.03395, 3, 24, 116.47, 13.87, 0.00687, 25, 31.68, 32.97, 0.66126, 28, -2.98, 47.29, 0.33186, 4, 25, 75.24, 52.94, 0.01955, 26, 16.95, 50.21, 0.03714, 27, -49.32, 75.32, 0.00093, 28, -3.98, -0.61, 0.94238, 3, 26, 51.13, 33.04, 0.5167, 27, -25.63, 45.3, 0.09151, 28, -13.9, -37.55, 0.39179, 3, 26, 81.23, 22.68, 0.47043, 27, -2.74, 23.18, 0.41823, 28, -17.98, -69.12, 0.11134, 3, 24, 77.6, 2.59, 0.03502, 25, 2.14, 5.3, 0.95182, 28, -15.01, 85.93, 0.01316], "hull": 19, "edges": [0, 2, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 24, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 6, 8, 48, 48, 42, 10, 50, 10, 12, 12, 14, 12, 52, 52, 38, 50, 40, 2, 4, 4, 6, 4, 54, 54, 34], "width": 340, "height": 166}}}}, "animations": {"idle": {"slots": {"body2": {"attachment": [{"time": 0, "name": "body"}, {"time": 1, "name": "body"}, {"time": 2, "name": "body"}, {"time": 3.3333, "name": "body"}, {"time": 4.3333, "name": "body"}]}, "eye": {"attachment": [{"time": 0, "name": "eye"}, {"time": 1, "name": "eye"}, {"time": 2, "name": "eye"}, {"time": 3.3333, "name": "eye"}, {"time": 4.3333, "name": "eye"}]}, "eyeBack": {"attachment": [{"time": 0, "name": "eyeBack"}, {"time": 1, "name": "eyeBack"}, {"time": 2, "name": "eyeBack"}, {"time": 3.3333, "name": "eyeBack"}, {"time": 4.3333, "name": "eyeBack"}]}, "head": {"attachment": [{"time": 0, "name": "head"}, {"time": 1, "name": "head"}, {"time": 2, "name": "head"}, {"time": 3.3333, "name": "head"}, {"time": 4.3333, "name": "head"}]}, "legL": {"attachment": [{"time": 0, "name": "legL"}, {"time": 1, "name": "legL"}, {"time": 2, "name": "legL"}, {"time": 3.3333, "name": "legL"}, {"time": 4.3333, "name": "legL"}]}, "legR": {"attachment": [{"time": 0, "name": "legR"}, {"time": 1, "name": "legR"}, {"time": 2, "name": "legR"}, {"time": 3.3333, "name": "legR"}, {"time": 4.3333, "name": "legR"}]}, "wingL2": {"attachment": [{"time": 0, "name": "wingL"}, {"time": 1, "name": "wingL"}, {"time": 2, "name": "wingL"}, {"time": 3.3333, "name": "wingL"}, {"time": 4.3333, "name": "wingL"}]}, "wingR": {"attachment": [{"time": 0, "name": null}, {"time": 1, "name": null}, {"time": 2, "name": null}, {"time": 3.3333, "name": null}, {"time": 4.3333, "name": null}]}, "wingR2": {"attachment": [{"time": 0, "name": "wingR"}, {"time": 1, "name": "wingR"}, {"time": 2, "name": "wingR"}, {"time": 3.3333, "name": "wingR"}, {"time": 4.3333, "name": "wingR"}]}}, "bones": {"root_0.29": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.5, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 3.8333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingR": {"rotate": [{"time": 0, "angle": -19.93, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 0.3333, "angle": 99.83, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 0.5333, "angle": 110.7, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.8, "angle": -1.23, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 1, "angle": -19.93, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 1.3333, "angle": 99.83, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 1.5333, "angle": 110.7, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 1.8, "angle": -1.23, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 2, "angle": -19.93, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 2.3333, "angle": 99.83, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 2.5333, "angle": 110.7, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 2.8, "angle": -1.23, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 3.3333, "angle": -19.93, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 3.6667, "angle": 99.83, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 3.8667, "angle": 110.7, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 4.1333, "angle": -1.23, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 4.3333, "angle": -19.93}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "body": {"rotate": [{"time": 0, "angle": 2.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 2.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "angle": 2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "angle": 2.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8667, "angle": 2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.3333, "angle": 2.44}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -3.7, "y": 42.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": -3.7, "y": 42.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "x": -3.7, "y": 42.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": -34.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8667, "x": -3.7, "y": 42.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "head": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 7.51, "y": -2.05, "curve": [0.323, 0, 0.764, 0.52]}, {"time": 0.5333, "x": -3.08, "y": 1.93, "curve": [0.323, 0, 0.469, 1]}, {"time": 1, "x": 7.51, "y": -2.05, "curve": [0.323, 0, 0.764, 0.52]}, {"time": 1.5333, "x": -3.08, "y": 1.93, "curve": [0.323, 0, 0.469, 1]}, {"time": 2, "x": 7.51, "y": -2.05, "curve": [0.323, 0, 0.764, 0.52]}, {"time": 2.5333, "x": -3.08, "y": 1.93, "curve": [0.323, 0, 0.469, 1]}, {"time": 3.3333, "x": 7.51, "y": -2.05, "curve": [0.323, 0, 0.764, 0.52]}, {"time": 3.8667, "x": -3.08, "y": 1.93, "curve": [0.323, 0, 0.469, 1]}, {"time": 4.3333, "x": 7.51, "y": -2.05}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "eye": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "earL": {"rotate": [{"time": 0, "angle": -1.03}, {"time": 0.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.9333, "angle": 0}, {"time": 1, "angle": -1.03}, {"time": 1.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 1.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1.9333, "angle": 0}, {"time": 2, "angle": -1.03}, {"time": 2.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 2.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2.9333, "angle": 0}, {"time": 3.3333, "angle": -1.03}, {"time": 3.4667, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 3.8, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.2667, "angle": 0}, {"time": 4.3333, "angle": -1.03}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "earR": {"rotate": [{"time": 0, "angle": 1.66}, {"time": 0.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.9, "angle": 0}, {"time": 1, "angle": 1.66}, {"time": 1.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 1.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1.9, "angle": 0}, {"time": 2, "angle": 1.66}, {"time": 2.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 2.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2.9, "angle": 0}, {"time": 3.3333, "angle": 1.66}, {"time": 3.4333, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 3.7667, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.2333, "angle": 0}, {"time": 4.3333, "angle": 1.66}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "legR": {"rotate": [{"time": 0, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.5333, "angle": 9.64, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 1.5333, "angle": 9.64, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 2.5333, "angle": 9.64, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 3.3333, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 3.8667, "angle": 9.64, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.5333, "x": 1.126, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 1.5333, "x": 1.126, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 2.5333, "x": 1.126, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 3.3333, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 3.8667, "x": 1.126, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "legL": {"rotate": [{"time": 0, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.5333, "angle": -6.63, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 1.5333, "angle": -6.63, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 2.5333, "angle": -6.63, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 3.3333, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 3.8667, "angle": -6.63, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.5333, "x": 1.09, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 1.5333, "x": 1.09, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 2.5333, "x": 1.09, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 3.3333, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 3.8667, "x": 1.09, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingL": {"rotate": [{"time": 0, "angle": 25.79, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 0.3333, "angle": -99.43, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 0.5333, "angle": -104.29, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.8, "angle": 4.19, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 1, "angle": 25.79, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 1.3333, "angle": -99.43, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 1.5333, "angle": -104.29, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 1.8, "angle": 4.19, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 2, "angle": 25.79, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 2.3333, "angle": -99.43, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 2.5333, "angle": -104.29, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 2.8, "angle": 4.19, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 3.3333, "angle": 25.79, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 3.6667, "angle": -99.43, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 3.8667, "angle": -104.29, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 4.1333, "angle": 4.19, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 4.3333, "angle": 25.79}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "earL2": {"rotate": [{"time": 0, "angle": -0.29, "curve": [0.387, 0.18, 0.715, 0.53]}, {"time": 0.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.9333, "angle": 0, "curve": [0.36, 0.01, 0.692, 0.36]}, {"time": 1, "angle": -0.29, "curve": [0.387, 0.18, 0.715, 0.53]}, {"time": 1.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 1.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1.9333, "angle": 0, "curve": [0.36, 0.01, 0.692, 0.36]}, {"time": 2, "angle": -0.29, "curve": [0.387, 0.18, 0.715, 0.53]}, {"time": 2.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 2.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2.9333, "angle": 0, "curve": [0.36, 0.01, 0.692, 0.36]}, {"time": 3.3333, "angle": -0.29, "curve": [0.387, 0.18, 0.715, 0.53]}, {"time": 3.4667, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 3.8, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.2667, "angle": 0, "curve": [0.36, 0.01, 0.692, 0.36]}, {"time": 4.3333, "angle": -0.29}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "earL3": {"rotate": [{"time": 0, "angle": -1.03}, {"time": 0.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.9333, "angle": 0}, {"time": 1, "angle": -1.03}, {"time": 1.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 1.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1.9333, "angle": 0}, {"time": 2, "angle": -1.03}, {"time": 2.1333, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 2.4667, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2.9333, "angle": 0}, {"time": 3.3333, "angle": -1.03}, {"time": 3.4667, "angle": -3.08, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 3.8, "angle": -29.77, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.2667, "angle": 0}, {"time": 4.3333, "angle": -1.03}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "earR2": {"rotate": [{"time": 0, "angle": 1.66}, {"time": 0.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.9, "angle": 0}, {"time": 1, "angle": 1.66}, {"time": 1.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 1.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1.9, "angle": 0}, {"time": 2, "angle": 1.66}, {"time": 2.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 2.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2.9, "angle": 0}, {"time": 3.3333, "angle": 1.66}, {"time": 3.4333, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 3.7667, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.2333, "angle": 0}, {"time": 4.3333, "angle": 1.66}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "earR3": {"rotate": [{"time": 0, "angle": 1.66}, {"time": 0.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.9, "angle": 0}, {"time": 1, "angle": 1.66}, {"time": 1.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 1.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1.9, "angle": 0}, {"time": 2, "angle": 1.66}, {"time": 2.1, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 2.4333, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2.9, "angle": 0}, {"time": 3.3333, "angle": 1.66}, {"time": 3.4333, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 3.7667, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.2333, "angle": 0}, {"time": 4.3333, "angle": 1.66}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingL2": {"rotate": [{"time": 0, "angle": -19.2, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 0.3333, "angle": -26.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.5333, "angle": -50.37, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.8, "angle": 10.83, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 1, "angle": -19.2, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 1.3333, "angle": -26.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 1.5333, "angle": -50.37, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 1.8, "angle": 10.83, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 2, "angle": -19.2, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 2.3333, "angle": -26.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 2.5333, "angle": -50.37, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 2.8, "angle": 10.83, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 3.3333, "angle": -19.2, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 3.6667, "angle": -26.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 3.8667, "angle": -50.37, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 4.1333, "angle": 10.83, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 4.3333, "angle": -19.2}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingL3": {"rotate": [{"time": 0, "angle": 39.23, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 0.1333, "angle": 67.52, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 0.3333, "angle": 63.01, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.5333, "angle": 30.47, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.8, "angle": -67.99, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 1, "angle": 39.23, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 1.1333, "angle": 67.52, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 1.3333, "angle": 63.01, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 1.5333, "angle": 30.47, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 1.8, "angle": -67.99, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 2, "angle": 39.23, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 2.1333, "angle": 67.52, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 2.3333, "angle": 63.01, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 2.5333, "angle": 30.47, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 2.8, "angle": -67.99, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 3.3333, "angle": 39.23, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 3.4667, "angle": 67.52, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 3.6667, "angle": 63.01, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 3.8667, "angle": 30.47, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 4.1333, "angle": -67.99, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 4.3333, "angle": 39.23}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingL4": {"rotate": [{"time": 0, "angle": 13.99, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 0.1333, "angle": 41.2, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 0.3333, "angle": 32.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.5333, "angle": 2.1, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 0.8, "angle": 3.26, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 1, "angle": 13.99, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 1.1333, "angle": 41.2, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 1.3333, "angle": 32.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 1.5333, "angle": 2.1, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 1.8, "angle": 3.26, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 2, "angle": 13.99, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 2.1333, "angle": 41.2, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 2.3333, "angle": 32.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 2.5333, "angle": 2.1, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 2.8, "angle": 3.26, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 3.3333, "angle": 13.99, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 3.4667, "angle": 41.2, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 3.6667, "angle": 32.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 3.8667, "angle": 2.1, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 4.1333, "angle": 3.26, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 4.3333, "angle": 13.99}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingR2": {"rotate": [{"time": 0, "angle": 8.69, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 0.3333, "angle": 22.46, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.5333, "angle": 37.75, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.8, "angle": -26.85, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 1, "angle": 8.69, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 1.3333, "angle": 22.46, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 1.5333, "angle": 37.75, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 1.8, "angle": -26.85, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 2, "angle": 8.69, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 2.3333, "angle": 22.46, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 2.5333, "angle": 37.75, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 2.8, "angle": -26.85, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 3.3333, "angle": 8.69, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 3.6667, "angle": 22.46, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 3.8667, "angle": 37.75, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 4.1333, "angle": -26.85, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 4.3333, "angle": 8.69}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingR3": {"rotate": [{"time": 0, "angle": -37.27, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 0.1333, "angle": -62.55, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 0.3333, "angle": -56.93, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.5333, "angle": -28.99, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.8, "angle": 79.25, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 1, "angle": -37.27, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 1.1333, "angle": -62.55, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 1.3333, "angle": -56.93, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 1.5333, "angle": -28.99, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 1.8, "angle": 79.25, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 2, "angle": -37.27, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 2.1333, "angle": -62.55, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 2.3333, "angle": -56.93, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 2.5333, "angle": -28.99, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 2.8, "angle": 79.25, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 3.3333, "angle": -37.27, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 3.4667, "angle": -62.55, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 3.6667, "angle": -56.93, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 3.8667, "angle": -28.99, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 4.1333, "angle": 79.25, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 4.3333, "angle": -37.27}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingR4": {"rotate": [{"time": 0, "angle": -19.93, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 0.1333, "angle": -37.48, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 0.3333, "angle": -38.05, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.5333, "angle": 7.22, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 0.8, "angle": -6.14, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 1, "angle": -19.93, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 1.1333, "angle": -37.48, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 1.3333, "angle": -38.05, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 1.5333, "angle": 7.22, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 1.8, "angle": -6.14, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 2, "angle": -19.93, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 2.1333, "angle": -37.48, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 2.3333, "angle": -38.05, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 2.5333, "angle": 7.22, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 2.8, "angle": -6.14, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 3.3333, "angle": -19.93, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 3.4667, "angle": -37.48, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 3.6667, "angle": -38.05, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 3.8667, "angle": 7.22, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 4.1333, "angle": -6.14, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 4.3333, "angle": -19.93}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingR5": {"rotate": [{"time": 0, "angle": -19.93, "curve": "stepped"}, {"time": 1, "angle": -19.93, "curve": "stepped"}, {"time": 2, "angle": -19.93, "curve": "stepped"}, {"time": 3.3333, "angle": -19.93, "curve": "stepped"}, {"time": 4.3333, "angle": -19.93}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "wingL5": {"rotate": [{"time": 0, "angle": 13.99, "curve": "stepped"}, {"time": 1, "angle": 13.99, "curve": "stepped"}, {"time": 2, "angle": 13.99, "curve": "stepped"}, {"time": 3.3333, "angle": 13.99, "curve": "stepped"}, {"time": 4.3333, "angle": 13.99}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "body2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.5333, "x": 2.89, "y": 1.16, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1, "x": 0, "y": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 1.5333, "x": 2.89, "y": 1.16, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 2.5333, "x": 2.89, "y": 1.16, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 3.8667, "x": 2.89, "y": 1.16, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.5333, "x": 0.846, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 1.5333, "x": 0.846, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 2.5333, "x": 0.846, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 3.3333, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 3.8667, "x": 0.846, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "head2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "head3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "head4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.5333, "x": 16.74, "y": -3.35, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1, "x": 0, "y": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 1.5333, "x": 16.74, "y": -3.35, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 2.5333, "x": 16.74, "y": -3.35, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 3.8667, "x": 16.74, "y": -3.35, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "head5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "head6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}, "head7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 4.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0}]}}, "transform": {"eye": [{"time": 0, "rotateMix": 0.15, "translateMix": 0.15, "scaleMix": 0.15, "shearMix": 0.15, "curve": "stepped"}, {"time": 1, "rotateMix": 0.15, "translateMix": 0.15, "scaleMix": 0.15, "shearMix": 0.15, "curve": "stepped"}, {"time": 2, "rotateMix": 0.15, "translateMix": 0.15, "scaleMix": 0.15, "shearMix": 0.15, "curve": "stepped"}, {"time": 3.3333, "rotateMix": 0.15, "translateMix": 0.15, "scaleMix": 0.15, "shearMix": 0.15, "curve": "stepped"}, {"time": 4.3333, "rotateMix": 0.15, "translateMix": 0.15, "scaleMix": 0.15, "shearMix": 0.15}], "head4": [{"time": 0, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 1, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 2, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 3.3333, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 4.3333, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2}], "head5": [{"time": 0, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 1, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 2, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 3.3333, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 4.3333, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2}], "head6": [{"time": 0, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 1, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 2, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 3.3333, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 4.3333, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1}], "head7": [{"time": 0, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 1, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 2, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 3.3333, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 4.3333, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1}], "legL": [{"time": 0, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.5333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": -0.185, "shearMix": 0.5, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 1, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 1.5333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": -0.185, "shearMix": 0.5, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 2, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 2.5333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": -0.185, "shearMix": 0.5, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 3.3333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 3.8667, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": -0.185, "shearMix": 0.5, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 4.3333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5}], "legR": [{"time": 0, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 0.5333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 1, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 1.5333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 2, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 2.5333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 3.3333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 3.8667, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 4.3333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5}], "wingL3": [{"time": 0, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 1, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 2, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 3.3333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 4.3333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5}], "wingR3": [{"time": 0, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 1, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 2, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 3.3333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 4.3333, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5}]}, "deform": {"default": {"body2": {"body": [{"time": 0, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.3333}]}, "head": {"head": [{"time": 0, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.3333}]}, "wingL2": {"wingL": [{"time": 0, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.3333}]}}}, "drawOrder": [{"time": 0}, {"time": 1}, {"time": 2}, {"time": 3.3333}, {"time": 4.3333}]}, "move": {"slots": {"body2": {"attachment": [{"time": 0, "name": "body"}, {"time": 0.5, "name": "body"}]}, "eye": {"attachment": [{"time": 0, "name": "eye"}, {"time": 0.5, "name": "eye"}]}, "eyeBack": {"attachment": [{"time": 0, "name": "eyeBack"}, {"time": 0.5, "name": "eyeBack"}]}, "head": {"attachment": [{"time": 0, "name": "head"}, {"time": 0.5, "name": "head"}]}, "legL": {"attachment": [{"time": 0, "name": "legL"}, {"time": 0.5, "name": "legL"}]}, "legR": {"attachment": [{"time": 0, "name": "legR"}, {"time": 0.5, "name": "legR"}]}, "wingL2": {"attachment": [{"time": 0, "name": "wingL"}, {"time": 0.5, "name": "wingL"}]}, "wingR": {"attachment": [{"time": 0, "name": null}, {"time": 0.5, "name": null}]}, "wingR2": {"attachment": [{"time": 0, "name": "wingR"}, {"time": 0.5, "name": "wingR"}]}}, "bones": {"root_0.29": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingR": {"rotate": [{"time": 0, "angle": -19.93, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 0.1667, "angle": 99.83, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 0.2667, "angle": 110.7, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.4, "angle": -1.23, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 0.5, "angle": -19.93}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.476, 0, 0.768, 0.43]}, {"time": 0.5, "x": 0.953, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "body": {"rotate": [{"time": 0, "angle": 2.44, "curve": [0.329, 0, 0.764, 0.52]}, {"time": 0.2667, "angle": 2.64, "curve": [0.329, 0, 0.469, 1]}, {"time": 0.5, "angle": 2.44}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.329, 0, 0.764, 0.52]}, {"time": 0.2667, "x": -3.7, "y": 29.04, "curve": [0.329, 0, 0.469, 1]}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "head": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 7.51, "y": -2.05, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.2667, "x": -3.08, "y": 1.93, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.5, "x": 7.51, "y": -2.05}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "eye": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 1.62, "y": 8.4, "curve": "stepped"}, {"time": 0.5, "x": 1.62, "y": 8.4}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "earL": {"rotate": [{"time": 0, "angle": -3.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -29.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.08}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "earR": {"rotate": [{"time": 0, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.1667, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.32}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0.723, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.2667, "x": 1, "y": 0.886, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.5, "x": 1, "y": 0.723}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "legR": {"rotate": [{"time": 0, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.2667, "angle": 9.64, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.2667, "x": 1.126, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "legL": {"rotate": [{"time": 0, "angle": 0, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.2667, "angle": -6.63, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.2667, "x": 1.09, "y": 1, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingL": {"rotate": [{"time": 0, "angle": 25.79, "curve": [0.575, 0, 0.734, 0.57]}, {"time": 0.1667, "angle": -99.43, "curve": [0.226, 0.56, 0.506, 1]}, {"time": 0.2667, "angle": -104.29, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.4, "angle": 4.19, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 0.5, "angle": 25.79}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "earL2": {"rotate": [{"time": 0, "angle": -3.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -29.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.08}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "earL3": {"rotate": [{"time": 0, "angle": -3.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -29.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.08}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "earR2": {"rotate": [{"time": 0, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.1667, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.32}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "earR3": {"rotate": [{"time": 0, "angle": 3.32, "curve": [0.432, 0.24, 0.707, 0.63]}, {"time": 0.1667, "angle": 32.12, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.32}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingL2": {"rotate": [{"time": 0, "angle": -19.2, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 0.1667, "angle": -26.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.2667, "angle": -50.37, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.4, "angle": 10.83, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 0.5, "angle": -19.2}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingL3": {"rotate": [{"time": 0, "angle": 39.23, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 0.0667, "angle": 67.52, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 0.1667, "angle": 63.01, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.2667, "angle": 30.47, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.4, "angle": -67.99, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 0.5, "angle": 39.23}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingL4": {"rotate": [{"time": 0, "angle": 13.99, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 0.0667, "angle": 41.2, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 0.1667, "angle": 32.7, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.2667, "angle": 2.1, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 0.4, "angle": 3.26, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 0.5, "angle": 13.99}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingR2": {"rotate": [{"time": 0, "angle": 8.69, "curve": [0.584, 0, 0.696, 0.63]}, {"time": 0.1667, "angle": 22.46, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.2667, "angle": 37.75, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.4, "angle": -26.85, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 0.5, "angle": 8.69}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingR3": {"rotate": [{"time": 0, "angle": -37.27, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 0.0667, "angle": -62.55, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 0.1667, "angle": -56.93, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.2667, "angle": -28.99, "curve": [0.541, 0, 0.77, 0.5]}, {"time": 0.4, "angle": 79.25, "curve": [0.228, 0.5, 0.458, 1]}, {"time": 0.5, "angle": -37.27}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingR4": {"rotate": [{"time": 0, "angle": -19.93, "curve": [0.461, 0, 0.762, 0.42]}, {"time": 0.0667, "angle": -37.48, "curve": [0.377, 0.3, 0.62, 0.7]}, {"time": 0.1667, "angle": -38.05, "curve": [0.237, 0.58, 0.539, 1]}, {"time": 0.2667, "angle": 7.22, "curve": [0.563, 0, 0.754, 0.54]}, {"time": 0.4, "angle": -6.14, "curve": [0.223, 0.53, 0.484, 1]}, {"time": 0.5, "angle": -19.93}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingR5": {"rotate": [{"time": 0, "angle": -19.93, "curve": "stepped"}, {"time": 0.5, "angle": -19.93}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "wingL5": {"rotate": [{"time": 0, "angle": 13.99, "curve": "stepped"}, {"time": 0.5, "angle": 13.99}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "body2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.381, 0, 0.764, 0.52]}, {"time": 0.2667, "x": -8.37, "y": 0.76, "curve": [0.381, 0, 0.469, 1]}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.381, 0, 0.764, 0.52]}, {"time": 0.2667, "x": 0.854, "y": 1, "curve": [0.381, 0, 0.469, 1]}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "head2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": -1.08, "y": -5.6, "curve": "stepped"}, {"time": 0.5, "x": -1.08, "y": -5.6}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "head3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "head4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 7, "y": 36.38, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.2667, "x": 6.18, "y": 31.65, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.5, "x": 7, "y": 36.38}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "head5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 0.86, "y": 4.48, "curve": "stepped"}, {"time": 0.5, "x": 0.86, "y": 4.48}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "head6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "head7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}}, "transform": {"eye": [{"time": 0, "rotateMix": 0.15, "translateMix": 0.15, "scaleMix": 0.15, "shearMix": 0.15, "curve": "stepped"}, {"time": 0.5, "rotateMix": 0.15, "translateMix": 0.15, "scaleMix": 0.15, "shearMix": 0.15}], "head4": [{"time": 0, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 0.5, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2}], "head5": [{"time": 0, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2, "curve": "stepped"}, {"time": 0.5, "rotateMix": 0.2, "translateMix": 0.2, "scaleMix": 0.2, "shearMix": 0.2}], "head6": [{"time": 0, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 0.5, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1}], "head7": [{"time": 0, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1, "curve": "stepped"}, {"time": 0.5, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1}], "legL": [{"time": 0, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": [0.552, 0, 0.764, 0.52]}, {"time": 0.2667, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": -0.185, "shearMix": 0.5, "curve": [0.225, 0.52, 0.469, 1]}, {"time": 0.5, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5}], "legR": [{"time": 0, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 0.2667, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5, "curve": "stepped"}, {"time": 0.5, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0.5}], "wingL3": [{"time": 0, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 0.5, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5}], "wingR3": [{"time": 0, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5, "curve": "stepped"}, {"time": 0.5, "rotateMix": 0.5, "translateMix": 0.5, "scaleMix": 0.5, "shearMix": 0.5}]}, "deform": {"default": {"body2": {"body": [{"time": 0, "curve": "stepped"}, {"time": 0.5}]}, "head": {"head": [{"time": 0, "curve": "stepped"}, {"time": 0.5}]}, "wingL2": {"wingL": [{"time": 0, "curve": "stepped"}, {"time": 0.5}]}}}, "drawOrder": [{"time": 0}, {"time": 0.5}]}}}