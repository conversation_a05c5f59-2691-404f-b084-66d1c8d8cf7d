{"skeleton": {"hash": "jRK/Mp3cLNExn3jetCxntFcxMNM", "spine": "3.6.50", "width": 199.41, "height": 128.63, "images": ""}, "bones": [{"name": "root", "scaleX": -0.4, "scaleY": 0.4}, {"name": "body", "parent": "root", "x": -12.19, "y": 110.13}, {"name": "front_leg", "parent": "body", "length": 56.57, "rotation": -73.01, "x": -83.91, "y": -1.38}, {"name": "front_leg2", "parent": "front_leg", "length": 34.91, "rotation": -19, "x": 57.06, "y": -0.22}, {"name": "front_leg3", "parent": "front_leg2", "length": 25.14, "rotation": -19.79, "x": 36.8, "y": -0.37}, {"name": "front_leg4", "parent": "body", "length": 56.57, "rotation": -79.09, "x": -67.17, "y": 1.98}, {"name": "front_leg5", "parent": "front_leg4", "length": 34.91, "rotation": -15.91, "x": 56.67, "y": 0.13}, {"name": "front_leg6", "parent": "front_leg5", "length": 25.14, "rotation": -16.8, "x": 37.57, "y": -0.04}, {"name": "head", "parent": "body", "length": 40.53, "rotation": 120.96, "x": -77.11, "y": 64.41}, {"name": "hind_leg", "parent": "body", "length": 55.53, "rotation": -84.52, "x": 75.1, "y": -0.22}, {"name": "hind_leg2", "parent": "hind_leg", "length": 42.96, "rotation": 33.15, "x": 49.52, "y": -0.82}, {"name": "hind_leg3", "parent": "hind_leg2", "length": 31.7, "rotation": -53.09, "x": 41.5, "y": -0.21}, {"name": "hind_leg4", "parent": "body", "length": 55.53, "rotation": -84.52, "x": 64.98, "y": 0.28}, {"name": "hind_leg5", "parent": "hind_leg4", "length": 42.96, "rotation": 33.15, "x": 49.52, "y": -0.82}, {"name": "hind_leg6", "parent": "hind_leg5", "length": 31.7, "rotation": -53.09, "x": 41.5, "y": -0.21}, {"name": "shield", "parent": "body", "x": 80.65, "y": 72.06}, {"name": "tail", "parent": "body", "length": 33.84, "rotation": -0.33, "x": 95.29, "y": 25.69}, {"name": "tail2", "parent": "tail", "length": 33.84, "x": 33.84}, {"name": "tail3", "parent": "tail2", "length": 33.84, "x": 33.84}, {"name": "tail4", "parent": "tail3", "length": 33.84, "x": 33.84}, {"name": "tail5", "parent": "tail4", "length": 33.84, "x": 33.84}, {"name": "tail6", "parent": "tail5", "length": 33.84, "x": 33.84}], "slots": [{"name": "hind_leg2", "bone": "hind_leg4", "attachment": "hind_leg_R"}, {"name": "front_leg2", "bone": "front_leg4", "attachment": "front_leg_R"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "head", "bone": "head", "attachment": "head2"}, {"name": "front_leg", "bone": "front_leg", "attachment": "front_leg"}, {"name": "hind_leg", "bone": "hind_leg", "attachment": "hind_leg"}, {"name": "tail", "bone": "tail", "attachment": "tail"}, {"name": "shield", "bone": "shield", "attachment": "shield"}], "skins": {"default": {"body": {"body": {"x": -5.73, "y": 23.54, "width": 246, "height": 173}}, "front_leg": {"front_leg": {"type": "mesh", "uvs": [0.3388, 0.00459, 0.17929, 0.07059, 0, 0.20919, 0, 0.41599, 0.15423, 0.58118, 0.2013, 0.63159, 0.20777, 0.67473, 0.21898, 0.74952, 0.22494, 0.78922, 0.2343, 0.85159, 0.12429, 0.93519, 0.1573, 1, 0.77329, 1, 0.9438, 0.92419, 0.96867, 0.84144, 0.98698, 0.78051, 1, 0.73719, 1, 0.62334, 1, 0.56394, 1, 0.50234, 1, 0.33267, 1, 0.16519, 1, 0.06619, 0.82829, 0, 0.59239, 0.53693, 0.64315, 0.58733, 0.66637, 0.63564, 0.6094, 0.77668, 0.60528, 0.8317, 0.61952, 0.74143, 0.52786, 0.37658], "triangles": [12, 11, 28, 11, 9, 28, 13, 12, 14, 11, 10, 9, 14, 12, 28, 9, 8, 28, 28, 27, 14, 14, 27, 15, 28, 8, 27, 8, 7, 27, 27, 29, 15, 15, 29, 16, 27, 7, 29, 7, 6, 29, 29, 26, 16, 29, 6, 26, 26, 17, 16, 6, 25, 26, 6, 5, 25, 17, 26, 18, 5, 24, 25, 5, 4, 24, 26, 25, 18, 18, 25, 19, 4, 30, 24, 4, 3, 30, 25, 24, 19, 19, 24, 20, 24, 30, 20, 30, 1, 0, 30, 3, 1, 3, 2, 1, 30, 23, 20, 30, 0, 23, 23, 21, 20, 23, 22, 21], "vertices": [1, 2, -29.63, 9.62, 1, 1, 2, -23.08, -2.49, 1, 2, 2, -6.5, -19.02, 0.99977, 3, -53.98, -38.47, 0.00023, 2, 2, 23.07, -28.38, 0.88418, 3, -22.97, -37.7, 0.11582, 3, 2, 49.49, -27.04, 0.29458, 3, 1.57, -27.82, 0.70534, 4, -23.85, -37.75, 7e-05, 3, 2, 57.55, -26.63, 0.0867, 3, 9.06, -24.81, 0.90609, 4, -17.83, -32.38, 0.00721, 3, 2, 63.84, -28.21, 0.0142, 3, 15.52, -24.26, 0.94748, 4, -11.94, -29.68, 0.03832, 2, 3, 26.72, -23.31, 0.78221, 4, -1.72, -24.99, 0.21779, 2, 3, 32.66, -22.8, 0.58931, 4, 3.7, -22.5, 0.41069, 2, 3, 42, -22, 0.21079, 4, 12.22, -18.59, 0.78921, 2, 3, 54.7, -28.29, 0.00987, 4, 26.29, -20.2, 0.99013, 1, 4, 34.64, -14.84, 1, 2, 3, 63.44, 10.88, 0.01075, 4, 21.26, 19.61, 0.98925, 2, 3, 51.82, 20.83, 0.18656, 4, 6.95, 25.03, 0.81344, 2, 3, 39.37, 22.01, 0.56777, 4, -5.16, 21.93, 0.43223, 3, 2, 93.07, 11.57, 5e-05, 3, 30.21, 22.88, 0.86642, 4, -14.07, 19.65, 0.13353, 3, 2, 87.11, 14.28, 0.00629, 3, 23.69, 23.49, 0.95898, 4, -20.41, 18.02, 0.03474, 2, 2, 70.83, 19.43, 0.23429, 3, 6.62, 23.07, 0.76571, 2, 2, 62.34, 22.12, 0.55426, 3, -2.29, 22.84, 0.44574, 2, 2, 53.53, 24.91, 0.84979, 3, -11.52, 22.61, 0.15021, 1, 2, 29.26, 32.59, 1, 1, 2, 5.31, 40.17, 1, 1, 2, -8.84, 44.65, 1, 1, 2, -21.42, 37.83, 1, 1, 2, 51.09, 0.03, 1, 1, 3, 1.76, 1.53, 1, 2, 2, 66.55, -0.21, 4e-05, 3, 8.97, 3.1, 0.99996, 1, 3, 30.2, 0.21, 1, 1, 4, 1.38, 1.08, 1, 1, 3, 24.9, 0.69, 1, 1, 2, 27, 3.6, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 18, 20, 20, 22, 22, 24, 24, 26, 42, 44, 44, 46, 0, 46, 6, 8, 8, 10, 8, 48, 48, 38, 10, 50, 36, 38, 50, 36, 10, 12, 12, 52, 32, 34, 34, 36, 52, 34, 16, 18, 16, 54, 30, 32, 54, 30, 18, 56, 26, 28, 28, 30, 56, 28, 12, 14, 14, 16, 14, 58, 58, 32, 6, 60, 38, 40, 40, 42, 60, 40], "width": 60, "height": 150}}, "front_leg2": {"front_leg_R": {"type": "mesh", "uvs": [0.29974, 0.0022, 0.0082, 0.22318, 0.0082, 0.46958, 0.09507, 0.56648, 0.13596, 0.62372, 0.18072, 0.68518, 0.21569, 0.78443, 0.22048, 0.82371, 0.20659, 0.86877, 0.04007, 0.91838, 0.06711, 1, 0.78121, 1, 0.85669, 0.93202, 0.92162, 0.85204, 0.93947, 0.80061, 0.95734, 0.73617, 0.9918, 0.64558, 0.9918, 0.55713, 0.9918, 0.49548, 0.99179, 0.42633, 0.9918, 0.08018, 0.84072, 0, 0.56869, 0.79555, 0.54215, 0.84413, 0.58818, 0.73853, 0.5328, 0.55566, 0.54805, 0.59373, 0.51777, 0.49889, 0.50359, 0.88626], "triangles": [22, 24, 14, 23, 7, 6, 22, 23, 6, 13, 22, 14, 23, 22, 13, 28, 7, 23, 8, 7, 28, 12, 23, 13, 28, 23, 12, 10, 9, 8, 11, 28, 12, 10, 8, 28, 11, 10, 28, 25, 27, 18, 25, 18, 17, 3, 27, 25, 26, 25, 17, 4, 3, 25, 4, 25, 26, 26, 17, 16, 5, 4, 26, 16, 24, 26, 15, 24, 16, 5, 26, 24, 6, 5, 24, 22, 6, 24, 14, 24, 15, 19, 21, 20, 27, 2, 1, 27, 0, 21, 27, 21, 19, 27, 19, 18, 27, 1, 0, 3, 2, 27], "vertices": [1, 5, -31.14, -3.68, 1, 2, 5, -1.62, -27.5, 0.99934, 6, -71.28, -11.74, 0.00066, 2, 5, 41.56, -38.49, 0.68313, 6, -28.37, -35.06, 0.31687, 3, 5, 64.34, -32.13, 0.2753, 6, -3.02, -34.15, 0.71945, 7, -16.2, -51.52, 0.00525, 3, 5, 75.51, -24.78, 0.06059, 6, 9.31, -29.88, 0.89148, 7, -8.27, -40.75, 0.04793, 3, 5, 84.02, -17.71, 0.00821, 6, 18.52, -25.62, 0.85485, 7, -2.8, -31.55, 0.13694, 2, 6, 31.44, -22.71, 0.56452, 7, 6.8, -21.33, 0.43548, 2, 6, 34.38, -22.69, 0.15013, 7, 9.63, -19.63, 0.84987, 2, 6, 40.42, -24.88, 0.09841, 7, 15.89, -17.85, 0.90159, 2, 6, 44.46, -36.55, 0.01035, 7, 26.07, -24.94, 0.98965, 1, 7, 36.74, -18.94, 1, 1, 7, 20.56, 21.5, 1, 2, 6, 58.52, 11.24, 0.00221, 7, 9.36, 22, 0.99779, 2, 6, 47.85, 18.02, 0.00411, 7, -3.26, 21.21, 0.99589, 3, 5, 90.97, 33.43, 4e-05, 6, 39.42, 20.99, 0.17296, 7, -12.15, 19.08, 0.827, 3, 5, 76.8, 33.57, 0.01357, 6, 25.98, 22.33, 0.67862, 7, -25.19, 13.45, 0.30781, 3, 5, 61.53, 29.71, 0.13292, 6, 11.48, 21.78, 0.85434, 7, -37.79, 4.59, 0.01274, 2, 5, 52.28, 24.5, 0.4353, 6, 1.62, 21.12, 0.5647, 2, 5, 46.44, 23.59, 0.7172, 6, -4.87, 23.44, 0.2828, 2, 5, 39.28, 25.51, 0.97272, 6, -12.49, 28.93, 0.02728, 2, 5, -11.34, 35.49, 0.99989, 6, -60.95, 55.48, 0.00011, 2, 5, -24.89, 28.72, 0.99995, 6, -76.92, 52.94, 5e-05, 2, 6, 34.41, -1.28, 0.39679, 7, -3.39, -1.81, 0.60321, 2, 6, 41.11, -4.13, 0.04532, 7, 4.25, -0.62, 0.95468, 2, 6, 25.58, -0.33, 0.86286, 7, -12.13, -5.63, 0.13714, 2, 6, -1.69, -6.31, 0.99991, 7, -32.23, -27.21, 9e-05, 2, 6, 3.93, -4.88, 0.99907, 7, -28.25, -22.54, 0.00093, 2, 5, 46.1, -6.23, 0.71391, 6, -14.33, -5.36, 0.28609, 2, 6, 46.67, -7.91, 0.02954, 7, 11.01, -0.43, 0.97046], "hull": 22, "edges": [0, 2, 2, 4, 18, 20, 20, 22, 38, 40, 40, 42, 0, 42, 10, 12, 12, 14, 12, 44, 26, 28, 44, 28, 14, 46, 46, 26, 10, 48, 28, 30, 30, 32, 48, 30, 4, 6, 6, 50, 32, 34, 50, 34, 6, 8, 8, 10, 8, 52, 52, 32, 4, 54, 34, 36, 36, 38, 54, 36, 14, 16, 16, 18, 16, 56, 22, 24, 24, 26, 56, 24], "width": 60, "height": 150}}, "head": {"head": {"x": 68.11, "y": 1.46, "rotation": -120.96, "width": 166, "height": 196}, "head2": {"x": 63.23, "y": 0.7, "rotation": -120.96, "width": 170, "height": 167}}, "hind_leg": {"hind_leg": {"type": "mesh", "uvs": [0, 0.19252, 0.21209, 0.03485, 0.48209, 0, 0.72709, 0, 0.92959, 0.12563, 0.96459, 0.28489, 0.94382, 0.38987, 0.92709, 0.47441, 0.94749, 0.51762, 0.97603, 0.5781, 1, 0.62889, 1, 0.71025, 1, 0.90122, 0.89959, 0.99359, 0.73709, 1, 0.44959, 1, 0.42209, 0.94436, 0.48748, 0.88188, 0.53209, 0.83925, 0.53709, 0.7851, 0.46782, 0.76068, 0.2577, 0.68659, 0.1396, 0.64495, 0.08522, 0.5612, 0, 0.42995, 0.72959, 0.68476, 0.75959, 0.71343, 0.78209, 0.75484, 0.74459, 0.88862, 0.4396, 0.56691, 0.5046, 0.58762, 0.4146, 0.52073], "triangles": [15, 17, 14, 14, 28, 13, 14, 17, 28, 15, 16, 17, 13, 28, 12, 28, 27, 12, 27, 11, 12, 17, 18, 28, 28, 18, 27, 27, 19, 26, 27, 18, 19, 27, 26, 11, 19, 25, 26, 19, 20, 25, 20, 30, 25, 20, 21, 30, 11, 26, 10, 9, 10, 26, 26, 25, 9, 21, 29, 30, 21, 22, 29, 25, 8, 9, 25, 30, 8, 22, 31, 29, 30, 7, 8, 30, 29, 7, 29, 31, 7, 22, 23, 31, 23, 24, 31, 7, 31, 6, 31, 2, 3, 31, 0, 1, 31, 1, 2, 5, 6, 3, 3, 6, 31, 31, 24, 0, 5, 3, 4], "vertices": [1, 9, 4.16, -40.04, 1, 1, 9, -15.28, -19.85, 1, 1, 9, -17.75, 3.71, 1, 2, 9, -16.01, 24.23, 0.98827, 10, -1.14, 76.74, 0.01173, 2, 9, 0.47, 38.42, 0.91848, 10, 17.63, 64.31, 0.08152, 2, 9, 18.36, 38.51, 0.75432, 10, 22.62, 45.98, 0.24568, 2, 9, 28.9, 36.15, 0.54371, 10, 22.91, 34.6, 0.45629, 2, 9, 36.56, 36.3, 0.29388, 10, 24.49, 27.31, 0.70612, 2, 9, 39.46, 40.18, 0.1675, 10, 28.09, 25.89, 0.8325, 2, 9, 44.4, 46.77, 0.06137, 10, 34.41, 23.9, 0.93863, 2, 9, 49.32, 52.85, 0.01949, 10, 40.5, 21.93, 0.98051, 3, 9, 60.33, 59.52, 0.0005, 10, 49.09, 15.4, 0.95325, 11, -12.76, 12.86, 0.04625, 2, 10, 64.76, -12.92, 0.0579, 11, 16.35, 22.44, 0.9421, 2, 10, 61.79, -28.71, 0.00078, 11, 31.03, 16.55, 0.99922, 1, 11, 35.36, 3.22, 1, 1, 11, 41.54, -20.72, 1, 2, 10, 21.84, -40.07, 0.0045, 11, 34.93, -24.82, 0.9955, 2, 10, 23.96, -30.21, 0.04472, 11, 25.84, -21.06, 0.95528, 2, 10, 26.15, -23.74, 0.15154, 11, 20.39, -18, 0.84846, 2, 10, 26.03, -18.85, 0.47036, 11, 16.86, -18.13, 0.52964, 3, 9, 89.81, 15.36, 0.00622, 10, 22.41, -22.46, 0.75238, 11, 20.98, -23.68, 0.2414, 3, 9, 86.67, -16.51, 0.40896, 10, -4.83, -31.28, 0.58163, 11, 23.6, -54.16, 0.00941, 3, 9, 74.07, -32.65, 0.74493, 10, -25.72, -27.17, 0.25506, 11, 14.11, -73.12, 1e-05, 2, 9, 56.16, -37.79, 0.94474, 10, -38.36, -12.86, 0.05526, 1, 9, 36.07, -43.1, 1, 2, 10, 31.3, 0.47, 0.82293, 11, -1.11, -9.41, 0.17707, 2, 10, 35.3, 0.5, 0.42991, 11, -1.29, -4.61, 0.57009, 2, 10, 39.14, -3.64, 0.04607, 11, 2.4, -1.18, 0.95393, 2, 10, 43.75, -21.49, 0.00291, 11, 20.65, 0.12, 0.99709, 2, 10, 3.66, -10.05, 0.99999, 11, 4.17, -42, 1e-05, 2, 10, 9.33, -7.42, 0.99968, 11, 2.66, -35.27, 0.00032, 2, 9, 55.74, -9.01, 0.66971, 10, -9.2, -2.93, 0.33029], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 0, 48, 38, 40, 40, 50, 18, 20, 50, 18, 38, 52, 52, 20, 36, 54, 20, 22, 22, 24, 54, 22, 32, 34, 34, 36, 34, 56, 56, 24, 44, 58, 58, 14, 40, 42, 42, 44, 42, 60, 14, 16, 16, 18, 60, 16, 44, 46, 46, 48, 46, 62, 10, 12, 12, 14, 62, 12], "width": 85, "height": 135}}, "hind_leg2": {"hind_leg_R": {"type": "mesh", "uvs": [0, 0.19252, 0.21209, 0.03485, 0.48209, 0, 0.72709, 0, 0.92959, 0.12563, 0.96459, 0.28489, 0.94382, 0.38987, 0.92709, 0.47441, 0.94749, 0.51762, 0.97603, 0.5781, 1, 0.62889, 1, 0.71025, 1, 0.90122, 0.89959, 0.99359, 0.73709, 1, 0.44959, 1, 0.42209, 0.94436, 0.48748, 0.88188, 0.53209, 0.83925, 0.53709, 0.7851, 0.46782, 0.76068, 0.2577, 0.68659, 0.1396, 0.64495, 0.08522, 0.5612, 0, 0.42995, 0.72959, 0.68476, 0.75959, 0.71343, 0.78209, 0.75484, 0.74459, 0.88862, 0.4396, 0.56691, 0.5046, 0.58762, 0.4146, 0.52073], "triangles": [15, 17, 14, 14, 28, 13, 14, 17, 28, 15, 16, 17, 13, 28, 12, 28, 27, 12, 27, 11, 12, 17, 18, 28, 28, 18, 27, 27, 19, 26, 27, 18, 19, 27, 26, 11, 19, 25, 26, 19, 20, 25, 20, 30, 25, 20, 21, 30, 11, 26, 10, 9, 10, 26, 26, 25, 9, 21, 29, 30, 21, 22, 29, 25, 8, 9, 25, 30, 8, 22, 31, 29, 30, 7, 8, 30, 29, 7, 29, 31, 7, 22, 23, 31, 23, 24, 31, 7, 31, 6, 31, 2, 3, 31, 0, 1, 31, 1, 2, 5, 6, 3, 3, 6, 31, 31, 24, 0, 5, 3, 4], "vertices": [1, 12, 4.16, -40.04, 1, 1, 12, -15.28, -19.85, 1, 1, 12, -17.75, 3.71, 1, 2, 12, -16.01, 24.23, 0.98827, 13, -1.14, 76.74, 0.01173, 2, 12, 0.47, 38.42, 0.91848, 13, 17.63, 64.31, 0.08152, 2, 12, 18.36, 38.51, 0.75432, 13, 22.62, 45.98, 0.24568, 2, 12, 28.9, 36.15, 0.54371, 13, 22.91, 34.6, 0.45629, 2, 12, 36.56, 36.3, 0.29388, 13, 24.49, 27.31, 0.70612, 2, 12, 39.46, 40.18, 0.1675, 13, 28.09, 25.89, 0.8325, 2, 12, 44.4, 46.77, 0.06137, 13, 34.41, 23.9, 0.93863, 2, 12, 49.32, 52.85, 0.01949, 13, 40.5, 21.93, 0.98051, 3, 12, 60.33, 59.52, 0.0005, 13, 49.09, 15.4, 0.95325, 14, -12.76, 12.86, 0.04625, 2, 13, 64.76, -12.92, 0.0579, 14, 16.35, 22.44, 0.9421, 2, 13, 61.79, -28.71, 0.00078, 14, 31.03, 16.55, 0.99922, 1, 14, 35.36, 3.22, 1, 1, 14, 41.54, -20.72, 1, 2, 13, 21.84, -40.07, 0.0045, 14, 34.93, -24.82, 0.9955, 2, 13, 23.96, -30.21, 0.04472, 14, 25.84, -21.06, 0.95528, 2, 13, 26.15, -23.74, 0.15154, 14, 20.39, -18, 0.84846, 2, 13, 26.03, -18.85, 0.47036, 14, 16.86, -18.13, 0.52964, 3, 12, 89.81, 15.36, 0.00622, 13, 22.41, -22.46, 0.75238, 14, 20.98, -23.68, 0.2414, 3, 12, 86.67, -16.51, 0.40896, 13, -4.83, -31.28, 0.58163, 14, 23.6, -54.16, 0.00941, 3, 12, 74.07, -32.65, 0.74493, 13, -25.72, -27.17, 0.25506, 14, 14.11, -73.12, 1e-05, 2, 12, 56.16, -37.79, 0.94474, 13, -38.36, -12.86, 0.05526, 1, 12, 36.07, -43.1, 1, 2, 13, 31.3, 0.47, 0.82293, 14, -1.11, -9.41, 0.17707, 2, 13, 35.3, 0.5, 0.42991, 14, -1.29, -4.61, 0.57009, 2, 13, 39.14, -3.64, 0.04607, 14, 2.4, -1.18, 0.95393, 2, 13, 43.75, -21.49, 0.00291, 14, 20.65, 0.12, 0.99709, 2, 13, 3.66, -10.05, 0.99999, 14, 4.17, -42, 1e-05, 2, 13, 9.33, -7.42, 0.99968, 14, 2.66, -35.27, 0.00032, 2, 12, 55.74, -9.01, 0.66971, 13, -9.2, -2.93, 0.33029], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 0, 48, 38, 40, 40, 50, 18, 20, 50, 18, 38, 52, 52, 20, 36, 54, 20, 22, 22, 24, 54, 22, 32, 34, 34, 36, 34, 56, 56, 24, 44, 58, 58, 14, 40, 42, 42, 44, 42, 60, 14, 16, 16, 18, 60, 16, 44, 46, 46, 48, 46, 62, 10, 12, 12, 14, 62, 12], "width": 85, "height": 135}}, "shield": {"shield": {"x": -50.94, "y": -35.83, "width": 318, "height": 207}}, "tail": {"tail": {"type": "mesh", "uvs": [1, 1, 0.95338, 1, 0.9077, 1, 0.86651, 1, 0.81934, 1, 0.7819, 1, 0.74371, 1, 0.70323, 1, 0.65606, 1, 0.62086, 1, 0.58116, 1, 0.54007, 1, 0.49664, 1, 0.45845, 1, 0.41847, 1, 0.37831, 1, 0.33862, 1, 0.30342, 1, 0.25673, 1, 0.21581, 1, 0.17613, 1, 0.14168, 1, 0.10846, 1, 0.07027, 1, 0.03658, 1, 0, 1, 0, 0, 0.03808, 0, 0.06878, 0, 0.10921, 0, 0.14318, 0, 0.17687, 0, 0.21506, 0, 0.25673, 0, 0.30268, 0, 0.33787, 0, 0.37756, 0, 0.41772, 0, 0.45845, 0, 0.49589, 0, 0.54232, 0, 0.57967, 0, 0.61787, 0, 0.65755, 0, 0.70098, 0, 0.74371, 0, 0.78115, 0, 0.81859, 0, 0.86652, 0, 0.90845, 0, 0.95038, 0, 1, 0], "triangles": [4, 47, 48, 3, 4, 48, 2, 48, 49, 3, 48, 2, 1, 50, 51, 0, 1, 51, 2, 49, 50, 1, 2, 50, 7, 44, 45, 8, 43, 44, 7, 8, 44, 6, 7, 45, 5, 46, 47, 6, 45, 46, 5, 6, 46, 5, 47, 4, 11, 12, 39, 40, 11, 39, 10, 41, 42, 40, 41, 10, 11, 40, 10, 9, 42, 43, 10, 42, 9, 8, 9, 43, 16, 35, 36, 15, 36, 37, 16, 36, 15, 14, 37, 38, 15, 37, 14, 13, 14, 38, 13, 38, 39, 12, 13, 39, 19, 32, 33, 20, 31, 32, 19, 20, 32, 18, 19, 33, 17, 34, 35, 18, 33, 34, 17, 18, 34, 17, 35, 16, 24, 25, 26, 27, 24, 26, 23, 28, 29, 24, 27, 28, 23, 24, 28, 22, 23, 29, 21, 29, 30, 22, 29, 21, 20, 30, 31, 21, 30, 20], "vertices": [1, 21, 38.83, -8.56, 1, 2, 20, 62.84, -8.62, 0.00115, 21, 28.99, -8.62, 0.99885, 2, 20, 53.2, -8.67, 0.00228, 21, 19.35, -8.67, 0.99772, 2, 20, 44.51, -8.72, 0.0033, 21, 10.67, -8.72, 0.9967, 2, 20, 34.55, -8.78, 0.45162, 21, 0.71, -8.78, 0.54838, 2, 20, 26.66, -8.82, 0.96531, 21, -7.19, -8.82, 0.03469, 3, 19, 52.44, -8.87, 0.00305, 20, 18.6, -8.87, 0.9791, 21, -15.25, -8.87, 0.01785, 2, 19, 43.9, -8.92, 0.00628, 20, 10.06, -8.92, 0.99372, 2, 19, 33.95, -8.98, 0.52173, 20, 0.1, -8.98, 0.47827, 2, 19, 26.52, -9.02, 0.96704, 20, -7.32, -9.02, 0.03296, 3, 18, 51.99, -9.07, 0.00432, 19, 18.14, -9.07, 0.97892, 20, -15.7, -9.07, 0.01676, 2, 18, 43.32, -9.12, 0.00878, 19, 9.47, -9.12, 0.99122, 2, 18, 34.15, -9.17, 0.44759, 19, 0.31, -9.17, 0.55241, 2, 18, 26.1, -9.22, 0.96059, 19, -7.75, -9.22, 0.03941, 3, 17, 51.5, -9.26, 0.00788, 18, 17.66, -9.26, 0.97237, 19, -16.18, -9.26, 0.01975, 2, 17, 43.03, -9.31, 0.01579, 18, 9.19, -9.31, 0.98421, 2, 17, 34.66, -9.36, 0.43695, 18, 0.81, -9.36, 0.56305, 2, 17, 27.23, -9.4, 0.92871, 18, -6.61, -9.4, 0.07129, 3, 16, 51.22, -9.46, 0.01534, 17, 17.38, -9.46, 0.95137, 18, -16.47, -9.46, 0.03329, 2, 16, 42.59, -9.51, 0.02878, 17, 8.74, -9.51, 0.97122, 2, 16, 34.21, -9.56, 0.50628, 17, 0.37, -9.56, 0.49372, 2, 16, 26.95, -9.6, 0.9589, 17, -6.9, -9.6, 0.0411, 2, 16, 19.94, -9.64, 0.96853, 17, -13.91, -9.64, 0.03147, 2, 16, 11.88, -9.69, 0.97961, 17, -21.96, -9.69, 0.02039, 2, 16, 4.77, -9.73, 0.98939, 17, -29.07, -9.73, 0.01061, 1, 16, -2.95, -9.77, 1, 1, 16, -3.05, 7.23, 1, 2, 16, 4.99, 7.27, 0.99294, 17, -28.86, 7.27, 0.00706, 2, 16, 11.47, 7.31, 0.98724, 17, -22.38, 7.31, 0.01276, 2, 16, 20, 7.36, 0.97974, 17, -13.85, 7.36, 0.02026, 2, 16, 27.16, 7.4, 0.97344, 17, -6.68, 7.4, 0.02656, 2, 16, 34.27, 7.44, 0.42401, 17, 0.43, 7.44, 0.57599, 2, 16, 42.33, 7.49, 0.00059, 17, 8.49, 7.49, 0.99941, 3, 16, 51.12, 7.54, 0.00031, 17, 17.28, 7.54, 0.98204, 18, -16.56, 7.54, 0.01765, 3, 16, 60.82, 7.6, 0, 17, 26.97, 7.6, 0.96288, 18, -6.87, 7.6, 0.03712, 3, 16, 68.24, 7.64, 0, 17, 34.4, 7.64, 0.40993, 18, 0.56, 7.64, 0.59007, 2, 17, 42.77, 7.69, 0.00518, 18, 8.93, 7.69, 0.99482, 3, 17, 51.25, 7.73, 0.00261, 18, 17.4, 7.73, 0.98505, 19, -16.44, 7.73, 0.01234, 2, 18, 26, 7.78, 0.97514, 19, -7.85, 7.78, 0.02486, 2, 18, 33.9, 7.83, 0.46435, 19, 0.05, 7.83, 0.53565, 2, 18, 43.69, 7.89, 0.00273, 19, 9.85, 7.89, 0.99727, 3, 18, 51.57, 7.93, 0.00138, 19, 17.73, 7.93, 0.98966, 20, -16.11, 7.93, 0.00896, 2, 19, 25.79, 7.98, 0.98187, 20, -8.05, 7.98, 0.01813, 2, 19, 34.16, 8.03, 0.44988, 20, 0.32, 8.03, 0.55012, 2, 19, 43.33, 8.08, 0.0028, 20, 9.48, 8.08, 0.9972, 3, 19, 52.34, 8.13, 0.00131, 20, 18.5, 8.13, 0.98897, 21, -15.34, 8.13, 0.00972, 2, 20, 26.4, 8.18, 0.98176, 21, -7.44, 8.18, 0.01824, 2, 20, 34.3, 8.22, 0.46577, 21, 0.46, 8.22, 0.53423, 1, 21, 10.57, 8.28, 1, 1, 21, 19.42, 8.33, 1, 1, 21, 28.26, 8.38, 1, 1, 21, 38.73, 8.44, 1], "hull": 52, "edges": [50, 52, 0, 102, 60, 42, 60, 62, 40, 42, 62, 40, 62, 64, 38, 40, 64, 38, 68, 34, 68, 70, 32, 34, 70, 32, 70, 72, 30, 32, 72, 30, 76, 26, 76, 78, 24, 26, 78, 24, 78, 80, 22, 24, 80, 22, 84, 18, 84, 86, 16, 18, 86, 16, 86, 88, 14, 16, 88, 14, 92, 10, 92, 94, 8, 10, 94, 8, 94, 96, 6, 8, 96, 6, 88, 90, 90, 92, 10, 12, 12, 14, 90, 12, 96, 98, 4, 6, 98, 4, 98, 100, 100, 102, 0, 2, 2, 4, 100, 2, 80, 82, 82, 84, 18, 20, 20, 22, 82, 20, 72, 74, 74, 76, 26, 28, 28, 30, 74, 28, 64, 66, 66, 68, 34, 36, 36, 38, 66, 36, 58, 60, 42, 44, 58, 44, 56, 58, 44, 46, 56, 46, 52, 54, 54, 56, 46, 48, 48, 50, 54, 48], "width": 211, "height": 17}}}}, "animations": {"idle": {"bones": {"body": {"rotate": [{"time": 0, "angle": -29.43, "curve": "stepped"}, {"time": 1, "angle": -29.43}], "translate": [{"time": 0, "x": 0, "y": -28.5, "curve": "stepped"}, {"time": 1, "x": 0, "y": -28.5}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg4": {"rotate": [{"time": 0, "angle": 10.09, "curve": "stepped"}, {"time": 2, "angle": 10.09}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "front_leg": {"rotate": [{"time": 0, "angle": 10.35, "curve": "stepped"}, {"time": 2, "angle": 10.35}], "translate": [{"time": 0, "x": 1.71, "y": -3.03, "curve": "stepped"}, {"time": 2, "x": 1.71, "y": -3.03}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "hind_leg": {"rotate": [{"time": 0, "angle": -62.88, "curve": "stepped"}, {"time": 1, "angle": -62.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg4": {"rotate": [{"time": 0, "angle": -57.91, "curve": "stepped"}, {"time": 1, "angle": -57.91}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "tail": {"rotate": [{"time": 0, "angle": -72.63}, {"time": 1, "angle": -74.96}, {"time": 2, "angle": -72.63}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "tail6": {"rotate": [{"time": 0, "angle": 13.03}, {"time": 1, "angle": -9.51}, {"time": 2, "angle": 13.03}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "tail2": {"rotate": [{"time": 0, "angle": -39.2}, {"time": 1, "angle": -41.52}, {"time": 2, "angle": -39.2}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "tail3": {"rotate": [{"time": 0, "angle": -38.82}, {"time": 1, "angle": -27.31}, {"time": 2, "angle": -38.82}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "tail4": {"rotate": [{"time": 0, "angle": -6.44}, {"time": 1, "angle": -16.17}, {"time": 2, "angle": -6.44}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "tail5": {"rotate": [{"time": 0, "angle": 10.04}, {"time": 1, "angle": -8.25}, {"time": 2, "angle": 10.04}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "hind_leg2": {"rotate": [{"time": 0, "angle": 9.73, "curve": "stepped"}, {"time": 1, "angle": 9.73}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg5": {"rotate": [{"time": 0, "angle": 2.63, "curve": "stepped"}, {"time": 1, "angle": 2.63}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg2": {"rotate": [{"time": 0, "angle": 11.74, "curve": "stepped"}, {"time": 1, "angle": 11.74}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg5": {"rotate": [{"time": 0, "angle": 8.14, "curve": "stepped"}, {"time": 1, "angle": 8.14}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "head": {"rotate": [{"time": 0, "angle": 25.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 29.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 25.76}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 1, "y": 1.039}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "shield": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}}, "deform": {"default": {"front_leg": {"front_leg": [{"time": 0, "curve": "stepped"}, {"time": 2}]}, "front_leg2": {"front_leg_R": [{"time": 0, "curve": "stepped"}, {"time": 2}]}, "hind_leg": {"hind_leg": [{"time": 0}]}, "hind_leg2": {"hind_leg_R": [{"time": 0}]}, "tail": {"tail": [{"time": 0, "curve": "stepped"}, {"time": 2}]}}}, "drawOrder": [{"time": 0}, {"time": 2}]}, "move": {"slots": {"head": {"attachment": [{"time": 0, "name": "head"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg2": {"rotate": [{"time": 0, "angle": 8.15}, {"time": 0.1667, "angle": 53.87}, {"time": 0.5, "angle": -7.52}, {"time": 0.6667, "angle": -7.96}, {"time": 1, "angle": 8.15}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "body": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2333, "x": 0, "y": 9.8}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.7333, "x": 0, "y": 9.8}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "head": {"rotate": [{"time": 0, "angle": 3.73}, {"time": 0.2333, "angle": -1.86}, {"time": 0.5, "angle": 3.73}, {"time": 0.7333, "angle": -1.86}, {"time": 1, "angle": 3.73}], "translate": [{"time": 0, "x": -3.5, "y": 4.67}, {"time": 0.2333, "x": 1.17, "y": -4.67}, {"time": 0.5, "x": -3.5, "y": 4.67}, {"time": 0.7333, "x": 1.17, "y": -4.67}, {"time": 1, "x": -3.5, "y": 4.67}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg": {"rotate": [{"time": 0, "angle": -30.77}, {"time": 0.5, "angle": 21.96}, {"time": 0.6667, "angle": 3.69}, {"time": 1, "angle": -30.77}], "translate": [{"time": 0, "x": 0, "y": -6.16}, {"time": 0.5, "x": 0, "y": -0.67}, {"time": 0.6667, "x": 0, "y": 8.02}, {"time": 1, "x": 0, "y": -6.16}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg": {"rotate": [{"time": 0, "angle": 23.51}, {"time": 0.1667, "angle": -10.05}, {"time": 0.5, "angle": -46.54}, {"time": 0.6667, "angle": -24.16}, {"time": 1, "angle": 23.51}], "translate": [{"time": 0, "x": 0, "y": -8.02}, {"time": 0.1667, "x": 0, "y": 2.67}, {"time": 0.5, "x": 0, "y": -1.78}, {"time": 1, "x": 0, "y": -8.02}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "shield": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "tail": {"rotate": [{"time": 0, "angle": -20.14}, {"time": 0.5, "angle": -6.77}, {"time": 1, "angle": -20.14}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "tail2": {"rotate": [{"time": 0, "angle": -15.28}, {"time": 0.5, "angle": -7.22}, {"time": 1, "angle": -15.28}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "tail3": {"rotate": [{"time": 0, "angle": -1.4}, {"time": 0.5, "angle": -13.42}, {"time": 1, "angle": -1.4}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "tail4": {"rotate": [{"time": 0, "angle": 17.58}, {"time": 0.5, "angle": -9.63}, {"time": 1, "angle": 17.58}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "tail5": {"rotate": [{"time": 0, "angle": 24.01}, {"time": 0.5, "angle": -9.23}, {"time": 1, "angle": 24.01}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "tail6": {"rotate": [{"time": 0, "angle": 11.61}, {"time": 0.5, "angle": -4.19}, {"time": 1, "angle": 11.61}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg3": {"rotate": [{"time": 0, "angle": 12.97}, {"time": 0.1667, "angle": 8.59}, {"time": 0.5, "angle": -11.91}, {"time": 0.6667, "angle": 29.43}, {"time": 1, "angle": 12.97}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg2": {"rotate": [{"time": 0, "angle": -40.01}, {"time": 0.1667, "angle": -14.79}, {"time": 0.5, "angle": 9.98}, {"time": 0.6667, "angle": -36.47}, {"time": 1, "angle": -40.01}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg3": {"rotate": [{"time": 0, "angle": 43.19}, {"time": 0.1667, "angle": -6.69}, {"time": 0.5, "angle": -13.86}, {"time": 0.6667, "angle": 40.69}, {"time": 1, "angle": 43.19}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg4": {"rotate": [{"time": 0, "angle": 20.5}, {"time": 0.5, "angle": -25.25}, {"time": 1, "angle": 20.5}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 0, "y": -5.13}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg6": {"rotate": [{"time": 0, "angle": -7.78}, {"time": 0.1667, "angle": 91.93}, {"time": 0.5, "angle": 1.83}, {"time": 0.6667, "angle": 14.25}, {"time": 1, "angle": -7.78}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "front_leg5": {"rotate": [{"time": 0, "angle": 10.38}, {"time": 0.1667, "angle": -74.84}, {"time": 0.5, "angle": -38.71}, {"time": 0.6667, "angle": -2.68}, {"time": 1, "angle": 10.38}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg4": {"rotate": [{"time": 0, "angle": -34.35}, {"time": 0.1667, "angle": -4.77}, {"time": 0.5, "angle": 29.42}, {"time": 0.6667, "angle": -10.38}, {"time": 1, "angle": -34.35}], "translate": [{"time": 0, "x": 0, "y": 8.91}, {"time": 0.5, "x": 0, "y": -22.27}, {"time": 0.6667, "x": 0, "y": -3.27}, {"time": 1, "x": 0, "y": 8.91}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg6": {"rotate": [{"time": 0, "angle": 5.06}, {"time": 0.1667, "angle": 15.5}, {"time": 0.5, "angle": 0.6}, {"time": 0.6667, "angle": -12.97}, {"time": 1, "angle": 5.06}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "hind_leg5": {"rotate": [{"time": 0, "angle": -13.11}, {"time": 0.1667, "angle": -14.74}, {"time": 0.5, "angle": 10.86}, {"time": 0.6667, "angle": 35.34}, {"time": 1, "angle": -13.11}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}}, "deform": {"default": {"front_leg": {"front_leg": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "front_leg2": {"front_leg_R": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "hind_leg": {"hind_leg": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "hind_leg2": {"hind_leg_R": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "tail": {"tail": [{"time": 0, "curve": "stepped"}, {"time": 1}]}}}, "drawOrder": [{"time": 0}, {"time": 0.5}, {"time": 1}]}}}