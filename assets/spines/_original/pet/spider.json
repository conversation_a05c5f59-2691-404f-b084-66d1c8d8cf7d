{"skeleton": {"hash": "LAnnyiDuGSAJn78K9/p0xbIINTI", "spine": "3.6.50", "width": 133.95, "height": 64.94, "images": ""}, "bones": [{"name": "root_0.16", "scaleX": -0.16, "scaleY": 0.16}, {"name": "spider", "parent": "root_0.16", "y": -13.21}, {"name": "body", "parent": "spider", "length": 58.45, "rotation": 89.06, "x": 3.41, "y": 270.16}, {"name": "body2", "parent": "body", "length": 54.45, "rotation": 2.79, "x": 58.74, "y": 0.42}, {"name": "head", "parent": "body", "length": 23.97, "rotation": 178.65, "x": -129.31, "y": -2.12}, {"name": "leg1", "parent": "body", "x": -147.11, "y": 148.18}, {"name": "upper_leg_left2", "parent": "root_0.16", "x": -250.73, "y": 204.66, "color": "ff3f00ff"}, {"name": "leg2", "parent": "body", "length": 51.87, "rotation": 86.7, "x": -132.31, "y": 64.19}, {"name": "leg3", "parent": "leg2", "length": 153.79, "rotation": -25.99, "x": 52.76, "y": 1.03}, {"name": "lower_leg_left2", "parent": "root_0.16", "x": -355.77, "y": -22.48, "color": "ff3f00ff"}, {"name": "leg4", "parent": "leg3", "length": 133.24, "rotation": 79.89, "x": 159.87, "y": 0.76}, {"name": "leg5", "parent": "leg4", "length": 108.88, "rotation": 27.5, "x": 149.14, "y": 10}, {"name": "upper_leg_right2", "parent": "root_0.16", "x": 259.73, "y": 217.79, "color": "ff3f00ff"}, {"name": "leg6", "parent": "body", "length": 51.87, "rotation": 93.37, "x": -128.29, "y": -76.82, "scaleX": -1}, {"name": "leg7", "parent": "leg6", "length": 153.79, "rotation": -33.12, "x": 52.76, "y": 1.03}, {"name": "lower_leg_right2", "parent": "root_0.16", "x": 356.31, "y": -10.67, "color": "ff3f00ff"}, {"name": "leg8", "parent": "leg7", "length": 133.24, "rotation": 79.89, "x": 159.87, "y": 0.76}, {"name": "leg9", "parent": "leg8", "length": 108.88, "rotation": 27.5, "x": 149.14, "y": 10}, {"name": "upper_leg_left3", "parent": "root_0.16", "x": -271.74, "y": 249.31, "color": "ff3f00ff"}, {"name": "leg10", "parent": "body", "length": 51.87, "rotation": 86.7, "x": -96.76, "y": 66.85, "scaleX": 1.1, "scaleY": 1.1}, {"name": "leg11", "parent": "leg10", "length": 153.79, "rotation": -26.05, "x": 52.76, "y": 1.03}, {"name": "lower_leg_left3", "parent": "root_0.16", "x": -392.54, "y": -1.48, "color": "ff3f00ff"}, {"name": "leg12", "parent": "leg11", "length": 133.24, "rotation": 79.89, "x": 159.87, "y": 0.76}, {"name": "leg13", "parent": "leg12", "length": 108.88, "rotation": 27.5, "x": 149.14, "y": 10}, {"name": "upper_leg_right3", "parent": "root_0.16", "x": 277.82, "y": 262.58, "color": "ff3f00ff"}, {"name": "leg14", "parent": "body", "length": 51.87, "rotation": 95.27, "x": -96.07, "y": -79.34, "scaleX": -1.1, "scaleY": 1.1}, {"name": "leg15", "parent": "leg14", "length": 153.79, "rotation": -32.3, "x": 52.76, "y": 1.03}, {"name": "lower_leg_right3", "parent": "root_0.16", "x": 390.89, "y": 6.11, "color": "ff3f00ff"}, {"name": "leg16", "parent": "leg15", "length": 133.24, "rotation": 79.89, "x": 159.87, "y": 0.76}, {"name": "leg17", "parent": "leg16", "length": 108.88, "rotation": 27.5, "x": 149.14, "y": 10}, {"name": "leg18", "parent": "body", "rotation": -176.23, "x": -142.24, "y": -148.58, "scaleX": -1}], "slots": [{"name": "leg4", "bone": "leg10", "attachment": "leg2"}, {"name": "leg5", "bone": "leg14", "attachment": "leg2"}, {"name": "leg2", "bone": "leg2", "attachment": "leg2"}, {"name": "leg3", "bone": "leg6", "attachment": "leg2"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "leg1", "bone": "leg1", "attachment": "leg1"}, {"name": "leg6", "bone": "leg18", "attachment": "leg1"}], "ik": [{"name": "lower_leg_left2", "order": 1, "bones": ["leg4", "leg5"], "target": "lower_leg_left2"}, {"name": "lower_leg_left3", "order": 3, "bones": ["leg12", "leg13"], "target": "lower_leg_left3"}, {"name": "lower_leg_right2", "order": 5, "bones": ["leg8", "leg9"], "target": "lower_leg_right2"}, {"name": "lower_leg_right3", "order": 7, "bones": ["leg16", "leg17"], "target": "lower_leg_right3"}, {"name": "upper_leg_left2", "order": 0, "bones": ["leg2", "leg3"], "target": "upper_leg_left2", "bendPositive": false}, {"name": "upper_leg_left3", "order": 2, "bones": ["leg10", "leg11"], "target": "upper_leg_left3", "bendPositive": false}, {"name": "upper_leg_right2", "order": 4, "bones": ["leg6", "leg7"], "target": "upper_leg_right2"}, {"name": "upper_leg_right3", "order": 6, "bones": ["leg14", "leg15"], "target": "upper_leg_right3"}], "skins": {"default": {"body": {"body": {"type": "mesh", "uvs": [0.49783, 1, 0.37479, 1, 0.2393, 0.97607, 0.14898, 0.93521, 0.07266, 0.86712, 0.02283, 0.78736, 0, 0.67453, 0, 0.55781, 0, 0.39829, 0.04307, 0.26016, 0.11471, 0.14538, 0.20037, 0.06173, 0.29693, 0.00921, 0.40594, 0, 0.50406, 0, 0.60529, 0, 0.70496, 0.02088, 0.78906, 0.05979, 0.86381, 0.12204, 0.91832, 0.18624, 0.96971, 0.27962, 1, 0.3691, 1, 0.46248, 1, 0.56753, 1, 0.6648, 0.97283, 0.76207, 0.92455, 0.84962, 0.86381, 0.90992, 0.78438, 0.96439, 0.70184, 0.98968, 0.60529, 1, 0.32839, 0.74699, 0.81766, 0.75307, 0.77799, 0.31441, 0.32858, 0.19422, 0.52082, 0.38608, 0.63396, 0.80101, 0.82077, 0.56465, 0.17397, 0.46055, 0.52844, 0.60134, 0.63682, 0.13733, 0.33622, 0.50775, 0.13382, 0.65738, 0.50047, 0.12005, 0.49478, 0.85822, 0.66202, 0.4666, 0.1661, 0.32772, 0.22385, 0.80631, 0.34844, 0.85689], "triangles": [43, 13, 14, 40, 15, 16, 34, 12, 13, 34, 13, 43, 11, 12, 34, 43, 14, 15, 40, 43, 15, 17, 40, 16, 35, 43, 40, 34, 43, 35, 10, 11, 34, 18, 40, 17, 33, 18, 19, 33, 19, 20, 18, 33, 40, 46, 10, 34, 9, 10, 46, 35, 40, 33, 8, 9, 46, 46, 41, 38, 34, 41, 46, 8, 46, 38, 33, 20, 21, 22, 33, 21, 45, 35, 33, 34, 35, 41, 7, 8, 38, 37, 33, 22, 45, 33, 37, 37, 22, 23, 39, 35, 45, 41, 35, 39, 42, 7, 38, 42, 38, 41, 37, 23, 24, 6, 7, 42, 31, 42, 41, 31, 41, 39, 37, 39, 45, 32, 37, 24, 37, 36, 39, 25, 32, 24, 5, 6, 42, 32, 36, 37, 44, 31, 39, 47, 42, 31, 5, 42, 47, 26, 32, 25, 44, 48, 31, 47, 31, 48, 39, 36, 44, 4, 5, 47, 27, 32, 26, 3, 4, 47, 28, 36, 32, 28, 32, 27, 2, 47, 48, 3, 47, 2, 29, 36, 28, 30, 44, 36, 1, 48, 44, 2, 48, 1, 30, 0, 44, 1, 44, 0, 36, 29, 30], "vertices": [1, 2, -176.6, 0.76, 1, 1, 2, -177.34, 45.79, 1, 2, 2, -171.14, 95.48, 0.9994, 3, -224.99, 106.13, 0.0006, 2, 2, -159.71, 128.74, 0.99521, 3, -211.96, 138.79, 0.00479, 2, 2, -140.22, 156.99, 0.9862, 3, -191.12, 166.06, 0.0138, 2, 2, -117.16, 175.61, 0.97421, 3, -167.17, 183.54, 0.02579, 2, 2, -84.24, 184.51, 0.95118, 3, -133.86, 190.82, 0.04882, 2, 2, -50.04, 185.07, 0.90998, 3, -99.68, 189.72, 0.09002, 2, 2, -3.31, 185.83, 0.82695, 3, -52.96, 188.21, 0.17305, 2, 2, 37.41, 170.73, 0.72819, 3, -13.02, 171.15, 0.27181, 2, 2, 71.47, 145.07, 0.59417, 3, 19.74, 143.86, 0.40583, 2, 2, 96.49, 114.12, 0.43249, 3, 43.23, 111.73, 0.56751, 2, 2, 112.46, 79.04, 0.25544, 3, 57.47, 75.92, 0.74456, 2, 2, 115.81, 39.19, 0.07212, 3, 58.88, 35.95, 0.92788, 2, 2, 116.4, 3.29, 0, 3, 57.73, 0.06, 1, 2, 2, 117, -33.76, 0.08516, 3, 56.53, -36.97, 0.91484, 2, 2, 111.49, -70.34, 0.3236, 3, 49.24, -73.23, 0.6764, 2, 2, 100.59, -101.3, 0.52025, 3, 36.85, -103.63, 0.47975, 2, 2, 82.8, -128.95, 0.67473, 3, 17.74, -130.39, 0.32527, 2, 2, 64.32, -149.21, 0.76995, 3, -1.7, -149.72, 0.23005, 2, 2, 37.27, -168.46, 0.85486, 3, -29.65, -167.64, 0.14514, 2, 2, 11.24, -179.98, 0.90348, 3, -56.22, -177.87, 0.09652, 2, 2, -16.12, -180.43, 0.94026, 3, -83.56, -176.99, 0.05974, 2, 2, -46.89, -180.93, 0.97297, 3, -114.33, -176, 0.02703, 2, 2, -75.39, -181.4, 0.98921, 3, -142.81, -175.08, 0.01079, 2, 2, -104.05, -171.92, 0.99701, 3, -170.98, -164.22, 0.00299, 2, 2, -129.98, -154.67, 0.99981, 3, -196.04, -145.73, 0.00019, 1, 2, -148.02, -132.74, 1, 1, 2, -164.45, -103.93, 1, 1, 2, -172.35, -73.85, 1, 1, 2, -175.96, -38.56, 1, 2, 2, -103.5, 63.98, 0.99612, 3, -158.96, 71.38, 0.00388, 2, 2, -102.34, -115.09, 0.99927, 3, -166.51, -107.54, 0.00073, 2, 2, 25.93, -98.47, 0.83618, 3, -37.58, -97.18, 0.16382, 2, 2, 58.44, 66.57, 0.49539, 3, 2.92, 66.09, 0.50461, 1, 2, 3.39, -4.7, 1, 1, 2, -117.49, -48.1, 1, 2, 2, -47.12, -115.33, 0.98052, 3, -111.37, -110.46, 0.01948, 2, 2, -20.51, 121.87, 0.86938, 3, -73.25, 125.16, 0.13062, 1, 2, -59.63, -8.52, 1, 2, 2, 76.96, -45.96, 0.35832, 3, 15.94, -47.21, 0.64168, 2, 2, -33.36, 62.27, 0.95909, 3, -88.99, 66.25, 0.04091, 2, 2, -78.41, 135.62, 0.9571, 3, -130.42, 141.71, 0.0429, 2, 2, 81.21, 4.02, 0.0045, 3, 22.61, 2.51, 0.9955, 1, 2, -135.08, 2.56, 1, 2, 2, -19.35, -56.76, 0.98618, 3, -80.78, -53.32, 0.01382, 2, 2, 18.36, 125.39, 0.748, 3, -34.26, 126.78, 0.252, 2, 2, -121.5, 101.95, 0.99288, 3, -175.09, 110.18, 0.00712, 2, 2, -135.57, 56.12, 0.99974, 3, -191.37, 65.08, 0.00026], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 0], "width": 366, "height": 293}}, "head": {"head": {"type": "mesh", "uvs": [0.37885, 0, 0.30496, 0, 0.18202, 0.03124, 0.03795, 0.17254, 0, 0.4089, 0, 0.69664, 0.05331, 0.83537, 0.1801, 0.96383, 0.31264, 1, 0.49025, 1, 0.69684, 1, 0.8486, 0.94841, 0.96578, 0.82253, 1, 0.70178, 1, 0.3755, 0.9043, 0.13144, 0.8217, 0.03638, 0.68339, 0, 0.63567, 0, 0.48937, 0, 0.4887, 0.08634, 0.49489, 0.26014, 0.49953, 0.48567, 0.49643, 0.68638, 0.49489, 0.8457, 0.31078, 0.0822, 0.24116, 0.2498, 0.18701, 0.47533, 0.22259, 0.64706, 0.24735, 0.74017, 0.68828, 0.08634, 0.77182, 0.23531, 0.8337, 0.48361, 0.8074, 0.64086, 0.78574, 0.7319], "triangles": [25, 1, 0, 2, 1, 25, 20, 0, 19, 30, 17, 16, 18, 17, 30, 31, 30, 16, 31, 16, 15, 26, 2, 25, 3, 2, 26, 25, 0, 20, 20, 18, 30, 18, 20, 19, 21, 30, 31, 21, 20, 30, 25, 20, 21, 26, 25, 21, 31, 15, 14, 4, 3, 26, 27, 4, 26, 32, 31, 14, 22, 21, 31, 22, 31, 32, 26, 21, 22, 27, 26, 22, 33, 22, 32, 28, 27, 22, 23, 28, 22, 23, 22, 33, 5, 4, 27, 5, 27, 28, 32, 14, 13, 33, 32, 13, 34, 23, 33, 34, 33, 13, 29, 28, 23, 12, 34, 13, 29, 6, 5, 29, 5, 28, 24, 29, 23, 24, 23, 34, 11, 34, 12, 10, 24, 34, 7, 6, 29, 8, 7, 29, 24, 8, 29, 9, 8, 24, 10, 9, 24, 34, 11, 10], "vertices": [-167.62, -49.39, -166.65, -73.68, -157.35, -113.79, -120.72, -159.76, -62.13, -169.91, 8.6, -167.09, 42, -148.2, 71.91, -105.25, 79.06, -61.32, 76.46, 3.67, 74, 64.97, 59.33, 114.36, 26.84, 151.64, -3.29, 161.7, -83.49, 158.49, -142.22, 124.64, -164.5, 96.55, -171.62, 50.72, -171, 35.03, -169.07, -13.06, -147.88, -11.42, -105.24, -7.67, -49.87, -3.93, -0.55, -1.45, 38.51, 2.66, -146.53, -70.69, -104.42, -91.84, -48.27, -107.4, -6.55, -93.59, 16.02, -84.72, -150.47, 53.32, -114.96, 82.37, -54.74, 105.23, -15.76, 98.48, 6.91, 92.07], "hull": 20, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 16, 18, 18, 20, 48, 18, 2, 0, 0, 38, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 16, 34, 36, 36, 38, 36, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 20, 56, 46, 46, 66, 54, 44, 44, 64, 52, 42, 42, 62], "width": 329, "height": 246}}, "leg1": {"leg1": {"x": -33.27, "y": -57.62, "rotation": -89.06, "width": 123, "height": 119}}, "leg2": {"leg2": {"type": "mesh", "uvs": [0, 1, 0, 0.74457, 0.0406, 0.50213, 0.06339, 0.45025, 0.08188, 0.40815, 0.17823, 0.18885, 0.28869, 0.05264, 0.39735, 0, 0.4511, 0, 0.53268, 0, 0.60379, 0, 0.72331, 0.21609, 0.82653, 0.48033, 0.86656, 0.49857, 0.88937, 0.50895, 1, 0.55933, 1, 0.73913, 0.83707, 0.77969, 0.79212, 0.79088, 0.7605, 0.75274, 0.60016, 0.55933, 0.48736, 0.36419, 0.47037, 0.34104, 0.44472, 0.34701, 0.37924, 0.50213, 0.27039, 0.63555, 0.25826, 0.65041, 0.23255, 0.68192, 0.13114, 0.87533, 0.05509, 1], "triangles": [26, 2, 3, 29, 0, 28, 0, 1, 28, 28, 1, 27, 1, 2, 27, 27, 2, 26, 10, 22, 9, 22, 8, 9, 8, 22, 23, 25, 4, 24, 24, 4, 23, 4, 5, 23, 5, 6, 23, 6, 7, 23, 8, 23, 7, 3, 4, 25, 25, 26, 3, 18, 13, 17, 18, 19, 13, 19, 12, 13, 19, 20, 12, 12, 20, 11, 20, 21, 11, 10, 11, 21, 10, 21, 22, 14, 17, 13, 16, 17, 14, 14, 15, 16], "vertices": [1, 11, 119.14, -1.79, 1, 1, 11, 67, -29.95, 1, 2, 10, 179.11, -24.24, 0.27, 11, 10.77, -44.21, 0.73, 2, 10, 165.77, -29.74, 0.59, 11, -3.6, -42.93, 0.41, 2, 10, 154.95, -34.2, 0.84, 11, -15.26, -41.89, 0.16, 1, 10, 98.56, -57.44, 1, 1, 10, 48.92, -61.97, 1, 2, 8, 211.75, 2.36, 0.05, 10, 10.68, -50.8, 0.95, 2, 8, 198.66, -11.08, 0.1, 10, -4.85, -40.27, 0.9, 2, 8, 187.75, -22.29, 0.37, 10, -17.79, -31.49, 0.63, 2, 8, 161.49, -49.26, 0.705, 10, -48.95, -10.37, 0.295, 1, 8, 96.47, -44.16, 1, 1, 8, 27.42, -27.19, 1, 2, 7, 41.01, -34.32, 0.36, 8, 14.64, -34.25, 0.64, 2, 7, 32.9, -32.51, 0.91366, 8, 7.36, -38.27, 0.08634, 1, 7, -6.47, -23.7, 1, 2, 7, -9.55, 17.89, 0.99802, 8, -57.84, -28.68, 0.00198, 2, 7, 46.46, 31.48, 0.84, 8, -24.9, 18.63, 0.16, 2, 7, 61.91, 35.23, 0.19, 8, -15.82, 31.68, 0.81, 1, 8, -1.78, 33.41, 1, 2, 8, 69.41, 42.19, 0.99865, 10, 24.91, 96.33, 0.00135, 2, 8, 131.47, 44.24, 0.76466, 10, 37.82, 35.59, 0.23534, 2, 8, 136.86, 44.42, 0.39, 10, 38.95, 30.31, 0.61, 3, 8, 136.27, 50.7, 0.16, 10, 45.02, 32, 0.82945, 11, -82.2, 67.59, 0.01055, 2, 10, 81.28, 42.08, 0.9578, 11, -45.38, 59.79, 0.0422, 2, 10, 130.1, 46.38, 0.82, 11, -0.1, 41.07, 0.18, 2, 10, 135.53, 46.86, 0.53, 11, 4.95, 38.98, 0.47, 2, 10, 147.06, 47.88, 0.28, 11, 15.64, 34.56, 0.72, 1, 11, 71.94, 24.74, 1, 1, 11, 110.01, 15.13, 1], "hull": 30, "edges": [0, 2, 2, 4, 10, 12, 12, 14, 20, 22, 22, 24, 30, 32, 54, 56, 56, 58, 0, 58, 24, 26, 26, 36, 36, 38, 38, 40, 24, 38, 26, 28, 28, 30, 32, 34, 34, 36, 28, 34, 18, 20, 44, 18, 40, 42, 42, 44, 42, 20, 44, 46, 46, 48, 14, 16, 16, 18, 46, 16, 52, 54, 4, 6, 52, 6, 48, 50, 50, 52, 6, 8, 8, 10, 50, 8, 54, 4], "width": 349, "height": 232}}, "leg3": {"leg2": {"type": "mesh", "uvs": [0, 1, 0, 0.74457, 0.0406, 0.50213, 0.06339, 0.45025, 0.08188, 0.40815, 0.17823, 0.18885, 0.28869, 0.05264, 0.39735, 0, 0.4511, 0, 0.53268, 0, 0.60379, 0, 0.72331, 0.21609, 0.82653, 0.48033, 0.86656, 0.49857, 0.88937, 0.50895, 1, 0.55933, 1, 0.73913, 0.83707, 0.77969, 0.79212, 0.79088, 0.7605, 0.75274, 0.60016, 0.55933, 0.48736, 0.36419, 0.47037, 0.34104, 0.44472, 0.34701, 0.37924, 0.50213, 0.27039, 0.63555, 0.25826, 0.65041, 0.23255, 0.68192, 0.13114, 0.87533, 0.05509, 1], "triangles": [26, 2, 3, 29, 0, 28, 0, 1, 28, 28, 1, 27, 1, 2, 27, 27, 2, 26, 10, 22, 9, 22, 8, 9, 8, 22, 23, 25, 4, 24, 24, 4, 23, 4, 5, 23, 5, 6, 23, 6, 7, 23, 8, 23, 7, 3, 4, 25, 25, 26, 3, 18, 13, 17, 18, 19, 13, 19, 12, 13, 19, 20, 12, 12, 20, 11, 20, 21, 11, 10, 11, 21, 10, 21, 22, 14, 17, 13, 16, 17, 14, 14, 15, 16], "vertices": [1, 17, 119.14, -1.79, 1, 1, 17, 67, -29.95, 1, 2, 16, 179.11, -24.24, 0.27, 17, 10.77, -44.21, 0.73, 2, 16, 165.77, -29.74, 0.59, 17, -3.6, -42.93, 0.41, 2, 16, 154.95, -34.2, 0.84, 17, -15.26, -41.89, 0.16, 1, 16, 98.56, -57.44, 1, 1, 16, 48.92, -61.97, 1, 2, 14, 211.75, 2.36, 0.05, 16, 10.68, -50.8, 0.95, 2, 14, 198.66, -11.08, 0.1, 16, -4.85, -40.27, 0.9, 2, 14, 187.75, -22.29, 0.37, 16, -17.79, -31.49, 0.63, 2, 14, 161.49, -49.26, 0.705, 16, -48.95, -10.37, 0.295, 1, 14, 96.47, -44.16, 1, 1, 14, 27.42, -27.19, 1, 2, 13, 41.01, -34.32, 0.36, 14, 14.64, -34.25, 0.64, 2, 13, 32.9, -32.51, 0.91366, 14, 7.36, -38.27, 0.08634, 1, 13, -6.47, -23.7, 1, 2, 13, -9.55, 17.89, 0.99802, 14, -57.84, -28.68, 0.00198, 2, 13, 46.46, 31.48, 0.84, 14, -24.9, 18.63, 0.16, 2, 13, 61.91, 35.23, 0.19, 14, -15.82, 31.68, 0.81, 1, 14, -1.78, 33.41, 1, 2, 14, 69.41, 42.19, 0.99865, 16, 24.91, 96.33, 0.00135, 2, 14, 131.47, 44.24, 0.76466, 16, 37.82, 35.59, 0.23534, 2, 14, 136.86, 44.42, 0.39, 16, 38.95, 30.31, 0.61, 3, 14, 136.27, 50.7, 0.16, 16, 45.02, 32, 0.82945, 17, -82.2, 67.59, 0.01055, 2, 16, 81.28, 42.08, 0.9578, 17, -45.38, 59.79, 0.0422, 2, 16, 130.1, 46.38, 0.82, 17, -0.1, 41.07, 0.18, 2, 16, 135.53, 46.86, 0.53, 17, 4.95, 38.98, 0.47, 2, 16, 147.06, 47.88, 0.28, 17, 15.64, 34.56, 0.72, 1, 17, 71.94, 24.74, 1, 1, 17, 110.01, 15.13, 1], "hull": 30, "edges": [0, 2, 2, 4, 10, 12, 12, 14, 20, 22, 22, 24, 30, 32, 54, 56, 56, 58, 0, 58, 24, 26, 26, 36, 36, 38, 38, 40, 24, 38, 26, 28, 28, 30, 32, 34, 34, 36, 28, 34, 18, 20, 44, 18, 40, 42, 42, 44, 42, 20, 44, 46, 46, 48, 14, 16, 16, 18, 46, 16, 52, 54, 4, 6, 52, 6, 48, 50, 50, 52, 6, 8, 8, 10, 50, 8, 54, 4], "width": 349, "height": 232}}, "leg4": {"leg2": {"type": "mesh", "color": "e0b2ffff", "uvs": [0, 1, 0, 0.74457, 0.0406, 0.50213, 0.06339, 0.45025, 0.08188, 0.40815, 0.17823, 0.18885, 0.28869, 0.05264, 0.39735, 0, 0.4511, 0, 0.53268, 0, 0.60379, 0, 0.72331, 0.21609, 0.82653, 0.48033, 0.86656, 0.49857, 0.88937, 0.50895, 1, 0.55933, 1, 0.73913, 0.83707, 0.77969, 0.79212, 0.79088, 0.7605, 0.75274, 0.60016, 0.55933, 0.48736, 0.36419, 0.47037, 0.34104, 0.44472, 0.34701, 0.37924, 0.50213, 0.27039, 0.63555, 0.25826, 0.65041, 0.23255, 0.68192, 0.13114, 0.87533, 0.05509, 1], "triangles": [26, 2, 3, 29, 0, 28, 0, 1, 28, 28, 1, 27, 1, 2, 27, 27, 2, 26, 10, 22, 9, 22, 8, 9, 8, 22, 23, 25, 4, 24, 24, 4, 23, 4, 5, 23, 5, 6, 23, 6, 7, 23, 8, 23, 7, 3, 4, 25, 25, 26, 3, 18, 13, 17, 18, 19, 13, 19, 12, 13, 19, 20, 12, 12, 20, 11, 20, 21, 11, 10, 11, 21, 10, 21, 22, 14, 17, 13, 16, 17, 14, 14, 15, 16], "vertices": [1, 23, 119.14, -1.79, 1, 1, 23, 67, -29.95, 1, 2, 22, 179.11, -24.24, 0.27, 23, 10.77, -44.21, 0.73, 2, 22, 165.77, -29.74, 0.59, 23, -3.6, -42.93, 0.41, 2, 22, 154.95, -34.2, 0.84, 23, -15.26, -41.89, 0.16, 1, 22, 98.56, -57.44, 1, 1, 22, 48.92, -61.97, 1, 2, 20, 211.75, 2.36, 0.05, 22, 10.68, -50.8, 0.95, 2, 20, 198.66, -11.08, 0.1, 22, -4.85, -40.27, 0.9, 2, 20, 187.75, -22.29, 0.37, 22, -17.79, -31.49, 0.63, 2, 20, 161.49, -49.26, 0.705, 22, -48.95, -10.37, 0.295, 1, 20, 96.47, -44.16, 1, 1, 20, 27.42, -27.19, 1, 2, 19, 41.01, -34.32, 0.36, 20, 14.64, -34.25, 0.64, 2, 19, 32.9, -32.51, 0.91366, 20, 7.36, -38.27, 0.08634, 1, 19, -6.47, -23.7, 1, 2, 19, -9.55, 17.89, 0.99802, 20, -57.84, -28.68, 0.00198, 2, 19, 46.46, 31.48, 0.84, 20, -24.9, 18.63, 0.16, 2, 19, 61.91, 35.23, 0.19, 20, -15.82, 31.68, 0.81, 1, 20, -1.78, 33.41, 1, 2, 20, 69.41, 42.19, 0.99865, 22, 24.91, 96.33, 0.00135, 2, 20, 131.47, 44.24, 0.76466, 22, 37.82, 35.59, 0.23534, 2, 20, 136.86, 44.42, 0.39, 22, 38.95, 30.31, 0.61, 3, 20, 136.27, 50.7, 0.16, 22, 45.02, 32, 0.82945, 23, -82.2, 67.59, 0.01055, 2, 22, 81.28, 42.08, 0.9578, 23, -45.38, 59.79, 0.0422, 2, 22, 130.1, 46.38, 0.82, 23, -0.1, 41.07, 0.18, 2, 22, 135.53, 46.86, 0.53, 23, 4.95, 38.98, 0.47, 2, 22, 147.06, 47.88, 0.28, 23, 15.64, 34.56, 0.72, 1, 23, 71.94, 24.74, 1, 1, 23, 110.01, 15.13, 1], "hull": 30, "edges": [0, 2, 2, 4, 10, 12, 12, 14, 20, 22, 22, 24, 30, 32, 54, 56, 56, 58, 0, 58, 24, 26, 26, 36, 36, 38, 38, 40, 24, 38, 26, 28, 28, 30, 32, 34, 34, 36, 28, 34, 18, 20, 44, 18, 40, 42, 42, 44, 42, 20, 44, 46, 46, 48, 14, 16, 16, 18, 46, 16, 52, 54, 4, 6, 52, 6, 48, 50, 50, 52, 6, 8, 8, 10, 50, 8, 54, 4], "width": 349, "height": 232}}, "leg5": {"leg2": {"type": "mesh", "color": "e0b2ffff", "uvs": [0, 1, 0, 0.74457, 0.0406, 0.50213, 0.06339, 0.45025, 0.08188, 0.40815, 0.17823, 0.18885, 0.28869, 0.05264, 0.39735, 0, 0.4511, 0, 0.53268, 0, 0.60379, 0, 0.72331, 0.21609, 0.82653, 0.48033, 0.86656, 0.49857, 0.88937, 0.50895, 1, 0.55933, 1, 0.73913, 0.83707, 0.77969, 0.79212, 0.79088, 0.7605, 0.75274, 0.60016, 0.55933, 0.48736, 0.36419, 0.47037, 0.34104, 0.44472, 0.34701, 0.37924, 0.50213, 0.27039, 0.63555, 0.25826, 0.65041, 0.23255, 0.68192, 0.13114, 0.87533, 0.05509, 1], "triangles": [26, 2, 3, 29, 0, 28, 0, 1, 28, 28, 1, 27, 1, 2, 27, 27, 2, 26, 10, 22, 9, 22, 8, 9, 8, 22, 23, 25, 4, 24, 24, 4, 23, 4, 5, 23, 5, 6, 23, 6, 7, 23, 8, 23, 7, 3, 4, 25, 25, 26, 3, 18, 13, 17, 18, 19, 13, 19, 12, 13, 19, 20, 12, 12, 20, 11, 20, 21, 11, 10, 11, 21, 10, 21, 22, 14, 17, 13, 16, 17, 14, 14, 15, 16], "vertices": [1, 29, 119.14, -1.79, 1, 1, 29, 67, -29.95, 1, 2, 28, 179.11, -24.24, 0.27, 29, 10.77, -44.21, 0.73, 2, 28, 165.77, -29.74, 0.59, 29, -3.6, -42.93, 0.41, 2, 28, 154.95, -34.2, 0.84, 29, -15.26, -41.89, 0.16, 1, 28, 98.56, -57.44, 1, 1, 28, 48.92, -61.97, 1, 2, 26, 211.75, 2.36, 0.05, 28, 10.68, -50.8, 0.95, 2, 26, 198.66, -11.08, 0.1, 28, -4.85, -40.27, 0.9, 2, 26, 187.75, -22.29, 0.37, 28, -17.79, -31.49, 0.63, 2, 26, 161.49, -49.26, 0.705, 28, -48.95, -10.37, 0.295, 1, 26, 96.47, -44.16, 1, 1, 26, 27.42, -27.19, 1, 2, 25, 41.01, -34.32, 0.36, 26, 14.64, -34.25, 0.64, 2, 25, 32.9, -32.51, 0.91366, 26, 7.36, -38.27, 0.08634, 1, 25, -6.47, -23.7, 1, 2, 25, -9.55, 17.89, 0.99802, 26, -57.84, -28.68, 0.00198, 2, 25, 46.46, 31.48, 0.84, 26, -24.9, 18.63, 0.16, 2, 25, 61.91, 35.23, 0.19, 26, -15.82, 31.68, 0.81, 1, 26, -1.78, 33.41, 1, 2, 26, 69.41, 42.19, 0.99865, 28, 24.91, 96.33, 0.00135, 2, 26, 131.47, 44.24, 0.76466, 28, 37.82, 35.59, 0.23534, 2, 26, 136.86, 44.42, 0.39, 28, 38.95, 30.31, 0.61, 3, 26, 136.27, 50.7, 0.16, 28, 45.02, 32, 0.82945, 29, -82.2, 67.59, 0.01055, 2, 28, 81.28, 42.08, 0.9578, 29, -45.38, 59.79, 0.0422, 2, 28, 130.1, 46.38, 0.82, 29, -0.1, 41.07, 0.18, 2, 28, 135.53, 46.86, 0.53, 29, 4.95, 38.98, 0.47, 2, 28, 147.06, 47.88, 0.28, 29, 15.64, 34.56, 0.72, 1, 29, 71.94, 24.74, 1, 1, 29, 110.01, 15.13, 1], "hull": 30, "edges": [0, 2, 2, 4, 10, 12, 12, 14, 20, 22, 22, 24, 30, 32, 54, 56, 56, 58, 0, 58, 24, 26, 26, 36, 36, 38, 38, 40, 24, 38, 26, 28, 28, 30, 32, 34, 34, 36, 28, 34, 18, 20, 44, 18, 40, 42, 42, 44, 42, 20, 44, 46, 46, 48, 14, 16, 16, 18, 46, 16, 52, 54, 4, 6, 52, 6, 48, 50, 50, 52, 6, 8, 8, 10, 50, 8, 54, 4], "width": 349, "height": 232}}, "leg6": {"leg1": {"x": -33.27, "y": -57.62, "rotation": -89.06, "width": 123, "height": 119}}}}, "animations": {"idle": {"slots": {"body": {"attachment": [{"time": 0, "name": "body"}, {"time": 1, "name": "body"}]}, "head": {"attachment": [{"time": 0, "name": "head"}, {"time": 1, "name": "head"}]}, "leg1": {"attachment": [{"time": 0, "name": "leg1"}, {"time": 1, "name": "leg1"}]}, "leg2": {"attachment": [{"time": 0, "name": "leg2"}, {"time": 1, "name": "leg2"}]}, "leg3": {"attachment": [{"time": 0, "name": "leg2"}, {"time": 1, "name": "leg2"}]}, "leg4": {"attachment": [{"time": 0, "name": "leg2"}, {"time": 1, "name": "leg2"}]}, "leg5": {"attachment": [{"time": 0, "name": "leg2"}, {"time": 1, "name": "leg2"}]}, "leg6": {"attachment": [{"time": 0, "name": "leg1"}, {"time": 1, "name": "leg1"}]}}, "bones": {"root_0.16": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "lower_leg_left3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 86.38, "y": 0, "curve": "stepped"}, {"time": 1, "x": 86.38, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "spider": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -35.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 17.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -35.14}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "body": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "x": 1.024, "y": 1, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "head": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 38.06, "y": 0.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "x": 1.167, "y": 1, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "upper_leg_left2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 48.31, "y": -7.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 48.31, "y": 8.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 48.31, "y": -7.32}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg2": {"rotate": [{"time": 0, "angle": 23.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -3.38}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg3": {"rotate": [{"time": 0, "angle": -47.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.27}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "lower_leg_left2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 122.98, "y": 0, "curve": "stepped"}, {"time": 1, "x": 122.98, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg4": {"rotate": [{"time": 0, "angle": 29.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -1.26}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg5": {"rotate": [{"time": 0, "angle": 35.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 6.02}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "upper_leg_right2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": -57.1, "y": -13.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -57.1, "y": 13.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -57.1, "y": -13.18}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg6": {"rotate": [{"time": 0, "angle": -25.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 6.52}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg7": {"rotate": [{"time": 0, "angle": -49.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 9.32}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "lower_leg_right2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": -128.83, "y": 0, "curve": "stepped"}, {"time": 1, "x": -128.83, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg8": {"rotate": [{"time": 0, "angle": 35.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.97}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg9": {"rotate": [{"time": 0, "angle": 39.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.65}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "upper_leg_left3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 35.14, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 35.14, "y": 11.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 35.14, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg10": {"rotate": [{"time": 0, "angle": 5.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.29}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg11": {"rotate": [{"time": 0, "angle": -22.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 7.53}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg12": {"rotate": [{"time": 0, "angle": 21.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -0.31}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg13": {"rotate": [{"time": 0, "angle": 21.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0.84}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "upper_leg_right3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": -45.38, "y": -10.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -45.38, "y": 8.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -45.38, "y": -10.25}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg14": {"rotate": [{"time": 0, "angle": -15.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.29}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg15": {"rotate": [{"time": 0, "angle": -34.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.17}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "lower_leg_right3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": -86.38, "y": 0, "curve": "stepped"}, {"time": 1, "x": -86.38, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg16": {"rotate": [{"time": 0, "angle": 27.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 11.09}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg17": {"rotate": [{"time": 0, "angle": 24.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.79}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "leg18": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "body2": {"rotate": [{"time": 0, "angle": -0.84}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "ik": {"lower_leg_left2": [{"time": 0, "curve": "stepped"}, {"time": 1}], "lower_leg_left3": [{"time": 0, "curve": "stepped"}, {"time": 1}], "lower_leg_right2": [{"time": 0, "curve": "stepped"}, {"time": 1}], "lower_leg_right3": [{"time": 0, "curve": "stepped"}, {"time": 1}], "upper_leg_left2": [{"time": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1, "bendPositive": false}], "upper_leg_left3": [{"time": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1, "bendPositive": false}], "upper_leg_right2": [{"time": 0, "curve": "stepped"}, {"time": 1}], "upper_leg_right3": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "deform": {"default": {"body": {"body": [{"time": 0}]}, "head": {"head": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "leg2": {"leg2": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "leg3": {"leg2": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "leg4": {"leg2": [{"time": 0, "curve": "stepped"}, {"time": 1}]}, "leg5": {"leg2": [{"time": 0, "curve": "stepped"}, {"time": 1}]}}}, "drawOrder": [{"time": 0}, {"time": 1}]}, "move": {"slots": {"body": {"attachment": [{"time": 0, "name": "body"}]}, "head": {"attachment": [{"time": 0, "name": "head"}]}, "leg1": {"attachment": [{"time": 0, "name": "leg1"}]}, "leg2": {"attachment": [{"time": 0, "name": "leg2"}]}, "leg3": {"attachment": [{"time": 0, "name": "leg2"}]}, "leg4": {"attachment": [{"time": 0, "name": "leg2"}]}, "leg5": {"attachment": [{"time": 0, "name": "leg2"}]}, "leg6": {"attachment": [{"time": 0, "name": "leg1"}]}}, "bones": {"root_0.16": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg18": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 354.39}, {"time": 0.6667, "angle": 0}, {"time": 1, "angle": 354.39}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "spider": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": -19.43, "y": 16.17}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1, "x": -19.43, "y": 16.17}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "body": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "head": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 23.28, "y": 9.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 23.28, "y": 9.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.973, "y": 1.089, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.973, "y": 1.089, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -8.4}, {"time": 0.6667, "angle": 0}, {"time": 1, "angle": -8.4}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg2": {"rotate": [{"time": 0, "angle": -48.79}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg3": {"rotate": [{"time": 0, "angle": 27.1}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg4": {"rotate": [{"time": 0, "angle": 3.98}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg5": {"rotate": [{"time": 0, "angle": 9.08}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg6": {"rotate": [{"time": 0, "angle": -23.25}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg7": {"rotate": [{"time": 0, "angle": -20.06}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg8": {"rotate": [{"time": 0, "angle": -17.87}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg9": {"rotate": [{"time": 0, "angle": 15.97}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg10": {"rotate": [{"time": 0, "angle": 83.06}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg11": {"rotate": [{"time": 0, "angle": -100.09}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg12": {"rotate": [{"time": 0, "angle": 60.8}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg13": {"rotate": [{"time": 0, "angle": 24.29}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg14": {"rotate": [{"time": 0, "angle": 47.2}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg15": {"rotate": [{"time": 0, "angle": 33.42}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg16": {"rotate": [{"time": 0, "angle": 41}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "leg17": {"rotate": [{"time": 0, "angle": 12.18}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "upper_leg_left2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 38.86, "y": 111.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 5.55, "y": 6.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 98.55, "y": 24.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 38.86, "y": 111.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 5.55, "y": 6.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 98.55, "y": 24.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 38.86, "y": 111.04}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "lower_leg_left2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 126.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -38.86, "y": 30.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 253.31, "y": 27.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 351.16, "y": 66.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 126.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -38.86, "y": 30.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 253.31, "y": 27.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 351.16, "y": 66.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 126.31}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "upper_leg_left3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 81.89, "y": -20.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 72.18, "y": 4.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 59.68, "y": 29.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 9.72, "y": -27.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 81.89, "y": -20.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 72.18, "y": 4.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 59.68, "y": 29.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 9.72, "y": -27.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 81.89, "y": -20.82}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "lower_leg_left3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 316.46, "y": 2.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 352.55, "y": 43.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -27.76, "y": 98.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -34.7, "y": 2.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 316.46, "y": 2.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 352.55, "y": 43.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -27.76, "y": 98.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": -34.7, "y": 2.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 316.46, "y": 2.78}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "upper_leg_right2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -2.78, "y": -27.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -61.07, "y": 63.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -48.58, "y": 77.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -48.58, "y": 8.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -2.78, "y": -27.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -61.07, "y": 63.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -48.58, "y": 77.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": -48.58, "y": 8.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -2.78, "y": -27.76}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "lower_leg_right2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 62.46, "y": 16.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0, "y": 115.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -174.89, "y": 77.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -251.23, "y": 13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 62.46, "y": 16.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 115.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -174.89, "y": 77.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": -251.23, "y": 13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 62.46, "y": 16.66}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "upper_leg_right3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -47.19, "y": 77.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -59.68, "y": -18.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -30.54, "y": -27.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -37.48, "y": 11.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -47.19, "y": 77.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -59.68, "y": -18.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -30.54, "y": -27.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": -37.48, "y": 11.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -47.19, "y": 77.73}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "lower_leg_right3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -156.84, "y": 62.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -290.09, "y": -9.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -4.16, "y": -12.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -2.78, "y": 37.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -156.84, "y": 62.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -290.09, "y": -9.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -4.16, "y": -12.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": -2.78, "y": 37.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -156.84, "y": 62.46}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "body2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -11.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 6.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -7.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "ik": {"lower_leg_left2": [{"time": 0}], "lower_leg_left3": [{"time": 0}], "lower_leg_right2": [{"time": 0}], "lower_leg_right3": [{"time": 0}], "upper_leg_left2": [{"time": 0, "bendPositive": false}], "upper_leg_left3": [{"time": 0, "bendPositive": false}], "upper_leg_right2": [{"time": 0}], "upper_leg_right3": [{"time": 0}]}, "deform": {"default": {"body": {"body": [{"time": 0}]}, "head": {"head": [{"time": 0}, {"time": 0.5, "offset": 40, "vertices": [0.50536, -12.63705, 0.61598, -15.40082, 0.75941, -18.98474, 0.63239, -15.79639, 0.53053, -13.26228, 0.45625, -11.39895, 0.492, -12.30378, 0.492, -12.30228, 0.50244, -12.55478, 0.4901, -12.25254, 0.46034, -11.51016, 0.48018, -12.00486, 0.47713, -11.92906, 0.48056, -12.01479, 0.47154, -11.78241], "curve": "stepped"}, {"time": 0.8333, "offset": 40, "vertices": [0.50536, -12.63705, 0.61598, -15.40082, 0.75941, -18.98474, 0.63239, -15.79639, 0.53053, -13.26228, 0.45625, -11.39895, 0.492, -12.30378, 0.492, -12.30228, 0.50244, -12.55478, 0.4901, -12.25254, 0.46034, -11.51016, 0.48018, -12.00486, 0.47713, -11.92906, 0.48056, -12.01479, 0.47154, -11.78241]}, {"time": 1.3333}]}, "leg2": {"leg2": [{"time": 0}]}, "leg3": {"leg2": [{"time": 0}]}, "leg4": {"leg2": [{"time": 0}]}, "leg5": {"leg2": [{"time": 0}]}}}, "drawOrder": [{"time": 0}]}}}