{"skeleton": {"hash": "uAW3v/GdL3WUQDCjR9nt9k+q6Cg", "spine": "3.5.51", "width": 0, "height": 0, "images": ""}, "bones": [{"name": "ghost", "length": 42.77, "rotation": 2.25, "x": 1.44, "y": 0.03, "scaleX": -0.8, "scaleY": 0.8}], "slots": [{"name": "ghost", "bone": "ghost"}], "skins": {"default": {}, "blue": {"ghost": {"ghost/red_ghost_1": {"name": "ghost/blue_ghost_1", "x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}, "ghost/red_ghost_2": {"name": "ghost/blue_ghost_2", "x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}, "ghost/red_ghost_3": {"name": "ghost/blue_ghost_3", "x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}, "ghost/red_ghost_4": {"name": "ghost/blue_ghost_4", "x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}, "ghost/red_ghost_5": {"name": "ghost/blue_ghost_5", "x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}}}, "red": {"ghost": {"ghost/ghost_haunting_0": {"width": 218, "height": 230}, "ghost/ghost_haunting_1": {"width": 218, "height": 230}, "ghost/ghost_haunting_2": {"width": 218, "height": 230}, "ghost/ghost_haunting_3": {"width": 218, "height": 230}, "ghost/ghost_haunting_4": {"width": 218, "height": 230}, "ghost/ghost_haunting_5": {"width": 218, "height": 230}, "ghost/ghost_haunting_6": {"width": 218, "height": 230}, "ghost/ghost_haunting_7": {"width": 218, "height": 230}, "ghost/red_ghost_1": {"x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}, "ghost/red_ghost_2": {"x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}, "ghost/red_ghost_3": {"x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}, "ghost/red_ghost_4": {"x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}, "ghost/red_ghost_5": {"x": 19.5, "y": 16.98, "rotation": -2.25, "width": 155, "height": 83}}}}, "animations": {"fly": {"slots": {"ghost": {"attachment": [{"time": 0, "name": "ghost/red_ghost_1"}, {"time": 0.1333, "name": "ghost/red_ghost_2"}, {"time": 0.2667, "name": "ghost/red_ghost_3"}, {"time": 0.4, "name": "ghost/red_ghost_4"}, {"time": 0.5333, "name": "ghost/red_ghost_5"}, {"time": 0.6667, "name": "ghost/red_ghost_1"}]}}, "bones": {"ghost": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "drawOrder": [{"time": 0}]}, "startle": {"slots": {"ghost": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "ghost/red_ghost_1"}, {"time": 0.1, "name": "ghost/red_ghost_2"}, {"time": 0.2, "name": "ghost/red_ghost_3"}, {"time": 0.3, "name": "ghost/red_ghost_4"}, {"time": 0.4, "name": "ghost/red_ghost_5"}, {"time": 0.5, "name": "ghost/red_ghost_1"}, {"time": 0.6, "name": "ghost/red_ghost_2"}, {"time": 0.7, "name": "ghost/red_ghost_3"}, {"time": 0.8, "name": "ghost/red_ghost_4"}, {"time": 0.9, "name": "ghost/red_ghost_5"}, {"time": 1, "name": "ghost/red_ghost_1"}, {"time": 1.1, "name": "ghost/ghost_haunting_0"}, {"time": 1.2, "name": "ghost/ghost_haunting_1"}, {"time": 1.3, "name": "ghost/ghost_haunting_2"}, {"time": 1.4, "name": "ghost/ghost_haunting_3"}, {"time": 1.5, "name": "ghost/ghost_haunting_5"}, {"time": 1.6, "name": "ghost/ghost_haunting_6"}, {"time": 1.7, "name": "ghost/ghost_haunting_7"}, {"time": 2, "name": null}]}}, "bones": {"ghost": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 2.13}, {"time": 1, "angle": 16.02}], "translate": [{"time": 0, "x": -151.24, "y": 198.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -151.24, "y": 195.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -151.24, "y": 175.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -152, "y": 191.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -150.48, "y": 170, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -151.24, "y": 192.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -39.07, "y": 192.64}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "drawOrder": [{"time": 0}, {"time": 1}, {"time": 1.6667}, {"time": 2}]}}}