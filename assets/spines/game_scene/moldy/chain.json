{"animations": {"break": {"bones": {"bone": {"rotate": [{"angle": 22.29, "time": 0}, {"angle": 78.99, "curve": "stepped", "time": 0.6667}, {"angle": 78.99, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"angle": -0.65, "time": 0}, {"angle": 9.67, "curve": "stepped", "time": 0.6667}, {"angle": 9.67, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"angle": -2.67, "time": 0}, {"angle": 0.13, "curve": "stepped", "time": 0.6667}, {"angle": 0.13, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"angle": -0.13, "time": 0}, {"angle": 64.01, "curve": "stepped", "time": 0.6667}, {"angle": 64.01, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"angle": -44.79, "time": 0}, {"angle": -161.54, "curve": "stepped", "time": 0.6667}, {"angle": -161.54, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"angle": 2.74, "time": 0}, {"angle": 73.12, "curve": "stepped", "time": 0.6667}, {"angle": 73.12, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"angle": 2.53, "time": 0}, {"angle": 104.87, "curve": "stepped", "time": 0.6667}, {"angle": 104.87, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"angle": 4.01, "time": 0}, {"angle": 21.87, "curve": "stepped", "time": 0.6667}, {"angle": 21.87, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"angle": 12.55, "time": 0}, {"angle": 53.08, "curve": "stepped", "time": 0.6667}, {"angle": 53.08, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"angle": 8.37, "time": 0}, {"angle": 43.32, "curve": "stepped", "time": 0.6667}, {"angle": 43.32, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"angle": 41.08, "time": 0}, {"angle": 130.18, "curve": "stepped", "time": 0.6667}, {"angle": 130.18, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"angle": 0, "curve": "stepped", "time": 0}, {"angle": 0, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"angle": 48.04, "time": 0}, {"angle": -98.06, "curve": "stepped", "time": 0.6667}, {"angle": -98.06, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"angle": -3.58, "time": 0}, {"angle": -47.59, "curve": "stepped", "time": 0.6667}, {"angle": -47.59, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"angle": -1.27, "time": 0}, {"angle": -53.6, "curve": "stepped", "time": 0.6667}, {"angle": -53.6, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"angle": -60.64, "time": 0}, {"angle": -86.75, "curve": "stepped", "time": 0.6667}, {"angle": -86.75, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone24": {"rotate": [{"angle": -24.92, "time": 0}, {"angle": -131.29, "curve": "stepped", "time": 0.6667}, {"angle": -131.29, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone25": {"rotate": [{"angle": 29.7, "curve": [0.302, 0.01, 1, -0.01], "time": 0}, {"angle": 133.77, "curve": "stepped", "time": 0.6667}, {"angle": 133.77, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"angle": 6.05, "time": 0}, {"angle": 33.13, "curve": "stepped", "time": 0.6667}, {"angle": 33.13, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"angle": -1.59, "time": 0}, {"angle": 5.29, "curve": "stepped", "time": 0.6667}, {"angle": 5.29, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"angle": -5.84, "time": 0}, {"angle": -54.71, "curve": "stepped", "time": 0.6667}, {"angle": -54.71, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"angle": -5.57, "time": 0}, {"angle": -106.8, "curve": "stepped", "time": 0.6667}, {"angle": -106.8, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"angle": -7.29, "time": 0}, {"angle": -15.48, "curve": "stepped", "time": 0.6667}, {"angle": -15.48, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"angle": -6.28, "time": 0}, {"angle": -14.08, "curve": "stepped", "time": 0.6667}, {"angle": -14.08, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"angle": -10.5, "time": 0}, {"angle": -32.45, "curve": "stepped", "time": 0.6667}, {"angle": -32.45, "time": 0.8333}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "root": {"rotate": [{"angle": 0, "curve": "stepped", "time": 0}, {"angle": 0, "time": 0.6667}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}}, "deform": {"default": {"mine/chain-0": {"mine/chain-0": [{"time": 0}]}}}, "drawOrder": [{"time": 0}], "slots": {"mine/chain-0": {"attachment": [{"name": "mine/chain-0", "time": 0}]}}}, "idle": {"bones": {"bone": {"rotate": [{"angle": 3.36, "time": 0}, {"angle": -15.35, "time": 0.3333}, {"angle": 3.36, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"angle": -1.55, "time": 0}, {"angle": -1.41, "time": 0.3333}, {"angle": -1.55, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"angle": -3.66, "time": 0}, {"angle": -2.7, "time": 0.3333}, {"angle": -3.66, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"angle": -6.39, "time": 0}, {"angle": -4.46, "time": 0.3333}, {"angle": -6.39, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"angle": -7.57, "time": 0}, {"angle": 0.17, "time": 0.3333}, {"angle": -7.57, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"angle": -1.25, "time": 0}, {"angle": 5.66, "time": 0.3333}, {"angle": -1.25, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"angle": 1.48, "time": 0}, {"angle": 9.22, "time": 0.3333}, {"angle": 1.48, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"angle": 5.76, "time": 0}, {"angle": 7.59, "time": 0.3333}, {"angle": 5.76, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"angle": 8.23, "time": 0}, {"angle": 0.47, "time": 0.3333}, {"angle": 8.23, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"angle": 6.19, "time": 0}, {"angle": -3.5, "time": 0.3333}, {"angle": 6.19, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"angle": 5.48, "time": 0}, {"angle": -6.82, "time": 0.3333}, {"angle": 5.48, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"angle": 0, "curve": "stepped", "time": 0}, {"angle": 0, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"angle": 3.15, "time": 0}, {"angle": -2.22, "time": 0.3333}, {"angle": 3.15, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"angle": -9.08, "time": 0}, {"angle": -10.07, "time": 0.3333}, {"angle": -9.08, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"angle": -9.54, "time": 0}, {"angle": -5.46, "time": 0.3333}, {"angle": -9.54, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"angle": -0.17, "time": 0}, {"angle": 15.48, "time": 0.3333}, {"angle": -0.17, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone24": {"rotate": [{"angle": 10.43, "time": 0}, {"angle": 9.82, "time": 0.3333}, {"angle": 10.43, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone25": {"rotate": [{"angle": -6.15, "time": 0}, {"angle": -11.18, "time": 0.3333}, {"angle": -6.15, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"angle": -0.81, "time": 0}, {"angle": 1.6, "time": 0.3333}, {"angle": -0.81, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"angle": -3.53, "time": 0}, {"angle": 0.33, "time": 0.3333}, {"angle": -3.53, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"angle": -1.31, "time": 0}, {"angle": 4.16, "time": 0.3333}, {"angle": -1.31, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"angle": 0.88, "time": 0}, {"angle": 3.13, "time": 0.3333}, {"angle": 0.88, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"angle": 1.66, "time": 0}, {"angle": -0.75, "time": 0.3333}, {"angle": 1.66, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"angle": 1.43, "time": 0}, {"angle": -1.7, "time": 0.3333}, {"angle": 1.43, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"angle": -1.78, "time": 0}, {"angle": -5.52, "time": 0.3333}, {"angle": -1.78, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}, "root": {"rotate": [{"angle": 3.48, "time": 0}, {"angle": 12.43, "time": 0.3333}, {"angle": 3.48, "time": 0.6667}], "scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}]}}, "deform": {"default": {"mine/chain-0": {"mine/chain-0": [{"curve": "stepped", "time": 0}, {"time": 0.6667}]}}}, "drawOrder": [{"time": 0}, {"time": 0.6667}], "slots": {"mine/chain-0": {"attachment": [{"name": "mine/chain-0", "time": 0}, {"name": "mine/chain-0", "time": 0.6667}]}}}}, "bones": [{"name": "root"}, {"length": 32.34, "name": "bone", "parent": "root", "rotation": 90, "x": 0.58, "y": 0.34}, {"length": 22.77, "name": "bone2", "parent": "bone", "x": 33}, {"length": 27.72, "name": "bone3", "parent": "bone", "rotation": 0.68, "x": 55.11}, {"length": 22.12, "name": "bone4", "parent": "bone3", "rotation": 1.03, "x": 27.71, "y": -0.66}, {"length": 27.39, "name": "bone5", "parent": "bone4", "rotation": -2.4, "x": 21.13, "y": 0.03}, {"length": 21.45, "name": "bone6", "parent": "bone5", "rotation": 0.69, "x": 28.04, "y": 0.67}, {"length": 26.74, "name": "bone7", "parent": "bone6", "rotation": -1.41, "x": 20.79, "y": -0.33}, {"length": 21.12, "name": "bone8", "parent": "bone7", "rotation": 1.41, "x": 25.74, "y": 0.31}, {"length": 29.38, "name": "bone9", "parent": "bone8", "rotation": 1.29, "x": 20.55, "y": -0.33}, {"length": 21.45, "name": "bone10", "parent": "bone9", "rotation": -0.41, "x": 29.8, "y": -0.34}, {"length": 27.06, "name": "bone11", "parent": "bone10", "rotation": -0.88, "x": 20.46, "y": -0.31}, {"length": 20.13, "name": "bone12", "parent": "bone11", "x": 26.6, "y": -0.66}, {"length": 28.05, "name": "bone13", "parent": "bone12", "rotation": 0.67, "x": 20.13}, {"length": 18.28, "name": "bone14", "parent": "bone13", "x": 28.09, "y": -0.01}, {"length": 29.24, "name": "bone15", "parent": "bone14", "rotation": 0.17, "x": 19.35, "y": -0.44}, {"length": 18.28, "name": "bone16", "parent": "bone15", "rotation": -0.17, "x": 29.02, "y": -0.21}, {"length": 29.89, "name": "bone17", "parent": "bone16", "rotation": 0.15, "x": 18.27, "y": -0.43}, {"length": 20, "name": "bone18", "parent": "bone17", "rotation": 0.41, "x": 29.71, "y": -0.21}, {"length": 28.17, "name": "bone19", "parent": "bone18", "rotation": -0.36, "x": 19.78, "y": -0.43}, {"length": 21.07, "name": "bone20", "parent": "bone19", "rotation": -0.29, "x": 28.02, "y": -0.21}, {"length": 27.75, "name": "bone21", "parent": "bone20", "rotation": 1.19, "x": 21.28, "y": -0.43}, {"length": 20.21, "name": "bone22", "parent": "bone21", "rotation": -2.39, "x": 26.72, "y": -0.61}, {"length": 28.6, "name": "bone23", "parent": "bone22", "rotation": 1.9, "x": 21.07, "y": -0.21}, {"length": 17.43, "name": "bone24", "parent": "bone23", "rotation": -3.41, "x": 28.39}, {"length": 31.4, "name": "bone25", "parent": "bone24", "rotation": 3.69, "x": 17.64, "y": 0.01}], "skeleton": {"hash": "CNuvRCSKnRf5gPZM0Ivm/Db7uGE", "height": 1284.18, "images": "../", "spine": "3.6.45", "width": 1920}, "skins": {"default": {"mine/chain-0": {"mine/chain-0": {"edges": [0, 2, 2, 4, 56, 58, 116, 118, 176, 178, 230, 0, 4, 6, 228, 230, 6, 228, 6, 8, 8, 10, 224, 226, 226, 228, 8, 226, 10, 12, 12, 14, 220, 222, 222, 224, 12, 222, 18, 20, 214, 216, 18, 216, 14, 16, 16, 18, 216, 218, 218, 220, 16, 218, 24, 210, 20, 22, 22, 24, 210, 212, 212, 214, 22, 212, 24, 26, 26, 28, 206, 208, 208, 210, 26, 208, 28, 30, 30, 32, 202, 204, 204, 206, 30, 204, 32, 34, 200, 202, 34, 200, 196, 198, 198, 200, 34, 36, 36, 38, 198, 36, 38, 40, 194, 196, 40, 194, 40, 42, 42, 44, 190, 192, 192, 194, 42, 192, 44, 46, 188, 190, 46, 188, 46, 48, 48, 50, 184, 186, 186, 188, 48, 186, 54, 56, 178, 180, 54, 180, 50, 52, 52, 54, 180, 182, 182, 184, 52, 182, 58, 60, 60, 62, 172, 174, 174, 176, 60, 174, 62, 64, 170, 172, 64, 170, 64, 66, 168, 170, 66, 168, 66, 68, 68, 70, 164, 166, 166, 168, 68, 166, 74, 76, 158, 160, 74, 160, 70, 72, 72, 74, 160, 162, 162, 164, 72, 162, 76, 78, 78, 80, 154, 156, 156, 158, 78, 156, 80, 82, 152, 154, 82, 152, 82, 84, 150, 152, 84, 150, 84, 86, 86, 88, 146, 148, 148, 150, 86, 148, 92, 94, 140, 142, 92, 142, 88, 90, 90, 92, 142, 144, 144, 146, 90, 144, 98, 100, 134, 136, 98, 136, 94, 96, 96, 98, 136, 138, 138, 140, 96, 138, 100, 102, 132, 134, 102, 132, 102, 104, 130, 132, 104, 130, 108, 110, 124, 126, 108, 126, 104, 106, 106, 108, 126, 128, 128, 130, 106, 128, 114, 116, 118, 120, 114, 120, 110, 112, 112, 114, 120, 122, 122, 124, 112, 122], "height": 627, "hull": 116, "triangles": [53, 64, 65, 53, 54, 64, 54, 63, 64, 54, 55, 63, 55, 62, 63, 62, 56, 61, 62, 55, 56, 56, 57, 61, 57, 60, 61, 57, 58, 60, 58, 59, 60, 49, 50, 68, 50, 67, 68, 67, 51, 66, 67, 50, 51, 51, 52, 66, 52, 65, 66, 52, 53, 65, 45, 46, 72, 46, 71, 72, 46, 47, 71, 47, 70, 71, 70, 48, 69, 70, 47, 48, 48, 49, 69, 49, 68, 69, 42, 75, 76, 42, 43, 75, 43, 74, 75, 43, 44, 74, 44, 73, 74, 73, 45, 72, 73, 44, 45, 39, 40, 77, 77, 41, 76, 77, 40, 41, 41, 42, 76, 37, 38, 80, 38, 79, 80, 38, 78, 79, 38, 39, 78, 39, 77, 78, 33, 84, 85, 33, 34, 84, 34, 83, 84, 34, 35, 83, 35, 82, 83, 82, 36, 81, 82, 35, 36, 36, 37, 81, 37, 80, 81, 86, 32, 85, 86, 31, 32, 32, 33, 85, 88, 30, 87, 30, 31, 87, 31, 86, 87, 28, 29, 88, 88, 29, 30, 91, 27, 90, 91, 26, 27, 27, 28, 90, 28, 89, 90, 28, 88, 89, 24, 93, 94, 24, 92, 93, 24, 25, 92, 25, 91, 92, 25, 26, 91, 96, 21, 95, 21, 22, 95, 95, 23, 94, 95, 22, 23, 23, 24, 94, 17, 99, 100, 99, 18, 98, 18, 19, 98, 19, 97, 98, 19, 20, 97, 20, 21, 97, 21, 96, 97, 14, 15, 102, 15, 101, 102, 15, 16, 101, 16, 100, 101, 16, 17, 100, 17, 18, 99, 105, 13, 104, 13, 14, 104, 14, 103, 104, 14, 102, 103, 105, 12, 13, 9, 108, 109, 9, 10, 108, 10, 107, 108, 10, 106, 107, 10, 11, 106, 11, 105, 106, 11, 12, 105, 5, 6, 111, 111, 6, 110, 6, 7, 110, 110, 8, 109, 110, 7, 8, 8, 9, 109, 114, 4, 113, 4, 5, 113, 5, 112, 113, 5, 111, 112, 1, 115, 0, 1, 2, 115, 2, 114, 115, 2, 3, 114, 114, 3, 4], "type": "mesh", "uvs": [1.0, 1.0, 0.0, 1.0, 0.0, 0.95, 0.0, 0.94028, 0.0, 0.90975, 0.0, 0.9, 0.0, 0.86028, 0.0, 0.85, 0.0, 0.83449, 0.0, 0.82502, 0.0, 0.8, 0.0, 0.79081, 0.0, 0.78133, 0.0, 0.7572, 0.0, 0.75, 0.0, 0.70576, 0.0, 0.7, 0.0, 0.68142, 0.0, 0.67627, 0.0, 0.65, 0.0, 0.62751, 0.0, 0.62168, 0.0, 0.6, 0.0, 0.59356, 0.0, 0.55619, 0.0, 0.55, 0.0, 0.52591, 0.0, 0.51871, 0.0, 0.5, 0.0, 0.45, 0.0, 0.44402, 0.0, 0.4, 0.0, 0.39376, 0.0, 0.37456, 0.0, 0.36907, 0.0, 0.35, 0.0, 0.32734, 0.0, 0.32151, 0.0, 0.3, 0.0, 0.29311, 0.0, 0.25, 0.0, 0.24362, 0.0, 0.21825, 0.0, 0.21242, 0.0, 0.2, 0.0, 0.17194, 0.0, 0.16646, 0.0, 0.15, 0.0, 0.14211, 0.0, 0.13354, 0.0, 0.1, 0.0, 0.09077, 0.0, 0.08528, 0.0, 0.07019, 0.0, 0.06402, 0.0, 0.05, 0.0, 0.01919, 0.0, 0.01267, 0.0, 0.0, 1.0, 0.0, 1.0, 0.01233, 1.0, 0.0185, 1.0, 0.05, 1.0, 0.06231, 1.0, 0.06779, 1.0, 0.08391, 1.0, 0.09042, 1.0, 0.1, 1.0, 0.1332, 1.0, 0.1404, 1.0, 0.15, 1.0, 0.16509, 1.0, 0.17126, 1.0, 0.2, 1.0, 0.21174, 1.0, 0.21791, 1.0, 0.24122, 1.0, 0.25, 1.0, 0.29311, 1.0, 0.3, 1.0, 0.31979, 1.0, 0.32631, 1.0, 0.35, 1.0, 0.36839, 1.0, 0.37388, 1.0, 0.39342, 1.0, 0.4, 1.0, 0.44368, 1.0, 0.45, 1.0, 0.5, 1.0, 0.51768, 1.0, 0.52591, 1.0, 0.55, 1.0, 0.55619, 1.0, 0.59322, 1.0, 0.6, 1.0, 0.62168, 1.0, 0.62751, 1.0, 0.65, 1.0, 0.67627, 1.0, 0.68176, 1.0, 0.7, 1.0, 0.70748, 1.0, 0.75, 1.0, 0.75583, 1.0, 0.78133, 1.0, 0.79081, 1.0, 0.8, 1.0, 0.82449, 1.0, 0.83396, 1.0, 0.85, 1.0, 0.86028, 1.0, 0.9, 1.0, 0.9087, 1.0, 0.94081, 1.0, 0.95], "vertices": [1, 1, -3.84, -12.42, 1, 1, 1, -3.84, 11.58, 1, 2, 1, 27.51, 11.58, 0.81004, 2, -5.49, 11.58, 0.18996, 3, 1, 33.6, 11.58, 0.65257, 2, 0.6, 11.58, 0.20872, 3, -21.37, 11.83, 0.13871, 3, 1, 52.74, 11.58, 0.15802, 2, 19.74, 11.58, 0.26765, 3, -2.23, 11.61, 0.57434, 2, 2, 25.86, 11.58, 0.28647, 3, 3.89, 11.53, 0.71353, 4, 2, 50.76, 11.58, 0.0589, 3, 28.79, 11.24, 0.21047, 4, 1.29, 11.88, 0.63487, 5, -20.32, 11, 0.09576, 3, 3, 35.23, 11.16, 0.08028, 4, 7.73, 11.68, 0.79918, 5, -13.88, 11.08, 0.12054, 4, 3, 44.96, 11.04, 0.05537, 4, 17.45, 11.39, 0.56468, 5, -4.15, 11.2, 0.21238, 6, -32.07, 10.92, 0.16757, 4, 3, 50.9, 10.97, 0.04016, 4, 23.39, 11.22, 0.42144, 5, 1.79, 11.27, 0.26847, 6, -26.13, 10.92, 0.26993, 3, 4, 39.07, 10.75, 0.04319, 5, 17.47, 11.46, 0.41659, 6, -10.44, 10.92, 0.54022, 5, 4, 44.83, 10.58, 0.03525, 5, 23.24, 11.53, 0.33999, 6, -4.68, 10.92, 0.53069, 7, -25.74, 10.62, 0.09258, 8, -51.21, 11.58, 0.00149, 5, 4, 50.77, 10.4, 0.02707, 5, 29.18, 11.6, 0.26106, 6, 1.26, 10.92, 0.52088, 7, -19.8, 10.76, 0.18797, 8, -45.27, 11.58, 0.00303, 5, 4, 65.89, 9.95, 0.00622, 5, 44.31, 11.78, 0.05997, 6, 16.4, 10.92, 0.49587, 7, -4.67, 11.14, 0.43099, 8, -30.13, 11.58, 0.00694, 3, 6, 20.91, 10.92, 0.48842, 7, -0.16, 11.25, 0.50347, 8, -25.62, 11.58, 0.00811, 3, 6, 48.65, 10.92, 0.05629, 7, 27.57, 11.93, 0.05803, 8, 2.12, 11.58, 0.88568, 1, 8, 5.73, 11.58, 1, 2, 8, 17.38, 11.58, 0.62887, 9, -2.9, 11.98, 0.37113, 2, 8, 20.61, 11.58, 0.52614, 9, 0.32, 11.9, 0.47386, 2, 8, 37.08, 11.58, 0.00143, 9, 16.79, 11.53, 0.99857, 3, 8, 51.18, 11.58, 0.00078, 9, 30.89, 11.22, 0.54944, 10, 1, 11.56, 0.44978, 3, 8, 54.83, 11.58, 0.00062, 9, 34.54, 11.14, 0.43301, 10, 4.66, 11.51, 0.56637, 1, 10, 18.25, 11.3, 1, 3, 10, 22.28, 11.24, 0.87134, 11, 1.65, 11.58, 0.04188, 12, -24.95, 12.24, 0.08678, 3, 10, 45.72, 10.88, 0.12419, 11, 25.08, 11.58, 0.28507, 12, -1.52, 12.24, 0.59074, 3, 10, 49.6, 10.82, 0.00051, 11, 28.96, 11.58, 0.32533, 12, 2.36, 12.24, 0.67416, 5, 10, 64.7, 10.58, 0.00026, 11, 44.07, 11.58, 0.16857, 12, 17.47, 12.24, 0.35585, 13, -2.52, 12.27, 0.47444, 14, -30.61, 12.28, 0.00087, 5, 10, 69.21, 10.52, 0.00019, 11, 48.58, 11.58, 0.12172, 12, 21.99, 12.24, 0.26072, 13, 2, 12.22, 0.61624, 14, -26.1, 12.23, 0.00114, 3, 12, 33.71, 12.24, 0.01356, 13, 13.73, 12.08, 0.98462, 14, -14.37, 12.09, 0.00182, 2, 14, 16.98, 11.72, 0.53646, 15, -2.33, 12.17, 0.46354, 3, 14, 20.73, 11.68, 0.47231, 15, 1.42, 12.11, 0.46755, 16, -27.64, 12.24, 0.06015, 2, 15, 29.02, 11.71, 0.49708, 16, -0.04, 11.92, 0.50292, 3, 15, 32.93, 11.65, 0.43508, 16, 3.87, 11.87, 0.44292, 17, -14.37, 12.34, 0.122, 3, 15, 44.97, 11.47, 0.24418, 16, 15.91, 11.73, 0.25816, 17, -2.33, 12.17, 0.49766, 3, 15, 48.41, 11.42, 0.18964, 16, 19.35, 11.69, 0.20537, 17, 1.11, 12.12, 0.60499, 2, 16, 31.31, 11.55, 0.02184, 17, 13.07, 11.95, 0.97816, 4, 16, 45.51, 11.38, 0.01194, 17, 27.27, 11.74, 0.53598, 18, -2.36, 11.97, 0.3569, 19, -22.21, 12.26, 0.09518, 4, 16, 49.17, 11.34, 0.00939, 17, 30.93, 11.69, 0.42224, 18, 1.3, 11.89, 0.4487, 19, -18.55, 12.2, 0.11966, 3, 17, 44.41, 11.5, 0.00259, 18, 14.78, 11.6, 0.78742, 19, -5.07, 12, 0.20999, 4, 17, 48.73, 11.43, 0.00223, 18, 19.1, 11.51, 0.67892, 19, -0.75, 11.93, 0.26971, 20, -28.83, 12, 0.04914, 2, 19, 26.28, 11.52, 0.64338, 20, -1.8, 11.72, 0.35662, 3, 19, 30.27, 11.46, 0.56135, 20, 2.19, 11.68, 0.3221, 21, -18.83, 12.51, 0.11654, 3, 19, 46.18, 11.22, 0.23484, 20, 18.1, 11.52, 0.18472, 21, -2.93, 12.02, 0.58044, 3, 19, 49.84, 11.16, 0.15983, 20, 21.76, 11.48, 0.15315, 21, 0.72, 11.9, 0.68702, 2, 20, 29.55, 11.4, 0.0859, 21, 8.51, 11.66, 0.9141, 3, 20, 47.14, 11.22, 0.0377, 21, 26.09, 11.12, 0.52837, 22, -1.11, 11.69, 0.43393, 3, 20, 50.58, 11.19, 0.02827, 21, 29.53, 11.01, 0.45294, 22, 2.33, 11.73, 0.51879, 2, 21, 39.84, 10.69, 0.22668, 22, 12.65, 11.84, 0.77332, 4, 21, 44.79, 10.54, 0.19092, 22, 17.59, 11.89, 0.65131, 23, -3.08, 12.21, 0.126, 24, -32.14, 10.31, 0.03178, 4, 21, 50.16, 10.37, 0.15205, 22, 22.97, 11.95, 0.51872, 23, 2.29, 12.09, 0.26292, 24, -26.77, 10.51, 0.06631, 2, 23, 23.32, 11.61, 0.79859, 24, -5.75, 11.28, 0.20141, 3, 23, 29.11, 11.48, 0.65114, 24, 0.03, 11.5, 0.17549, 25, -16.84, 12.6, 0.17337, 3, 23, 32.54, 11.4, 0.56351, 24, 3.47, 11.62, 0.16008, 25, -13.4, 12.51, 0.27641, 3, 23, 42, 11.19, 0.32253, 24, 12.92, 11.98, 0.11772, 25, -3.94, 12.25, 0.55975, 3, 23, 45.87, 11.1, 0.22395, 24, 16.79, 12.12, 0.10038, 25, -0.07, 12.14, 0.67566, 2, 24, 25.57, 12.44, 0.06101, 25, 8.72, 11.9, 0.93899, 2, 24, 44.88, 13.16, 0.02341, 25, 28.03, 11.37, 0.97659, 2, 24, 48.96, 13.31, 0.01546, 25, 32.11, 11.26, 0.98454, 1, 25, 40.05, 11.04, 1, 1, 25, 39.4, -12.95, 1, 2, 24, 50.07, -10.67, 0.02111, 25, 31.67, -12.74, 0.97889, 2, 24, 46.2, -10.81, 0.03168, 25, 27.8, -12.63, 0.96832, 2, 24, 26.46, -11.54, 0.08562, 25, 8.06, -12.09, 0.91438, 3, 23, 46.4, -12.92, 0.18376, 24, 18.75, -11.83, 0.12693, 25, 0.34, -11.88, 0.68931, 3, 23, 42.97, -12.84, 0.26568, 24, 15.31, -11.95, 0.14534, 25, -3.09, -11.79, 0.58898, 3, 23, 32.86, -12.61, 0.50631, 24, 5.21, -12.33, 0.19944, 25, -13.2, -11.51, 0.29425, 3, 23, 28.78, -12.52, 0.60359, 24, 1.13, -12.48, 0.2213, 25, -17.28, -11.4, 0.1751, 2, 23, 22.78, -12.38, 0.74656, 24, -4.87, -12.7, 0.25344, 3, 22, 23.44, -12.05, 0.54665, 23, 1.97, -11.91, 0.36817, 24, -25.67, -13.47, 0.08518, 3, 22, 18.92, -12.1, 0.66523, 23, -2.55, -11.81, 0.28609, 24, -30.18, -13.64, 0.04868, 2, 22, 12.9, -12.16, 0.82338, 23, -8.57, -11.68, 0.17662, 4, 20, 51.19, -12.82, 0.03273, 21, 29.65, -13.01, 0.26478, 22, 3.44, -12.26, 0.57916, 23, -18.02, -11.46, 0.12333, 4, 20, 47.32, -12.78, 0.04612, 21, 25.78, -12.89, 0.37312, 22, -0.43, -12.3, 0.47924, 23, -21.89, -11.38, 0.10153, 3, 20, 29.3, -12.6, 0.10847, 21, 7.76, -12.33, 0.87761, 22, -18.45, -12.49, 0.01393, 4, 19, 49.9, -12.84, 0.14045, 20, 21.94, -12.52, 0.17726, 21, 0.41, -12.1, 0.67163, 22, -25.81, -12.57, 0.01066, 4, 19, 46.03, -12.79, 0.21433, 20, 18.07, -12.48, 0.21344, 21, -3.46, -11.98, 0.56329, 22, -29.68, -12.61, 0.00894, 4, 19, 31.41, -12.56, 0.4934, 20, 3.45, -12.33, 0.35013, 21, -18.07, -11.53, 0.15403, 22, -44.29, -12.77, 0.00244, 2, 19, 25.91, -12.48, 0.59843, 20, -2.05, -12.28, 0.40157, 4, 17, 48.39, -12.56, 0.0022, 18, 18.58, -12.48, 0.69303, 19, -1.12, -12.07, 0.24943, 20, -29.08, -12, 0.05534, 3, 17, 44.07, -12.5, 0.00255, 18, 14.26, -12.39, 0.8038, 19, -5.44, -12, 0.19365, 4, 16, 49.96, -12.67, 0.00236, 17, 31.66, -12.32, 0.39504, 18, 1.86, -12.12, 0.4856, 19, -17.84, -11.81, 0.11699, 4, 16, 45.88, -12.62, 0.00314, 17, 27.57, -12.26, 0.52424, 18, -2.23, -12.04, 0.38087, 19, -21.93, -11.75, 0.09176, 2, 16, 31.02, -12.45, 0.00597, 17, 12.72, -12.05, 0.99403, 3, 15, 48.48, -12.58, 0.16063, 16, 19.49, -12.31, 0.21092, 17, 1.19, -11.88, 0.62844, 3, 15, 45.04, -12.53, 0.20856, 16, 16.05, -12.27, 0.27207, 17, -2.25, -11.84, 0.51937, 3, 15, 32.79, -12.35, 0.37929, 16, 3.8, -12.13, 0.48991, 17, -14.5, -11.66, 0.1308, 2, 15, 28.66, -12.29, 0.43676, 16, -0.33, -12.08, 0.56324, 3, 14, 20.66, -12.33, 0.42754, 15, 1.28, -11.89, 0.50124, 16, -27.71, -11.76, 0.07122, 2, 14, 16.7, -12.28, 0.48943, 15, -2.68, -11.83, 0.51057, 3, 12, 33.71, -11.76, 0.00196, 13, 13.44, -11.92, 0.98659, 14, -14.65, -11.91, 0.01145, 4, 11, 49.23, -12.42, 0.1023, 12, 22.63, -11.76, 0.25253, 13, 2.36, -11.79, 0.63777, 14, -25.73, -11.78, 0.0074, 4, 11, 44.07, -12.42, 0.14992, 12, 17.47, -11.76, 0.36918, 13, -2.8, -11.73, 0.47538, 14, -30.89, -11.72, 0.00552, 2, 11, 28.96, -12.42, 0.28933, 12, 2.36, -11.76, 0.71067, 3, 10, 45.35, -13.12, 0.04067, 11, 25.08, -12.42, 0.3366, 12, -1.52, -11.76, 0.62273, 3, 10, 22.13, -12.76, 0.28409, 11, 1.86, -12.42, 0.61955, 12, -24.74, -11.76, 0.09636, 2, 10, 17.88, -12.7, 0.32866, 11, -2.39, -12.42, 0.67134, 3, 9, 34, -12.86, 0.43053, 10, 4.29, -12.49, 0.18924, 11, -15.98, -12.42, 0.38023, 3, 9, 30.35, -12.78, 0.54628, 10, 0.63, -12.43, 0.15176, 11, -19.64, -12.42, 0.30196, 2, 9, 16.25, -12.46, 0.99285, 10, -13.47, -12.22, 0.00715, 4, 7, 46.65, -11.6, 0.33157, 8, 20.61, -12.42, 0.19389, 9, -0.22, -12.09, 0.47114, 10, -29.94, -11.96, 0.00339, 4, 7, 43.21, -11.69, 0.40081, 8, 17.17, -12.42, 0.23438, 9, -3.66, -12.01, 0.3622, 10, -33.38, -11.91, 0.00261, 2, 7, 31.77, -11.97, 0.63101, 8, 5.73, -12.42, 0.36899, 4, 5, 75.77, -11.84, 0.01248, 6, 47.57, -13.08, 0.05957, 7, 27.09, -12.09, 0.61414, 8, 1.04, -12.42, 0.31381, 3, 5, 49.11, -12.16, 0.08344, 6, 20.91, -13.08, 0.39831, 7, 0.43, -12.74, 0.51825, 3, 5, 45.46, -12.2, 0.19024, 6, 17.26, -13.08, 0.3519, 7, -3.22, -12.83, 0.45786, 3, 5, 29.46, -12.4, 0.6578, 6, 1.26, -13.08, 0.14871, 7, -19.21, -13.23, 0.19349, 3, 5, 23.53, -12.47, 0.83146, 6, -4.68, -13.08, 0.07324, 7, -25.14, -13.38, 0.09529, 1, 5, 17.76, -12.54, 1, 3, 3, 50.94, -13.03, 0.06232, 4, 23, -12.78, 0.35022, 5, 2.41, -12.72, 0.58746, 3, 3, 45, -12.96, 0.08642, 4, 17.06, -12.61, 0.4857, 5, -3.53, -12.79, 0.42788, 3, 3, 34.95, -12.84, 0.12723, 4, 7.01, -12.31, 0.71502, 5, -13.59, -12.92, 0.15775, 4, 2, 50.76, -12.42, 0.05945, 3, 28.5, -12.76, 0.24722, 4, 0.57, -12.11, 0.56802, 5, -20.03, -12.99, 0.12532, 2, 2, 25.86, -12.42, 0.28914, 3, 3.6, -12.46, 0.71086, 3, 1, 53.4, -12.42, 0.13788, 2, 20.4, -12.42, 0.27496, 3, -1.85, -12.4, 0.58716, 3, 1, 33.27, -12.42, 0.64665, 2, 0.27, -12.42, 0.22264, 3, -21.98, -12.16, 0.13071, 2, 1, 27.51, -12.42, 0.79234, 2, -5.49, -12.42, 0.20766], "width": 24}}}}, "slots": [{"attachment": "mine/chain-0", "bone": "root", "name": "mine/chain-0"}]}