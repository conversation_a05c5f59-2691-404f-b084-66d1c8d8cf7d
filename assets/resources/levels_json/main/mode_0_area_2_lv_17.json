{"name": "mode_0_area_2_lv_17", "children": [{"name": "background", "anchorPoint": [0.5, 0.5], "position": [0, 0], "scale": [1, 1], "rotation": 0, "filePath": "prefabs/level_backgrounds/area_2_day.prefab"}, {"name": "railway_4", "anchorPoint": [0.5, 0.5], "position": [0, 300], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/railway_4.prefab"}, {"name": "fish_tank", "anchorPoint": [0.5, 0.5], "position": [-259, -200], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/fish_tank.prefab"}, {"name": "niece_0", "anchorPoint": [0.4, 0], "position": [-187, -598], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/niece_0.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [580, 190], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "gold_0", "anchorPoint": [0.5, 0.5], "position": [734, 192], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_0.prefab"}, {"name": "stone_1", "anchorPoint": [0.5, 0.5], "position": [891, 175], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/stone_1.prefab"}, {"name": "seashell", "anchorPoint": [0.5, 0.5], "position": [140, -345], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/seashell.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [-861, 208], "scale": [1, 1], "rotation": -33, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "stone_1", "anchorPoint": [0.5, 0.5], "position": [405, 175], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/stone_1.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [188, 187], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "stone_1", "anchorPoint": [0.5, 0.5], "position": [-21, 175], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/stone_1.prefab"}, {"name": "seashell", "anchorPoint": [0.5, 0.5], "position": [326, -542], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/seashell.prefab"}, {"name": "gold_1", "anchorPoint": [0.5, 0.48], "position": [-277, -313], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/gold_1.prefab"}, {"name": "diamond", "anchorPoint": [0.5, 0.5], "position": [-287, -316], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/diamond.prefab"}, {"name": "fish_0", "anchorPoint": [0.5, 0.5], "position": [-321, -129], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/fish_0.prefab"}, {"name": "diamond_mouse", "anchorPoint": [0.5, 0], "position": [-953, 76], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/diamond_mouse.prefab"}, {"name": "diamond_mouse", "anchorPoint": [0.5, 0], "position": [-6, -271], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/diamond_mouse.prefab"}, {"name": "diamond_mouse", "anchorPoint": [0.5, 0], "position": [583, -294], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/diamond_mouse.prefab"}, {"name": "diamond_mouse", "anchorPoint": [0.5, 0], "position": [-741, -211], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/diamond_mouse.prefab"}, {"name": "bone_2", "anchorPoint": [0.5, 0.5], "position": [493, -175], "scale": [1, 1], "rotation": 0, "filePath": "levels_json/level_items/bone_2.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [96, -27], "scale": [1, 1], "rotation": -149, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_4", "anchorPoint": [0.5, 0.5], "position": [-480, -66], "scale": [1, 1], "rotation": -149, "filePath": "levels_json/level_items/bone_4.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [780, -449], "scale": [1, 1], "rotation": -173, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [511, 27], "scale": [1, 1], "rotation": -92, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [-744, -549], "scale": [1, 1], "rotation": -62, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_5", "anchorPoint": [0.5, 0.5], "position": [-420, 173], "scale": [1, 1], "rotation": -62, "filePath": "levels_json/level_items/bone_5.prefab"}, {"name": "bone_0", "anchorPoint": [0.5, 0.5], "position": [-716, -345], "scale": [1, 1], "rotation": 36, "filePath": "levels_json/level_items/bone_0.prefab"}, {"name": "bone_0", "anchorPoint": [0.5, 0.5], "position": [254, -294], "scale": [1, 1], "rotation": 36, "filePath": "levels_json/level_items/bone_0.prefab"}, {"name": "bone_3", "anchorPoint": [0.5, 0.5], "position": [508, -439], "scale": [1, 1], "rotation": -2, "filePath": "levels_json/level_items/bone_3.prefab"}, {"name": "bone_3", "anchorPoint": [0.5, 0.5], "position": [-607, 50], "scale": [1, 1], "rotation": -2, "filePath": "levels_json/level_items/bone_3.prefab"}, {"name": "wolf", "anchorPoint": [0.5, 0], "position": [869, -41], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/wolf.prefab"}, {"name": "wolf", "anchorPoint": [0.5, 0], "position": [1051, -490], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/wolf.prefab"}, {"name": "wolf", "anchorPoint": [0.5, 0], "position": [1177, -595], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/wolf.prefab"}, {"name": "wolf", "anchorPoint": [0.5, 0], "position": [1251, -408], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/wolf.prefab"}, {"name": "wolf", "anchorPoint": [0.5, 0], "position": [1436, -531], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/wolf.prefab"}, {"name": "wolf", "anchorPoint": [0.5, 0], "position": [1125, -143], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/wolf.prefab"}, {"name": "wolf", "anchorPoint": [0.5, 0], "position": [1346, -9], "scale": [-1, 1], "rotation": 0, "filePath": "levels_json/level_items/wolf.prefab"}]}