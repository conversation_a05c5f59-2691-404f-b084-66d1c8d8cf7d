{"animations": {"idle": {"bones": {"bone": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "root": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "twinkle": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1.9333, "x": 0.2, "y": 0.2}, {"time": 2.0667, "x": 0.797, "y": 0.797}, {"time": 2.2, "x": 1, "y": 1}, {"time": 2.3333, "x": 0.797, "y": 0.797}, {"time": 2.4667, "x": 0.2, "y": 0.2}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.9333, "x": 0, "y": 23.64}, {"time": 2.2, "x": 0, "y": 0.31}, {"time": 2.4667, "x": 0, "y": -22.08}]}}, "drawOrder": [{"time": 0}], "slots": {"quart-violet": {"attachment": [{"name": "quart-violet", "time": 0}]}, "sparkle-3": {"attachment": [{"name": null, "time": 0}]}, "sparkle-4": {"attachment": [{"name": null, "time": 0}]}, "sparkle-5": {"attachment": [{"name": null, "time": 0}]}, "sparkle-6": {"attachment": [{"name": null, "time": 0}]}, "twinkle-2": {"attachment": [{"name": null, "time": 0}, {"name": "twinkle-3", "time": 1.9333}, {"name": "twinkle-3", "time": 2.4667}, {"name": null, "time": 2.5}]}}}, "pulled": {"bones": {"bone": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"angle": 0, "curve": "stepped", "time": 0}, {"angle": 0, "time": 2.3333}, {"angle": 168.25, "time": 2.8667}, {"angle": -27.56, "time": 3.4}, {"angle": 63.92, "time": 3.6667}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.105, "y": 0.105}, {"time": 1.1, "x": 0.634, "y": 0.634}, {"time": 1.2333, "x": 0.725, "y": 0.725}, {"time": 1.3667, "x": 0.634, "y": 0.634}, {"curve": "stepped", "time": 1.6333, "x": 0.105, "y": 0.105}, {"time": 2.3333, "x": 0.105, "y": 0.105}, {"time": 2.7333, "x": 0.634, "y": 0.634}, {"time": 2.9333, "x": 0.725, "y": 0.725}, {"time": 3.2, "x": 0.634, "y": 0.634}, {"time": 3.6, "x": 0.105, "y": 0.105}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"curve": "stepped", "time": 0.8333, "x": 0, "y": 0}, {"time": 1.6667, "x": 0, "y": 0}, {"curve": "stepped", "time": 2.3333, "x": -8.74, "y": 7.88}, {"time": 3, "x": -8.74, "y": 7.88}, {"time": 3.6667, "x": 27.69, "y": 78.44}]}, "bone3": {"rotate": [{"angle": 0, "curve": "stepped", "time": 0}, {"angle": 0, "time": 1.8333}, {"angle": -129.13, "time": 2.1667}, {"angle": 89.84, "time": 2.5}, {"angle": -63.68, "time": 2.8333}, {"angle": 174.25, "time": 3.1667}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3333, "x": 0.105, "y": 0.105}, {"time": 0.5667, "x": 0.634, "y": 0.634}, {"time": 0.7, "x": 0.725, "y": 0.725}, {"time": 0.8333, "x": 0.634, "y": 0.634}, {"curve": "stepped", "time": 1.1, "x": 0.105, "y": 0.105}, {"time": 1.8333, "x": 0.105, "y": 0.105}, {"time": 2.2333, "x": 0.634, "y": 0.634}, {"time": 2.4667, "x": 0.725, "y": 0.725}, {"time": 2.6667, "x": 0.634, "y": 0.634}, {"time": 3.1333, "x": 0.105, "y": 0.105}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"curve": "stepped", "time": 0.3333, "x": 0, "y": 0}, {"time": 1.1333, "x": 0, "y": 0}, {"curve": "stepped", "time": 1.8333, "x": 14.08, "y": 11.2}, {"time": 2.4667, "x": 14.08, "y": 11.2}, {"time": 3.1667, "x": 59.73, "y": -41.52}]}, "bone4": {"rotate": [{"angle": 0, "curve": "stepped", "time": 0}, {"angle": 0, "time": 1.6667}, {"angle": 145.72, "time": 2}, {"angle": -67.41, "time": 2.3333}, {"angle": 78.92, "time": 2.6667}, {"angle": -119.58, "time": 3}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.0333, "x": 0.105, "y": 0.105}, {"time": 0.3, "x": 0.634, "y": 0.634}, {"time": 0.4333, "x": 0.725, "y": 0.725}, {"time": 0.5667, "x": 0.634, "y": 0.634}, {"curve": "stepped", "time": 0.8333, "x": 0.105, "y": 0.105}, {"time": 1.6667, "x": 0.105, "y": 0.105}, {"time": 2.0667, "x": 0.634, "y": 0.634}, {"time": 2.3, "x": 0.725, "y": 0.725}, {"time": 2.5, "x": 0.634, "y": 0.634}, {"time": 2.9667, "x": 0.105, "y": 0.105}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"curve": "stepped", "time": 0.0333, "x": 5.68, "y": 0}, {"time": 0.8667, "x": 5.68, "y": 0}, {"curve": "stepped", "time": 1.6667, "x": 4.19, "y": 4.26}, {"time": 2.3333, "x": 4.19, "y": 4.26}, {"time": 3, "x": -32.69, "y": -12.5}]}, "bone6": {"rotate": [{"angle": 0, "curve": "stepped", "time": 0}, {"angle": 0, "time": 2}, {"angle": -129.13, "time": 2.3333}, {"angle": 89.84, "time": 2.6667}, {"angle": -63.68, "time": 3}, {"angle": 174.25, "time": 3.3333}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 2, "x": 0.105, "y": 0.105}, {"time": 2.4, "x": 0.634, "y": 0.634}, {"time": 2.6333, "x": 0.725, "y": 0.725}, {"time": 2.8333, "x": 0.634, "y": 0.634}, {"time": 3.3, "x": 0.105, "y": 0.105}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"curve": "stepped", "time": 0, "x": 0, "y": 0}, {"time": 2.6333, "x": 0, "y": 0}, {"time": 3.3333, "x": -23.53, "y": 55.33}]}, "root": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}, "twinkle": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}], "translate": [{"time": 0, "x": 0, "y": 0}]}}, "drawOrder": [{"time": 0}], "slots": {"quart-violet": {"attachment": [{"name": "quart-violet", "time": 0}]}, "sparkle-3": {"attachment": [{"name": null, "time": 0}, {"name": "sparkle-0", "time": 0.3333}, {"name": null, "time": 1.1333}, {"name": "sparkle-0", "time": 1.8333}, {"name": null, "time": 3.1667}]}, "sparkle-4": {"attachment": [{"name": null, "time": 0}, {"name": "sparkle-0", "time": 0.0333}, {"name": null, "time": 0.8667}, {"name": "sparkle-0", "time": 1.6667}, {"name": null, "time": 3}]}, "sparkle-5": {"attachment": [{"name": null, "time": 0}, {"name": "sparkle-0", "time": 0.8333}, {"name": null, "time": 1.6667}, {"name": "sparkle-0", "time": 2.3333}, {"name": null, "time": 3.6667}]}, "sparkle-6": {"attachment": [{"name": null, "time": 0}, {"name": "sparkle-0", "time": 2}, {"name": null, "time": 3.3333}]}, "twinkle-2": {"attachment": [{"name": null, "time": 0}]}}}}, "bones": [{"name": "root"}, {"length": 35.38, "name": "bone", "parent": "root", "rotation": 90}, {"length": 28.51, "name": "bone2", "parent": "bone", "rotation": 0.45, "x": 15.2, "y": 13.85}, {"length": 24.29, "name": "bone3", "parent": "bone", "rotation": 0.45, "x": -2.48, "y": -20.61}, {"length": 19.01, "name": "bone4", "parent": "bone", "rotation": 0.45, "x": -23.74, "y": -2.83}, {"length": 19.01, "name": "bone6", "parent": "bone", "rotation": 0.45, "scaleX": 0.79, "scaleY": 0.79, "x": -2.11, "y": 6.71}, {"length": 21.5, "name": "twinkle", "parent": "bone", "rotation": -89.55, "x": -13.4, "y": -0.5}], "skeleton": {"audio": "", "hash": "UpA2mlk4pJe+BDw7Xu5k4ul8LNU", "height": 73.43, "images": "", "spine": "3.7.91", "width": 55.57}, "skins": {"default": {"quart-violet": {"quart-violet": {"height": 73, "rotation": -89.55, "width": 55, "x": -0.34, "y": -0.5}}, "sparkle-3": {"sparkle-0": {"color": "e3b2ffff", "height": 100, "rotation": -90.91, "width": 100}}, "sparkle-4": {"sparkle-0": {"color": "ebbcffff", "height": 100, "rotation": -89.55, "width": 100, "x": 2.64, "y": 0.02}}, "sparkle-5": {"sparkle-0": {"color": "f5a6ffff", "height": 100, "rotation": -89.55, "width": 100, "x": -1.05, "y": -0.54}}, "sparkle-6": {"sparkle-0": {"color": "eabcffff", "height": 100, "rotation": -89.55, "width": 100, "x": 2.64, "y": 0.02}}, "twinkle-2": {"twinkle-3": {"color": "ffffff91", "height": 51, "path": "twinkle-1", "rotation": -0.45, "width": 19, "x": -1.34, "y": 0.88}}}}, "slots": [{"attachment": "quart-violet", "bone": "bone", "name": "quart-violet"}, {"blend": "additive", "bone": "bone2", "name": "sparkle-5"}, {"blend": "additive", "bone": "bone3", "name": "sparkle-3"}, {"blend": "additive", "bone": "bone4", "name": "sparkle-4"}, {"blend": "additive", "bone": "bone6", "name": "sparkle-6"}, {"blend": "additive", "bone": "twinkle", "name": "twinkle-2"}]}