{"beginner_offer_pack": "<PERSON><PERSON><PERSON>er", "blow_up_safe": "Blow up safe %{number} times", "booster_ant_spray": "<PERSON><PERSON>", "booster_clock": "Clock", "booster_desc_ant_spray": "Send those little guys to ant heaven when you spray this on your claw.", "booster_desc_clock": "Gives you an extra 10 seconds on the next level.", "booster_desc_diamond_polish": "During the next level diamonds will be worth more money. Only good for one level.", "booster_desc_dynamite": "After you have grabbed onto something with your claw, click on a stick of dynamite to throw a piece of dynamite at it and blow it up.", "booster_desc_glove": "You can grab spinning Hedgehogs!", "booster_desc_lucky": "This will increase the chances of getting something good out of the grab bags on the next level. This is only good for one level.", "booster_desc_oil": "Makes the miner's cart move faster.", "booster_desc_old_ham": "You can grab Red vultures!", "booster_desc_pick_up": "Allows you to instantly collect an item by tapping on it", "booster_desc_pirate_hat": "Pirates know about the sea and the treasures in it. Pearls will be worth more money on the next level.", "booster_desc_spirit_jar": "With this you can capture red ghosts and get money.", "booster_desc_spooky_doll": "This doll of the dead greatly increase the value of bones.", "booster_desc_strength": "The Miner will reel up objects a little faster on the next level. The drink only lasts for one level.", "booster_desc_titan_rope": "Allows you to lift very heavy objects.", "booster_desc_wanted_poster": "Get 500GB reward for catching this sneaky crook.", "booster_diamond_polish": "Diamond Polish", "booster_dynamite": "Dynamite", "booster_glove": "Glove", "booster_lucky": "<PERSON> Clover", "booster_offer_pack": "<PERSON><PERSON><PERSON>", "booster_oil": "Oil Can", "booster_old_ham": "Old Ham", "booster_pack_info": "Get one random booster from the following list", "booster_pick_up": "Pick Up", "booster_pirate_hat": "Pirate Hat", "booster_spirit_jar": "Spirit Jar", "booster_spooky_doll": "Spooky Doll", "booster_strength": "Strength Drink", "booster_titan_rope": "Titan Rope", "booster_wanted_poster": "Wanted Poster", "button_open": "Open", "button_submit": "Submit", "cancel_button": "Cancel", "cant_open_event_mode": "Complete level 10 to unlock Event mode", "card_offer_pack": "Card Offer", "card_refresh_cooldown": "New cards in", "card_reroll": "Refresh now", "card_reroll_confirm": "Refresh cards?", "chest_slot_description": "Buy now and permantly increase your chest slot by one", "collect_all_gems": "Collect all gems", "collect_all_poker_chip": "Collect %{number} chip", "collect_blue_ghost": "Collect ${number} blue ghosts", "collect_bone": "Collect %{number} bone", "collect_coral": "Collect %{number} corals", "collect_diamond": "Collect %{number} diamond", "collect_fish_blue": "Collect %{number} blue fish", "collect_fish_green": "Collect %{number} green fish", "collect_fish_puffer": "Collect %{number} puffer fish", "collect_fish_purple": "Collect %{number} purple fish", "collect_fish_red": "Collect %{number} red fish", "collect_gold": "Collect %{number} gold", "collect_hedgehog": "Collect %{number} hedgehog", "collect_metal_box": "Collect %{number} metal boxes", "collect_mineral": "Collect %{number} minerals", "collect_mole": "Collect %{number} mole", "collect_money_bag": "Collect %{number} money bags", "collect_monkey": "Collect %{number} monkeys", "collect_pearl": "Collect %{number} pearls", "collect_rat": "Collect %{number} rat", "collect_rock": "Collect %{number} rock", "collect_safe": "Collect %{number} safe", "collect_scorpion": "Collect %{number} scorpions", "collect_shark_blue": "Collect %{number} blue sharks", "collect_tool_box": "Collect %{number} tool box", "collect_vulture_white": "Collect %{number} white vulture", "collect_wolf": "Collect %{number} wolves", "complete_level": "Complete a level", "confirm_buy_booster": "BUY BOOSTER?", "confirm_buy_card": "BUY CARDS ?", "confirm_buy_chest_slot": "BUY CHEST SLOT ?", "confirm_buy_energy": "BUY ENERGY?", "confirm_buy_gold": "BUY GOLD?", "confirm_buy_ticket_pvp": "BUY TICKETS ?", "confirm_delete_content": "if you want to delete your account, type\n\"DELETE\" into the feild below.", "confirm_delete_title": "Are you sure?", "confirm_signout": "You will lose your progress if you sign out. Do you want to continue?", "current_rank_at": "YOU ARE AT", "current_rank_enter": "YOU'VE ENTERED", "daily_quest_complete_all": "Complete all tasks to get", "daily_reward_no_ads": "Daily ViP Reward + No Ads", "data_account_level": "Account\nLevel", "data_story_mode_level": "Story Mode\nLevel", "data_vip_level": "VIP Level", "deck_0": "DECK 1", "deck_1": "DECK 2", "deck_2": "DECK 3", "deck_3": "Common Set: <PERSON><PERSON>", "delete_account_button": "Delete Account", "delete_account_notify": "If you delete your account you will lose all your game progress, golds, rubies, boosters and purchases!\n\nAccount deletion process might take up to 30 days. Once your account is deleted, this action can’t be reversed.\n\nIf you return to the game before your account is deleted, you can cancel the deletion process and restore your account.", "delete_account_title": "Account Deletion", "delete_button": "Delete", "deleted_content": "You've logged into a game account that is currently being deleted. To cancel the deletion and continue playing, tap the \"Cancel\" button.", "deleted_title": "Account is being deleted", "desc_card_contain": "Contains at least", "desc_let_us_know": "What would you like to see in game", "desc_no_skill": "<color=#5e3c29>Collect <color=#dd33dd>Epic cards</color> or <color=#dd4444>Legendary cards</color> to unlock Skills</color>", "desc_pre_register_reward": "You've got a full set of epic cards!\nUnlock the Customize tab to use them!", "desc_skill_set_bat": "<color=#5e3c29><color=#dd4444><PERSON><PERSON> Skill</color> <outline color=white width=3>UTILITY BELT</outline><br><size=25>Every 10 seconds, you'll get to use a unique random claw</color>", "desc_skill_set_doll": "<color=#5e3c29><color=#dd4444>Epic Skill</color> <outline color=white width=3>MIRROR IMAGE</outline><br><size=25>A mirror image will copy your every move and can pull gold for you</color>", "desc_skill_set_goku": "<color=#5e3c29><color=#dd4444>Epic Skill</color> <outline color=white width=3>KAME BLAST</outline><br><size=25>Go into Super Mode and launch destructive energy balls</color>", "desc_skill_set_iron": "<color=#5e3c29><color=#dd4444><PERSON><PERSON> Skill</color> <outline color=white width=3>DEATH RAY</outline><br><size=25>Fire a laser beam that melts everything in its path</color>", "desc_skill_set_michael": "<color=#5e3c29><color=#dd4444>Epic Skill</color> <outline color=white width=3>MOONWALK</outline><br><size=25>You can walk while launching or pulling the claw</color>", "desc_skill_set_spider": "<color=#5e3c29><color=#dd4444><PERSON><PERSON>ll</color> <outline color=white width=3>WEB SHOOTER</outline><br><size=25>Fire a web that catches a bunch of stuff and pulls them up together</color>", "desc_skill_set_super": "<color=#5e3c29><color=#dd4444><PERSON><PERSON>ll</color> <outline color=white width=3>SUPER POWERS</outline><br><size=25>You now have a super claw, can freeze TNTs and fire lasers.\nBut harmful Kryptonite will line the bottom of the level.</color>", "desc_skill_set_thor": "<color=#5e3c29><color=#dd4444>Epic Skill</color> <outline color=white width=3>HAMMER</outline><br><size=25>Throw an invincible hammer rather than a dynamite</color>", "desc_skill_set_wk": "<color=#5e3c29><color=#dd4444>Epic Skill</color> <outline color=white width=3>MONKEY ARMY</outline><br><size=25>Send up to 3 monkeys to get stuff for you</color>", "download_button_text": "Load Data", "download_data_confirm_message": "Do you want to download and import data in previous?", "energy_offer_pack": "\bEnergy Offer", "Enerji teklifi": "<PERSON><PERSON><PERSON>er", "event_jaki_desc": "Explore <PERSON><PERSON>’s World: Dive into <PERSON><PERSON>'s exciting world and claim rewards!", "event_jaki_unlocked": "You have unlocked <PERSON>aki event", "event_title": "Welcome to The Inferno: Survive today's Deadly Sin to claim a Legendary Chest!. Warning : Level's difficulty are extreme", "event_unlock": "You have unlocked the 7 Deadly Sins event!", "Friday": "FRIDAY", "get_info_failed_message": "Get data failed", "goldbar_offer_pack": "Gold Offer", "help_ants_key": "Ants: Everything's bigger in vega! and they bite, too!", "help_bag_money_key": "Bags money: Grab this... Trust us! You could find money.", "help_bones_key": "Bones: They won't hurt you... Trust us!", "help_broke_tv_key": "Brokeb TVs: Why watch TV when there is Gold to be mined? Not worth the time.", "help_broken_wall_key": "Broken Walls: Why would you want to lift huge heavy walls? ", "help_chips_key": "Chips: Collect all four chips to get something specical.", "help_coral_key": "Corals: Not worth much and very heavy. Can be blown up with dynamite.", "help_dinner_plates_key": "Dinner Plates: Skip the Buffet? All the Strength you need is right here!", "help_dynamite_key": "Stick of Dynamite - can be used to blow up unwanted objects at the end of your line", "help_fish_key": "Fish: Yes, you can mine them too!", "help_gem_key": "Gem: Collect all four gems to get something specical.", "help_ghost_key": "Ghost: Grab blue ghosts only. After you grab one, another spirit will turn blue and their value will increase.", "help_gold_key": "Gold: The meaning of life! Get as much as you can! The bigger it is the better the more it's worth.", "help_grab_key": "Grab bags: You could find money, explosives, or even new mining gadgets!", "help_hansel_character": "<PERSON>el - Gold miner is rival, Catch him if you can.", "help_info_guide1": "Play a level in story mode", "help_info_guide2": "Pick up the chest reward", "help_info_guide3": "Open chest to receive card", "help_info_guide4": "Complete new character card set", "help_info_guide5": "Claim <PERSON>!", "help_info_pick_up": "Tap the item you want to pick it up", "help_info_thank": "Thank you for reading the information", "help_Joey_character": "Joey joe joe the Crook - Catch him before he takes your money!\"", "help_julie_character": "Julie - Sweet niece of the gold miner. Let her find stuff for you", "help_laser_claw": "Laser Claw - Take perfect aim with laser pointer.", "help_lava_key": "Lava monsters: Your <PERSON>law can handle the cool stuff, but when they heats up... don't touch them!", "help_mask_key": "Mask: Collect all four Masks to get something specical.", "help_mouse_key": "Mouse: Harmless fellas, but may be found carrying diamonds. And they like to steal things with their wheelbarrows.", "help_pearl_key": "Pearl: Grab this... Trust us!", "help_rock_key": "Rocks: Just plain rocks. Not worth much and very heavy. Can be blown up with dynamite", "help_safe_key": "Safe: Blow up safes to pull them up really fast.", "help_scorpion_key": "<PERSON><PERSON><PERSON>: They won't hurt you... Trust us!", "help_soda_machine_key": "Soda Machine: Have you ever Tried to lift on of these before?", "help_spike_claw": "<PERSON>law - Breaks throught worthless objects to get to the good stuff.", "help_super_claw": "Super Claw - <PERSON><PERSON> in huge stuff with the strength!", "help_tnt_key": "Grab this and blow up everything around it. This could be good or very, very bad.", "help_treasure_key": "Treasure Chests: Grab Bags of the sea.", "help_water_bomb_key": "Water bomb: These aren't the mines you're looking! These metal things go boom.", "help_wolf_key": "Wolf: Wolves will destroy anything in their path if you don't catch them.", "iap_purchase_failed": "Purchase Failed", "info": "Info", "jaki_event_unlock": "All cards in the Jaki set must be at least level 2 to unlock Jaki mode", "login_facebook_play": "Login to Facebook to play!", "login_reward_title": "Login Successfully!", "Monday": "MONDAY", "name_car_ariana": "<PERSON><PERSON>", "name_car_bat": "<PERSON>", "name_car_cactus": "<PERSON><PERSON><PERSON>", "name_car_doll": "<PERSON>", "name_car_fighter": "Fighter Cart", "name_car_goku": "<PERSON><PERSON>", "name_car_iron": "Iron Cart", "name_car_jaki": "<PERSON><PERSON>", "name_car_lord": "<PERSON> Lord <PERSON>", "name_car_miner": "Basic Cart", "name_car_pirate": "Pirate Cart", "name_car_samurai": "Samurai Cart", "name_car_spider": "<PERSON>", "name_car_thor": "<PERSON>", "name_char_ariana": "<PERSON><PERSON>", "name_char_bat": "Bat Miner", "name_char_cactus": "Cactus", "name_char_doll": "<PERSON>", "name_char_fighter": "Fighter", "name_char_goku": "Super Saiyan", "name_char_iron": "Iron Miner", "name_char_jaki": "<PERSON><PERSON>", "name_char_lord": "Dark Lord", "name_char_miner": "Gold Miner", "name_char_pirate": "Pirate", "name_char_samurai": "Samurai", "name_char_spider": "<PERSON>", "name_char_thor": "<PERSON>", "name_claw_ariana": "Throwing Lotus", "name_claw_bat": "<PERSON>", "name_claw_cactus": "Cactus Spike", "name_claw_doll": "Blue Claw", "name_claw_fighter": "Cutlass", "name_claw_goku": "<PERSON><PERSON>", "name_claw_iron": "Iron Claw", "name_claw_jaki": "Red Claw", "name_claw_lord": "Sword", "name_claw_miner": "Basic Claw", "name_claw_pirate": "Cutlass", "name_claw_samurai": "Kunai", "name_claw_spider": "<PERSON>", "name_claw_thor": "Lightning Claw", "name_pet_ariana": "<PERSON>", "name_pet_bat": "Bat", "name_pet_cactus": "<PERSON><PERSON><PERSON>", "name_pet_doll": "<PERSON><PERSON>", "name_pet_fighter": "Crocodile", "name_pet_goku": "Star Ball", "name_pet_iron": "<PERSON><PERSON><PERSON><PERSON>", "name_pet_jaki": "Cat", "name_pet_lord": "Lich", "name_pet_miner": "Koala", "name_pet_null": "None", "name_pet_pirate": "<PERSON><PERSON><PERSON>", "name_pet_samurai": "Crane", "name_pet_spider": "<PERSON><PERSON><PERSON>", "name_pet_thor": "<PERSON><PERSON><PERSON><PERSON>", "name_rope_ariana": "<PERSON><PERSON>", "name_rope_bat": "<PERSON>", "name_rope_cactus": "Cactus <PERSON>", "name_rope_doll": "<PERSON>", "name_rope_fighter": "Fighter Rope", "name_rope_goku": "<PERSON><PERSON>", "name_rope_iron": "Iron Rope", "name_rope_jaki": "<PERSON><PERSON>", "name_rope_lord": "Dark Lord Rope", "name_rope_miner": "Normal Rope", "name_rope_pirate": "Pirate Rope", "name_rope_samurai": "Samurai Rope", "name_rope_spider": "<PERSON>", "name_rope_thor": "<PERSON>", "name_set_ariana": "Rare Set: <PERSON><PERSON>", "name_set_bat": "Legendary Set: <PERSON>", "name_set_cactus": "Common Set: Cactus", "name_set_doll": "Epic Set: <PERSON>", "name_set_fighter": "Rare Set: Fighter", "name_set_goku": "Epic Set: Super Saiyan", "name_set_iron": "Legendary Set: Iron Miner", "name_set_jaki": "Common Set: <PERSON><PERSON>", "name_set_lord": "Rare Set: Fighter", "name_set_miner": "Common Set: Gold Miner", "name_set_pirate": "Common Set: Pirate", "name_set_samurai": "Rare Set: Samurai", "name_set_spider": "Legendary Set: <PERSON>", "name_set_thor": "Epic Set: <PERSON>", "no_internet_notify": "You're offline. Please reconnect to continue.", "null": "_", "previous_area_is": "Previous area:", "previous_area_not_complete": "Reach maximum stars in the previous area to continue", "profile_best_league_text": "Best League", "profile_best_level": "Furthest Levels ", "profile_best_rank_text": "Highest Rank", "profile_best_win_streak_text": "Longest Win Streak", "profile_card_found": "Card Found", "profile_chest_acquired": "Chest Opened", "profile_fast_win": "Fast Wins", "profile_first_try_win": "First Try Wins", "profile_level_full_clear": "Level Full Clear", "profile_miner_friends": "Miner Friends", "profile_story_text": "Story", "profile_three_stars_level": "3 Stars Levels", "profile_title_text": "PROFILE", "profile_win_rate": "Win %", "profile_win_text": "Win ", "pvp_day_left": "Ends in %{number} days", "pvp_finding_opponent": "FINDING\nOPPONENT...", "pvp_introduction": "May the best one win", "pvp_result_draw": "DRAW!", "pvp_result_lose": "WE'LL WIN \nTHE NEXT ONE!", "pvp_result_win": "YOU WIN!", "pvp_streak_number": "%{number} wins", "pvp_streak_rewards": "Win many time in a row\nfor a sweet rewards!", "pvp_unlock": "You have open Pvp Mode.Compete against other player for sweet rewards", "rank_hint": "Be at the top 3 ranks by the end of the week to advance to the coveted ", "rank_hint_down": "<color=#5E3C29>Be careful: If you are at the <color=#BE0000>bottom 3 Ranks</color> by the end of the week,\nyou will <color=#BE0000>Move Down</color> to the previous League</color>", "rank_hint_max": "Great! Keep yourself above the bottom 3 rank by the end of the week to stay at", "rank_hint_up": "<color=#5E3C29>To <color=#BE0000>Move Up</color> to the next League,\nstay at the <color=#BE0000>top 3 Ranks</color> of the current League by the end of the week</color>", "rescue_julie": "Rescue Julie %{number} times", "restore_purchase_success": "Purchases restored successfully.", "ruby_offer_pack": "<PERSON>", "Saturday": "SATURDAY", "save_progress_button": "Save Progress", "save_progress_hint": "More fun with friends!", "save_progress_success": "Progress saved successfully", "score_pvp": "PVP MODE", "score_story_mode": "STORY MODE", "share_failed_text": "Your sharing was not successful.", "signout_button": "Sign out", "signout_success": "You are logged out of your current account. Please log in to avoid missing the current progress.", "skill_activate_remind": "Equip the full set of this card to activate!", "splash_play_label": "Tap To Play", "stats_description_bonus": "Bonus:\nIncreases the gold reward of some items.", "stats_description_luck": "Luck: \nIncreases gold rewards from money bags", "stats_description_speed": "Speed:\nIncreases your movement speed.", "stats_description_stability": "Stability:\nReduces your claw's random rotation after it returns.", "stats_description_strength": "Strength:\nIncreases your pull speed.", "stats_description_title": "STATS DESCRIPTION", "story_leader_title": "Story Leaderboard", "Sunday": "SUNDAY", "temple_claim_reward": "Claim reward", "temple_collect_pickaxes": "Collect Pickaxes", "temple_complete the levels": "Complete the levels", "temple_completed": "You have completed the Hidden Temple mini-game.", "temple_find_gem_and_open_gates": "Find gems and open gates", "temple_has_end": "The mini-game Hidden Temple duration has ended.\nThis mode will no longer be available.", "temple_hidden_temple": "Hidden Temple", "temple_no_pickaxes_left": "No Pickaxes Left!", "temple_tap_the_chest_to_open_it": "Tap the chest to open it", "temple_tutorial": "Use your pickaxes to dig the ground and uncover the gems", "temple_tutorial_no_pickaxes": "No pickaxes left, play story mode to collect more pickaxes", "temple_unlock": "You have unlocked Mini game Hidden Temple", "temple_you_won_the_game": "You've won the game!", "text_about": "About", "text_all": "All", "text_area_clear": "AREA CLEAR!", "text_arizona_complete": "Complete area 10, 11, 12", "text_arizona_map": "SCORCHING!", "text_arizona_progress": "%{number} area cleared", "text_australia_complete": "Complete area 1, 2, 3", "text_australia_map": "<PERSON><PERSON><PERSON>", "text_australia_progress": " %{number} area cleared", "text_auto": "AUTO", "text_auto_equip": "Auto equip", "text_auto_mine": "Get auto play in 10 minutes", "text_auto_mine_acquire": "Get 10 minutes", "text_beast_master": "BEAST MASTER", "text_beast_progress": " %{number} Pets adopted", "text_big_gold": "GREED IS GOOD", "text_big_gold_collect": "Collect %{number} gold nuggets", "text_blue_ghost": "GHOST BUSTER!", "text_blue_ghost_progress": " %{number} Blue Ghosts caught", "text_boomerang_catch": "Catch %{number} boomerang", "text_boomerang_catcher": "NICE CATCH!", "text_boomerang_progress": " %{number} Boomerang progress", "text_booster": "<PERSON><PERSON><PERSON>", "text_booster_pack": "BOOSTER PACK", "text_button_claim": "<PERSON><PERSON><PERSON>", "text_button_claim_now": "Claim now", "text_button_claim_uppper": "<PERSON><PERSON><PERSON>", "text_button_complete": "Completed", "text_car_collect": "Collect %{number} cars", "text_car_collect_progress": "%{number} cars collect", "text_car_collector": "HOT WHEEL", "text_card": "CARD", "text_cards_pinnacle": "Cards Pinnacles", "text_cards_pinnacle_collector": "One card reached level %{number}", "text_cards_pinnacle_progress": "The highest level card reached is %{number}", "text_cardview_ally": "PET", "text_cardview_car": "CAR", "text_cardview_character": "CHARACTER", "text_cardview_claw": "CLAW", "text_cardview_loadout": "DECK", "text_cardview_rope": "ROPE", "text_catch_hansel_progress": " %{number} Boat caught", "text_catch_theif_progress": " %{number} Thief catched", "text_catch_thief": "Catch %{number} thief with wanted poster", "text_character_conqueror": "Characters Conqueror", "text_character_conqueror_collector": "One set cards reached level %{number}", "text_character_conqueror_progress": "The highest level set cards reached is %{number}", "text_chest": "WHAT'S IN THE BOX?!", "text_chest_copper": "Bronze chest", "text_chest_diamond": "Diamond chest", "text_chest_free": "Free Chest", "text_chest_gold": "Gold chest", "text_chest_open": "Open %{number} chests", "text_chest_progress": " %{number} Chests opened", "text_chest_silver": "Silver chest", "text_chest_star": "Legendary chest", "text_chip_collect": "ALL IN!", "text_chip_collector": "CHIP COLLECTOR", "text_chip_progress": "Collect all casino chips in a level to complete", "text_claim": "CLAIM", "text_cloud": "In cloud storage", "text_collected": "Collected", "text_coming_soon": "Coming soon...", "text_common": "Common", "text_compete_online": "Compete Online!", "text_complete": "Completed", "text_copy_right": "Copyright 2019 © Senspark Ltd.", "text_currently": "Currently", "text_daily_reward": "DAILY REWARD", "text_day": "Day %{number}", "text_destroyer": "DESTROYER", "text_destroyer_hit": "Hit a detonator", "text_destroyer_progress": " %{number} Detonator hit", "text_device": "On this device", "text_diamond": "OOH SHINY!", "text_diamond_collect": "Collect %{number} big diamond", "text_diamond_progress": " %{number} Big diamond collected", "text_email": "EMAIL", "text_enemies": "ENEMIES", "text_energy": "ENERGY", "text_energy_in": "in", "text_envy": "ENVY", "text_epic": "Epic", "text_event": "7 DEADLY SINS", "text_event_area_clear": "You have conquered a Deadly Sin! Take a rest and be prepared for tomorrow's gauntlet...", "text_event_jaki": "<PERSON><PERSON>", "text_event_locked": "Complete area 1 to unlock", "text_event_mode_clear": "The path of redemption", "text_event_mode_clear_collector": "Complete %{number} days of Event", "text_event_mode_clear_progress": "Completed %{number} days in event mode", "text_exit_title": "DO YOU WANT TO EXIT GAME?", "text_facebook": "FACEBOOK", "text_facebook_progress": "Log into facebook to complete!", "text_facebook_share": "SHARE FACEBOOK", "text_facebook_share_lower_case": "Share facebook", "text_fanpage": "FANPAGE", "text_finished": "Finished", "text_fish_catch": "Catch %{number} fishes", "text_free": "Free", "text_free_chest": "Free Chest", "text_friends": "FRIENDS", "text_game_clear": "Game Clear!", "text_game_congrat_desc": "You have finished\nall the current levels!\nMore levels coming soon!", "text_game_congratulation": "CONGRATULATIONS!", "text_gem_collect": "Collect all 4 gems", "text_gem_collector": "MAGPIE", "text_gem_progress": "Collect all gems in a level to complete", "text_get_energy": "Get ", "text_get_more": "Get more", "text_get_more_at": "Get more at the shop!", "text_get_more_card": "Get more card", "text_get_more_gold": "Get more golds", "text_get_more_pvp_ticket": "GET MORE TICKETS", "text_get_more_ruby": "Get more ruby", "text_ghost_catch": "Catch %{number} blue ghost", "text_gift_code": "GIFT CODE", "text_gluttony": "GLUTTONY", "text_gold_bars": "GOLD BARS", "text_gold_progress": " %{number} gold nuggets collected", "text_google": "GOOGLE+", "text_greed": "GREED", "text_hansel_boat": "NICE BOAT", "text_hansel_boat_catch": "Catch <PERSON><PERSON>'s boat", "text_hard": "HARD", "text_have_miner": "Have %{number} miners", "text_have_pet": "Have %{number} pets", "text_hawaii_complete": "Complete area 4, 5, 6", "text_hawaii_map": "ALOHA!", "text_hawaii_progress": " %{number} area cleared", "text_help": "HELP", "text_high_score": "HIGH SCORE", "text_hint": "HINT", "text_how_lowercase": "How To Play", "text_how_uppercase": "HOW TO PLAY", "text_in_deck": "IN DECK %{number}", "text_incomplete": "Incomplete", "text_invite": "MINERS, ASSEMBLE!", "text_invite_friend": "Invite %{number} friend", "text_invite_progress": " %{number} Friend invited", "text_jackpot": "JACKPOT!", "text_jackpot_progress": " %{number} Lottery won", "text_jackpot_win": "Win a slot machines", "text_jaki_button": "<PERSON><PERSON>", "text_jaki_event_area_clear": "You have conquered the Jaki Event.\nCongratulations!", "text_king_fisher": "KING FISHER", "text_king_fisher_progress": " %{number} Fish caught", "text_king_miner": "KING OF MINERS", "text_language": "Language", "text_las_vegas_progress": " %{number} area cleared", "text_last_vip": "Your VIP level is max", "text_lasvegas_complete": "Complete area 7, 8, 9", "text_lasvegas_map": "What happens in Vegas...", "text_lava_ghost": "EXORCISM", "text_lava_ghost_kill": "Kill %{number} lava ghost", "text_lava_ghost_progress": " %{number} <PERSON><PERSON> killed", "text_leader_board": "Leaderboard", "text_legendary": "Legendary", "text_let_us_know": "LET US KNOW", "text_level": "LEVEL", "text_level_area": "LEVEL", "text_level_lock": "Unlocks\nat account level %{number}", "text_level_maxed": "Level Maxed", "text_level_number": "Level %{number}", "text_level_up": "LEVEL UP!", "text_like_fanpage": "LIKE FANPAGE", "text_like_fanpage_lower_case": "Like fanpage", "text_like_fanpage_progress": "Like our fanpage to complete!", "text_log_into_facebook": "LOG IN", "text_log_out_facebook": "LOG OUT", "text_login_facebook": "FRIENDLY COMPETITION", "text_login_facebook_lower_case": "Login facebook", "text_login_fb": "LOGIN", "text_lose_hard": "We'll get 'em next time!", "text_lose_light": "So close!", "text_lose_medium": "Almost there!", "text_lucky": "LUCKY", "text_lucky_progress": " %{number} Times spinned", "text_lucky_spin": "Spin lucky wheel %{number} times", "text_lucky_wheel": "LUCKY WHEEL", "text_lust": "LUST", "text_mask_collection": "Collect all 4 different mask", "text_mask_collector": "MASQUERADE", "text_mask_progress": "Collect all mask in a level to complete ", "text_max": "MAX", "text_max_tier": "You've got max tier.", "text_message": "Message", "text_miner_progress": " %{number} Miners collected", "text_more_game": "More games", "text_new": "New", "text_next_daily_task_in": "Next Daily Task In", "text_next_level": "NEXT LEVEL", "text_next_level_game": "NEXT LEVEL", "text_next_tier": "Next tier", "text_no": "No", "text_no_ads": "No Ads", "text_not_enough_energy": "Not enough energy", "text_not_enough_gold": "Not enough golds", "text_not_enough_rubies": "Not enough rubies", "text_not_enough_tickets": "Not enough tickets", "text_object": "OBJECT", "text_offer": "OFFER", "text_ok": "ok", "text_open_in": "Open in", "text_open_now": "Open now", "text_open_now_capital": "Open Now!", "text_or_here": "or here", "text_pearl_collect": "Collect %{number} pearl", "text_pearl_driver": "PEARL DRIVER", "text_pearl_progress": " %{number} Pearl collected", "text_play": "PLAY", "text_policeman": "POLICEMAN", "text_potentially": "Potentially", "text_pride": "\bPRIDE", "text_pvp": "PVP", "text_pvp_locked": "Achieve account level 6 to unlock", "text_rare": "Rare", "text_rate": "Rate", "text_rate_description": "Do you want to review this game on Google Play?", "text_rate_game": "Rate game", "text_rate_progress": "Rate our game to complete!", "text_rate_select_no": "No Thanks", "text_rate_select_yes": "Go Review", "text_rate_title": "Review Game", "text_refill_now": "Refill now", "text_rescue_julie": "A HELPING CLAW", "text_rescue_julie_lower_case": "Rescue <PERSON> the niece", "text_rescue_progress": " %{number} Times <PERSON> is rescued", "text_reset": "RESET", "text_reset_game": "RESET GAME", "text_reset_guide": "Press and hold for 3 seconds to reset.", "text_restore_purchase": "RESTORE PURCHASE", "text_reveal_all": "Reveal All", "text_reward": "REWARDS", "text_rock": "THE SWORD IN THE STONE", "text_rock_break": "Break %{number} rock", "text_rock_progress": " %{number} Rock broken", "text_ruby": "RUBY", "text_sale_off": "Sale off %{number}%", "text_select_booster": "SELECT BOOSTERS", "text_setting": "SETTING", "text_share": "SHARE", "text_share_facebook ": "Share facebook %{number} time", "text_share_facebook_progress": " %{number} Times share", "text_show_score": "SHOW SCORE", "text_skip": "<PERSON><PERSON>", "text_sloth": "SLOTH", "text_so_close": "So close", "text_social": "SOCIAL", "text_spin_more": "Spin more to unlock the next tier!", "text_tap_to_claim_reward": "Tap to claim reward", "text_tap_to_close": "Tap to close", "text_tap_to_continue": "Tap to continue", "text_tap_to_start_timer": "Tap to start timer", "text_target": "Target: %{number}", "text_tasks": "Tasks", "text_throw_dynamite": "BLOW UP PULLING ITEM", "text_throw_dynamite_tutorial": "Tap here to destroy", "text_ticket": "LUCKY TICKET", "text_ticket_pvp": "PVP TICKET", "text_tier": "Tier %{number}", "text_title_about": "ABOUT", "text_title1_vip_reward_dialog": "VIP LEVEL UP", "text_title2_vip_reward_dialog": "Your VIP level has reached Level %{number}", "text_total_card": "Total", "text_total_reward": "TOTAL REWARD", "text_uncollected": "Uncollected", "text_unlimited": "UNLIMITED", "text_unlimited_value": "Unlimited\n%{number}:00:00", "text_unlocked": "Unlocked", "text_upgrade": "Upgrade", "text_use_in_deck": "USE IN DECK %{number}", "text_valued_customer_description": "Buy %{number} Booster", "text_valued_customer_progress": " %{number} Boosters", "text_values_customer": "ARMED TO THE TEETH", "text_values_customer_buy": "Buy %{number} item in shop", "text_version": "Version 2.0.0", "text_vip_reward_energy": "Auto refill energy limit +30", "text_warning": "Warning: Doing this will reset your level progress and boosters.", "text_website": "WEBSITE", "text_win_level_to_keep_it": "Win level to keep it !", "text_wrath": "WRATH", "text_yes": "Yes", "text_you_got": "YOU'VE GOT", "text_you_only_need": "You only need more", "text_youtube": "YOUTUBE", "thank_for_preregister": "THANKS FOR PRE-REGISTER!!!", "thank_last_vip": "Senspark and the Gold Miner team would like to thank you for your generous contributions! All the best to you and happy mining!", "Thursday": "THURSDAY", "title_achievement": "ACHIEVEMENTS", "title_auto_mine": "Mine Idle", "title_bag": "BAG", "title_chest": "CHEST", "title_claim_free_daily": "<PERSON><PERSON><PERSON>", "title_daily_task": "DAILY TASKS", "title_fanpage": "FANPAGE", "title_invite": "INVITE", "title_join_game": "JOIN THOUSANDS OF PLAYERS", "title_last_vip": "Last Vip", "title_message": "MESSAGE", "title_new_full_card": "New full card set collected", "title_next_level": "NEXT LEVEL", "title_next_vip": "Next Vip", "title_out_of_time": "OUT OF TIME", "title_player_online": "%{number} players online", "title_promotion_received": "Promotion Received", "title_select_booster": "SELECT BOOSTERS", "title_slot_full": "Slots full", "title_try_again": "TRY AGAIN?", "title_you_win": "YOU WIN!", "Tuesday": "TUESDAY", "tutorial_auto_play": "Tap the auto button to let your miner play the game for you", "tutorial_chip": "Grab all 4 chip for 2000 gold!", "tutorial_detonator": "All TNTs will explode if you grab the detonator!", "tutorial_diamond_coral": "Don't grab diamonds when the corals close!", "tutorial_dynamite": "You can grab Live Dynamites!", "tutorial_gem": "Grab all 4 gems for 2000 gold!", "tutorial_ghost": "Grab all blue ghosts for 3000 gold!", "tutorial_lava": "Angry lava blows up when grabbed!", "tutorial_mask": "Grab all 4 tiki masks for 2000 gold!", "tutorial_mineral": "Blow up the mineral to pull it faster!", "tutorial_mining": "Tap to launch claw.\nGrab gold and diamonds to win!", "tutorial_moving": "Tap the rail to move!", "tutorial_next_level": "Tap to play the next level", "tutorial_niece_stuck": "Rescue <PERSON> for 1000 gold!", "tutorial_pearl": "Don't grab pearls when its mouth is closed!", "tutorial_pick_up": "Tap the pick up button to pick up any item.", "tutorial_porcupine": "Don't grab porcupines when they're rolling!", "tutorial_quart": "Grab all 4 quart for 2000 gold!", "tutorial_red_rock": "Hot Red Rocks explode when grabbed!", "tutorial_robber": "Grab the robber to scare him off!", "tutorial_safe": "Blow up the safe to pull money inside faster!", "tutorial_slots": "Grab the slots! get gold by matching symbols!", "tutorial_spike": "This lets you break trash!", "tutorial_super": "This lets you pull faster AND pull big stuff!", "tutorial_tnt": "TNTs explode when grabbed!", "tutorial_vulture": "Grab all white vultures for 3000 gold!", "tutorial_white_wolf": "Every 7 seconds, White Wolves call Black ones!", "unlock_at_level": "unlock at story mode level %{number}", "unlock_jaki_popup": "You have unlocked the event mode. Test your skills in this challenging journey", "upgrade_gives": "Upgrade gives", "upload_data_failed_message": "Upload game failed", "upload_data_success_message": "Upload game successfully!", "vip_label_benefits": "Benefits", "vip_label_daily_reward": "Daily Reward", "vip_label_free_chest": "Free chest time -%{number}%", "vip_label_free_daily": "Daily VIP Rewards", "vip_label_player_level": "YOUR VIP LEVEL", "vip_label_progress_title": "Spend %{string} more in game to unlock next vip level!", "vip_label_pvp_match": "Pvp match win gold +%{number}%", "vip_label_remove_ads": "Remove Ads", "vip_label_story_level": "Story level win gold +%{number}%", "vip_label_time_reward": "1-Time Reward", "vip_level_up": "VIP LEVEL %{number} REWARD", "vip_offer_pack": "Vip + No Ads Offer", "Wednesday": "WEDNESDAY", "win_slot_machine": "Win slot machine %{number} times", "text_all_event_title": "EVENTS", "text_select_button": "Select", "text_area_title": "Areas", "text_area": "Area %{number}", "text_story": "STORY", "text_daily_task_newline": "\bDAILY TASK", "text_describe_vip_offer": "\bUpgrade to VIP 4 instantly", "profile_levels_complete": "Level completed", "piggy_bank": "PIGGY BANK", "piggy_bank_collect": "Collect more Rubies by completing levels", "piggy_bank_full": "Your Piggy Bank is full! Purchase now to open it", "piggy_bank_last_chance": "Your last chance to get the Piggy Bank", "text_tap_to_skip": "Tap to skip", "text_use_in_loadout": "Use in deck %{number}", "txt_timeout_extra_time": "+20s to keep playing!", "txt_timeout_notice": "You will lost your activity progress!", "txt_timeout_watchad": "Watch ads", "txt_timeout_playon": "Play on!", "txt_edit_profile": "Edit Profile", "txt_edit": "Edit", "txt_save": "Save", "txt_enter_name": "Enter Name", "txt_what_your_name": "What is your name?", "txt_type_your_name": "Type your name", "you_have_more_change_name": "You have %{number} more chances to change your name.", "txt_continue": "Continue", "txt_name": "Name", "txt_avatar": "Avatar", "txt_frame": "<PERSON>ame", "you_have_one_change_name": "You have 1 more chance to change your name.", "txt_shop": "Shop", "unlock_vip": "Unlock at VIP level %{number}", "unlock_account": "Unlock at account level %{number}", "txt_minishop_title": "Hot Deal", "txt_getmore_button": "More offer", "hidden_temple_what_is": "What is Hidden Temple?", "piggy_what_is": "What is the Piggy Bank?", "piggy_win_levels": "Win levels – <PERSON><PERSON><PERSON> every time you complete a level!", "piggy_cashback_purchases": "Cashback on purchases – Get back a portion of spent Rubies into the Piggy Bank!", "piggy_you_can_unlock": "You can unlock your Piggy Bank once the minimum is reached - fill it up for the best deal!", "leaderboard_top_player_title": "Hall of Miners", "leaderboard_top_weekly_title": "Weekly Challenge", "leaderboard_player_page": "Players", "leaderboard_weekly_page": "Weekly", "ranking_txt": "RANK", "world_txt": "World", "nation_txt": "Nation", "leaderboard_reward_text": "You finished the Weekly Challenge season in %{string} place", "leaderboard_try_again": "Try Again", "play_pass": "PLAY PASS", "text_in_use": "In use", "text_upgraded": "Upgraded", "pass_more_with_gold_pass": "More rewards with Gold Pass!", "text_complete immediately": "Complete immediately", "text_watch_ads": "Watch Ads", "pass_attempts_remaining": "%{number} attempts remaining today", "pass_attempt_remaining": "%{number} attempt remaining today", "pass_active_gold_pass": "Activate the <color=#ffff00>Gold Pass</color> to unlock the Bonus Bank", "pass_gold_pass_grants": "<outline color=#000000 width=4>The <color=#ffff00>Gold Pass</color> grants you\naccess to premium rewards</outline>", "text_special_bonuses": "Special Bonuses", "text_gold_pass": "GOLD PASS", "text_watch_now": "Watch now!", "pass_collect_play_pass": "<outline color=#000000 width=4>Collect Play Pass points & unlock amazing rewards!\nUpgrade to <color=#ffff00>Gold Pass</color> for even bigger prizes!</outline>", "pass_what_is": "What is the Play Pass!", "pass_win_level": "Winning Level", "pass_collect_points": "Collect Play Pass points", "pass_unlock_premium": "Unlock premium rewards with <color=#ffff00>Gold Pass!</color>\n", "support_txt": "Support", "text_bonus_bank": "BONUS BANK", "AF": "Afghanistan", "AL": "Albania", "DZ": "Algeria", "AS": "American Samoa", "AD": "Andorra", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AQ": "Antarctica", "AG": "Antigua & Barbuda", "AR": "Argentina", "AM": "Armenia", "AW": "Aruba", "AU": "Australia", "AT": "Austria", "AZ": "Azerbaijan", "BS": "Bahamas", "BH": "Bahrain", "BD": "Bangladesh", "BB": "Barbados", "BY": "Belarus", "BE": "Belgium", "BZ": "Belize", "BJ": "Benin", "BM": "Bermuda", "BT": "Bhutan", "BO": "Bolivia", "BQ": "Bonaire, Sint Eustatius & Saba", "BA": "Bosnia & Herzegovina", "BW": "Botswana", "BV": "Bouvet Island", "BR": "Brazil", "IO": "British Indian Ocean Territory", "BN": "Brunei Darussalam", "BG": "Bulgaria", "BF": "Burkina Faso", "BI": "Burundi", "CV": "Cabo Verde", "KH": "Cambodia", "CM": "Cameroon", "CA": "Canada", "KY": "Cayman Islands", "CF": "Central African Republic", "TD": "Chad", "CL": "Chile", "CN": "China", "CX": "Christmas Island", "CC": "Cocos Islands", "CO": "Colombia", "KM": "Comoros", "CD": "Congo (Democratic Republic)", "CG": "Congo", "CK": "Cook Islands", "CR": "Costa Rica", "HR": "Croatia", "CU": "Cuba", "CW": "Curaçao", "CY": "Cyprus", "CZ": "Czechia", "CI": "Côte d'Ivoire", "DK": "Denmark", "DJ": "Djibouti", "DM": "Dominica", "DO": "Dominican Republic", "EC": "Ecuador", "EG": "Egypt", "SV": "El Salvador", "GQ": "Equatorial Guinea", "ER": "Eritrea", "EE": "Estonia", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Ethiopia", "FK": "Falkland Islands", "FO": "Faroe Islands", "FJ": "Fiji", "FI": "Finland", "FR": "France", "GF": "French Guiana", "PF": "French Polynesia", "TF": "French Southern Territories", "GA": "Gabon", "GM": "Gambia", "GE": "Georgia", "DE": "Germany", "GH": "Ghana", "GI": "Gibraltar", "GR": "Greece", "GL": "Greenland", "GD": "Grenada", "GP": "Guadeloupe", "GU": "Guam", "GT": "Guatemala", "GG": "Guernsey", "GN": "Guinea", "GW": "Guinea-Bissau", "GY": "Guyana", "HT": "Haiti", "HM": "Heard Island and McDonald Islands", "VA": "Holy See", "HN": "Honduras", "HK": "Hong Kong", "HU": "Hungary", "IS": "Iceland", "IN": "India", "ID": "Indonesia", "IR": "Iran", "IQ": "Iraq", "IE": "Ireland", "IM": "Isle of Man", "IL": "Israel", "IT": "Italy", "JM": "Jamaica", "JP": "Japan", "JE": "Jersey", "JO": "Jordan", "KZ": "Kazakhstan", "KE": "Kenya", "KI": "Kiribati", "KP": "North Korea", "KR": "South Korea", "KW": "Kuwait", "KG": "Kyrgyzstan", "LA": "Laos", "LV": "Latvia", "LB": "Lebanon", "LS": "Lesotho", "LR": "Liberia", "LY": "Libya", "LI": "Liechtenstein", "LT": "Lithuania", "LU": "Luxembourg", "MO": "Macao", "MG": "Madagascar", "MW": "Malawi", "MY": "Malaysia", "MV": "Maldives", "ML": "Mali", "MT": "Malta", "MH": "Marshall Islands", "MQ": "Martinique", "MR": "Mauritania", "MU": "Mauritius", "YT": "Mayotte", "MX": "Mexico", "FM": "Micronesia", "MD": "Moldova", "MC": "Monaco", "MN": "Mongolia", "ME": "Montenegro", "MS": "Montserrat", "MA": "Morocco", "MZ": "Mozambique", "MM": "Myanmar", "NA": "Namibia", "NR": "Nauru", "NP": "Nepal", "NL": "Netherlands", "NC": "New Caledonia", "NZ": "New Zealand", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigeria", "NU": "Niue", "NF": "Norfolk Island", "MP": "Northern Mariana Islands", "NO": "Norway", "OM": "Oman", "PK": "Pakistan", "PW": "<PERSON><PERSON>", "PS": "Palestine", "PA": "Panama", "PG": "Papua New Guinea", "PY": "Paraguay", "PE": "Peru", "PH": "Philippines", "PN": "Pitcairn", "PL": "Poland", "PT": "Portugal", "PR": "Puerto Rico", "QA": "Qatar", "MK": "North Macedonia", "RO": "Romania", "RU": "Russia", "RW": "Rwanda", "RE": "Réunion", "BL": "<PERSON>", "SH": "Saint Helena", "KN": "Saint Kitts and Nevis", "LC": "Saint Lucia", "MF": "<PERSON> (French part)", "PM": "Saint Pierre and Miquelon", "VC": "Saint Vincent and the Grenadines", "WS": "Samoa", "SM": "San Marino", "ST": "Sao Tome and Principe", "SA": "Saudi Arabia", "SN": "Senegal", "RS": "Serbia", "SC": "Seychelles", "SL": "Sierra Leone", "SG": "Singapore", "SX": "Sint Maarten", "SK": "Slovakia", "SI": "Slovenia", "SB": "Solomon Islands", "SO": "Somalia", "ZA": "South Africa", "GS": "South Georgia and the South Sandwich Islands", "SS": "South Sudan", "ES": "Spain", "LK": "Sri Lanka", "SD": "Sudan", "SR": "Suriname", "SJ": "Svalbard and <PERSON>", "SE": "Sweden", "CH": "Switzerland", "SY": "Syria", "TW": "Taiwan", "TJ": "Tajikistan", "TZ": "Tanzania", "TH": "Thailand", "TL": "Timor-Leste", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad and Tobago", "TN": "Tunisia", "TR": "Turkey", "TM": "Turkmenistan", "TC": "Turks and Caicos Islands", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ukraine", "AE": "United Arab Emirates", "GB": "United Kingdom ", "UM": "United States Minor Outlying Islands", "US": "United States of America", "UY": "Uruguay", "UZ": "Uzbekistan", "VU": "Vanuatu", "VE": "Venezuela ", "VN": "Viet Nam", "VG": "Virgin Islands (British)", "VI": "Virgin Islands (U.S.)", "WF": "Wallis and Futuna", "EH": "Western Sahara", "YE": "Yemen", "ZM": "Zambia", "ZW": "Zimbabwe", "AX": "Åland Islands", "link_account_confirm_message": "Do you want to link this account to the account on your device?", "top_txt": "Top", "bottom_txt": "Bottom", "your_high_score": "Your high score: %{number}", "server_high_score": "Level high score: %{number}", "mission": "Mission", "button_event": "EVENT", "text_team": "TEAM", "text_my_team": "MY TEAM", "text_join_team": "JOIN TEAM", "text_description": "Description", "text_choose": "<PERSON><PERSON>", "text_view": "View", "text_request": "Request", "text_latest": "Latest", "text_join": "Join", "text_leave": "Leave", "text_search": "Search", "text_create": "Create", "text_open": "Open", "text_closed": "Closed", "text_members": "Members", "text_create_team": "Create Team", "text_search_team": "Search Team", "text_leave_team": "Leave Team", "text_kick_out": "Kick Out!", "text_leader": "Leader", "text_co_leader": "Co-Leader", "text_choose_badge": "<PERSON><PERSON>", "team_activity": "Activity", "chat_ask_energy": "asking for energy!", "chat_help": "Help", "chat_helps": "Helps", "chat_had_join": "had joined the team.", "chat_had_left": "has left the team.", "chat_was_remove": "was removed from the team", "chat_had_promoted_co_leader": "has been promoted to team Co-leader.", "chat_has_demoted": "has been demoted from Co-leader.", "chat_had_promoted_leader": "has been promoted to team Leader", "team_events": "Team Events", "team_free_energy": "Free Energy", "team_chat": "Team Chat", "team_unlock_at": "Team unlock at Account Level 3", "team_available": "Team is now availlable!", "team_join_unlock": "Join a team and unlock exclusive team benefits", "team_create_leader": "Create your team and become the team leader!", "team_score": "Team Score", "team_type": "Team Type", "team_required": "Required Acc. Level", "team_name": "Team name", "team_badge": "Team Badge", "team_ask_leave": "Are you sure you want to leave your team?", "team_ask_kick": "Are you sure you want to remove %{string} from your team?", "team_type_name": "Enter team name...", "promote_member_successfully": "%{number} has been promoted to Co-leader", "demote_member_successfully": "%{number} has been demoted from Co-leader", "chat_send_energy": "%{0} send 2 energy for %{1}", "error_team_ruby": "Not enough rubies", "error_team_account_level": "You haven't reached the required account level", "error_team_name": "Please enter a team name", "text_medium": "medium", "text_high": "high", "text_low": "low", "error_view_team": "The team no longer exists", "error_join_team": "Unable to join the team", "demote_member_failed": "Failed to demote member", "kick_member_failed": "Failed to remove member", "promote_member_failed": "Failed to promote member", "kick_member_successfully": "%{number} has been removed from your team", "txt_send": "Send", "txt_type_your_message": "Type your message", "txt_yesterday": "Yesterday", "chat_few_secs_ago": "Few secs ago", "chat_min_ago": "%{0} min ago", "chat_mins_ago": "%{0} mins ago", "chat_hour_ago": "%{0} hour ago", "chat_hours_ago": "%{0} hours ago", "chat_day_ago": "%{0} day ago", "chat_days_ago": "%{0} days ago", "txt_demote_co_leader": "Demote Co-leader", "txt_promote_co_leader": "Promote Co-leader", "txt_kick_out": "Kick out", "txt_view_profile": "View Profile", "me3": "The team no longer exists", "me14": "You already have a pending request for this team", "title_join_requests": "Join Requests", "team_request_join": "request to join you team!", "txt_reject": "Reject", "txt_accept": "Accept", "txt_accept_all": "Accept All", "txt_new": "NEW!", "team_not_enough_spots": "Not enough spots left in the team", "team_no_pending": "No pending join team requests", "team_was_accept": "%{0} was accepted to the team by %{1}", "team_request_rejected": "your join request has been rejected", "rejected_join_request": "Your join request has been rejected", "cancelled_join_request": "Your join request has been canceled", "expired_join_request": "Your join request has been expired", "pending_join_request": "Your join request has been sent.\nWaiting for approval", "text_pending": "Cancel Request", "leaderboard_top_team_title": "Hall of Teams", "txt_team_profile": "TEAM PROFILE", "request_banned": "You has been banned!", "join_approved": "You has been joined to %{0}"}