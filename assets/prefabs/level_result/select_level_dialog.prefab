[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "select_level_dialog", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 9}, {"__id__": 149}, {"__id__": 172}], "_active": true, "_components": [{"__id__": 322}, {"__id__": 323}], "_prefab": {"__id__": 324}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 640, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 44, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 6}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8424crwa5RCYYljfYdhC54f", "handler": "hide", "customEventData": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b207xfsFxKzrBqyDNEd95g", "sync": false}, {"__type__": "cc.Node", "_name": "\bmenu", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 10}, {"__id__": 14}, {"__id__": 21}, {"__id__": 33}, {"__id__": 44}, {"__id__": 48}, {"__id__": 71}, {"__id__": 76}, {"__id__": 144}], "_active": true, "_components": [{"__id__": 147}], "_prefab": {"__id__": 148}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 900, "height": 920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -80, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 899.5, "height": 920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_prefab": {"__uuid__": "b1c4ffd4-2cdb-466c-b280-786320ea0afe"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.24999999999998934, "_right": 0.25000000000001066, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 810, "_originalHeight": 790, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33mBBBpiFIPYzaJ8SxxoAN", "sync": false}, {"__type__": "cc.Node", "_name": "star", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 15}], "_active": true, "_components": [{"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 620, "height": 220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, 608.361, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "starBar", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 688.8889071382127, "height": 244.44445092001095}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8999999761581421, 0.8999999761581421, 0.8999999761581421]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_prefab": {"__uuid__": "118f9f23-5ac4-4eb6-ad24-48fc05a65039"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 700, "_originalHeight": 250, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b2BmR3akRA2r92G2EFkyKZ", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": -236.361, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "20355EzI9GP4pOoHKLNfIn", "sync": false}, {"__type__": "cc.Node", "_name": "for_win", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 22}, {"__id__": 29}], "_active": true, "_components": [], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 21}, "_children": [{"__id__": 23}], "_active": true, "_components": [{"__id__": 26}, {"__id__": 27}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 457.225, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "levelLabel", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 24}], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_prefab": {"__uuid__": "c2fd014d-002d-4c5b-a2fc-8ee860d6779a"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddGrp638xI05iHrre4fv1W", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": -502.225, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "29855d0f-c716-4d5a-ad96-4ec53325c0e3"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3zspYXgpJmrbc3x+IuJL5", "sync": false}, {"__type__": "cc.Node", "_name": "frame", "_objFlags": 0, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 30}], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 510, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 457.225, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a5d04df2-7a39-4915-a7f1-23de77747c5f"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afhqvm2bZHFpl+Pi/pesbk", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7nIb5RvVCNqAon7qJdPMf", "sync": false}, {"__type__": "cc.Node", "_name": "for_try_again", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 34}, {"__id__": 40}], "_active": false, "_components": [], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "TRY AGAIN?", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 130, "g": 70, "b": 22, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 402.96, "height": 119.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 329.402, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "TRY AGAIN?", "_N$string": "TRY AGAIN?", "_fontSize": 80, "_lineHeight": 85, "_enableWrapText": true, "_N$file": {"__uuid__": "772c665b-1639-4a72-aa97-6fced35228f1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "923f9T6EU1Mxa5WqjBIKlfj", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_key": "title_try_again", "_paramValues": [], "_id": ""}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_width": 6, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": -388.952, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aa9XeIytpKaKy+M3sfr6ns", "sync": false}, {"__type__": "cc.Node", "_name": "levelLabel", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 41}], "_prefab": {"__id__": 42}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 245.707, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_prefab": {"__uuid__": "c2fd014d-002d-4c5b-a2fc-8ee860d6779a"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4VH4sueZJzrx5LArkkw0+", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3tYMseW5FOaZMJSr83su7", "sync": false}, {"__type__": "cc.Node", "_name": "level<PERSON>arget", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 45}, {"__id__": 46}], "_prefab": {"__id__": 47}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 770, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 294.046, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "_prefab": {"__uuid__": "5d904456-c4e3-4976-b90c-02a1a1daf2ae"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 80.95400000000001, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07NFknApdPkI+7hHZjwtuR", "sync": false}, {"__type__": "cc.Node", "_name": "boosterPanel", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 49}, {"__id__": 54}], "_active": true, "_components": [{"__id__": 68}, {"__id__": 69}], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 784.5, "height": 420.00000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -49.70000000000002, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "labelSelect", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 50}, {"__id__": 51}, {"__id__": 52}], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 94, "g": 60, "b": 41, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 389.5, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 164.061, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "SELECT BOOSTERS", "_N$string": "SELECT BOOSTERS", "_fontSize": 50, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "7620fafd-6f76-45f1-83e5-8e7471cc9211"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "923f9T6EU1Mxa5WqjBIKlfj", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_key": "title_select_booster", "_paramValues": [], "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 14.439000000000021, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bchD2sJ1JOFKCfROYBftVV", "sync": false}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [{"__id__": 55}], "_active": true, "_components": [{"__id__": 65}, {"__id__": 66}], "_prefab": {"__id__": 67}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 347.4565}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37.33100000000002, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "booster_panel", "_objFlags": 0, "_parent": {"__id__": 54}, "_children": [{"__id__": 56}], "_active": true, "_components": [{"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 297.45649999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 57}, {"__id__": 58}], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 977.8781999999997, "height": 297.4564999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 977.8781999999997, "height": 297.4564999999999}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 15, "_N$paddingRight": 10, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 40, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 21, "_left": 0, "_right": 0, "_top": 2.842170943040401e-14, "_bottom": 2.842170943040401e-14, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02+VNACvxHhrRbG6cHcrRN", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": false, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 10, "_bottom": 10, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 880, "_originalHeight": 200, "_id": ""}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "horizontal": true, "vertical": false, "inertia": true, "brake": 0.5, "elastic": true, "bounceDuration": 1, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 56}, "content": {"__id__": 56}, "_id": ""}, {"__type__": "19376PgcZVAQZ+T5+W96sNT", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_layout": {"__id__": 56}, "_itemPrefab": {"__uuid__": "0c1c114e-1868-421f-907d-4e5507d974a4"}, "_boosterDialogPrefab": {"__uuid__": "88794033-19fc-453e-adfa-9b5e8d55bba5"}, "_id": ""}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6fB28Ue7VHJ6UZPCvlEj9t", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -7.75, "_right": -7.75, "_top": 73.60275000000003, "_bottom": -1.0592499999999916, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 650, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fa/G0SFY5NqJ9b5PaKwUV8", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9f1303da-7a2a-4d40-9a42-cd24f15d73b0"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 57.75, "_right": 57.75000000000003, "_top": 299.7, "_bottom": 200.29999999999995, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 730, "_originalHeight": 300, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5eHdIlkBFCBpW3gKjq60pl", "sync": false}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": false, "_components": [{"__id__": 72}, {"__id__": 73}, {"__id__": 74}], "_prefab": {"__id__": 75}, "_opacity": 162, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 9810, "height": 9000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -4455, "_right": -4455, "_top": -4040, "_bottom": -4040, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43aiBKh3xHf7l1Bsl44J/D", "sync": false}, {"__type__": "cc.Node", "_name": "bot", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 77}, {"__id__": 97}, {"__id__": 138}], "_active": true, "_components": [{"__id__": 141}, {"__id__": 142}], "_prefab": {"__id__": 143}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 510, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.478, -364, 0, 0, 0, 0, 1, 1.2, 1.2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "playButton", "_objFlags": 0, "_parent": {"__id__": 76}, "_children": [{"__id__": 78}, {"__id__": 81}], "_active": false, "_components": [{"__id__": 93}, {"__id__": 94}], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-260, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon-play", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 79, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-70, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "17cb9a6d-3d89-4d52-8541-cecc62122ce6"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7ORHQujZMzocp+5pQNrUi", "sync": false}, {"__type__": "cc.Node", "_name": "frame", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [{"__id__": 82}], "_active": true, "_components": [{"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [85, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "play_layer", "_objFlags": 0, "_parent": {"__id__": 81}, "_children": [{"__id__": 83}, {"__id__": 86}], "_active": true, "_components": [{"__id__": 89}], "_prefab": {"__id__": 90}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "energyLabel", "_objFlags": 0, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 84}], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 21, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-23, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "1", "_N$string": "1", "_fontSize": 50, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "2985a547-453d-4778-9367-163bce6fab07"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dkEIZuudHXqhdLHWXhvB9", "sync": false}, {"__type__": "cc.Node", "_name": "icon_energy", "_objFlags": 0, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 87}], "_prefab": {"__id__": 88}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [18, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f9a92bc0-5742-460a-9ae7-9b06f6d3ab67"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89QA5u7W9MHLoQFr0Rw+sY", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 67, "height": 0}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 15, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90/7UGN9VLY50061iMIHcv", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4e1c378c-beb2-4ca8-8b6f-e2080aad411b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94a7h8KnpHhIU3L1XVMxx6", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "84c60d4b-2a25-4e13-8b56-85a54188f8b9"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 95}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 77}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8424crwa5RCYYljfYdhC54f", "handler": "onPlayButtonPressed", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daq0n4A/pPg5issQRm0LVF", "sync": false}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 76}, "_children": [{"__id__": 98}, {"__id__": 122}], "_active": true, "_components": [{"__id__": 136}], "_prefab": {"__id__": 137}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 510, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "playAdsButton", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [{"__id__": 99}, {"__id__": 102}, {"__id__": 105}, {"__id__": 108}, {"__id__": 114}], "_active": true, "_components": [{"__id__": 117}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 121}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-80, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon-play", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 100}], "_prefab": {"__id__": 101}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 79, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-112.856, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "17cb9a6d-3d89-4d52-8541-cecc62122ce6"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62wGFD69xPOoCPBtaU7hpL", "sync": false}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 103}], "_prefab": {"__id__": 104}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 44.8, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-42.316, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "5", "_N$string": "5", "_fontSize": 80, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "2985a547-453d-4778-9367-163bce6fab07"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e55k6W5nVMxpjBbsnVFizl", "sync": false}, {"__type__": "cc.Node", "_name": "icon_energy", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 106}], "_prefab": {"__id__": 107}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [6.984, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f9a92bc0-5742-460a-9ae7-9b06f6d3ab67"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fv51R9OBPmLMGpCiONfVc", "sync": false}, {"__type__": "cc.Node", "_name": "frame", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [{"__id__": 109}], "_active": true, "_components": [{"__id__": 112}], "_prefab": {"__id__": 113}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [97.86, 0, 0, 0, 0, 0, 1, 1, -1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon-ads", "_objFlags": 0, "_parent": {"__id__": 108}, "_children": [], "_active": true, "_components": [{"__id__": 110}], "_prefab": {"__id__": 111}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "711cf594-d3a0-493f-8ad1-8ad640111454"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "878DN5ZhBK6owl/nlTRVCx", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4e1c378c-beb2-4ca8-8b6f-e2080aad411b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5190kh7JpAyrQAMV7kQVtZ", "sync": false}, {"__type__": "cc.Node", "_name": "waiting_icon", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 115}], "_prefab": {"__id__": 116}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.35, 0.35, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_prefab": {"__uuid__": "9fcdc6bf-1428-4385-b4d5-70166affa444"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d7VDIjAPJIgLbe6gQ5dcB8", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "84c60d4b-2a25-4e13-8b56-85a54188f8b9"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": {"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 119}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 98}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8424crwa5RCYYljfYdhC54f", "handler": "onPlayAdsButtonPressed", "customEventData": ""}, {"__type__": "71e63NWdHtOnLZUEWqqKH7F", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "_videoButton": {"__id__": 118}, "_waitingIcon": {"__id__": 115}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7X5UoyZ5FMJbQDwISNmQO", "sync": false}, {"__type__": "cc.Node", "_name": "moreEnergyButton", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [{"__id__": 123}], "_active": true, "_components": [{"__id__": 132}, {"__id__": 133}], "_prefab": {"__id__": 135}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 122}, "_children": [{"__id__": 124}, {"__id__": 127}], "_active": true, "_components": [{"__id__": 130}], "_prefab": {"__id__": 131}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 88.6, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 125}], "_prefab": {"__id__": 126}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 35.2, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-26.699999999999996, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 124}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "+", "_N$string": "+", "_fontSize": 80, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "2985a547-453d-4778-9367-163bce6fab07"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96QmJ9ryREF4bWHgCcVODa", "sync": false}, {"__type__": "cc.Node", "_name": "icon_energy", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 128}], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [22.600000000000005, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f9a92bc0-5742-460a-9ae7-9b06f6d3ab67"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9Wl4q1tdIAa8w/OddglRj", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 88.6, "height": 0}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 10, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0drr5KquhKdrEpm0JD7ZcF", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c5048645-2e54-4e3a-839e-5d685883eca5"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 134}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 122}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8424crwa5RCYYljfYdhC54f", "handler": "onMoreEnergyButtonPressed", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2fQ94EsVFOzJJWrjp16l6T", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 510, "height": 110}, "_resize": 0, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 20, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "400exha+xGg7jjB7MirjhQ", "sync": false}, {"__type__": "cc.Node", "_name": "previousArea", "_objFlags": 0, "_parent": {"__id__": 76}, "_children": [], "_active": false, "_components": [{"__id__": 139}], "_prefab": {"__id__": 140}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 94, "g": 60, "b": 41, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 273, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 138}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Previous Area", "_N$string": "Previous Area", "_fontSize": 50, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "16e2c37c-da48-415e-869c-429bffad6231"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fQz/CUcVOpZOqM81Gvv2V", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 510, "height": 110}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 20, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 30, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1e4FUzhWNOzrUC8tTnEBFZ", "sync": false}, {"__type__": "cc.Node", "_name": "hand_tutorial", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": false, "_components": [{"__id__": 145}], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 161, "height": 136.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-84.749, -366.534, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "hand_click", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "hand_click", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "acfccca3-046c-4fb1-b835-1419fe30b301"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5ZHODliFOsqGcQuVhRfef", "sync": false}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6/w7idbJPXqnhwdK9+J65", "sync": false}, {"__type__": "cc.Node", "_name": "save_progress_panel", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 150}], "_active": false, "_components": [{"__id__": 170}], "_prefab": {"__id__": 171}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1820, "height": 218}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -509.949, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "sign_in", "_objFlags": 0, "_parent": {"__id__": 149}, "_children": [{"__id__": 151}, {"__id__": 157}], "_active": true, "_components": [], "_prefab": {"__id__": 169}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 150}, "_children": [], "_active": true, "_components": [{"__id__": 152}, {"__id__": 153}, {"__id__": 154}, {"__id__": 155}], "_prefab": {"__id__": 156}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 476.5, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64.805, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "More fun with friends!", "_N$string": "More fun with friends!", "_fontSize": 50, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "7620fafd-6f76-45f1-83e5-8e7471cc9211"}, "_isSystemFontUsed": false, "_spacingX": -4, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "82c1bta0plLL5mN6H04fub1", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "_id": ""}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 4, "_id": ""}, {"__type__": "923f9T6EU1Mxa5WqjBIKlfj", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "_key": "save_progress_hint", "_paramValues": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8W/JLMhtIw5cIuA2dqMYw", "sync": false}, {"__type__": "cc.Node", "_name": "save_progress_button", "_objFlags": 0, "_parent": {"__id__": 150}, "_children": [{"__id__": 158}], "_active": true, "_components": [{"__id__": 166}], "_prefab": {"__id__": 168}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 32.999, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 157}, "_children": [{"__id__": 159}], "_active": true, "_components": [{"__id__": 163}, {"__id__": 164}], "_prefab": {"__id__": 165}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 158}, "_children": [], "_active": true, "_components": [{"__id__": 160}, {"__id__": 161}], "_prefab": {"__id__": 162}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Save Progress", "_N$string": "Save Progress", "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "7620fafd-6f76-45f1-83e5-8e7471cc9211"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": ""}, {"__type__": "923f9T6EU1Mxa5WqjBIKlfj", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_key": "save_progress_button", "_paramValues": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f6bh7OKzxBkrApRCT5b0wW", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c5048645-2e54-4e3a-839e-5d685883eca5"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edYoBaDaFGzJvYFZ9FdFm4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 157}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 167}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "c5048645-2e54-4e3a-839e-5d685883eca5"}, "_N$pressedSprite": {"__uuid__": "c5048645-2e54-4e3a-839e-5d685883eca5"}, "pressedSprite": {"__uuid__": "c5048645-2e54-4e3a-839e-5d685883eca5"}, "_N$hoverSprite": {"__uuid__": "c5048645-2e54-4e3a-839e-5d685883eca5"}, "hoverSprite": {"__uuid__": "c5048645-2e54-4e3a-839e-5d685883eca5"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 158}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 149}, "component": "", "_componentId": "e57a55jnnVPuYUi1VU8cHR+", "handler": "onPressSignIn", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "77LAT844JL2r4Ksyd7klmm", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffUAcQIrxHRpTs4sr6HUS5", "sync": false}, {"__type__": "e57a55jnnVPuYUi1VU8cHR+", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "_loginLayer": {"__id__": 150}, "_infoLayer": null, "_signOutLayer": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dN+aqowRN0ZqPFJVQLzbe", "sync": false}, {"__type__": "cc.Node", "_name": "booster_offer_banner", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 173}, {"__id__": 177}, {"__id__": 181}, {"__id__": 188}, {"__id__": 222}, {"__id__": 295}, {"__id__": 308}], "_active": false, "_components": [{"__id__": 319}, {"__id__": 320}], "_prefab": {"__id__": 321}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 900, "height": 196}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -529.165, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [], "_active": true, "_components": [{"__id__": 174}, {"__id__": 175}], "_prefab": {"__id__": 176}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 900, "height": 196}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b678d506-6225-4604-870f-0b379c9e320a"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1100, "_originalHeight": 500, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4LfYjlxBOp7SUsNT47t5i", "sync": false}, {"__type__": "cc.Node", "_name": "innerBackground", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}], "_prefab": {"__id__": 180}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 846, "height": 176.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ccecac1e-4ad4-44a6-a6a5-40aea23a0995"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.03, "_right": 0.03, "_top": 0.05, "_bottom": 0.05, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1150, "_originalHeight": 530, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "875WLle/9Ig7BmRSw3uGBi", "sync": false}, {"__type__": "cc.Node", "_name": "titleLayer", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 182}], "_active": true, "_components": [{"__id__": 186}], "_prefab": {"__id__": 187}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 425.69999999999993, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-199.85400000000004, 54.553, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "textLabel", "_objFlags": 0, "_parent": {"__id__": 181}, "_children": [], "_active": true, "_components": [{"__id__": 183}, {"__id__": 184}], "_prefab": {"__id__": 185}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1218, "height": 236}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-35.525, -1.845, 0, 0, 0, 0, 1, 0.2966101694915254, 0.2966101694915254, 0.2966101694915254]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1fa34f9a-1969-45c1-98f4-d477c83921b4"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "58cb5KdYLRF6Kl3iyP5Q0RV", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "_policy": 2, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5p8F7EjpObZLecVZRNYKr", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.04144000000000002, "_right": 0.4855600000000001, "_top": 0.0430969387755102, "_bottom": 0.5997602040816327, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 352.86, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bdLx/+2lJKaaFjR0R+02k", "sync": false}, {"__type__": "cc.Node", "_name": "time<PERSON>ayer", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 189}], "_active": false, "_components": [{"__id__": 220}], "_prefab": {"__id__": 221}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 386.20000000000084, "height": 33.359215686274496}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [258.646, 58.468, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "level_time", "_objFlags": 0, "_parent": {"__id__": 188}, "_children": [{"__id__": 190}, {"__id__": 194}], "_active": true, "_components": [{"__id__": 218}], "_prefab": {"__id__": 219}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 672, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.37065795206971663, 0.37065795206971663, 0.37065795206971663]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 189}, "_children": [], "_active": true, "_components": [{"__id__": 191}, {"__id__": 192}], "_prefab": {"__id__": 193}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 672, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b678d506-6225-4604-870f-0b379c9e320a"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 80, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fcCJ4OPZpEYJ8Fx45U/Jne", "sync": false}, {"__type__": "cc.Node", "_name": "inner", "_objFlags": 0, "_parent": {"__id__": 189}, "_children": [{"__id__": 195}, {"__id__": 209}], "_active": true, "_components": [{"__id__": 216}], "_prefab": {"__id__": 217}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 662, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "progressBar", "_objFlags": 0, "_parent": {"__id__": 194}, "_children": [{"__id__": 196}, {"__id__": 200}], "_active": true, "_components": [{"__id__": 204}, {"__id__": 205}, {"__id__": 206}, {"__id__": 207}], "_prefab": {"__id__": 208}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 562.7, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [49.650000000000034, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 197}, {"__id__": 198}], "_prefab": {"__id__": 199}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 337.62, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-281.35, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7493271c-e5de-4b0a-be9b-fb8d327a8940"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 13, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 80, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0c2Bgyua5IA4ijTl73Bu3q", "sync": false}, {"__type__": "cc.Node", "_name": "time<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 201}, {"__id__": 202}], "_prefab": {"__id__": 203}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 547.7, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "99s", "_N$string": "99s", "_fontSize": 50, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "16e2c37c-da48-415e-869c-429bffad6231"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 10, "_right": 5, "_top": 5, "_bottom": 5, "_verticalCenter": -4, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 64.8, "_originalHeight": 50, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66L+xzK1FENpcwLaTuxn2C", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "76ef2fdf-2b9f-4628-8069-461f38d29b21"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "_N$totalLength": 562.7, "_N$barSprite": {"__id__": 197}, "_N$mode": 0, "_N$progress": 0.6, "_N$reverse": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.15, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 250, "_originalHeight": 80, "_id": ""}, {"__type__": "df031vx0ehPoJYm7u4xisyW", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "41FnCz/RxJo5bvutq9I6Vf", "sync": false}, {"__type__": "cc.Node", "_name": "clock_scale", "_objFlags": 0, "_parent": {"__id__": 194}, "_children": [{"__id__": 210}], "_active": true, "_components": [{"__id__": 214}], "_prefab": {"__id__": 215}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 99.30000000000007, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-281.34999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon_clock", "_objFlags": 0, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 211}, {"__id__": 212}], "_prefab": {"__id__": 213}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8641975308641975, 0.8641975308641975, 0.8641975308641975]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d1597625-7b4d-4f35-93b3-71d237f918ff"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "58cb5KdYLRF6Kl3iyP5Q0RV", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "_policy": 2, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91byTge3tFe6kfpYRGfZlp", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.85, "_top": 5, "_bottom": 5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 71, "_originalHeight": 87, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27ddzJRz5FKoqBaVQR/2R9", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 194}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 5, "_right": 5, "_top": 5, "_bottom": 5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e69kcNINhOnJySOWI5Z4f5", "sync": false}, {"__type__": "58cb5KdYLRF6Kl3iyP5Q0RV", "_name": "", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "_policy": 2, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61+IKqQnVHLYCYYnGx3yer", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 188}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.5728288888888885, "_right": -0.0019400000000004672, "_top": 0.11659383753501398, "_bottom": 0.7132060824329732, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 330, "_originalHeight": 80, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3e5rDItwBFtpulOwj+bXvM", "sync": false}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 223}], "_active": true, "_components": [{"__id__": 292}, {"__id__": 293}], "_prefab": {"__id__": 294}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 503.9999999999999, "height": 117.07333333333338}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [135.264, -31.370000000000005, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "rewardLayer", "_objFlags": 0, "_parent": {"__id__": 222}, "_children": [{"__id__": 224}, {"__id__": 250}, {"__id__": 263}, {"__id__": 276}], "_active": true, "_components": [{"__id__": 289}, {"__id__": 290}], "_prefab": {"__id__": 291}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 460, "height": 117.07333333333341}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.8100000000000307, 0.538831241283134, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "rewardLayer", "_objFlags": 0, "_parent": {"__id__": 223}, "_children": [{"__id__": 225}, {"__id__": 245}], "_active": true, "_components": [], "_prefab": {"__id__": 249}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-180, 18.221, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "card", "_objFlags": 0, "_parent": {"__id__": 224}, "_children": [{"__id__": 226}, {"__id__": 230}, {"__id__": 234}, {"__id__": 238}], "_active": true, "_components": [{"__id__": 242}, {"__id__": 243}], "_prefab": {"__id__": 244}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -61.235000000000014, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "clover", "_objFlags": 0, "_parent": {"__id__": 225}, "_children": [], "_active": true, "_components": [{"__id__": 227}, {"__id__": 228}], "_prefab": {"__id__": 229}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 89.9}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.764, 13.021999999999998, 0, 0, 0, 0, 1, -1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d0764726-0893-4d9a-87e4-2dca7f854750"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 17, "_left": 0, "_right": 0, "_top": 17.028, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": -1.764, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "959Wv6ElVOn59HRYwSrSak", "sync": false}, {"__type__": "cc.Node", "_name": "dynamite", "_objFlags": 0, "_parent": {"__id__": 225}, "_children": [], "_active": true, "_components": [{"__id__": 231}, {"__id__": 232}], "_prefab": {"__id__": 233}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 20.5, "height": 94.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32.783, -7.784999999999997, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9f1e1772-e730-4caf-8810-dc97d95ed4ff"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 36, "_left": 0, "_right": 31.967, "_top": 0, "_bottom": 19.815000000000005, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70R4LO61NFHZwoQtpVSPuk", "sync": false}, {"__type__": "cc.Node", "_name": "oil", "_objFlags": 0, "_parent": {"__id__": 225}, "_children": [], "_active": true, "_components": [{"__id__": 235}, {"__id__": 236}], "_prefab": {"__id__": 237}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 85.4, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.359, -26.190000000000005, 0, 0, 0, 0, 1, 0.800000011920929, 0.800000011920929, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 234}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ddd3661d-af65-4fbd-b5f2-e1832704bb5c"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 234}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 20, "_left": 0, "_right": 0, "_top": 0, "_bottom": 18.409999547004695, "_verticalCenter": 0, "_horizontalCenter": -1.359, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dCml8QxFN4Km6V+E9SBt9", "sync": false}, {"__type__": "cc.Node", "_name": "drink", "_objFlags": 0, "_parent": {"__id__": 225}, "_children": [], "_active": true, "_components": [{"__id__": 239}, {"__id__": 240}], "_prefab": {"__id__": 241}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 71.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-33.051, -21.576, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 238}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "08985714-d7b1-49bc-9644-a6a00e4f05c2"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 238}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 12, "_left": 20.949, "_right": 0, "_top": 0, "_bottom": 17.573999999999995, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cvxM9xkhEnolHmUKndjEO", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c5048645-2e54-4e3a-839e-5d685883eca5"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 36.235000000000014, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4bQA7Md/pGBIWaXuNmwcw7", "sync": false}, {"__type__": "cc.Node", "_name": "textLabel", "_objFlags": 0, "_parent": {"__id__": 224}, "_children": [], "_active": true, "_components": [{"__id__": 246}, {"__id__": 247}], "_prefab": {"__id__": 248}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 27.53, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 38.766, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 245}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "4", "_N$string": "4", "_fontSize": 44.4000000000002, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "16e2c37c-da48-415e-869c-429bffad6231"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 245}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 80.6406441196936, "_right": 74.34064411969361, "_top": 0.9392452819524015, "_bottom": 63.766, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59MlVkS2BHaJgG8r0v8cYK", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2pZVzCTpKppZUx6VDxktT", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 223}, "_children": [{"__id__": 251}, {"__id__": 258}], "_active": true, "_components": [], "_prefab": {"__id__": 262}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-60, 18.221, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "card", "_objFlags": 0, "_parent": {"__id__": 250}, "_children": [{"__id__": 252}], "_active": true, "_components": [{"__id__": 255}, {"__id__": 256}], "_prefab": {"__id__": 257}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -61.235000000000014, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "chest_gold", "_objFlags": 0, "_parent": {"__id__": 251}, "_children": [], "_active": true, "_components": [{"__id__": 253}], "_prefab": {"__id__": 254}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 252}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ac0420da-b106-4f31-90a8-9e057443bede"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f09tdfpnpIsJXPlNQw+jtU", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fc4c3c1d-3b48-4315-b783-6ef7852b37ba"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 36.235000000000014, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01g3jh/ltBc6R4LvJcfIVu", "sync": false}, {"__type__": "cc.Node", "_name": "textLabel", "_objFlags": 0, "_parent": {"__id__": 250}, "_children": [], "_active": true, "_components": [{"__id__": 259}, {"__id__": 260}], "_prefab": {"__id__": 261}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 18.65, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 38.766, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 258}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "1", "_N$string": "1", "_fontSize": 44.4000000000002, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "16e2c37c-da48-415e-869c-429bffad6231"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 258}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 80.6406441196936, "_right": 74.34064411969361, "_top": 0.9392452819524015, "_bottom": 63.766, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9ffof04StHz6nRILEaxTvM", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1OOXWE+NJyZvhEE5UzHiR", "sync": false}, {"__type__": "cc.Node", "_name": "ruby<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 223}, "_children": [{"__id__": 264}, {"__id__": 271}], "_active": true, "_components": [], "_prefab": {"__id__": 275}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [60, 18.221, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "card", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [{"__id__": 265}], "_active": true, "_components": [{"__id__": 268}, {"__id__": 269}], "_prefab": {"__id__": 270}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -61.235000000000014, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon_ruby", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 266}], "_prefab": {"__id__": 267}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7b04b01f-e989-4e08-a321-8d21d4f57302"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f17Op1N9VHv5/B7R4o7OoI", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fc4c3c1d-3b48-4315-b783-6ef7852b37ba"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 36.235000000000014, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cTsacmwtBhorRcW2nBZL5", "sync": false}, {"__type__": "cc.Node", "_name": "textLabel", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [], "_active": true, "_components": [{"__id__": 272}, {"__id__": 273}], "_prefab": {"__id__": 274}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 73.7, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 38.766, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "100", "_N$string": "100", "_fontSize": 44.4000000000002, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "16e2c37c-da48-415e-869c-429bffad6231"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 80.6406441196936, "_right": 74.34064411969361, "_top": 0.9392452819524015, "_bottom": 63.766, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1Cf716EFMmIkyhGEMbzxr", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4kmlJEwxNKbe2mBWg4eLf", "sync": false}, {"__type__": "cc.Node", "_name": "gold<PERSON>ayer", "_objFlags": 0, "_parent": {"__id__": 223}, "_children": [{"__id__": 277}, {"__id__": 284}], "_active": true, "_components": [], "_prefab": {"__id__": 288}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [180, 18.221, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "card", "_objFlags": 0, "_parent": {"__id__": 276}, "_children": [{"__id__": 278}], "_active": true, "_components": [{"__id__": 281}, {"__id__": 282}], "_prefab": {"__id__": 283}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -61.235000000000014, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "gold", "_objFlags": 0, "_parent": {"__id__": 277}, "_children": [], "_active": true, "_components": [{"__id__": 279}], "_prefab": {"__id__": 280}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 278}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "27d4bfd7-86c2-4b83-b334-d110866c7b86"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25B8xSK0pFxomBUVh9iC1X", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 277}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fc4c3c1d-3b48-4315-b783-6ef7852b37ba"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 277}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 36.235000000000014, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92Y/dkWCZCXZEzCzXVd26V", "sync": false}, {"__type__": "cc.Node", "_name": "textLabel", "_objFlags": 0, "_parent": {"__id__": 276}, "_children": [], "_active": true, "_components": [{"__id__": 285}, {"__id__": 286}], "_prefab": {"__id__": 287}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 122.54, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 38.766, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 284}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "25000", "_N$string": "25000", "_fontSize": 44.4000000000002, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "16e2c37c-da48-415e-869c-429bffad6231"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 284}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 80.6406441196936, "_right": 74.34064411969361, "_top": 0.9392452819524015, "_bottom": 63.766, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "751MEPLSBIB6aXnGk8dHwh", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36SV0JJ1BMXYXeb7cddUDY", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.04525793650793648, "_right": 0.04204365079365065, "_top": -0.004602510460251192, "_bottom": 0.004602510460251001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 460, "height": 117.07333333333341}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 20, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3auN5B9k1CtbsDGBMuR68m", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 222}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7c2e7730-72b1-44fd-ad74-974d02d1b856"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 222}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.3702933333333334, "_right": 0.06970666666666674, "_top": 0.3613945578231292, "_bottom": 0.0412925170068026, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 368, "_originalHeight": 227, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29DxyxOYFLmaAXHs2+7NRD", "sync": false}, {"__type__": "cc.Node", "_name": "buyLayer", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 296}], "_active": true, "_components": [{"__id__": 306}], "_prefab": {"__id__": 307}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 78.9333333333333}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-276.112, -31.904, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "button", "_objFlags": 0, "_parent": {"__id__": 295}, "_children": [{"__id__": 297}], "_active": true, "_components": [{"__id__": 301}, {"__id__": 302}, {"__id__": 304}], "_prefab": {"__id__": 305}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 78.9333333333333}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "price", "_objFlags": 0, "_parent": {"__id__": 296}, "_children": [], "_active": true, "_components": [{"__id__": 298}, {"__id__": 299}], "_prefab": {"__id__": 300}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 92.96613333333329}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 297}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "đ 230,000", "_N$string": "đ 230,000", "_fontSize": 40, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "16e2c37c-da48-415e-869c-429bffad6231"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 297}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 10, "_right": 10, "_top": -7.016400000000001, "_bottom": -7.016400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128.7, "_originalHeight": 50, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dcwL64JdNhKLeNudOyfjQ", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 296}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "84c60d4b-2a25-4e13-8b56-85a54188f8b9"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 296}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 303}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 296}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 172}, "component": "", "_componentId": "8218dVeBjdNsozSE/VyxLaN", "handler": "onItemPressed", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 296}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 5, "_left": 0.2570376016260163, "_right": 0.6070376016260163, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 214, "_originalHeight": 60, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4bAztsHtJK9qrlfMu3cX1A", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 295}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 250, "height": 78.9333333333333}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 30, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27iNhiHfhAvYg1aCX4HLV+", "sync": false}, {"__type__": "cc.Node", "_name": "discountLayer", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 309}], "_active": true, "_components": [{"__id__": 317}], "_prefab": {"__id__": 318}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 176.39999999999986, "height": 40.73999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.0400000000000205, 97.266, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "frame-discount", "_objFlags": 0, "_parent": {"__id__": 308}, "_children": [{"__id__": 310}], "_active": true, "_components": [{"__id__": 314}, {"__id__": 315}], "_prefab": {"__id__": 316}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 176.39999999999986, "height": 40.73999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "textLabel", "_objFlags": 0, "_parent": {"__id__": 309}, "_children": [], "_active": true, "_components": [{"__id__": 311}, {"__id__": 312}], "_prefab": {"__id__": 313}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 117.65, "height": 37.79999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0.7324999999999982, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 310}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "EXTRA 20%", "_N$string": "EXTRA 20%", "_fontSize": 22.800000000000004, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "7620fafd-6f76-45f1-83e5-8e7471cc9211"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 310}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 29.37499999999993, "_right": 29.37499999999993, "_top": 0.7374999999999972, "_bottom": 2.202499999999998, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 206.4, "_originalHeight": 63, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "179ItAeQ1CKIYQTjdziYXZ", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 309}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "14f67b43-d71c-40ce-9564-25828c9ec8f2"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 309}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 280, "_originalHeight": 80, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11jyKdDuRCI7X5jq8++QF4", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 308}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.4053777777777779, "_right": 0.39862222222222227, "_top": -0.10018367346938774, "_bottom": 0.892326530612245, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 352.86, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5MvjPE1lMlbBZOjP0Wyzi", "sync": false}, {"__type__": "8218dVeBjdNsozSE/VyxLaN", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "priceLabel": {"__id__": 298}, "salePercent": {"__id__": 311}, "time": {"__id__": 201}, "goldLabel": {"__id__": 285}, "rubyLabel": {"__id__": 272}, "boosterLabel": {"__id__": 246}, "cardLabel": null, "energyLabel": null, "energyUnlimitedLabel": null, "chestLabel": {"__id__": 259}, "packIndex": 4, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": false, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": -4.532000000000039, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "08gp+I9+FO6L4fG+tLzRD6", "sync": false}, {"__type__": "8424crwa5RCYYljfYdhC54f", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_starBar_prefab": {"__id__": 16}, "_levelLabel_prefab": {"__id__": 24}, "levelTryAgainLabel_prefab": {"__id__": 41}, "_levelTarget_prefab": {"__id__": 45}, "_boosterPanel": {"__id__": 62}, "_energyLabel": {"__id__": 84}, "_recordButton": null, "playButton": {"__id__": 94}, "_adsLayer": {"__id__": 97}, "_playAdsButton": {"__id__": 118}, "_playAdsLabel": {"__id__": 103}, "UIWin": [{"__id__": 21}, {"__id__": 149}], "UITryAgain": [{"__id__": 33}, {"__id__": 172}], "_boosterOffer": {"__id__": 319}, "overlayTutorial": {"__id__": 71}, "tutorialAnimation": {"__id__": 145}, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 900, "_originalHeight": 870, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]