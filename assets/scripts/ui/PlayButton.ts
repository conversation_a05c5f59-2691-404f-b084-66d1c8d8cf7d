import * as ee from "../libraries/ee/index";
import {crashlytics} from "../manager/crashlytic/CrashlyticManager";

const {ccclass, property} = cc._decorator;
type Callback = () => void;

@ccclass
export class PlayButton extends cc.Component {
    @property({type: ee.LanguageComponent, visible: true})
    private nextLevelLabel: ee.LanguageComponent | null = null;

    private _controller: Callback = () => {};

    public setPressedCallback(callback: Callback){
        this._controller = callback;
    }

    public setNextLevel(value: number) {
        this.nextLevelLabel.key = `text_level_number`;
        this.nextLevelLabel.paramValues = [`${value}`]
    }

    public setNextLevel_v2(value: number) {
        this.nextLevelLabel.key = `${value}`;
    }

    protected onLoad() {
        this.node.on('click',this.onPressed,this);
    }

    @crashlytics
    private onPressed() {
        this._controller && this._controller();
    }
}
