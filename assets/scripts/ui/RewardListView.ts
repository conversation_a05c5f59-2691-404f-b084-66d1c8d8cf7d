import {
    RewardInfo,
    RewardType,
} from "../manager/reward/RewardManager";
import { CommonReward } from "../scenes/common/CommonReward";

const { ccclass, disallowMultiple, property } = cc._decorator;

@ccclass
@disallowMultiple
export abstract class RewardListView extends cc.Component {
    private version = 0;
    private types: RewardType[] = [];

    /** Gets the prefab for the specified reward type. */
    protected abstract getPrefab(type: RewardType): cc.Prefab;

    public setItems(items: RewardInfo[]): void {
        if (items.length !== this.types.length
            || items.some((item, index) => item.type !== this.types[index])) {
            this.node.removeAllChildren();
            this.types = [];
            for (let i = 0, n = items.length; i < n; ++i) {
                const item = items[i];
                this.types.push(item.type);
                const prefab = this.getPrefab(item.type);
                const view = cc.instantiate(prefab);
                this.node.addChild(view);
            }
        }
        const currentVersion = ++this.version;
        items.forEach((item, index) => {
            const control = this.node.children[index].getComponent(CommonReward);
            control.setQuantity(item.quantity);
            item.preview.load().then(view => {
                if (currentVersion === this.version) {
                    control.setView(view);
                }
            });
        });
    }
}
