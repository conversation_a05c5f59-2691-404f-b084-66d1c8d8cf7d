import { PrefabLoader } from "./PrefabLoader";

export interface NodeLoader {
    load(): Promise<cc.Node>;
}

export class NullNodeLoader implements NodeLoader {
    public load(): Promise<cc.Node> {
        return Promise.reject(`NullNodeLoader.load`);
    }
}

export class NodeLoaderFromPrefab implements NodeLoader {
    public constructor(private readonly prefab: cc.Node) {
    }

    public async load(): Promise<cc.Node> {
        return cc.instantiate(this.prefab);
    }
}

export class NodeLoaderFromPrefabLoader implements NodeLoader {
    public constructor(private readonly loader: PrefabLoader) {
    }

    public async load(): Promise<cc.Node> {
        const prefab = await this.loader.load();
        return cc.instantiate(prefab);
    }
}