import { ResourcesUtils } from "./ResourcesUtils";


export class PrefabUtils {
    /**
     * Asynchronously loads a prefab.
     * @param path The asset path to the prefab.
     */

    private static winSize = cc.winSize;

    public static loadPrefab(path: string): Promise<cc.Prefab> {
        return ResourcesUtils.loadResources(path, cc.Prefab);
    }

    /**
     * Asynchronously creates a prefab.
     * @param type The prototype of the prefab.
     * @param path The asset path to the prefab.
     */
    public static async createPrefab<T extends cc.Component>(type: { prototype: T }, path: string): Promise<T> {
        const prefab = await this.loadPrefab(path);
        const node = cc.instantiate(prefab);
        return node.getComponent(type);
    }

    /**
     * Asynchronously creates multiple prefabs.
     * @param type The prototype of the prefab.
     * @param path The asset path to the prefab.
     * @param count How many prefabs will be created.
     */
    public static async createMultiplePrefab<T extends cc.Component>(
        type: { prototype: T }, path: string, count: number): Promise<T[]> {
        const prefab = await this.loadPrefab(path);
        const nodes: cc.Node[] = [];
        for (let i = 0; i < count; ++i) {
            nodes.push(cc.instantiate(prefab));
        }
        return nodes.map(item => item.getComponent(type));
    }
}