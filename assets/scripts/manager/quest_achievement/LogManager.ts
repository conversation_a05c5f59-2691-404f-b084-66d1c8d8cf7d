import * as ee from "../../libraries/ee/index";

type Observer = (dict: { [key: string]: number }) => void;

@ee.service("Log")
export abstract class LogManager extends ee.DefaultObserverManager<Observer> implements ee.Service {
    public abstract destroy(): void;
    public abstract countEvent(dict: { [key: number]: number }): void;
}

export class DefaultLogManager extends LogManager {
    public constructor() {
        super();
    }

    public destroy(): void {
    }

    public countEvent(dict: { [key: number]: number }): void {
        this.dispatch(async observer => observer(dict));
    }
}

export enum CountType {
    Ant,
    Bone,
    Boomerang,
    BreakStone,
    Chest,
    Coral,
    Detonator,
    Diamond,
    BigDiamond,
    Fish,
    FishBlue,
    FishRed,
    CollectGem,
    BlueGhost,
    Gold,
    LavaGhost,
    CollectMask,
    MoneyBag,
    Mouse,
    Niece,
    Pearl,
    CollectChip,
    SlotMachine,
    Stone,
    StrangerBoat,
    Thief,
    Wolf,

    CollectCharacterCard,
    CollectCartCard,
    CollectPetCard,

    CompleteAustralia,
    CompleteHawaii,
    CompleteVegas,
    CompleteArizona,

    CompleteEvent,

    BuyBooster,
    SpinWheel,
    OpenChest,
    RateGame,
    LoginFacebook,
    InviteFriends,
    LikeFanpage,
    ShareFacebook,

    CompleteLevel,
    FishPurple,
    FishPuffer,
    FishColorado,
    Hedgehog,
    Monkey,
    Safe,
    Scorpion,
    BlueShark,
    ClawChest,
    VultureWhite,
    BlowUpSafe,
    Mineral,

    UpgradeCard,
    UpgradeSetCard,
}