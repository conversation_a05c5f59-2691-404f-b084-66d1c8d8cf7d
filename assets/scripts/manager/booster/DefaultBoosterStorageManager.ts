import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {
    SceneName,
    TrackResultBooster,
    TrackResultIap,
    TrackResultSoftCurrency,
    TrackSinkType,
    TrackSourceType,
} from '../analytics/AnalyticsConfig';
import {TrackingManager} from '../analytics/TrackingManager';
import {ConfigManager} from '../config/ConfigManager';
import {DataManager} from '../data/DataManager';
import {CountType, LogManager,} from '../quest_achievement/LogManager';
import {StoreItem, StoreManager,} from '../store/StoreManager';
import {
    BoosterCategory,
    BoosterFiler,
    BoosterInfo,
    BoosterStorageController,
    BoosterStorageManager,
} from './BoosterStorageManager';
import {BoosterStorageUtils} from './BoosterStorageUtils';
import {FeatureManager} from "../config/FeatureManager";
import {LevelManager} from "../level/LevelManager";
import {BoosterType} from "../../engine/gm_engine";

enum Key {
    Data = 'booster_storage',
}

interface PriceInfo {
    type: BoosterCategory;
    price: number;
    price_gold: number;
}

interface BoosterConfig {
    ant: PriceInfo;
    clock: PriceInfo;
    diamond: PriceInfo;
    doll: PriceInfo;
    dynamite: PriceInfo;
    ghost: PriceInfo;
    lucky: PriceInfo;
    oil: PriceInfo;
    pirate: PriceInfo;
    strength: PriceInfo;
    titan: PriceInfo;
    wanted: PriceInfo;
    glove: PriceInfo;
    oldHam: PriceInfo;
    PickUp: PriceInfo;
}

export class DefaultBoosterStorageManager extends BoosterStorageManager {
    /** Config. */
    private boosterConfig: BoosterConfig;
    private levelBoosterConfig: { [area: number]: { [level: number]: string[] | undefined } | undefined };

    /** Data. */
    private storage: { [key: number]: number };

    /** Aux. */
    private _controller: BoosterStorageController;

    /** Updater. */
    private dataDirty = false;
    private readonly node: cc.Node;

    /** After buy currency */
    private _afterBuyMoreCurrency?(boosterType: gm.BoosterType, amount: number, cost: number): void;
    private _boosterBuyFrom: string;
    private _boosterBuyAmount: number;
    private _boosterBuyCost: number;
    private _rubyPosNode:cc.Node;

    public constructor(
        configManager: ConfigManager,
        private readonly dataManager: DataManager,
        private readonly storeManager: StoreManager,
        private readonly logManager: LogManager) {
        super();

        // Config.
        this.boosterConfig = configManager.getValue("booster_price_v2");
        this.levelBoosterConfig = configManager.getValue('level_boosters');

        // Data.
        this.storage = dataManager.getValue(Key.Data, {});

        // Sync handler.
        dataManager.addHandler('booster', {
            load: data => {
                this.storage = data[Key.Data] || {};
                this.saveData();
            },
            save: data => {
                data[Key.Data] = this.storage;
            },
        });

        // Setup.
        this._controller = {
            needMoreCurrency: () => { },
            get dialogManager(): ee.DialogManager {
                // default
                return ee.ServiceLocator.resolve(ee.DialogManager);
            },
        };

        // Data updater.
        this.node = new cc.Node();
        cc.director.getScheduler().schedule(() => {
            if (this.dataDirty) {
                this.dataDirty = false;
                this.saveData();
            }
        }, this.node, 0);
    }

    public set controller(controller: BoosterStorageController) {
        this._controller = controller;
    }

    public get controller(): BoosterStorageController {
        return this._controller;
    }

    public destroy(): void {
        this.dataManager.removeHandler('booster');
        this.node.destroy();
    }

    public getBalance(type: gm.BoosterType): number {
        let freeBoosterAfterUnlock =  ee.ServiceLocator.resolve(FeatureManager).freeBoosterAfterUnlock;
        return this.storage[type] !== undefined ? this.storage[type] : freeBoosterAfterUnlock;
    }

    public addBalance(type: gm.BoosterType, value: number): void {
        let current = this.getBalance(type);
        current += value;
        this.storage[type] = current;
        this.dataDirty = true;
        this.dispatch(async observer =>
            observer.onBalanceChanged && observer.onBalanceChanged(type));
    }

    private saveData(): void {
        this.dataManager.setValue(Key.Data, this.storage);
    }

    public get boosterBuyFrom(): string {
        return this._boosterBuyFrom;
    }

    public set boosterBuyFrom(value: string) {
        this._boosterBuyFrom = value;
    }

    public set boosterBuyAmount(value: number) {
        this._boosterBuyAmount = value;
    }

    public get boosterBuyAmount(): number {
        return this._boosterBuyAmount;
    }

    public set boosterBuyCost(value: number) {
        this._boosterBuyCost = value;
    }

    public get boosterBuyCost(): number {
        return this._boosterBuyCost;
    }

    public set rubyPosNode(value: cc.Node) {
        this._rubyPosNode = value;
    }
    public get rubyPosNode(): cc.Node {
        return this._rubyPosNode;
    }

    public set afterBuyMoreCurrency(callback: (boosterType: gm.BoosterType, amount: number, cost: number) => void) {
        this._afterBuyMoreCurrency = callback;
    }

    public get afterBuyMoreCurrency(): (boosterType: gm.BoosterType, amount: number, cost: number) => void {
        return this._afterBuyMoreCurrency;
    }

    public buyBooster(type: gm.BoosterType, storeItem: StoreItem, scene: SceneName, options?: {
        amount?: number,
        discount?: number,
        cost?: number,
    }): boolean {
        const amount = (options && options.amount) || 1;
        const discount = (options && options.discount) || 0;
        const cost = options && options.cost !== undefined
            ? options.cost
            : this.getCost(type, storeItem) * amount * (1 - discount / 100);
        const balance = this.storeManager.getItemBalance(storeItem);
        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
        const name = `booster_${this.getBoosterName(type)}`;

        if (balance >= cost) {
            this.logManager.countEvent({
                [CountType.BuyBooster]: amount,
            });

            this.addBalance(type, amount);
            this.storeManager.addItemBalance(storeItem, -cost);
            if (storeItem === StoreItem.Gold) {
                trackingManager.trackEventSinkSoftCurrency(
                    scene, TrackResultSoftCurrency.Bought, TrackSinkType.GoldBar, cost);
            } else {
                trackingManager.trackEventSinkIap(
                    scene, TrackResultIap.Bought, TrackSinkType.Ruby, name, cost);
            }
            trackingManager.trackEventSourceBooster(
                scene, TrackResultBooster.Bought, TrackSourceType.Booster, name, amount);

            return true;
        }

        trackingManager.trackEventSourceBooster(
            scene, TrackResultBooster.Error, TrackSourceType.Booster, name, 0);

        this.controller.needMoreCurrency(type, storeItem, cost);
        return false;
    }

    private getBoosterPriceInfo(type: gm.BoosterType): PriceInfo {
        const dict: { [key: number]: PriceInfo } = {
            [gm.BoosterType.AntSpray /*     */]: this.boosterConfig.ant,
            [gm.BoosterType.Clock /*        */]: this.boosterConfig.clock,
            [gm.BoosterType.DiamondPolish /**/]: this.boosterConfig.diamond,
            [gm.BoosterType.Dynamite /*     */]: this.boosterConfig.dynamite,
            [gm.BoosterType.LuckyClover /*  */]: this.boosterConfig.lucky,
            [gm.BoosterType.PirateHat /*    */]: this.boosterConfig.pirate,
            [gm.BoosterType.Speed /*        */]: this.boosterConfig.oil,
            [gm.BoosterType.SpiritJar /*    */]: this.boosterConfig.ghost,
            [gm.BoosterType.SpookyDoll /*   */]: this.boosterConfig.doll,
            [gm.BoosterType.Strength /*     */]: this.boosterConfig.strength,
            [gm.BoosterType.TitanRope /*    */]: this.boosterConfig.titan,
            [gm.BoosterType.WantedPoster /* */]: this.boosterConfig.wanted,
            [gm.BoosterType.Glove        /* */]: this.boosterConfig.glove,
            [gm.BoosterType.OldHam       /* */]: this.boosterConfig.oldHam,
            [gm.BoosterType.PickUp /*       */]: this.boosterConfig.PickUp
        };
        return dict[type];
    }

    // TODO currently hard coded
    public getStoreItemType(type: gm.BoosterType): StoreItem {
        const value = StoreItem.Ruby;
        return value;
    }

    public getCost(type: gm.BoosterType, storeItem: StoreItem): number {
        const value = storeItem === StoreItem.Ruby ? this.getBoosterPriceInfo(type).price :
            this.getBoosterPriceInfo(type).price_gold;
        return value;
    }

    public getCategory(type: gm.BoosterType): BoosterCategory {
        const priceInfo = this.getBoosterPriceInfo(type);
        return priceInfo.type;
    }

    public getEventBoosters(): BoosterInfo[] {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const lastUnlockedArea = levelManager.getLastUnlockedArea();
        const info = levelManager.getStoryAreaInfo(lastUnlockedArea);

        const boosters: gm.BoosterType[] = [
            gm.BoosterType.Strength,
            gm.BoosterType.Speed,
            gm.BoosterType.TitanRope,
            gm.BoosterType.DiamondPolish,
            gm.BoosterType.LuckyClover,
        ];
        // Add dynamite booster if the current user has none.
        if (this.getBalance(gm.BoosterType.Dynamite) === 0) {
            boosters.push(gm.BoosterType.Dynamite);
        }
        return boosters.map(booster => {
            return {
                type: booster,
                unlock: this.isBoosterUnlocked(booster,
                    lastUnlockedArea,
                    info.unlockedLevels - 1,
                    lastUnlockedArea,
                    info.unlockedLevels - 1),
            };
        });
    }

    public getStoryBoosters(
        area: number, level: number, unlockedArea: number, unlockedLevel: number): BoosterInfo[] {
        const boosters: gm.BoosterType[] = [];
        // Add dynamite booster if the current user has none.
        if (this.getBalance(gm.BoosterType.Dynamite) === 0) {
            boosters.push(gm.BoosterType.Dynamite);
        }
        const boosterNames = (this.levelBoosterConfig[area] || {})[level] || [];
        const boosterNameToTypes: { [key: string]: gm.BoosterType } = {
            ['diamond_polish' /**/]: gm.BoosterType.DiamondPolish,
            ['lucky_clover'   /**/]: gm.BoosterType.LuckyClover,
            ['oil_can'        /**/]: gm.BoosterType.Speed,
            ['strength_drink' /**/]: gm.BoosterType.Strength,
            ['titan_rope'     /**/]: gm.BoosterType.TitanRope,
            ['pirate_hat'     /**/]: gm.BoosterType.PirateHat,
            ['spirit_jar'     /**/]: gm.BoosterType.SpiritJar,
            ['spooky_doll'    /**/]: gm.BoosterType.SpookyDoll,
            ['ant_spray'      /**/]: gm.BoosterType.AntSpray,
            ['wanted_poster'  /**/]: gm.BoosterType.WantedPoster,
            ['glove'          /**/]: gm.BoosterType.Glove,
            ['old_ham'        /**/]: gm.BoosterType.OldHam,
            ['pick_up'        /**/]: gm.BoosterType.PickUp,
        };
        boosters.push(...boosterNames.map(item => boosterNameToTypes[item]));
        return boosters.map(booster => {
            return {
                type: booster,
                unlock: this.isBoosterUnlocked(booster, area, level, unlockedArea, unlockedLevel),
            };
        });
    }

    public getBoosters(
        area: number, level: number, unlockedArea: number, unlockedLevel: number,
        filter: BoosterFiler = BoosterFiler.All
        ): BoosterInfo[] {
        const boosters: gm.BoosterType[] = [];
        boosters.push(gm.BoosterType.Dynamite);
        //boosters.push(gm.BoosterType.Undo);
        boosters.push(gm.BoosterType.Strength);
        boosters.push(gm.BoosterType.Speed);
        boosters.push(gm.BoosterType.TitanRope);
        boosters.push(gm.BoosterType.DiamondPolish)
        boosters.push(gm.BoosterType.LuckyClover);

        const featureManager = ee.ServiceLocator.resolve(FeatureManager);
        if (featureManager.isBoosterPickupEnabled) {
            boosters.push(gm.BoosterType.PickUp);
        }

        boosters.push(gm.BoosterType.PirateHat);
        boosters.push(gm.BoosterType.SpookyDoll);
        boosters.push(gm.BoosterType.SpiritJar);
        boosters.push(gm.BoosterType.AntSpray);
        boosters.push(gm.BoosterType.WantedPoster);
        boosters.push(gm.BoosterType.Glove);
        boosters.push(gm.BoosterType.OldHam);

        const boosterList = boosters.map(booster => {
            let freeBoosterAfterUnlock = ee.ServiceLocator.resolve(FeatureManager).freeBoosterAfterUnlock;
            const unlock = booster === gm.BoosterType.Dynamite ? true : this.isBoosterUnlocked(booster, area, level, unlockedArea, unlockedLevel);
            return {
                type: booster,
                unlock: unlock,
                balance: this.getBalance(booster) - (unlock ? 0 : freeBoosterAfterUnlock),
            };
        });

        switch (filter) {
            // sort đã unlock lên trên.
            case BoosterFiler.All:
                boosterList.sort((a, b) => {
                    return (b.unlock ? 1 : 0) - (a.unlock ? 1 : 0);
                });
                return boosterList;
            case BoosterFiler.Collected:
                return boosterList.filter(booster => booster.balance > 0);
            case BoosterFiler.Unlocked:
                return boosterList.filter(booster => booster.unlock);
        }
    }

    private isBoosterUnlocked(
        type: gm.BoosterType, area: number, areaLevel: number, unlockedArea: number, unlockedLevel: number): boolean {
        let [requiredArea, requiredLevel] = BoosterStorageUtils.getBoosterUnlockedAreaLevel(type);
        requiredArea -= 1;
        requiredLevel -= 1;
        const result =
            area > requiredArea ||
            (area === requiredArea && areaLevel >= requiredLevel) ||
            unlockedArea > requiredArea ||
            (unlockedArea === requiredArea && unlockedLevel >= requiredLevel);
        return result;
    }

    private getBoosterName(type: gm.BoosterType): string {
        const nameMap: { [key: number]: string } = {
            [gm.BoosterType.AntSpray]: 'AntSpray',
            [gm.BoosterType.Clock]: 'Clock',
            [gm.BoosterType.DiamondPolish]: 'DiamondPolish',
            [gm.BoosterType.Dynamite]: 'Dynamite',
            [gm.BoosterType.LuckyClover]: 'LuckyClover',
            [gm.BoosterType.PirateHat]: 'PirateHat',
            [gm.BoosterType.Speed]: 'Speed',
            [gm.BoosterType.SpiritJar]: 'SpiritJar',
            [gm.BoosterType.SpookyDoll]: 'SpookyDoll',
            [gm.BoosterType.Strength]: 'Strength',
            [gm.BoosterType.TitanRope]: 'TitanRope',
            [gm.BoosterType.WantedPoster]: 'WantedPoster',
            [gm.BoosterType.Glove]: 'Glove',
            [gm.BoosterType.OldHam]: 'OldHame',
            [gm.BoosterType.PickUp]: 'pickup',
        };
        return nameMap[type];
    }

    public override refreshData(): void {
        this.storage = this.dataManager.getValue(Key.Data, {});
    }
}
