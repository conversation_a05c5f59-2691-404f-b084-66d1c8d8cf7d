import * as ee from '../../libraries/ee/index';

/** Manages application configuration data. */
@ee.service('Config')
export abstract class ConfigManager implements ee.Service {
    public abstract initialize(): Promise<void>;

    public abstract destroy(): void;

    /** Gets the database timestamp. */
    public abstract getTimestamp(): number;

    /** Sets the default value for the specified key. */
    public abstract setDefaultValue<T>(key: string, value: T): void;

    /** Gets the value for the specified key. */
    public abstract getValue<T>(key: string): T;

    public abstract getString(key: string): string;
}

export const ConfigKey = {
    LEVELS_OVERRIDE: 'levels_override',
}