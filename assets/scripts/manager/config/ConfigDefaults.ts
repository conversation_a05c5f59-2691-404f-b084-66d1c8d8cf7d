const {ccclass, disallowMultiple, property} = cc._decorator;

@ccclass
export class ConfigDefaults extends cc.Component {
    @property(cc.JsonAsset)
    public remote_config_defaults: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public achievement_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public ads_config: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public ads_config_test: cc.JsonAsset = null;

    @property(cc.TextAsset)
    public ads_interstitial_settings: cc.TextAsset = null;

    @property(cc.JsonAsset)
    public auto_mine_config: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public booster_price: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public card_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public card_upgrade: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public chest_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public daily_quest: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public daily_reward: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public energy_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public event_map_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public experience: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public help_object: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public level_boosters: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public map_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public object_weight: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public player_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public prize_wheel: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public pvp_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public pvp_ticket_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public rating_config: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public shop_config: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public shop_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public special_reward: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public test_device_ids: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public vip_info: cc.JsonAsset = null;

    @property(cc.TextAsset)
    public card_per_lv_complete: cc.TextAsset = null;

    @property(cc.TextAsset)
    public chest_per_area: cc.TextAsset = null;

    @property(cc.TextAsset)
    public feature_config: cc.TextAsset = null;

    @property(cc.JsonAsset)
    public vip_offer_info: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public levels_override: cc.JsonAsset = null;

    @property(cc.JsonAsset)
    public daily_task_quest: cc.JsonAsset = null;
}