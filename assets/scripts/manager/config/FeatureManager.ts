import * as ee from '../../libraries/ee/index';
import jsyaml from '../../libraries/js-yaml.js'
import {ConfigManager} from "./ConfigManager";

/**
 * Đọc file feature_config.yaml
 */
@ee.service('FeatureManager')
export abstract class FeatureManager implements ee.Service {
    public abstract isNextLevelButtonEnabled: boolean;
    public abstract isBoosterPickupEnabled: boolean;
    public abstract showInstantWinDialogEnabled: boolean;
    public abstract levelApplyShowInstantWinDialog: number;
    public abstract moreCurrencyIsPack: boolean;
    public abstract freeBoosterAfterUnlock: number;
    public abstract enableAdsButtonLevels: number[];

    public abstract destroy(): void;
    public abstract enableAdsButtonAtLevel(levelIndex: number, areaIndex: number): boolean;
}

export class DefaultFeatureManager extends FeatureManager {
    public isNextLevelButtonEnabled: boolean;
    public isBoosterPickupEnabled: boolean;
    public showInstantWinDialogEnabled: boolean;
    public levelApplyShowInstantWinDialog: number;
    public moreCurrencyIsPack: boolean;
    public enableAdsButtonLevels: number[]; 
    
    public freeBoosterAfterUnlock: number;

    constructor(configManager: ConfigManager) {
        super();
        try {
            const configYaml = configManager.getString("feature_config");
            const config: IData = jsyaml.load(configYaml);
            this.isNextLevelButtonEnabled = config.show_next_lv_btn;
            this.isBoosterPickupEnabled = config.booster_pick_up_enabled;
            this.showInstantWinDialogEnabled = config.instant_early_level_you_win_dialog;
            this.levelApplyShowInstantWinDialog = config.early_level_step;
            this.moreCurrencyIsPack = config.more_currency_is_pack && false;
            this.freeBoosterAfterUnlock = config.free_booster_after_unlock;
            this.enableAdsButtonLevels = config.enable_ads_button_levels ?? [];
        } catch (e) {
            console.error(e);
        }
    }

    destroy(): void {
    }

    public enableAdsButtonAtLevel(levelIndex: number, areaIndex: number): boolean {
        let enableAdsButtonLevels = this.enableAdsButtonLevels
        let level = levelIndex + 1;
        let area = areaIndex;
        let worldLevel = (area * 20 + level);
        return enableAdsButtonLevels.includes(worldLevel)
    }
}

interface IData {
    show_next_lv_btn: boolean;
    booster_pick_up_enabled: boolean;
    instant_early_level_you_win_dialog: boolean;
    early_level_step: number;
    more_currency_is_pack: boolean;
    free_booster_after_unlock: number;
    enable_ads_button_levels: number[];
}