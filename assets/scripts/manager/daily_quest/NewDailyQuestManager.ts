import {DailyObjective, DailyQuest, DailyQuestManager, DailyReward,} from "./DailyQuestManager";
import {ConfigManager} from "../config/ConfigManager";
import {RawReward, RewardInfo, RewardManager} from "../reward/RewardManager";
import {DataManager} from "../data/DataManager";
import * as gm from '../../engine/gm_engine';
import {TimeManager} from "../time/TimeManager";
import {CountType, LogManager} from "../quest_achievement/LogManager";
import {StoreItem} from "../store/StoreManager";
import {DefaultPrefabLoader} from "../../utils/PrefabLoader";
import {LevelManager} from "../level/LevelManager";
import {GameStatic} from "../../utils/GameStatic";

interface TaskSaveData {
    progress: { [key: string]: TaskProgress };
    isClaimed: boolean;
    timePoint: number;
}

interface TaskProgress {
    target: number;
    collected: number;
    isClaimed: boolean
}

interface DailyQuestConfig {
    quest: RawDailyQuest[];
}

interface RawDailyQuest {
    area: number;
    reward: RawReward[];
    tasks: RawTask;
}

interface RawTask {
    reward_base_exp: number;
    reward: RawReward[];
    missionPool: RawMission[];
}

interface RawMission {
    name: string;
    target: number;
}

type ClaimCallback = () => void;


enum Key {
    Config = "new_daily_quest",
    Data = "daily_task_progress",
}

const NameTaskDict: { [key: string]: string } = {
    [CountType.CompleteLevel]: GameStatic.questTaskName.complete_level,
    [CountType.CollectGem]: GameStatic.questTaskName.collect_all_gems,
    [CountType.Coral]: GameStatic.questTaskName.collect_coral,
    [CountType.CollectChip]: GameStatic.questTaskName.collect_all_poker_chip,
    [CountType.Bone]: GameStatic.questTaskName.collect_bone,
    [CountType.BlueGhost]: GameStatic.questTaskName.collect_blue_ghost,
    [CountType.BlowUpSafe]: GameStatic.questTaskName.blow_up_safe,
    [CountType.Diamond]: GameStatic.questTaskName.collect_diamond,
    [CountType.FishBlue]: GameStatic.questTaskName.collect_fish_blue,
    [CountType.FishColorado]: GameStatic.questTaskName.collect_fish_green,
    [CountType.FishPuffer]: GameStatic.questTaskName.collect_fish_puffer,
    [CountType.FishPurple]: GameStatic.questTaskName.collect_fish_purple,
    [CountType.FishRed]: GameStatic.questTaskName.collect_fish_red,
    [CountType.Gold]: GameStatic.questTaskName.collect_gold,
    [CountType.Hedgehog]: GameStatic.questTaskName.collect_hedgehog,
    [CountType.Mineral]: GameStatic.questTaskName.collect_mineral,
    [CountType.Monkey]: GameStatic.questTaskName.collect_monkey,
    [CountType.MoneyBag]: GameStatic.questTaskName.collect_money_bag,
    [CountType.Pearl]: GameStatic.questTaskName.collect_pearl,
    [CountType.Mouse]: GameStatic.questTaskName.collect_rat,
    [CountType.Stone]: GameStatic.questTaskName.collect_rock,
    [CountType.Safe]: GameStatic.questTaskName.collect_safe,
    [CountType.Scorpion]: GameStatic.questTaskName.collect_scorpion,
    [CountType.BlueShark]: GameStatic.questTaskName.collect_shark_blue,
    [CountType.ClawChest]: GameStatic.questTaskName.collect_tool_box,
    [CountType.VultureWhite]: GameStatic.questTaskName.collect_vulture_white,
    [CountType.Wolf]: GameStatic.questTaskName.collect_wolf,
    [CountType.Niece]: GameStatic.questTaskName.rescue_julie,
    [CountType.SlotMachine]: GameStatic.questTaskName.win_slot_machine,
}

const IconDict: { [key: string]: string } = {
    [GameStatic.questTaskName.complete_level]: "achievement/star",
    [GameStatic.questTaskName.collect_all_gems]: "achievement/Gem-Colector",
    [GameStatic.questTaskName.collect_all_poker_chip]: "achievement/Chip-Colector",
    [GameStatic.questTaskName.collect_bone]: "daily_quest/bone",
    [GameStatic.questTaskName.collect_blue_ghost]: "achievement/Blue-Ghost-I",
    [GameStatic.questTaskName.collect_diamond]: "daily_quest/diamond",
    [GameStatic.questTaskName.collect_fish_blue]: "daily_quest/fish_blue",
    [GameStatic.questTaskName.collect_fish_green]: "daily_quest/fish_green",
    [GameStatic.questTaskName.collect_fish_puffer]: "daily_quest/fish_blue_spot",
    [GameStatic.questTaskName.collect_fish_purple]: "daily_quest/fish_violet",
    [GameStatic.questTaskName.collect_fish_red]: "daily_quest/fish-1",
    [GameStatic.questTaskName.collect_gold]: "daily_quest/gold",
    [GameStatic.questTaskName.collect_hedgehog]: "daily_quest/hedgehog",
    [GameStatic.questTaskName.collect_mineral]: "daily_quest/black_mineral",
    [GameStatic.questTaskName.collect_monkey]: "daily_quest/monkey",
    [GameStatic.questTaskName.collect_money_bag]: "daily_quest/money_bags",
    [GameStatic.questTaskName.collect_pearl]: "daily_quest/icon_pearl",
    [GameStatic.questTaskName.collect_rat]: "daily_quest/mouse",
    [GameStatic.questTaskName.collect_rock]: "daily_quest/stone",
    [GameStatic.questTaskName.collect_safe]: "daily_quest/icon_safe_box_close",
    [GameStatic.questTaskName.collect_scorpion]: "daily_quest/scorpions",
    [GameStatic.questTaskName.collect_shark_blue]: "daily_quest/shark",
    [GameStatic.questTaskName.collect_tool_box]: "daily_quest/chest_claw_spike",
    [GameStatic.questTaskName.collect_vulture_white]: "daily_quest/vulture_white",
    [GameStatic.questTaskName.collect_wolf]: "daily_quest/wolves",
    [GameStatic.questTaskName.rescue_julie]: "achievement/Rescuer",
    [GameStatic.questTaskName.win_slot_machine]: "achievement/Jackpot",
    [GameStatic.questTaskName.blow_up_safe]: "daily_quest/icon_safe_box_opened",
    [GameStatic.questTaskName.collect_coral]: "daily_quest/big_coral"
};


class DailyRewardImpl implements DailyReward {
    public constructor(
        private readonly config: RawReward[],
        private readonly rewardManager: RewardManager) {
    }

    public get items(): RewardInfo[] {
        // Config.
        const loaders = {
            storePrefabLoader: (item: StoreItem) => {
                const dict: { [key: string]: string } = {
                    [StoreItem.Gold]: "icon_gold",
                };
                const commonPrefabPath = `prefabs/daily_quest/${dict[item]}`;
                return new DefaultPrefabLoader(commonPrefabPath);
            },
        };
        return this.config.map(item => this.rewardManager.createReward(item, loaders));
    }
}

class DailyObjectiveImpl implements DailyObjective {
    public constructor(
        private readonly _name: string,
        private readonly _dataProgress: TaskProgress,
        private readonly _expReward: number,
        private readonly _reward: RewardInfo[],
        private readonly _onClaimCallback: ClaimCallback) {
    }

    public get rewards(): RewardInfo[] {
        return this._reward;
    }

    public get expReward(): number {
        return this._expReward;
    }

    public get name(): string {
        return this._name;
    }

    public get target(): number {
        return this._dataProgress.target;
    }

    public get icon(): string {
        return IconDict[this._name];
    }

    public get progress(): number {
        return this._dataProgress.collected;
    }

    public isCompleted(): boolean {
        return this.progress >= this.target;
    }

    public get isClaimed() {
        return this._dataProgress.isClaimed;
    }

    canClaim(): boolean {
        return this.isCompleted() && !this.isClaimed;
    }

    claim(): void {
        this._dataProgress.collected = this._dataProgress.target;
        this._dataProgress.isClaimed = true;
        this._onClaimCallback && this._onClaimCallback();
    }
}

class DailyQuestImpl implements DailyQuest {
    reward: DailyReward;
    objectives: DailyObjective[];

    constructor(
        private readonly _data: TaskSaveData,
        private readonly _rewardManager: RewardManager,
        private readonly _timeManager: TimeManager,
        private readonly _rawQuest: RawDailyQuest,
        private readonly _onClaimCallback: ClaimCallback,
    ) {
        this.refreshData();
    }

    public refreshData() {
        this.reward = new DailyRewardImpl(this._rawQuest.reward, this._rewardManager);
        this.objectives = [];
        let exp = 0;

        Object.keys(this._data.progress).forEach((name) => {
            const progress = this._data.progress[name];
            this.objectives.push(new DailyObjectiveImpl(
                name,
                progress,
                this._rawQuest.tasks.reward_base_exp + exp,
                this.generateRewards(),
                this._onClaimCallback,
            ))
            exp++;
        })
    }

    public isTimeOut(): boolean {
        return this.getRemainingTime() <= 0;
    }

    public getRemainingTime(): number {
        return this._timeManager.getSecondsLeftToNextDay();
    }

    public isCompleted(): boolean {
        return this.objectives.every(item => item.isCompleted());
    }

    public claim(): void {
        this._data.isClaimed = true;
        this._onClaimCallback && this._onClaimCallback();
    }

    public isClaimed(): boolean {
        return this._data.isClaimed;
    }

    public canClaim(): boolean {
        return this.isCompleted() && !this.isClaimed();
    }

    private generateRewards(): RewardInfo[] {
        let rewards: RewardInfo[] = [];
        const loader = {
            randomCardPrefabLoader: () => {
                return new DefaultPrefabLoader(`prefabs/daily_quest/icon_card`);
            },
        };
        for (let r of this._rawQuest.tasks.reward) {
            rewards.push(this._rewardManager.createReward(r, loader));
        }
        return rewards;
    }
}

export class NewDailyQuestManager extends DailyQuestManager {
    private readonly _config: RawDailyQuest[];
    private _taskSaveData: TaskSaveData;
    private _currentQuest: DailyQuestImpl;
    /** 1-sec updater. */
    private readonly node: cc.Node;

    private _currentRawDailyQuest: RawDailyQuest;

    constructor(
        configManager: ConfigManager,
        private readonly _dataManager: DataManager,
        private readonly _rewardManager: RewardManager,
        private readonly _timeManager: TimeManager,
        private readonly _logManager: LogManager,
        private readonly _levelManager: LevelManager,
    ) {
        super();

        this._config = configManager.getValue<DailyQuestConfig>(Key.Config).quest;

        this.changeCurrentQuest();
        this._taskSaveData = this._dataManager.getValue(Key.Data, this.createDefaultData());

        this._currentQuest = new DailyQuestImpl(
            this._taskSaveData,
            this._rewardManager,
            this._timeManager,
            this._currentRawDailyQuest,
            this.saveData.bind(this),
        )

        this.node = new cc.Node();
        cc.director.getScheduler().schedule(() => this.update(), this.node, 1);

        _logManager.addObserver("daily_task_quest", (dict: { [key: string]: number }) => {
            for (const key of Object.keys(dict)) {
                const taskName = NameTaskDict[key];
                if (taskName !== undefined) {
                    const value = dict[key];
                    this.setQuestProgress(taskName, value);
                    this.saveData();
                }
            }
        });
    }

    private update(): void {
        if (this.shouldResetCurrentQuest()) {
            this.changeCurrentQuest();
            this.resetData();
        }
    }

    /**
     * Only used for test
     * List of tasks:
     *    -  collect gold ✅
     *    -  collect money bag ✅
     *    -  collect diamond ✅
     *    -  collect rock ✅
     *    -  collect bone ✅
     *    -  collect wolf ✅
     *    -  collect all gems ✅
     *    -  collect fish green ✅
     *    -  collect rat ✅
     *    -  collect fish blue ✅
     *    -  collect pearl ✅
     *    -  collect fish red ✅
     *    -  collect coral ✅
     *    -  collect blue ghost ✅
     *    -  collect scorpion ✅
     *    -  rescue Julie ✅
     *    -  collect all poker chip ✅
     *    -  win slot machine ✅
     *    -  collect tool box ✅
     *    -  blow up safe ✅
     *    -  collect safe ✅
     *    -  collect hedgehog ✅
     *    -  collect monkey ✅
     *    -  collect fish puffer ✅
     *    -  collect fish purple ✅
     *    -  collect mineral ✅
     *    -  collect vulture white ✅
     */
    public forceChangeCurrentQuest() {
        this._taskSaveData.isClaimed = false;
        this._taskSaveData.progress = {
            "collect_money_bag": {target: 5, collected: 0, isClaimed: false},
            "blow_up_safe": {target: 5, collected: 0, isClaimed: false},
            "collect_safe": {target: 5, collected: 0, isClaimed: false},
        };
        this._taskSaveData.timePoint = this._timeManager.secondsNow();

        this._currentQuest = new DailyQuestImpl(
            this._taskSaveData,
            this._rewardManager,
            this._timeManager,
            this._currentRawDailyQuest,
            this.saveData.bind(this),
        )
        this.saveData();
    }

    private shouldResetCurrentQuest() : boolean {
        let now = this._timeManager.dateNow();
        let latest = Math.floor(this._taskSaveData.timePoint / 86400);
        return now - latest > 0;
    }

    private resetData() {
        this._taskSaveData.isClaimed = false;
        this._taskSaveData.progress = this.generate3Tasks(this._currentRawDailyQuest.tasks.missionPool);
        this._taskSaveData.timePoint = this._timeManager.secondsNow();

        this._currentQuest = new DailyQuestImpl(
            this._taskSaveData,
            this._rewardManager,
            this._timeManager,
            this._currentRawDailyQuest,
            this.saveData.bind(this),
        )
        this.saveData();
    }

    private createDefaultData() {
        const defaultSaveData: TaskSaveData =
            {
                progress: this.generate3Tasks(this._currentRawDailyQuest.tasks.missionPool),
                isClaimed: false,
                timePoint: this._timeManager.secondsNow(),
            }
        return defaultSaveData;
    }

    public destroy(): void {
        this._logManager.removeObserver("daily_task_quest");
    }

    public changeCurrentQuest(): void {
        let area = this._levelManager.getLastUnlockedArea();
        this.changeCurrentQuestToArea(area);
    }

    public changeCurrentQuestToArea(areaId: number): void {
        this._currentRawDailyQuest = this._config.find(item => item.area === areaId) || this._config[0];
    }

    public completeAllSubTask(): void {
        for(let key of Object.keys(this._taskSaveData.progress)) {
            this._taskSaveData.progress[key].collected = this._taskSaveData.progress[key].target;
        }
        this.saveData();
    }

    private saveData() {
        this._dataManager.setValue(Key.Data, this._taskSaveData);
    }

    private generate3Tasks(missionPool: RawMission[]): { [key: string]: TaskProgress } {
        const randomIndex1 = gm.MathUtils.getRandomInt(0, missionPool.length - 1);
        let randomIndex2 = randomIndex1;
        while (randomIndex2 === randomIndex1) {
            randomIndex2 = gm.MathUtils.getRandomInt(0, missionPool.length - 1);
        }
        let data1 = missionPool[randomIndex1];
        let data2 = missionPool[randomIndex2];
        return {
            "complete_level": {target: 1, collected: 0, isClaimed: false},
            [data1.name]: {target: data1.target, collected: 0, isClaimed: false},
            [data2.name]: {target: data2.target, collected: 0, isClaimed: false},
        };
    }

    getCurrentQuest(): DailyQuest {
        return this._currentQuest;
    }

    getQuestProgress(name: string): number {
        return this._taskSaveData.progress[name].collected;
    }

    setQuestProgress(name: string, progress: number): void {
        if (this._taskSaveData.progress[name]) {
            this._taskSaveData.progress[name].collected += progress;
        }
        this.saveData();
    }
}
