import * as ee_x from "@senspark/ee-x";
import { AdResult } from "@senspark/ee-x";
import * as gm from '../../engine/gm_engine';
import { AdsManager } from "./AdsManager";
import { InterstitialSettingsParser } from "./InterstitialSettingsParser";
import { resolve } from "app-root-path";

export class SimulatedAdsManager extends AdsManager {
    public removeRewardedAdsReadyShow(id: string): void {
    }
    public isReadyShowInterstitialVideo(): boolean {
        return true;
    }
    public skipInterstitial: boolean;

    public constructor() {
        super();
    }

    public destroy(): void {
    }

    public showRewardedVideo(): Promise<ee_x.AdResult> {
        return new Promise((resolve) => {
            const rnd = gm.MathUtils.getRandom(0, 100);
            if (rnd > 30) {
                resolve(ee_x.AdResult.Completed);
            } else {
                resolve(ee_x.AdResult.Failed);
            }
        })
    }

    public onRewardedAdsShowed(callBack: Function): void {
    }

    public showInterstitialAd(): Promise<ee_x.AdResult> {
        return new Promise((resolve) => {
            const rnd = gm.MathUtils.getRandom(0, 100);
            if (rnd > 30) {
                resolve(ee_x.AdResult.Completed);
            } else {
                resolve(ee_x.AdResult.Failed);
            }
        })
    }

    public onInterstitialShowed(callBack: Function): void {
    }

    public canShowLevelIntroInterstitialAd(level: number): boolean {
        return false;
    }

    public canShowLevelOutroInterstitialAd(level: number): boolean {
        return false;
    }

    public removeAdsByVip(): boolean {
        return true;
    }

    public setUseAdsTest(value: boolean): void {
    }

    openAdsInspector(): void {
    }

    getUseAdsTest(): boolean {
        return false;
    }

    isReadyShowRewardedVideo(): boolean {
        return true;
    }

    onRewardedAdsReadyShow(id: string, callBack: Function): void {
    }
}
