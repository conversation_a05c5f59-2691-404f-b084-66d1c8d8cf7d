import {GameScene} from "../../scenes/game_scene/GameScene";
import * as ee from "../../libraries/ee/index";
import {AreaInfo, GameMode, LevelInfo, LevelManager} from "../level/LevelManager";
import {SceneManager} from "./SceneManager";
import * as gm from "../../engine/gm_engine";
import {MenuSceneAction} from "../../scenes/MenuScene";

/** Story: Có 12 Area, mỗi area có 20 level (map_info.json)
 * Events: Có 7 Area, mỗi area có 20 level (event_map_info.json)
 * **/
export class SceneUtils {

    /** GameScene load from HomeLayer */
    public static fromHome: boolean;

    /** Load level của mode Story
     * @param area {number} từ 0 -> x
     * @param level {number} từ 0 -> x
     * @param boosters {gm.BoosterType[]} danh sách booster (optional)
     * @param recording {boolean} Enable recording ? (optional)
     * **/
    public static async loadGameSceneStory(
        area: number,
        level: number,
        boosters: gm.BoosterType[] = null,
        recording: boolean = false,
    ): Promise<GameScene> {
        cc.log(`Request loadGameScene area: ${area} level: ${level}`)

        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const maxArea = levelManager.getStoryAreas();
        const maxLevel = levelManager.getLevelsPerArea(GameMode.STORY);

        area = Math.max(0, Math.min(area, maxArea - 1));
        level = Math.max(0, Math.min(level, maxLevel - 1));

        const areaInfo = levelManager.getStoryAreaInfo(area);
        const levelInfo = areaInfo.levels[level];

        cc.log(`Confirm loadGameScene area: ${areaInfo.index} level: ${levelInfo.index}`)
        return this.loadGameScene(areaInfo, levelInfo, boosters, recording, true);
    }

    public static async loadGameSceneEvent(
        area: number,
        level: number,
        boosters: gm.BoosterType[] = null
    ): Promise<GameScene> {
        cc.log(`Request loadGameScene Event area: ${area} level: ${level}`)

        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const maxArea = levelManager.getEventAreas();
        const maxLevel = levelManager.getLevelsPerArea(GameMode.EVENT);

        area = Math.max(0, Math.min(area, maxArea - 1));
        level = Math.max(0, Math.min(level, maxLevel - 1)); 

        const areaInfo = levelManager.getEventAreaInfo(area);
        const levelInfo = areaInfo.levels[level];

        cc.log(`Confirm loadGameScene Event area: ${areaInfo.index} level: ${levelInfo.index}`)
        return this.loadGameScene(areaInfo, levelInfo, boosters, true);
    }

    /** Load level bất kỳ
     * @param areaInfo {AreaInfo}
     * @param levelInfo {LevelInfo}
     * @param boosters {gm.BoosterType[]} danh sách booster (optional)
     * @param recording {boolean} Enable recording ? (optional)
     * @param fromHome {boolean} GameScene load from HomeLayer ?
     * **/
    public static async loadGameScene(
        areaInfo: AreaInfo,
        levelInfo: LevelInfo,
        boosters: gm.BoosterType[] = null,
        recording: boolean = false,
        fromHome: boolean = false,
    ): Promise<GameScene> {
        if (!areaInfo || !levelInfo) {
            return null;
        }
        this.fromHome = fromHome;
        const sceneManager = ee.ServiceLocator.resolve(SceneManager);
        if (boosters && boosters.length === 0) {
            boosters = null;
        }
        return await sceneManager.loadScene('game_scene', GameScene, {
            post: async scene => {
                await scene.loadLevel({
                    area: areaInfo.index,
                    level: levelInfo.index,
                    targets: levelInfo.targets,
                    areaType: areaInfo.areaType,
                    boosters,
                    isRecordingEnabled: recording,
                });
            },
        });
    }

    public static getGameScene(): GameScene | null {
        const scene = cc.director.getScene();
        return scene.getComponentInChildren("GameSceneImpl")
    }

    public static parseGameModeToMenuSceneAction(mode: GameMode) {
        if (this.fromHome) {
            return 0;
        }
        switch (mode) {
            case GameMode.STORY:
                return MenuSceneAction.OpenMap;
            case GameMode.EVENT:
                return MenuSceneAction.OpenEvent;
            case GameMode.CHARACTER:
                return MenuSceneAction.OpenCharacterEvent;
        }
    }
}