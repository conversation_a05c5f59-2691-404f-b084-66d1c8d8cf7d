import * as ee_x from "@senspark/ee-x";
import * as ee from "../../libraries/ee/index";
import {DialogManager} from "../../libraries/ee/index";
import * as gm from '../../engine/gm_engine';

import {CheatManager} from "./ICheatManager";
import {GameScene} from "../../scenes/game_scene/GameScene";
import {SceneUtils} from "../scene/SceneUtils";
import {LevelManager} from "../level/LevelManager";
import {DataManager} from "../data/DataManager";
import {StoreItem, StoreManager} from "../store/StoreManager";
import {AutoMineManager} from "../auto_mine/AutoMineManager";
import {DebugUtils} from "../../utils/DebugUtils";
import {TimeCheat} from "./TimeCheat";
import {TimeManager} from "../time/TimeManager";
import {EditorCommandReceiver} from "./EditorCommandReceiver";
import {UserProfileManager} from "../profile/UserProfileManager";
import {DailyQuestManager} from "../daily_quest/DailyQuestManager";
import {RewardUtils} from "../reward/RewardUtils";
import {SceneName, TrackAdsRewardWatch} from "../analytics/AnalyticsConfig";
import {CardManager} from "../card/CardManager";
import {MenuScene} from "../../scenes/MenuScene";
import {ChestManager} from "../chest/ChestManager";
import {BoosterStorageManager} from "../booster/BoosterStorageManager";
import {AdsManager} from "../ads/AdsManager";
import {VipManager} from "../vip/VipManager";
import {FeatureManager} from "../config/FeatureManager";
import {NewDailyQuestManager} from "../daily_quest/NewDailyQuestManager";
import {HiddenTempleManager} from "../../scenes/hidden_temple/HiddenTempleManager";
import {OfferCategory} from "../../scenes/common/OfferCategory";
import {IapShopCategory} from "../../scenes/IapShopCategory";
import {RubyPacksDialog} from "../../scenes/game_scene/dialogs/RubyPacksDialog";
import {PiggyBankManager} from "../../scenes/PiggyBank/PiggyBankManager";
import {EditProfileDialog} from "../../dialog/EditProfileDialog";
import {GameServerManager} from "../game_server/GameServerManager";
import {AudioManager} from "../audio/AudioManager";
import {SoundType} from "../audio/SoundType";
import {isServerTest} from "../level/DefaultServerConfig";
import {SceneManager} from "../scene/SceneManager";
import {PlayPassManager} from "../../scenes/play_pass/PlayPassManager";
import {DefaultLeaderboardManager} from "../leaderboard/DefaultLeaderboardManager";
import {ChatNetworkManager} from "../../team/chat/Manager/ChatNetworkManager";
import {MyTeamManager} from "../team/MyTeamManager";

export class DefaultCheatManager extends CheatManager {
    private _bridge: ee_x.ICommandReceiver
    private readonly _observerHandle: number = -1;
    private readonly _timeCheat: TimeCheat;
    private readonly _commandMaps: Map<string, (data: string) => void> = new Map<string, (data: string) => void>();
    private readonly _dataManager: DataManager;
    private _skipInterstitial: boolean = false;

    public constructor(dataManager: DataManager, timeManager: TimeManager) {
        super();
        if (CC_EDITOR || CC_PREVIEW) {
            this._bridge = new EditorCommandReceiver(null, null, null);
        } else {
            this._bridge = ee_x.PluginManager.createPlugin<ee_x.ICommandReceiver>(ee_x.Plugin.CommandReceiver);
        }

        this._dataManager = dataManager;
        this._skipInterstitial = dataManager.getValue("skip_interstitial", false);

        this._bridge.initialize(dataManager);

        this._observerHandle = this._bridge.addObserver({
            onReceived: this.onCommandReceived.bind(this)
        });

        this._timeCheat = new TimeCheat(dataManager, timeManager);

        // editor web only
        if (CC_PREVIEW && !CC_JSB) {
            window["cheatManager"] = this;
            console.log("nhanc19 add cheatManager to window");
        } else {
            let poco = require("../../../third_party/Poco");
            window["poco"] = new poco();
        }
    }

    initialize() {
        this.addCommands();
        const adsManager = ee.ServiceLocator.resolve(AdsManager);
        adsManager.skipInterstitial = this._skipInterstitial;
    }

    destroy(): void {
        if (this._observerHandle >= 0) {
            this._bridge.removeObserver(this._observerHandle);
        }
    }

    get isCheatDevice(): boolean {
        return this._bridge.isCheatDevice;
    }

    private addCommands(): void {
        const allCommands = [
            // First order
            {
                cmd: "win",
                desc: "Thắng Level",
                sample: "_",
                order: 0,
                group: "Game",
                callback: this.win.bind(this)
            },
            {
                cmd: "lose",
                desc: "Thua Level",
                sample: "_",
                order: 1,
                group: "Game",
                callback: this.lose.bind(this)
            },

            // Group Feature
            {
                cmd: "bật tắt Pickup",
                desc: "Bật Pickup",
                sample: "true",
                order: 0,
                group: "Feature",
                callback: this.togglePickup.bind(this)
            },

            // Group Resources
            {
                cmd: "add_ruby",
                desc: "Cộng/trừ Ruby",
                sample: "5000",
                order: 0,
                group: "Tài nguyên",
                callback: this.addRuby.bind(this)
            },
            {
                cmd: "add_gold",
                desc: "Cộng/trừ Vàng",
                sample: "5000",
                order: 1,
                group: "Tài nguyên",
                callback: this.addGold.bind(this)
            },
            {
                cmd: "add_energy",
                desc: "Cộng/trừ Energy",
                sample: "200",
                order: 2,
                group: "Tài nguyên",
                callback: this.addEnergy.bind(this)
            },
            {
                cmd: "add_vip_point",
                desc: "Thêm VIP point",
                sample: "200",
                order: 3,
                group: "Tài nguyên",
                callback: this.addVipPoint.bind(this)
            },
            {
                cmd: "add_exp",
                desc: "Cộng điểm kinh nghiệm _",
                sample: "100",
                order: 4,
                group: "Tài nguyên",
                callback: this.addExp.bind(this)
            },
            {
                cmd: "add_dynamite",
                desc: "Thêm Dynamite",
                sample: "1",
                order: 5,
                group: "Tài nguyên",
                callback: this.addDynamite.bind(this)
            },
            {
                cmd: "receive_this_card",
                desc: "Nhận Card đang được chọn",
                sample: "_",
                order: 6,
                group: "Tài nguyên",
                callback: this.receiveThisCard.bind(this)
            },
            {
                cmd: "collect_full_set_card",
                desc: "Nhận đủ một set card",
                sample: "set_pirate",
                order: 7,
                group: "Tài nguyên",
                callback: this.receiveFullSetCard.bind(this)
            },

            // Group Map
            {
                cmd: "open_lv",
                desc: "Mở Level Story _",
                sample: "15",
                order: 0,
                group: "Map",
                callback: this.openLv.bind(this)
            },
            {
                cmd: "open_lv_event",
                desc: "Mở Level Event _",
                sample: "4 15",
                order: 1,
                group: "Map",
                callback: this.openLvEvent.bind(this)
            },
            {
                cmd: "complete_story_area",
                desc: "Hoàn thành một story area",
                sample: "_",
                order: 2,
                group: "Map",
                callback: this.completeStoryArea.bind(this)
            },
            {
                cmd: "complete_event_area",
                desc: "Hoàn thành event area hiện tại",
                sample: "_",
                order: 3,
                group: "Map",
                callback: this.completeEventArea.bind(this)
            },
            {
                cmd: "complete_jaki_area",
                desc: "Hoàn thành jaki area",
                sample: "_",
                order: 4,
                group: "Map",
                callback: this.completeJakiArea.bind(this)
            },
            {
                cmd: "open_events",
                desc: "Mở mode sự kiện",
                sample: "_",
                order: 5,
                group: "Map",
                callback: this.openEvents.bind(this)
            },

            // Group Level
            {
                cmd: "unlimited_time",
                desc: "Thời gian chơi vô hạn",
                sample: "_",
                order: 0,
                group: "Level",
                callback: this.unlimitedTime.bind(this)
            },
            {
                cmd: "auto_mine",
                desc: "Mở tính năng tự động đào",
                sample: "_",
                order: 1,
                group: "Level",
                callback: this.autoMine.bind(this)
            },

            // Group Tasks & Chest
            {
                cmd: "next_day",
                desc: "Tua nhanh đến ngày hôm sau",
                sample: "_",
                order: 0,
                group: "Tasks & Chest",
                callback: this.nextDay.bind(this)
            },
            {
                cmd: "change_task",
                desc: "Load lại task",
                sample: "_",
                order: 1,
                group: "Tasks & Chest",
                callback: this.forceChangeCurrentQuest.bind(this)
            },
            {
                cmd: "complete_daily_task",
                desc: "Hoàn thành ngay nhiệm vụ trong ngày",
                sample: "_",
                order: 2,
                group: "Tasks & Chest",
                callback: this.completeDailyTask.bind(this)
            },
            {
                cmd: "complete_all_sub_task",
                desc: "Hoàn thành ngay tất cả nhiệm vụ nhỏ",
                sample: "_",
                order: 3,
                group: "Tasks & Chest",
                callback: this.completeAllSubTask.bind(this)
            },

            // Group Ads
            {
                cmd: "no_interstitial",
                desc: "Skip hết interstitial",
                sample: this._skipInterstitial ? "false" : "true",
                order: 0,
                group: "Quảng cáo",
                callback: this.skipInterstitial.bind(this)
            },
            {
                cmd: "play_interstitial",
                desc: "Phát ngay 1 quảng cáo Interstitial",
                sample: "_",
                order: 1,
                group: "Quảng cáo",
                callback: this.playInterstitial.bind(this)
            },
            {
                cmd: "play_rewarded",
                desc: "Phát ngay 1 quảng cáo Rewarded",
                sample: "_",
                order: 2,
                group: "Quảng cáo",
                callback: this.playRewarded.bind(this)
            },
            {
                cmd: "use_test_ads",
                desc: "Sử dụng Quảng cáo Test (Phải tắt game mở lại)",
                sample: this.getUseAdsTest(),
                order: 3,
                group: "Quảng cáo",
                callback: this.useAdsTest.bind(this)
            },
            {
                cmd: "open_ads_inspector",
                desc: "Mở Ads Debugger",
                sample: "_",
                order: 4,
                group: "Quảng cáo",
                callback: this.openAdsInspector.bind(this)
            },

            // Group Misc
            {
                cmd: "toggle_debug_mode",
                desc: "Bật Debug Mode",
                sample: "true",
                order: 0,
                group: "Misc",
                callback: this.toggleDebugMode.bind(this)
            },
            {
                cmd: "clear_data",
                desc: "Xoá hết game data",
                sample: "_",
                order: 1,
                group: "Misc",
                callback: this.clearData.bind(this)
            },
            {
                cmd: "print_hierarchy",
                desc: "Print hierarchy (debug)",
                sample: "_",
                order: 2,
                group: "Misc",
                callback: this.printScene.bind(this)
            },
            {
                cmd: "print_dialog",
                desc: "Print dialog (debug)",
                sample: "_",
                order: 3,
                group: "Misc",
                callback: this.printDialog.bind(this)
            },

            // Group Jaki
            {
                cmd: "unlock_jaki_event",
                desc: "Mở Jaki Event",
                sample: "_",
                order: 0,
                group: "Jaki",
                callback: this.unlockJakiEvent.bind(this)
            },

            // Group Hidden Temple
            {
                cmd: "add_axe",
                desc: "Cộng búa rìu",
                sample: "100",
                order: 0,
                group: "Hidden Temple",
                callback: this.addAxe.bind(this)
            },
            {
                cmd: "map_hidden_temple",
                desc: "open map (1-5))",
                sample: "1",
                order: 1,
                group: "Hidden Temple",
                callback: this.openMap.bind(this)
            },
            {
                cmd: "end_event_20",
                desc: "kết thúc event (giây)",
                sample: "20",
                order: 2,
                group: "Hidden Temple",
                callback: this.endEvent.bind(this)
            },
            {
                cmd: "reset_event",
                desc: "Bắt đầu event (7 ngày)",
                sample: "_",
                order: 3,
                group: "Hidden Temple",
                callback: this.beginEvent.bind(this)
            },

            // Group Piggy Bank
            {
                cmd: "add_ruby_bank",
                desc: "Cộng ruby trong piggy bank",
                sample: "1500",
                order: 0,
                group: "Piggy Bank",
                callback: this.addRubyPiggyBank.bind(this)
            },
            {
                cmd: "end_event_bank",
                desc: "kết thúc event bank (giây)",
                sample: "20",
                order: 1,
                group: "Piggy Bank",
                callback: this.endEventBank.bind(this)
            },
            {
                cmd: "reset_event_bank",
                desc: "reset event",
                sample: "_",
                order: 2,
                group: "Piggy Bank",
                callback: this.resetEventBank.bind(this)
            },

            // Group Play Pass
            {
                cmd: "add_play_pass",
                desc: "Cộng thể play pass",
                sample: "3",
                order: 0,
                group: "Play Pass",
                callback: this.addPlayPass.bind(this)
            },
            {
                cmd: "add_watch_ads",
                desc: "Cộng số lần xem quảng cáo",
                sample: "5",
                order: 2,
                group: "Play Pass",
                callback: this.addWatchAds.bind(this)
            },
            {
                cmd: "set_level_pass",
                desc: "set level pass (0-30)",
                sample: "0",
                order: 3,
                group: "Play Pass",
                callback: this.setLevelPass.bind(this)
            },
            {
                cmd: "end_event_play_pass",
                desc: "kết thúc event play pass (giây)",
                sample: "20",
                order: 4,
                group: "Play Pass",
                callback: this.endEvenPlayPass.bind(this)
            },
            {
                cmd: "reset_event_play_pass",
                desc: "Reset event play pass",
                sample: "_",
                order: 5,
                group: "Play Pass",
                callback: this.resetEventPlayPass.bind(this)
            },

            // Group Offer
            {
                cmd: "show_offer_information",
                desc: "Show offer information",
                sample: "_",
                order: 0,
                group: "Offer",
                callback: this.showOfferInformation.bind(this)
            },

            // Group Account Level
            {
                cmd: "increase_account_level",
                desc: "Increase account level",
                sample: "_",
                order: 0,
                group: "Account Level",
                callback: this.increaseAccountLevel.bind(this)
            },

            // Group Test feature
            {
                cmd: "test_dialog",
                desc: "Test Dialog",
                sample: "_",
                order: 0,
                group: "Test feature",
                callback: this.testDialog.bind(this)
            },
            {
                cmd: "test_sound",
                desc: "Test Sound (input: sound name)",
                sample: "ButtonPress",
                order: 1,
                group: "Test feature",
                callback: this.testSound.bind(this)
            },
            {
                cmd: "upload_data",
                desc: "Upload data",
                sample: "_",
                order: 0,
                group: "Game Server",
                callback: this.forceUploadData.bind(this)
            },
            {
                cmd: "add_score",
                desc: "Add score",
                sample: "1000",
                order: 1,
                group: "Game Server",
                callback: this.addScore.bind(this)
            },
            {
                cmd: "reload_leaderboard",
                desc: "Reload leaderboard",
                sample: "_",
                order: 2,
                group: "Game Server",
                callback: this.reloadLeaderboard.bind(this)
            },
            {
                cmd: "reset_weekly_season",
                desc: "Reset weekly season",
                sample: "_",
                order: 3,
                group: "Game Server",
                callback: this.resetWeeklySeason.bind(this)
            },
            {
                cmd: "delete_account",
                desc: "Delete Account",
                sample: "_",
                order: 4,
                group: "Game Server",
                callback: this.deleteAccount.bind(this)
            },
            // Team
            {
                cmd: "add_bot",
                desc: "Thêm bot",
                sample: "1",
                order: 0,
                group: "Team",
                callback: this.addBotToTeam.bind(this)

            },
            {
                cmd: "add_virtual_team",
                desc: "Thêm team",
                sample: "_",
                order: 1,
                group: "Team",
                callback: this.addVirtualTeam.bind(this)
            },
            {
                cmd: "clear_ban",
                desc: "Clear ban",
                sample: "_",
                order: 1,
                group: "Team",
                callback: this.cleanTeamBan.bind(this)
            },
            // Chat
            {
                cmd: "add_chat_text",
                desc: "Tạo chat text",
                sample: "_",
                order: 0,
                group: "Room Chat",
                callback: this.addChatText.bind(this)
            },
            {
                cmd: "create_request",
                desc: "Tạo request",
                sample: "_",
                order: 1,
                group: "Room Chat",
                callback: this.addRequest.bind(this)
            },
            {
                cmd: "help_nearest_request",
                desc: "Giúp request gần nhất",
                sample: "_",
                order: 2,
                group: "Room Chat",
                callback: this.helpNearestRequest.bind(this)
            },
            {
                cmd: "reset_time_for_next_request",
                desc: "Reset time request",
                sample: "_",
                order: 3,
                group: "Room Chat",
                callback: this.resetTimeForNextRequest.bind(this)
            },
        ];
        for (const cmd of allCommands) {
            this._commandMaps.set(cmd.cmd, cmd.callback);
            delete cmd.callback; // set vào Map rồi thì ko cần nữa
            this._bridge.addCommand(cmd);
        }
    }


    private togglePickup(data: string) {
        const featureManager = ee.ServiceLocator.resolve(FeatureManager);
        featureManager.isBoosterPickupEnabled = data.toLowerCase() == "true";
    }

    private onCommandReceived(message: ee_x.ICommand): void {
        cc.log(`Received message: {message}`);
        if (!this._commandMaps.has(message.command)) {
            return;
        }
        const callback = this._commandMaps.get(message.command);
        if (callback) {
            callback(message.data);
        }
    }

    private async testEditProfileDialog() {
        let dialog = await EditProfileDialog.create();
        dialog.show(ee.ServiceLocator.resolve(DialogManager));
    }

    private win(): void {
        const gameScene = SceneUtils.getGameScene();
        if (!gameScene) {
            return;
        }
        const progress = gameScene.levelView.progress;
        progress.progress += 1e4;
        progress.time = 60;
    }

    private lose(): void {
        const gameScene = SceneUtils.getGameScene();
        if (!gameScene) {
            return;
        }
        const progress = gameScene.levelView.progress;
        progress.progress = 0;
        progress.time = 60;
    }

    private addRuby(data: string): void {
        try {
            let amount = parseInt(data);
            const tag = StoreItem.Ruby;
            this.addItemBalance(tag, amount);
        } catch (error) {
            cc.log(error);
        }
    }

    private addGold(data: string): void {
        try {
            let amount = parseInt(data);
            const tag = StoreItem.Gold;
            this.addItemBalance(tag, amount);
        } catch (error) {
            cc.log(error);
        }
    }

    private addEnergy(data: string): void {
        try {
            let amount = parseInt(data);
            const tag = StoreItem.Energy;
            this.addItemBalance(tag, amount);
        } catch (error) {
            cc.log(error);
        }
    }

    private addVipPoint(data: string): void {
        try {
            let vipMgr = ee.ServiceLocator.resolve(VipManager);
            let amount = parseInt(data);
            vipMgr.addVipPoint(amount);
        } catch (error) {
            cc.log(error);
        }
    }

    private addItemBalance(item: StoreItem, amount: number): void {
        const store = ee.ServiceLocator.resolve(StoreManager);
        store.addItemBalance(item, amount);
    }

    private receiveThisCard(data: string): void {
        const root = cc.director.getScene();
        const menu = root.getComponentInChildren(MenuScene);
        if (!menu) {
            return;
        }
        const cardManager = ee.ServiceLocator.resolve(CardManager);
        const characterLayer = menu.characterLayer;
        if (!characterLayer) {
            return;
        }
        const selectedCard = characterLayer.currentSelectedCard;
        if (!selectedCard) {
            return;
        }
        const card = cardManager.findCardById(selectedCard.id);
        let amount = parseInt(data);
        if (isNaN(amount)) {
            amount = 1;
        }
        card.collect(amount);
    }

    private receiveFullSetCard(data: string): void {
        const cardManager = ee.ServiceLocator.resolve(CardManager);
        let IdCardCollections: Map<string, string[]> = new Map();
        IdCardCollections.set("set_pirate", ["char_pirate", "car_pirate", "claw_pirate", "rope_pirate", "pet_pirate"]);
        IdCardCollections.set("set_jaki", ["char_jaki", "car_jaki", "claw_jaki", "rope_jaki", "pet_jaki"]);
        IdCardCollections.set("set_cactus", ["char_cactus", "car_cactus", "claw_cactus", "rope_cactus", "pet_cactus"]);
        IdCardCollections.set("set_fighter", ["char_fighter", "car_fighter", "claw_fighter", "rope_fighter", "pet_fighter"]);
        IdCardCollections.set("set_ariana", ["char_ariana", "car_ariana", "claw_ariana", "rope_ariana", "pet_ariana"]);
        IdCardCollections.set("set_samurai", ["char_samurai", "car_samurai", "claw_samurai", "rope_samurai", "pet_samurai"]);
        IdCardCollections.set("set_thor", ["char_thor", "car_thor", "claw_thor", "rope_thor", "pet_thor"]);
        IdCardCollections.set("set_doll", ["char_doll", "car_doll", "claw_doll", "rope_doll", "pet_doll"]);
        IdCardCollections.set("set_goku", ["char_goku", "car_goku", "claw_goku", "rope_goku", "pet_goku"]);
        IdCardCollections.set("set_bat", ["char_bat", "car_bat", "claw_bat", "rope_bat", "pet_bat"]);
        IdCardCollections.set("set_iron", ["char_iron", "car_iron", "claw_iron", "rope_iron", "pet_iron"]);
        IdCardCollections.set("set_spider", ["char_spider", "car_spider", "claw_spider", "rope_spider", "pet_spider"]);
        let setName: string = `set_${data.toLowerCase()}`;
        if (IdCardCollections.has(setName)) {
            IdCardCollections.get(setName).forEach(card => {
                let c = cardManager.findCardById(card);
                c.collect(1)
            })
        }
    }


    private openLv(data: string): void {
        try {
            let lvNumber = parseInt(data);
            lvNumber = Math.max(0, lvNumber - 1);

            const area = Math.floor(lvNumber / 20);
            const level = lvNumber % 20;
            SceneUtils.loadGameSceneStory(area, level).then();
        } catch (error) {
            cc.log(error);
        }
    }

    private openLvEvent(data: string): void {
        try {
            const [areaName, level] = data.split(" ");
            this.openLvEventName(areaName, parseInt(level));
        } catch (error) {
            cc.log(error);
        }
    }

    private completeStoryArea() {
        const keyName: string = "all_level_info";
        const dataManager = ee.ServiceLocator.resolve(DataManager);
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        let lastUnlockArea = levelManager.getLastUnlockedArea()
        let data = dataManager.getValue(keyName, {})
        try {
            data[lastUnlockArea].levels.forEach((level) => {
                level.highestStar = 3;
                level.latestStar = 3;
                level.score = 1e4;
            })
            data[lastUnlockArea].shouldReward = true;
            data[lastUnlockArea].unlockedLevels = 20;
            if (lastUnlockArea < 11) {
                data[lastUnlockArea + 1].unlockedLevels = 1;
            }
            dataManager.setValue(keyName, data);
        } catch (e) {
            console.log(e);
        }
    }

    private completeEventArea() {
        const keyName: string = "all_event_level_info";
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const dataManager = ee.ServiceLocator.resolve(DataManager);
        let currentArea = levelManager.getCurrentEventArea();
        let data = dataManager.getValue(keyName, {})
        try {
            data[currentArea].levels.forEach((level) => {
                level.highestStar = 3;
                level.latestStar = 3;
                level.score = 1e4;
            })
            data[currentArea].shouldReward = true;
            data[currentArea].unlockedLevels = 20;
            dataManager.setValue(keyName, data);
        } catch (e) {
            console.log(e);
        }
    }

    private completeJakiArea() {
        const keyName: string = "character_level_info";
        const dataManager = ee.ServiceLocator.resolve(DataManager);
        let data = dataManager.getValue(keyName, {})
        try {
            data[0].levels.forEach((level) => {
                level.highestStar = 3;
                level.latestStar = 3;
                level.score = 1e4;
            })
            data[0].shouldReward = true;
            data[0].unlockedLevels = 10;
            dataManager.setValue(keyName, data);
        } catch (e) {
            console.log(e);
        }
    }

    private openLvEventName(areaName: string, level: number): void {
        let area = 0;
        switch (areaName) {
            case "2":
                area = 0;
                break;
            case "3":
                area = 1;
                break;
            case "4":
                area = 2;
                break;
            case "5":
                area = 3;
                break;
            case "6":
                area = 4;
                break;
            case "7":
                area = 5;
                break;
            default: // chu nhat
                area = 6;
                break;
        }
        level = Math.max(0, Math.min(level - 1, 19));
        this.openLvEventImpl(area, level);
    }

    private openLvEventImpl(area: number, level: number): void {
        try {
            area = Math.max(0, Math.min(area, 6));
            level = Math.max(0, Math.min(level, 19));
            SceneUtils.loadGameSceneEvent(area, level).then();
        } catch (error) {
            cc.log(error);
        }
    }

    private addExp(data: string): void {
        try {
            const exp = parseInt(data);
            const dialogManager = ee.ServiceLocator.resolve(DialogManager);
            const userProfileManager = ee.ServiceLocator.resolve(UserProfileManager);
            userProfileManager.addExp(exp, null);
            //userProfileManager.processExp(dialogManager).then();
        } catch (error) {
            cc.log(error);
        }
    }

    private openEvents(): void {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        levelManager.isEventModeUnlocked = true;
    }

    private addAxe(data: string): void {
        try {
            const axe = parseInt(data);
            const hiddenTempleManager = ee.ServiceLocator.resolve(HiddenTempleManager);
            hiddenTempleManager.cheatAxeAmount(axe);
        } catch (error) {
            cc.log(error);
        }
    }

    private openMap(data: string): void {
        try {
            const level = parseInt(data);
            const hiddenTempleManager = ee.ServiceLocator.resolve(HiddenTempleManager);
            hiddenTempleManager.openMap(level);
        } catch (error) {
            cc.log(error);
        }
    }


    private endEvent(data: string): void {
        try {
            const second = parseInt(data);
            const hiddenTempleManager = ee.ServiceLocator.resolve(HiddenTempleManager);
            hiddenTempleManager.endEvent(second);
        } catch (error) {
            cc.log(error);
        }
    }

    private beginEvent() {
        const hiddenTempleManager = ee.ServiceLocator.resolve(HiddenTempleManager);
        hiddenTempleManager.beginEvent();
    }

    private addRubyPiggyBank(data: string): void {
        try {
            const rubyBank = parseInt(data);
            const piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
            piggyBankManager.cheatRubyBankAmount(rubyBank);
        } catch (error) {
            cc.log(error);
        }
    }

    private endEventBank(data: string): void {
        try {
            const second = parseInt(data);
            const piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
            piggyBankManager.endEventBank(second);
        } catch (error) {
            cc.log(error);
        }
    }

    private resetEventBank(): void {
        try {
            const piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
            piggyBankManager.resetEventBank();
        } catch (error) {
            cc.log(error);
        }
    }

    private addPlayPass(data: string): void {
        try {
            const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
            const pass = parseInt(data);
            playPassManager.addPass(pass);
        } catch (error) {
            cc.log(error);
        }
    }

    private addWatchAds(data: string): void {
        try {
            const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
            const adsWatch = parseInt(data);
            playPassManager.addWatchTimes(adsWatch);
        } catch (error) {
            cc.log(error);
        }
    }

    private setLevelPass(data: string): void {
        try {
            const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
            const level = parseInt(data);
            playPassManager.setLevelPass(level);
        } catch (error) {
            cc.log(error);
        }
    }

    private endEvenPlayPass(data: string): void {
        try {
            const second = parseInt(data);
            const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
            playPassManager.endEventPlayPass(second);
        } catch (error) {
            cc.log(error);
        }
    }

    private resetEventPlayPass(): void {
        try {
            const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
            playPassManager.resetEventPlayPass();
        } catch (error) {
            cc.log(error);
        }
    }

    private autoMine(): void {
        const autoMineManager = ee.ServiceLocator.resolve(AutoMineManager);
        autoMineManager.isEnabled = !autoMineManager.isEnabled;
        autoMineManager.duration = autoMineManager.getMaxDuration();
        cc.director.getScene().getComponentInChildren(GameScene)?.updateAutoButton();
    }


    private nextDay(): void {
        this._timeCheat.addOneDay();
        cc.director.loadScene("menu_scene");
    }

    private unlockAllLevels(): void {
        // const levelManager = ee.ServiceLocator.resolve(LevelManager);
        // const maxStoryArea = levelManager.getStoryAreas();
        // const maxEventArea = levelManager.getEventAreas();
        // const maxLevel = levelManager.getLevelsPerArea();
        // levelManager.setCurrentStoryLevel(maxStoryArea - 1, maxLevel - 1);
        // levelManager.setCurrentStoryLevel(maxEventArea - 1, maxLevel - 1);
        // levelManager.isEventModeUnlocked = true;
    }

    private testDialog(): void {
        let dialogManager = ee.ServiceLocator.resolve(DialogManager);
        RubyPacksDialog.create().then(dialog => {
            dialog.show(dialogManager);
        })
    }

    private forceChangeCurrentQuest() {
        const dailyQuest = ee.ServiceLocator.resolve(NewDailyQuestManager);
        dailyQuest.forceChangeCurrentQuest();
        cc.director.loadScene("menu_scene");
    }

    private completeDailyTask(): void {
        const dailyQuest = ee.ServiceLocator.resolve(DailyQuestManager);
        const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);

        const quest = dailyQuest.getCurrentQuest();
        if (!quest) {
            return;
        }
        RewardUtils.showRewardDialog(quest.reward.items, dialogManager, SceneName.SceneDailyReward)
            .then(dialog => {
                dialog.setTitleKey('text_reward');
                dialog.setAdsReward(TrackAdsRewardWatch.TaskCompleteMission);
                quest.claim();
            });
    }

    private completeAllSubTask(): void {
        const dailyQuest = ee.ServiceLocator.resolve(DailyQuestManager);
        dailyQuest.completeAllSubTask();
    }


    private addGameModeChestImpl(chestName: string, area: number, level: number) {
        let chestType = undefined;
        switch (chestName) {
            case "free":
            case "copper":
            case "bronze":
                chestType = gm.ChestType.Copper;
                break;
            case "silver":
                chestType = gm.ChestType.Silver;
                break;
            case "gold":
                chestType = gm.ChestType.Gold;
                break;
            case "diamond":
                chestType = gm.ChestType.Diamond;
                break;
            case "star":
                chestType = gm.ChestType.Star;
                break;
        }
        const chestManager = ee.ServiceLocator.resolve(ChestManager);
        chestManager.addChestFromLevel(chestType, 'story', area, level);

        // Reload scene
        cc.director.loadScene("menu_scene");
    }

    private addDynamite(data: string) {
        const amount = parseInt(data) || 1;
        const boosterManager = ee.ServiceLocator.resolve(BoosterStorageManager);
        boosterManager.addBalance(gm.BoosterType.Dynamite, amount);
    }

    private unlimitedTime(data: string) {
        SceneUtils.getGameScene().levelView.timeView.time = -9999;
    }

    private skipInterstitial(data: string) {
        const adsManager = ee.ServiceLocator.resolve(AdsManager);
        adsManager.skipInterstitial = data.toLowerCase() == "true";

        this._skipInterstitial = adsManager.skipInterstitial;
        this._dataManager.setValue("skip_interstitial", this._skipInterstitial);
    }

    private getUseAdsTest(): string {
        const adsManager = ee.ServiceLocator.resolve(AdsManager);
        return adsManager?.getUseAdsTest() ? "false" : "true";
    }

    private useAdsTest(data: string) {
        const adsManager = ee.ServiceLocator.resolve(AdsManager);
        adsManager.setUseAdsTest(data.toLowerCase() == "true");
    }

    private playInterstitial(data: string) {
        const adsManager = ee.ServiceLocator.resolve(AdsManager);
        adsManager.showInterstitialAd().then();
    }

    private playRewarded(data: string) {
        const adsManager = ee.ServiceLocator.resolve(AdsManager);
        adsManager.showRewardedVideo().then();
    }

    private openAdsInspector(data: string) {
        const adsManager = ee.ServiceLocator.resolve(AdsManager);
        adsManager.openAdsInspector();
    }

    private toggleDebugMode(data: string) {
        cc.debug.setDisplayStats(data == "true");
    }

    private clearData(): void {
        const currentDataManager = ee.ServiceLocator.resolve(DataManager);
        currentDataManager.clear();
        currentDataManager.refreshData();
        currentDataManager.destroy();
        cc.log(cc.sys.localStorage);

        // Restart App
        cc.director.loadScene("splash_scene");
    }

    private printDialog(): void {
        DebugUtils.printDialogHierarchy();
    }

    private printScene(): void {
        let root = DebugUtils.convertTree(cc.director.getScene());
        DebugUtils.printNodeHierarchy(root);
    }

    private unlockJakiEvent() {
        const cardManager = ee.ServiceLocator.resolve(CardManager);
        let jakiCards = ['char_jaki', 'car_jaki', 'claw_jaki', 'rope_jaki', 'pet_jaki'];
        for (let card of jakiCards.map(c => cardManager.findCardById(c))) {
            card.collect(4);
            card.upgrade();
        }
    }

    private unlockToLatestLevel() {
        let dataManager = ee.ServiceLocator.resolve(DataManager);
        const data = [{
            unlockedLevels: 10,
            levels: Array.from({length: 10}, (_, index) => ({
                highestStar: index < 9 ? 3 : 0,
                latestStar: index < 9 ? 3 : 0,
                score: index < 9 ? 10000 : 0,
            })),
            shouldReward: false
        }];
        dataManager.setValue('character_level_info', data);
        cc.director.loadScene("menu_scene");
    }

    private goToMenuScene() {
        const sceneManager = ee.ServiceLocator.resolve(SceneManager);
        sceneManager.loadScene('menu_scene', MenuScene).then();
    }

    private showOfferInformation() {
        const root = cc.director.getScene();
        const menu = root.getComponentInChildren(MenuScene);
        if (!menu) {
            return;
        }
        let offerNode = menu.shopLayer.getNodeByCategory(IapShopCategory.Offer);
        if (offerNode) {
            let infoNode = offerNode.getComponent(OfferCategory).cheatInfo;
            infoNode.active = !infoNode.active;
        }
    }

    private increaseAccountLevel() {
        const userProfileManager = ee.ServiceLocator.resolve(UserProfileManager);
        const dialogManager = ee.ServiceLocator.resolve(DialogManager);
        let currentExp = userProfileManager.getCurrentExp();
        let currentLevel = userProfileManager.getCurrentLevel();
        try {
            let nextExp = userProfileManager.getExpForLevel(currentLevel + 1);
            userProfileManager.addExp(nextExp - currentExp, null);
            //userProfileManager.processExp(dialogManager).then();
        } catch (e) {
            console.log(e);
        }
    }

    private testSound(soundName: string): void {
        try {
            const audioManager = ee.ServiceLocator.resolve(AudioManager);
            const soundType = SoundType[soundName as keyof typeof SoundType];

            if (soundType === undefined) {
                cc.log(`Sound ${soundName} not found`);
                return;
            }

            // Play sound with normalized volume
            audioManager.playSound(soundType, false);

            // Log sound information
            cc.log(`Playing sound: ${soundName}`);
            cc.log(`Normalization factor: ${audioManager.getNormalizationFactor(soundName)}`);
            cc.log(`Current measurement: ${audioManager.getSoundMeasurement(soundName)}`);
        } catch (error) {
            cc.log(`Error testing sound: ${error}`);
        }
    }

    private forceUploadData() {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.saveGame();
    }

    private addScore(score: string) {
        const keyName: string = "all_level_info";
        const dataManager = ee.ServiceLocator.resolve(DataManager);
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        let data = dataManager.getValue(keyName, {})
        try {
            let s = parseInt(score);
            let oldScore = data[0].levels[0].score;
            data[0].levels[0].score = oldScore + s;
            dataManager.setValue(keyName, data);
            this.goToMenuScene();

        } catch (e) {
            console.log(e);
        }
    }

    private reloadLeaderboard() {
        ee.ServiceLocator.resolve(DefaultLeaderboardManager).NeedRenew = true
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.reloadLeaderboard();
    }

    private resetWeeklySeason() {
        if (!isServerTest) {
            return;
        }
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.resetWeeklySeason();
    }

    private deleteAccount() {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.deleteAccountImmediately().then(
            () => {
                gameServerManager.getAndReplaceData();
            }
        );
    }

    private addBotToTeam(amount: string) {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        const teamManager = ee.ServiceLocator.resolve(MyTeamManager);
        gameServerManager.addBotToTeam(teamManager.getTeamInfo().teamId ,parseInt(amount));
    }
    private addVirtualTeam()
    {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.addVirtualTeam();
    }
    private addChatText()
    {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        const teamManager = ee.ServiceLocator.resolve(MyTeamManager);
        gameServerManager.addChatText(teamManager.getTeamInfo().teamId);
    }
    private addRequest()
    {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        const teamManager = ee.ServiceLocator.resolve(MyTeamManager);
        gameServerManager.addRequest(teamManager.getTeamInfo().teamId);
    }
    private helpNearestRequest()
    {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        const teamManager = ee.ServiceLocator.resolve(MyTeamManager);
        gameServerManager.helpNearestRequest(teamManager.getTeamInfo().teamId); 
    }
    private resetTimeForNextRequest() {
        const chatNetworkManager = ee.ServiceLocator.resolve(ChatNetworkManager);
        chatNetworkManager.resetTimeForNextRequest().then();
    }
    private cleanTeamBan()
    {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.cleanTeamBan();        
    }
    
}