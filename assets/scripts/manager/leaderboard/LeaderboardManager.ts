import * as ee from '../../libraries/ee/index';
import UserRankingInfos from "../../scenes/leaderboard/leaderboad_v2/UserRankingInfos";
import { LeaderboardReward } from "../../scenes/leaderboard_v2/LeaderboardReward";
import {IRefreshable} from "../IRefreshable";

export interface LeaderBoardOption {
    limit?: number;
    type?: string;
    nation?: string;
}

export interface TeamRankingData {
    teamId: string;
    teamName: string;
    avatar: string;
    teamSize: number;
    teamType: string;
    totalStar: number;
}

export interface UserRankingData {
    /** Entry's unique ID. */
    uid: string;

    /** Entry's display name. */
    name: string;

    /** Entry's avatar ID. */
    avatarId: string;

    frameId: string;

    nation: string;

    /** Entry's level. */
    level: number;

    /** Entry's team. */
    team: string;

    /** Entry's score value. */
    totalStar: number;
}

export interface LeaderboardData {
    playersRanking: LeaderboardDataItem;
    weeklyRanking: LeaderboardDataItem;
}

export interface LeaderboardDataItem {
    all: UserRankingData[];
    nation: UserRankingData[];
}

@ee.service('LeaderboardManager')
export abstract class LeaderboardManager implements ee.Service, IRefreshable {

    public Reward: LeaderboardReward;

    public abstract destroy(): void;

    /**
     * Fetches a sorted list of players in the leaderboard. 
     * If internet or server is not available, it returns empty array
     * 
     * @param options - Optional parameters for fetching the leaderboard.
     * @returns A promise that resolves to an array of user ranking information.
     */
    public abstract fetchLeaderboard(options?: LeaderBoardOption): Promise<UserRankingInfos[]>;

    public abstract getTimeRemainingToNextRefreshWeekly(): number;

    public abstract getTimeToNextRefreshWeekly(): Date;

    refreshData(): void {
    }

}