import {
    LeaderboardData,
    LeaderboardManager,
    LeaderBoardOption,
    TeamRankingData,
    UserRankingData
} from "./LeaderboardManager";

import UserRankingInfos from "../../scenes/leaderboard/leaderboad_v2/UserRankingInfos";
import {LeaderboardReward} from "../../scenes/leaderboard_v2/LeaderboardReward";
import {GameServerManager} from "../game_server/GameServerManager";
import * as ee from "../../libraries/ee/index";
import {AudioManager} from "../audio/AudioManager";
import {SoundType} from "../audio/SoundType";
import {UserProfileManager} from "../profile/UserProfileManager";


const TIME_RENEW_LEADERBOARD = 10 * 60 * 1000;

export class DefaultLeaderboardManager extends LeaderboardManager {
    private cacheLeaderboardData = new Map<string, UserRankingData[]>();
    private needRenew = new Map<string, boolean>();
    private scoreInLeaderboard = 0

    public constructor(
        private _gameServer: GameServerManager) {
        super();
        this.Reward = new LeaderboardReward(_gameServer);
        setInterval(() => {
            this.NeedRenew = true
        }, TIME_RENEW_LEADERBOARD)
    }


    destroy(): void {
    }

    public set NeedRenew(value: boolean) {
        if (value) {
            this.needRenew = new Map<string, boolean>();
        }
    }

    override refreshData() {
        this.scoreInLeaderboard = 0;
        this.needRenew = new Map<string, boolean>();
    }

    public async fetchLeaderboard(options?: LeaderBoardOption): Promise<UserRankingInfos[]> {

        const cacheKey = `${options.type}_${options.nation}`;
        if (!this.needRenew.has(cacheKey)) {
            this.needRenew.set(cacheKey, true);
        }

        if (this.needRenew.get(cacheKey)) {
            let userRankingDataList: UserRankingData[] = [];
            if (options.type === "TEAM") {
                const teamRankingDataList = await this._gameServer.getTeamLeaderBoard(options);
                teamRankingDataList.forEach(teamRanking => {
                    userRankingDataList.push(this.convertRanking(teamRanking));
                })

            } else {
                userRankingDataList = await this._gameServer.getUserLeaderBoard(options);
            }

            if (userRankingDataList.length != 0) {
                this.needRenew.set(cacheKey, false);
                await this.saveCacheData(userRankingDataList, options)
            }
        }
        return this.getCacheData(options, options.type === "TEAM");
    }

    private convertRanking(team: TeamRankingData): UserRankingData {
        return {
            uid: team.teamId,
            name: team.teamName,
            avatarId: team.avatar,
            frameId: "",
            nation: "",
            level: team.teamSize,
            team: team.teamType,
            totalStar: team.totalStar
        }
    }

    private async saveCacheData(data: UserRankingData[], options: LeaderBoardOption) {
        const cacheKey = `${options.type}_${options.nation}`;
        if (options.type == "PLAYER" && (!options.nation || options.nation == "ALL")) {
            // Lấy điểm người chơi đạt được để cập nhâpj trực tiếp vào leaderboard
            const myUid = await this._gameServer.getUId();
            const userIndex = data.findIndex(data => data.uid == myUid.toString());
            if (userIndex != -1) {
                this.scoreInLeaderboard = data[userIndex].totalStar;
            }

        }
        this.cacheLeaderboardData.set(cacheKey, data);
    }

    private getCacheData(options: LeaderBoardOption, forTeam: boolean = false): Promise<UserRankingInfos[]> {
        const cacheKey = `${options.type}_${options.nation}`;
        return this.sortAndArrangeData(this.cacheLeaderboardData.get(cacheKey) || [], forTeam);
    }

    private async sortAndArrangeData(data: UserRankingData[], forTeam: boolean = false): Promise<UserRankingInfos[]> {
        const myUid = await this._gameServer.getUId();
        const userIndex = data.findIndex(data => data.uid == myUid.toString());
        const newData = data.map(items => ({
            ...items
        }))

        // Thêm local user vào danh sách ngoại trừ danh sách Team
        if (!forTeam) {
            const userProfileManager = ee.ServiceLocator.resolve(UserProfileManager)
            if (userIndex !== -1) {
                const additionalScore = this.getAdditionalScore()
                newData[userIndex].totalStar += additionalScore
                if (newData[userIndex].totalStar < 0) {
                    newData[userIndex].totalStar = 0
                }
                newData[userIndex].frameId = userProfileManager.socialUser.frame
                newData[userIndex].name = userProfileManager.socialUser.name
                newData[userIndex].avatarId = userProfileManager.socialUser.picture
            } else {
                const minScore = data.length > 0 ? data[data.length - 1].totalStar : 0
                if (userProfileManager.getMedalCount() > minScore) {
                    newData.push({
                        uid: myUid.toString(),
                        name: userProfileManager.socialUser.name,
                        avatarId: userProfileManager.socialUser.picture,
                        frameId: userProfileManager.socialUser.frame,
                        nation: userProfileManager.socialUser.nationCode,
                        level: userProfileManager.getCurrentLevel(),
                        team: "",
                        totalStar: userProfileManager.getMedalCount()
                    })
                }
            }
        }

        newData.sort((a, b) => b.totalStar - a.totalStar);
        return newData.map((data, index) => ({
            ...data,
            rank: index + 1
        }));
    }

    private getAdditionalScore(): number {
        const currentScore = ee.ServiceLocator.resolve(UserProfileManager).getMedalCount()
        return currentScore - this.scoreInLeaderboard
    }

    // Get time to next refresh weekly
    public getTimeToNextRefreshWeekly(): Date {
        const now = new Date();
        const nextRun = new Date(now);
        // Set the next run to the next Sunday at 23:59:00
        nextRun.setUTCDate(now.getUTCDate() + ((7 - now.getUTCDay()) % 7));
        nextRun.setUTCHours(23, 59, 0, 0);
        return nextRun
    }

    // Get time remaining to next refresh weekly in seconds
    public getTimeRemainingToNextRefreshWeekly(): number {
        const now = new Date();
        const nextRun = this.getTimeToNextRefreshWeekly();
        const delay = nextRun.getTime() - now.getTime();
        return delay / 1000
    }

}