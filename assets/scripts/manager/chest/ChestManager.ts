import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';

import {RewardInfo} from '../reward/RewardManager';
import {IRefreshable} from "../IRefreshable";

export interface SimpleReward {
    min: number;
    max: number;
}

export interface CardReward {
    total: number;
    common_unique: number;
    rare_unique: number;
    rare_guarantee: number;
    rare_bonus: number;
    epic_unique: number;
    epic_guarantee: number;
    epic_bonus: number;
    legend_unique: number;
    legend_guarantee: number;
    legend_bonus: number;
}

export interface RawChestReward {
    card: CardReward;
    booster: SimpleReward;
    gold: SimpleReward;
    ruby?: SimpleReward;
}

export interface ChestConfig {
    type: string;
    price: number;
    price_gold: number;
    duration: number;
    maxClaim: number;
    rewards: RawChestReward;
}

export interface ChestInfo {
    /** Type of the chest. */
    type: gm.ChestType;

    /** Name of the chest. */
    name: string;

    /** Ruby price of the chest. */
    price: number;

    /** Gold price of the chest. */
    priceGold: number;

    /** Unlock duration. */
    duration: number;

    /** Max claim */
    maxClaim: number;

    // /** How many taps required to open the chest. */
    // count: number;

    // /** Chest's rewards. */
    // rewards: RewardInfo[];
}

export interface ChestSlot {
    /**
     * Index of the slot.
     */
    slotId: number;

    /** Whether the slot is empty. */
    isEmpty(): boolean;

    /** Whether the slot is idle. */
    isIdle(): boolean;

    /** Whether the slot is opening. */
    isOpening(): boolean;

    /** Whether the slot is unlocked. */
    isUnlocked(): boolean;

    /** Whether the slot is bought. Normal slot are automatically bought */
    isBought(): boolean;

    getRemainingTime(): number;

    /** Unlock duration. */
    getDuration(): number;

    getPriceRubyToOpen(remainingTime: number): number;

    getPriceGoldToOpen(remainingTime: number): number;

    /** Gets the chest slot type. */
    getType(): gm.ChestType;

    /** Opens the chest slot. */
    open(): void;

    /** Unlocks the chest slot. */
    unlock(): void;

    /** Clears the chest slot. */
    clear(): void;

    decreaseRemainingTime(seconds: number): void;
}

@ee.service('Chest')
export abstract class ChestManager implements ee.Service, IRefreshable {
    refreshData(): void {
    }
    /** Gets or sets the last free chest time point. */
    public abstract freeChestTimePoint: number;

    /** Gets or sets the current free chest count. */
    public abstract freeChestCount: number;

    public abstract destroy(): void;

    /** Check whether feature is available */
    public abstract isChestUnlocked(): boolean;

    /** Add and check whether any chest is added */
    public abstract addChest(type: gm.ChestType): boolean;

    /**
     * Thêm Chest và thông tin cụ thể nó từ Area & Level bao nhiêu
     * @param type
     * @param gameMode 'story' | 'event'
     * @param area
     * @param level
     * @returns {boolean} true nếu có slot trống
     */
    public abstract addChestFromLevel(type: gm.ChestType, gameMode: string, area: number, level: number): boolean;

    public abstract getAdsBonusDuration(): number;

    /** Checks whether there is a chest to open. */
    public abstract hasAvailableChest(): boolean;

    /** Checks whether there is an opening chest. */
    public abstract hasOpeningChest(): boolean;

    /** Gets the currently opening chest. */
    public abstract getOpeningChest(): ChestSlot | undefined;

    /** Gets the chest for the specified slot. */
    public abstract getSlot(index: number): ChestSlot;

    public abstract getRandomSpawnChestType(): gm.ChestType;

    public abstract getChestConfig(type: gm.ChestType): ChestConfig;

    public abstract getChestInfo(type: gm.ChestType): ChestInfo;

    public abstract getChestRewards(type: gm.ChestType): RewardInfo[];

    public abstract getChestRewardsByChestSlot(type: gm.ChestType, slotId: number): RewardInfo[];

    public abstract getChestRewardsByLevel(type: gm.ChestType, area: number, level: number): RewardInfo[];

    /**
     * Generate ra Chest reward khi user đang chơi ở area nhất định
     * @param type
     * @param area
     * @param level
     */
    public abstract getChestRewardsByLevel(type: gm.ChestType, area: number, level: number): RewardInfo[];

    /** Claims a free chest. */
    public abstract claimFreeChest(): void;

    public abstract getRubyToGoldRatio(): number;

    public abstract buyChestSlot(index: number): void;
}
