import {OfferManager} from "./OfferManager";
import * as ee from '../../libraries/ee/index';
import {DialogManager} from '../../libraries/ee/index';
import {LevelManager} from "../level/LevelManager";
import {OfferDialog, OfferType} from "../../dialog/OfferDialog";
import {OfferPack, ShopManager} from "../shop/ShopManager";
import {RatingManager} from "../rating/RatingManager";
import {StoreManager} from "../store/StoreManager";
import {SceneName} from "../analytics/AnalyticsConfig";
import {DefaultUserProfileManager} from "../profile/DefaultUserProfileManager";
import {IAPUtils} from "../../utils/IAPUtils";

enum Key {
    Data = 'rating_storage',
}


export interface Config {
    currentLevel: number,
    session: number,
    hasRated: boolean,
    purchasedOffers: OfferPack[],
}

/**
 * Class này quản lý các kịch bản hiện offer dialog
 */
export class DefaultOfferManager extends OfferManager {
    private readonly orderDialogA: OfferType[] = [OfferType.Beginner_Offer, OfferType.Booster_Offer, OfferType.Ruby_Offer];
    private readonly orderDialogB: OfferType[] = [OfferType.Beginner_Offer, OfferType.Booster_Offer, OfferType.Ruby_Offer];
    private readonly orderDialogC: OfferType[] = [OfferType.Ruby_Offer, OfferType.Booster_Offer];
    private readonly orderDialogD: OfferType[] = [OfferType.Booster_Offer, OfferType.Ruby_Offer];
    public alreadyShowOnMenu: boolean = false;

    constructor(private readonly profileManager: DefaultUserProfileManager,
                private readonly levelManager: LevelManager,
                private readonly ratingManager: RatingManager,
                private readonly shopManager: ShopManager) {
        super();
    }
    
    private selectOfferDialogChainByConfig(config: Config) : OfferType[] {
        let finallyOrder : OfferType[] = []
        if(!config.hasRated && config.session == 2) {
            finallyOrder = this.orderDialogA;
            if(config.purchasedOffers.some(pack => pack.name == 'beginner_offer_pack')){ // special case
                finallyOrder =  [OfferType.Booster_Offer, OfferType.Ruby_Offer];
            }
        }
        if(config.session >= 3) finallyOrder = this.orderDialogB;
        if(config.currentLevel >= 60) finallyOrder = this.orderDialogC;
        if(config.purchasedOffers.length == 5) finallyOrder = this.orderDialogD;
        return finallyOrder;
    }


    public getConfig() {
        let config : Config = {
            currentLevel: this.levelManager.getUnlockedStoryLevelsCount() - 1,
            session: this.profileManager.getProfileInfo().timesPlayed,
            hasRated: this.ratingManager.isRated(),
            purchasedOffers: this.shopManager.getOfferPacks().filter(o => {
                return o.isNew;
            }),
        }
        return config;
    }

    destroy(): void {
    }

    public async showOfferDialogByType(dialogManager: DialogManager, type: OfferType): Promise<boolean> {
        const storeManager = ee.ServiceLocator.resolve(StoreManager);
        let dialog = await OfferDialog.create(type);
        let info = this.shopManager.getOfferPacks()[type];
        const tier = info.tier;
        const tierInfo = info.tiers[tier];
        let price = storeManager.getMarketPriceAndCurrency(info.name + (info.name === "vip_offer_pack" ? "" : tier));
        let shouldClose = false;

        dialog
            .setDuration(tierInfo.duration)
            .setPrice(price === '' ? '--' : price)
            .setRemainTime(info.getRemainingTime())
            .setSalePercent(tierInfo.discount)
            .setController({
                onItemPressedCallback: () => {
                    shouldClose = true;
                    IAPUtils.buyOfferPack(info, SceneName.SceneMenuShop).then(()=>{
                        // dialog.hide();
                    })
                    dialog.hide();
                }
            });
        let boosterCount = 0;
        tierInfo.items.map(reward => {
            const actions = {
                gold: () => reward.raw.type === "store" ? dialog.setGold(reward.quantity) : dialog.setChest(reward.quantity, reward.raw.subType),
                ruby: () => dialog.setRuby(reward.quantity),
                energy: () => dialog.setEnergy(reward.quantity),
                unlimited_energy: () => dialog.setEnergyUnlimited(reward.quantity),
                // booster_pack: () => dialog.setBooster(reward.quantity),
                random: () => dialog.setCard(reward.quantity),
            };
            if(reward.raw.type == 'booster') {
                boosterCount += reward.quantity;
            }
            actions[reward.raw.subType]?.();
        });
        dialog.setBooster(boosterCount);

        // reduce all items have type is booster

        dialog.updateProgress();
        dialog.show(dialogManager);
        await new Promise<boolean>(resolve => dialog.onDidHide(() => {
            resolve(dialog.isActive());
        }))
        return !shouldClose;
    }

    public async showOfferDialogScript(dialogManager: DialogManager) {
        //if(cc.sys.isBrowser) return;
        let asyncFunctions : Function[] = [];
        for (let f of this.selectOfferDialogChainByConfig(this.getConfig())) {
            asyncFunctions.push( () => this.showOfferDialogByType(dialogManager,f))
        }
        for (const promise of asyncFunctions) {
            const shouldProcessNext = await promise();
            if (!shouldProcessNext) {
                break;
            }
        }
    }
    public async setupOfferDialog(dialog: OfferDialog) : Promise<void>
    {
        const storeManager = ee.ServiceLocator.resolve(StoreManager);
        const type = dialog.packIndex;
        let info = this.shopManager.getOfferPacks()[type];
        const tier = info.tier;
        const tierInfo = info.tiers[tier];
        let price = storeManager.getMarketPriceAndCurrency(`${info.name}${tier}`);
        dialog
            .setDuration(tierInfo.duration)
            .setPrice(price === '' ? '--' : price)
            .setRemainTime(info.getRemainingTime())
            .setSalePercent(tierInfo.discount)
            .setController({
                onItemPressedCallback: () => {
                    IAPUtils.buyOfferPack(info, SceneName.SceneMenuShop).then(()=>{
                        //dialog.hide();
                    })
                    dialog.hide();
                }
            });
        let boosterCount = 0;
        tierInfo.items.map(reward => {
            const actions = {
                gold: () => reward.raw.type === "store" ? dialog.setGold(reward.quantity) : dialog.setChest(reward.quantity, reward.raw.subType),
                ruby: () => dialog.setRuby(reward.quantity),
                energy: () => dialog.setEnergy(reward.quantity),
                unlimited_energy: () => dialog.setEnergyUnlimited(reward.quantity),
                // booster_pack: () => dialog.setBooster(reward.quantity),
                random: () => dialog.setCard(reward.quantity),
            };
            if(reward.raw.type == 'booster') {
                boosterCount += reward.quantity;
            }
            actions[reward.raw.subType]?.();
        });
        dialog.setBooster(boosterCount);
        dialog.updateProgress();

    }

}
