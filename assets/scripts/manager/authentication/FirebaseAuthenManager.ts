import * as ee_x from "@senspark/ee-x";
import { AccountInfo, ErrorCode, IErrResponse, Platform } from "@senspark/ee-x";
import * as ee from "../../libraries/ee/index";
import { DialogManager } from "../../libraries/ee/index";
import { GameServerManager } from "../game_server/GameServerManager";
import { IDownloadDataResponse } from "../game_server/IGameServerManager";
import { RewardUtils } from "../reward/RewardUtils";
import { SceneName } from "../analytics/AnalyticsConfig";
import { RawReward, RewardManager } from "../reward/RewardManager";
import ConfirmDialog from "../../dialog/ConfirmDialog";
import { AlertDialog } from "../../dialog/AlertDialog";
import { checkInternetAndShowDialog } from "../../server/InternetChecking";
import value = cc.js.value;
import { CloudSaveStatus } from "../game_server/CloudSaveLoadGameController";
import { TrackingManager } from "../analytics/TrackingManager";
import {IAuthenManager} from "./IAuthenManager";

export type CallbackWithBool = (state: boolean) => void;

export class FirebaseAuthenManager extends IAuthenManager {
    private _gameServerManager: GameServerManager;
    private readonly _bridge: ee_x.IFirebaseAuthentication
    private signInSuccess = false

    public constructor() {
        super();
        this._bridge = ee_x.PluginManager.createPlugin<ee_x.IFirebaseAuthentication>(ee_x.Plugin.FireBaseAuthentication)
    }

    public async initialize(): Promise<void> {
        await this._bridge.initialize()
        const user = await this.getUserInfo()
        this.signInSuccess = user != null
    }


    private _signInSuccessCallbacks: { [key: string]: CallbackWithBool } = {};

    public addLoginCallback(key: string, callback: CallbackWithBool): void {
        this._signInSuccessCallbacks[key] = callback;
    }

    public removeLoginCallback(key: string): void {
        delete this._signInSuccessCallbacks[key];
    }

    // Dùng để bật tắt các UI, hiện tại callback không xử lý logic
    private invokeSignInSuccessCallbacks(state: boolean): void {
        Object.values(this._signInSuccessCallbacks).forEach(callback => callback(state));
    }

    public hasSignIn(): boolean {
        return this.signInSuccess
    }


    public async getUserInfo(): Promise<AccountInfo | null> {
        return this._bridge.getGGAccountInfo()
    }

    public destroy(): void {
        this._bridge.destroy();
    }

    async signInWithGoogleAccount(): Promise<boolean> {
        this.signInSuccess = false;
        const hasInternet = await checkInternetAndShowDialog()
        if (!hasInternet) return false;
        if (this._gameServerManager == null)
            this._gameServerManager = ee.ServiceLocator.resolve(GameServerManager);

        const info = await this._bridge.signInWithGGAccount()
        // SignIn thất bại
        if (info == null) return false;
        await this._gameServerManager.initGGAccount(info)
        await new Promise(resolve => setTimeout(resolve, 300))
        const data = await this._gameServerManager.downloadGameData()

        this.signInSuccess = true;
        const deviceId = await Platform.getDeviceIdentifier();
        const googleId = info.uid
        if ((data as IErrResponse).errCode === ErrorCode.NOT_FOUND) {
            // Lần đầu sign in

            // Kiểm tra xem có thể liên kết tài khoản không, nếu không thì tải lại data chơi từ đầu
            const canLink = await this.canLinkAccount(deviceId, googleId);
            if (canLink && (await this.linkAccount(deviceId, googleId))) {
                this._gameServerManager.autoSave();
                await this.showRewardFirstSiginDialog();
            } else {
                await this._gameServerManager.forceReloadCloudSaveData(null, this.showRewardFirstSiginDialog.bind(this));
            }

        } else if (data == null) {
            // Signin thành công nhưng lấy data thấy bại(Do loi server)

            await this._bridge.signOutGGAccount().then(
                (rs) => this._gameServerManager.initDeviceAccount()
            )
            this.signInSuccess = false
        } else {
            // Mở dialog thông báo đã login thành công
            const dialogManager = ee.ServiceLocator.resolve(DialogManager);
            const alertDialog = await AlertDialog.create()
            alertDialog.setKey("save_progress_success")
            alertDialog.show(dialogManager)
            await new Promise(resolve => alertDialog.onDidHide(resolve))
            // Đã có data ở server

            const canLink = await this.canLinkAccount(deviceId, googleId);

            if (canLink) {
                await this.askToLinkAccount(deviceId, googleId, data as IDownloadDataResponse)
            } else {
                await this._gameServerManager.forceReloadCloudSaveData(data)
            }
        }
        this.invokeSignInSuccessCallbacks(this.signInSuccess);
        return this.signInSuccess

    }

    async signOut(): Promise<boolean> {
        try {
            const hasInternet = await checkInternetAndShowDialog()
            if (!hasInternet) return false;
            if (this._gameServerManager == null)
                this._gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
            return new Promise<boolean>((resolve) => {
                const onYes = async () => {
                    await this._gameServerManager.saveGame()
                    const rs = await this._bridge.signOutGGAccount()
                    if (rs) {
                        await this._gameServerManager.initDeviceAccount()
                        await new Promise(resolve => setTimeout(resolve, 300))

                        const data = await this._gameServerManager.downloadGameData()

                        // Mở dialog thông báo
                        const dialogManager = ee.ServiceLocator.resolve(DialogManager);
                        const alertDialog = await AlertDialog.create()
                        alertDialog.setKey("signout_success")
                        alertDialog.show(dialogManager)
                        await new Promise(resolve => alertDialog.onDidHide(resolve))

                        await this._gameServerManager.forceReloadCloudSaveData(data)
                        this.signInSuccess = false
                        this.invokeSignInSuccessCallbacks(this.signInSuccess);
                        resolve(true)
                    }
                };
                const onNo = () => {
                    resolve(false);
                };
                const dialogManager = ee.ServiceLocator.resolve(DialogManager);

                ConfirmDialog.create().then(dialog => {
                    dialog.initialize(
                        onYes,
                        onNo
                    );
                    dialog.setKeyContent("confirm_signout");
                    dialog.show(dialogManager);
                });
            })
        } catch (err) {
            return false;
        }

    }

    private async showRewardFirstSiginDialog() {
        let rewardList: RawReward[] = [
            {
                type: "store",
                subType: "ruby",
                value: 50
            }
        ];
        const rewardManager = ee.ServiceLocator.resolve(RewardManager)
        const dialog = await RewardUtils.showRewardDialog(
            rewardList.map(items => rewardManager.createReward(items)),
            ee.ServiceLocator.resolve(DialogManager),
            SceneName.SceneMenuHome
        )
        dialog.setTitleKey('login_reward_title');
        await new Promise(resolve => dialog.onDidHide(resolve))
    }

    private async canLinkAccount(deviceId: string, googleId: string): Promise<boolean> {
        return this._gameServerManager.canLinkAccount(deviceId, googleId);
    }

    private async linkAccount(deviceId: string, googleId: string): Promise<boolean> {
        return this._gameServerManager.linkAccount(deviceId, googleId);
    }

    private async askToLinkAccount(deviceId: string, googleId: string, data: IDownloadDataResponse): Promise<void> {
        const dialogManager = ee.ServiceLocator.resolve(DialogManager);
        return new Promise(async (resolve) => {
            // Chọn YES: link account và tiep tuc choi voi tai khoan guest
            const onYes = async () => {
                ee.ServiceLocator.resolve(TrackingManager).trackEventClick(
                    SceneName.DialogConfirmLinkAccount,
                    "btn_yes"
                );
                const linkAccountSuccess = await this.linkAccount(deviceId, googleId)
                if (!linkAccountSuccess) {
                    await this._gameServerManager.forceReloadCloudSaveData(data)
                }
                this._gameServerManager.autoSave();
                resolve();
            };

            // Chọn NO: tải lại data của account trên server
            const onNo = async () => {
                ee.ServiceLocator.resolve(TrackingManager).trackEventClick(
                    SceneName.DialogConfirmLinkAccount,
                    "btn_no"
                );
                await this._gameServerManager.forceReloadCloudSaveData(data)
                resolve();
            };

            ConfirmDialog.create().then((dialog) => {
                dialog.initialize(onYes.bind(this), onNo.bind(this));
                dialog.setKeyContent("link_account_confirm_message");
                dialog.show(dialogManager);
            });
        })
    }
}