import * as ee from '../../libraries/ee/index';
import {IRefreshable} from "../IRefreshable";
import {TeamInfo} from "../../scenes/team/TeamLayer";
import {MemberInfo} from "../../scenes/team/MemberItem";
import {RequestJoin} from "../../team/chat/Manager/ChatManager";
import {TeamInfoResponse} from "../game_server/GameServerManager";

interface Observer {
    onTeamInfoChanged?(sender: MyTeamManager, teamInfo: TeamInfo): void;
}

export enum ROLE {
    LEADER = "LEADER",
    COLEADER = "COLEADER",
    MEMBER = "MEMBER",
}

export enum TeamType {
    OPEN,
    CLOSE
}


@ee.service('Team')
export abstract class MyTeamManager
    extends ee.DefaultObserverManager<Observer> implements ee.Service, IRefreshable {
    refreshData(): void {

    }

    abstract destroy(): void;


    abstract getUserID(): Promise<string>;

    abstract getTeamInfo(): TeamInfo;
    abstract setTeamInfo(teamInfo: TeamInfo): void;
    abstract isClosed(): boolean;

    abstract clearData(): void;

    abstract loadData(): void;

    abstract isHadShowUnlock(): boolean;

    abstract setHadShowUnlock(): void;

    abstract isHadShowUnlockDialog(): boolean;

    abstract setHadShowUnlockDialog(): void;

    abstract getMyRole(): ROLE;

    abstract loadTeamProfile(): Promise<void>;
    abstract updateTeamInfoFromProfile(teamInfo: TeamInfo): void;
    abstract updateTeamInfoFromResponse(teamInfo: TeamInfoResponse): void;

    abstract getTeamMembers(): MemberInfo[];
    abstract loadTeamMembers(): Promise<void>;

    abstract getPendingJoinRequests(): RequestJoin[];
    abstract loadPendingJoinRequests(): Promise<void>;
    abstract deletePendingJoinRequests(request: RequestJoin): boolean;
    abstract addPendingJoinRequests(request: RequestJoin): void;
}