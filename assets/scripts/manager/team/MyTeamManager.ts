import * as ee from '../../libraries/ee/index';
import {IRefreshable} from "../IRefreshable";
import {TeamInfo} from "../../scenes/team/TeamLayer";
import {MemberInfo} from "../../scenes/team/MemberItem";

interface Observer {
    onTeamInfoChanged?(sender: MyTeamManager, teamInfo: TeamInfo): void;
}

export enum ROLE {
    LEADER = "LEADER",
    COLEADER = "COLEADER",
    MEMBER = "MEMBER",
}

export enum TeamType {
    OPEN,
    CLOSE
}


@ee.service('Team')
export abstract class MyTeamManager
    extends ee.DefaultObserverManager<Observer> implements ee.Service, IRefreshable {
    refreshData(): void {

    }

    abstract destroy(): void;

    abstract getUserID(): Promise<string>;

    abstract getTeamInfo(): TeamInfo;
    abstract setTeamInfo(teamInfo: TeamInfo): void;

    abstract clearData(): void;

    abstract loadData(): void;

    abstract isHadShowUnlock(): boolean;

    abstract setHadShowUnlock(): void;

    abstract isHadShowUnlockDialog(): boolean;

    abstract setHadShowUnlockDialog(): void;

    abstract getMyRole(): Promise<ROLE>;
    abstract getMyTeamMember(): Promise<MemberInfo[]>;
    abstract getPendingJoinRequests(): Promise<string[]>;
}