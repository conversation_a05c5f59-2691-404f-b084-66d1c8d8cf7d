import {MyTeamManager, ROLE, TeamType} from "./MyTeamManager";
import {TeamInfo} from "../../scenes/team/TeamLayer";
import {GameServerManager} from "../game_server/GameServerManager";
import {DataManager} from "../data/DataManager";
import {MemberInfo} from "../../scenes/team/MemberItem";
enum Key {
    Data = 'button_join_team_clicked',
    UnlockDialogShowed = 'unlock_dialog_showed',
}
export class DefaultMyTeamManager extends MyTeamManager {
    private _hadShowUnlock = false;
    private _hadShowUnlockDialog = false;

    public constructor(
        private readonly gameServerManager: GameServerManager,
        private readonly dataManager: DataManager) {
        super();
        this._teamInfo = {
            activity: "",
            avatar: "",
            description: "",
            minimumLevelRequired: 0,
            teamId: "",
            teamName: "",
            teamScore: 0,
            teamSize: 0,
            teamType: TeamType[0]
        }
        this._hadShowUnlock = dataManager.getValue(Key.Data, false);
        this._hadShowUnlockDialog = dataManager.getValue(Key.UnlockDialogShowed, false);

    }

    private _teamInfo: TeamInfo;
    private _teamMember: MemberInfo[] = null;
    private _waitingForAcceptJoinList: string[] = [];
    private _myRole: ROLE = null;
    private _uid: string;

    // public get myRole(): ROLE {
    //     return this._myRole;
    // }
    //
    // public set myRole(value: ROLE) {
    //     this._myRole = value;
    // }
    //
    // public setMyRole(value: string) {
    //     const role = ROLE[value as keyof typeof ROLE];
    //     this._myRole = role !== undefined ? role : ROLE.MEMBER;
    // }

    public getTeamInfo(): TeamInfo {
        return this._teamInfo;
    }

    public async getUserID(): Promise<string> {
        return this._uid || (this._uid = await this.gameServerManager.getUId());
    }

    public checkUnlock(): boolean {
        return this._teamInfo.teamId !== "";
    }

    public isHadShowUnlock(): boolean {
        return this._hadShowUnlock;
    }

    public setHadShowUnlock(): void {
        this._hadShowUnlock = true;
        this.dataManager.setValue(Key.Data, true);
    }

    public isHadShowUnlockDialog(): boolean {
        return this._hadShowUnlockDialog;
    }

    public setHadShowUnlockDialog(): void {
        this._hadShowUnlockDialog = true;
        this.dataManager.setValue(Key.UnlockDialogShowed, true);
    }


    public loadData(): void {
        this.gameServerManager.getMyTeamProfile().then(teamProfile => {
            if (teamProfile == null) {
                return;
            } else {
                this._teamInfo = teamProfile;
            }
        });
    }

    public setTeamInfo(teamInfo: TeamInfo): void {
        this._teamInfo = teamInfo;
        this.dispatch(async observer =>
            observer.onTeamInfoChanged && observer.onTeamInfoChanged(this, teamInfo));
    }


    public clearData(): void {
        this._teamInfo = {
            activity: "",
            avatar: "",
            description: "",
            minimumLevelRequired: 0,
            teamId: "",
            teamName: "",
            teamScore: 0,
            teamSize: 0,
            teamType: TeamType[0]
        }
    }

    public destroy(): void {
    }

    public refreshData(): void {
        this.clearData();
        this._hadShowUnlock = this.dataManager.getValue(Key.Data, false);
        this._hadShowUnlockDialog = this.dataManager.getValue(Key.UnlockDialogShowed, false);
        this.loadData();
    }


    public async getMyRole(): Promise<ROLE> {
        if(this._teamInfo == null || this._teamInfo.teamId == ""){
            cc.error("You are not in a team");
        }
        if(this._myRole != null){
            return this._myRole;
        }
        const userID = await this.getUserID();
        const role = (await this.getMyTeamMember()).find(member => member.userId === userID)?.role;
        if(role === ROLE.LEADER){
            this._myRole = ROLE.LEADER;
        }else if(role === ROLE.COLEADER){
            this._myRole = ROLE.COLEADER;
        }else{
            this._myRole = ROLE.MEMBER;
        }
        return this._myRole;
    }

    public async getMyTeamMember(): Promise<MemberInfo[]> {
        if(this._teamInfo == null || this._teamInfo.teamId == ""){
            return [];
        }
        if(this._teamMember != null){
            return this._teamMember;
        }
        this._teamMember = await this.gameServerManager.getTeamMembers(this._teamInfo.teamId);
        return this._teamMember;
    }

    /**
     * Get list of pending join requests.<br/>
     * If you are in a team, it will return empty.<br/>
     * Otherwise, it will return list of team IDs that you have sent request to
     * @returns Array of user IDs
     */
    public async getPendingJoinRequests(): Promise<string[]> {
        if(this._teamInfo != null && this._teamInfo.teamId != ""){
            return [];
        }
        if(this._waitingForAcceptJoinList != null){
            return this._waitingForAcceptJoinList;
        }
        this._waitingForAcceptJoinList = (await this.gameServerManager.getMyTeamRequests()).map(request => request.teamId);
        return this._waitingForAcceptJoinList;
    }

}