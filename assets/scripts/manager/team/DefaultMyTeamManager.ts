import {MyTeamManager, ROLE, TeamType} from "./MyTeamManager";
import {TeamInfo} from "../../scenes/team/TeamLayer";
import {GameServerManager, TeamInfoResponse} from "../game_server/GameServerManager";
import {DataManager} from "../data/DataManager";
import {MemberInfo} from "../../scenes/team/MemberItem";
import {RequestJoin} from "../../team/chat/Manager/ChatManager";

enum Key {
    Data = 'button_join_team_clicked',
    UnlockDialogShowed = 'unlock_dialog_showed',
}

export class DefaultMyTeamManager extends MyTeamManager {
    private _hadShowUnlock = false;
    private _hadShowUnlockDialog = false;

    public constructor(
        private readonly gameServerManager: GameServerManager,
        private readonly dataManager: DataManager) {
        super();
        this._teamInfo = {
            activity: "",
            avatar: "",
            description: "",
            minimumLevelRequired: 0,
            teamId: "",
            teamName: "",
            teamScore: 0,
            teamSize: 0,
            teamType: TeamType[0]
        }
        this._hadShowUnlock = dataManager.getValue(Key.Data, false);
        this._hadShowUnlockDialog = dataManager.getValue(Key.UnlockDialogShowed, false);

    }

    private _teamInfo: TeamInfo;
    private _teamMember: MemberInfo[] = null;
    private _waitingForAcceptJoinList: RequestJoin[] = [];
    private _myRole: ROLE = null;
    private _uid: string;

    public getTeamInfo(): TeamInfo {
        return this._teamInfo;
    }

    public async getUserID(): Promise<string> {
        if (this._uid == null) {
            this._uid = await this.gameServerManager.getUId();
            return this._uid;
        }
        return this._uid;
    }

    public checkUnlock(): boolean {
        return this._teamInfo.teamId !== "";
    }

    public isHadShowUnlock(): boolean {
        return this._hadShowUnlock;
    }

    public setHadShowUnlock(): void {
        this._hadShowUnlock = true;
        this.dataManager.setValue(Key.Data, true);
    }

    public isHadShowUnlockDialog(): boolean {
        return this._hadShowUnlockDialog;
    }

    public setHadShowUnlockDialog(): void {
        this._hadShowUnlockDialog = true;
        this.dataManager.setValue(Key.UnlockDialogShowed, true);
    }


    public async loadData() {
        await this.getUserID();
        await this.loadTeamProfile();
        this.loadTeamMembers().then()
        this.loadPendingJoinRequests().then()
    }

    public setTeamInfo(teamInfo: TeamInfo): void {
        this._teamInfo = teamInfo;
        this.dispatch(async observer =>
            observer.onTeamInfoChanged && observer.onTeamInfoChanged(this, teamInfo));
    }

    public isClosed(): boolean {
        return this._teamInfo.teamType === "CLOSE";
    }

    public getMyRole(): ROLE {
        return this._myRole;
    }

    public async loadTeamProfile() {
        this._teamInfo = await this.gameServerManager.getMyTeamProfile();
        if (this._teamInfo == null) {
            this._teamInfo = {
                activity: "",
                avatar: "",
                description: "",
                minimumLevelRequired: 0,
                teamId: "",
                teamName: "",
                teamScore: 0,
                teamSize: 0,
                teamType: TeamType[0]
            };
        }
    }

    public updateTeamInfoFromProfile(teamInfo: TeamInfo) {
        if (this._teamInfo.teamId !== teamInfo.teamId) {
            return;
        }
        this._teamInfo.activity = teamInfo.activity;
        this._teamInfo.avatar = teamInfo.avatar;
        this._teamInfo.description = teamInfo.description;
        this._teamInfo.minimumLevelRequired = teamInfo.minimumLevelRequired;
        this._teamInfo.teamName = teamInfo.teamName;
        this._teamInfo.teamScore = teamInfo.teamScore;
        this._teamInfo.teamSize = teamInfo.teamSize;
        this._teamInfo.teamType = teamInfo.teamType;
    }

    public updateTeamInfoFromResponse(teamInfo: TeamInfoResponse): void {
        if (this._teamInfo.teamId !== teamInfo.teamId) {
            return;
        }
        this._teamInfo.teamName = teamInfo.teamName;
        this._teamInfo.avatar = teamInfo.avatar;
        this._teamInfo.description = teamInfo.description;
        this._teamInfo.minimumLevelRequired = teamInfo.minimumLevelRequired;
        this._teamInfo.teamType = teamInfo.teamType;
    }

    public async loadTeamMembers(): Promise<void> {
        if (this._teamInfo == null || this._teamInfo.teamId == "") {
            this._teamMember = [];
            return;
        }
        this._teamMember = await this.gameServerManager.getTeamMembers(this._teamInfo.teamId);
        const userId = await this.getUserID();
        this._myRole = this._teamMember.find(member => member.userId === userId)?.role as ROLE;
    }


    public getTeamMembers(): MemberInfo[] {
        return this._teamMember;
    }

    public async getTeamProfileByID(id: string): Promise<TeamInfo> {
        const teamProfile = await this.gameServerManager.getTeamProfile(id);
        this.updateTeamInfoFromProfile(teamProfile)
        return teamProfile;
    }

    public async getTeamMemberByID(id: string): Promise<MemberInfo[]> {
        return await this.gameServerManager.getTeamMembers(id);
    }

    public async loadPendingJoinRequests(): Promise<void> {
        if (this._teamInfo != null && this._teamInfo.teamId != "") {
            this._waitingForAcceptJoinList = [];
            return;
        }
        this._waitingForAcceptJoinList = (await this.gameServerManager.getMyTeamRequests());
    }

    public deletePendingJoinRequests(request: RequestJoin): boolean {
        const index = this._waitingForAcceptJoinList.findIndex(r => r.requestId === request.requestId);
        if (index < 0) {
            return false;
        }
        this._waitingForAcceptJoinList.splice(index, 1);
        return true;
    }

    public addPendingJoinRequests(request: RequestJoin): void {
        this._waitingForAcceptJoinList.push(request);
    }

    public getPendingJoinRequests(): RequestJoin[] {
        return this._waitingForAcceptJoinList;
    }

    public clearData(): void {
        this._teamInfo = {
            activity: "",
            avatar: "",
            description: "",
            minimumLevelRequired: 0,
            teamId: "",
            teamName: "",
            teamScore: 0,
            teamSize: 0,
            teamType: TeamType[0]
        };
        this._teamInfo = null;
        this._teamMember = null;
        this._waitingForAcceptJoinList = [];
        this._myRole = null;
        this._uid = null;
    }

    public destroy(): void {
    }

    public async refreshData() {
        this.clearData();
        this._hadShowUnlock = this.dataManager.getValue(Key.Data, false);
        this._hadShowUnlockDialog = this.dataManager.getValue(Key.UnlockDialogShowed, false);
        await this.loadData();
    }
}