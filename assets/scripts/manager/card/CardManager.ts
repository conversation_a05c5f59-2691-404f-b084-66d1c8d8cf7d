import * as ee from '../../libraries/ee/index';
import {NodeLoader} from "../../utils/NodeLoader";
import {ResourcesLoader} from '../../utils/ResourcesLoader';
import {IRefreshable} from "../IRefreshable";

export enum ItemCardFilterType {
    Collected /*  */ = 1 << 0,
    Uncollected /**/ = 1 << 1,
    AllState /*   */ = Collected | Uncollected,

    Common /*     */ = 1 << 2,
    Rare /*       */ = 1 << 3,
    Epic /*       */ = 1 << 4,
    Legendary /*  */ = 1 << 5,
    AllRarity /*  */ = Common | Rare | Epic | Legendary,

    Character /*  */ = 1 << 6,
    Car /*        */ = 1 << 7,
    Claw /*       */ = 1 << 8,
    Rope /*       */ = 1 << 9,
    Pet /*        */ = 1 << 10,
    AllCategory /**/ = Character | Car | Claw | Rope | Pet,

    Currently /*  */ = 1 << 11,
    Potentially /**/ = 1 << 12,
}

export class ItemCardFilterBuilder {
    // TODO.
    // public get mask():
}

/** Type of item card. */
export enum ItemCardType {
    Character,
    Car,
    Claw,
    Rope,
    Pet,
}

export enum CharacterDeck {
    Jaki = 3,
}


/** The rarity of the item card. */
export enum ItemCardRarity {
    Common,
    Rare,
    Epic,
    Legendary,
}

/** Type of item stats. */
export enum ItemCardStatsType {
    Strength,
    Speed,
    Stability,
    Luck,
    Bonus,
}

export interface ItemCardStats {
    type: ItemCardStatsType;

    /** Base stats. */
    base: number;

    /** Boosted stats, i.e. percentage. */
    boost: number;
}

export interface ItemCard {
    /** Unique ID of the item. */
    id: string;

    /** Used to load the card icon. */
    iconLoader: ResourcesLoader<cc.SpriteFrame>;

    /** Used to load the card preview. */
    previewLoader: NodeLoader;

    /** Gets the item type. */
    type: ItemCardType;

    /** Gets the item rarity. */
    rarity: ItemCardRarity;

    /** Gets the name of the card. */
    name: string;

    /** Gets the name of set */
    set: string;

    /** Whether this card is owned. */
    collected: boolean;

    /** Whether this card is read/checked. */
    new: boolean;

    /** Gets the current level. */
    level: number;

    /** Gets the collected number of cards for the current level. */
    progress: number;

    /** Gets the required number of cards to upgrade. */
    next: number;

    /** Gets bonus stats when upgrade level of card  */
    bonus: number;

    /** Get cost to upgrade */
    upgradeCost: number;

    /** Gets type of main stats of card */
    mainStatsType: ItemCardStatsType;

    /** Get all stats of card */
    stats(): ItemCardStats[];

    /** Checks whether this item is equipped in the specified deck. */
    isEquipped(deckIndex: number): boolean;

    /** Equips this item. */
    equip(deckIndex: number): void;

    /** Upgrade card */
    upgrade(): void;

    /** Can upgrade this card */
    canUpgrade(): boolean;

    /** Collect a number of card */
    collect(quantity: number): void;
}

export interface ItemCardDeck {
    /** The index of the deck. */
    index: number;

    /** Equips the specified item. */
    equip(type: ItemCardType, itemId: string): void;

    /** Gets the card in the specified slot. */
    getItem(type: ItemCardType): ItemCard;

    getStats(): ItemCardStats[];

    /** Selects this deck. */
    select(): void;

}

interface Observer {
    onDataUpdated?(sender: CardManager): void;
}

/** Manages all cards. */
@ee.service("Card")
export abstract class CardManager extends ee.DefaultObserverManager<Observer> implements ee.Service, IRefreshable {
    public abstract destroy(): void;

    /** Gets the specified deck. */
    public abstract getDeck(deckIndex: number): ItemCardDeck

    public abstract getJakiDeck(): ItemCardDeck;

    /** Gets the currently selected deck. */
    public abstract getCurrentDeck(): ItemCardDeck;
    public abstract getCurrentDeckIndex(): number;

    /** Finds card matches the specified id. */
    public abstract findCardById(id: string): ItemCard;

    /** Finds cards matches the specified filter. */
    public abstract findCards(filter?: ItemCardFilterType): ItemCard[];

    /** Get number of cards to upgrade, level is current level */
    public abstract getUpgradeRequiredCard(rarity: ItemCardRarity, level: number): number;

    /** Get number of gold to upgrade, level is current level */
    public abstract getUpgradeRequiredPrice(rarity: ItemCardRarity, level: number): number;

    public abstract getMaxLevel(rarity: ItemCardRarity): number;

    public abstract getMaxStats(type: ItemCardStatsType): number;

    public abstract getLoadoutMaxStats(type: ItemCardStatsType): number;

    public abstract getRandomStateFilter(): ItemCardFilterType;

    public abstract getRandomCategoryFilter(): ItemCardFilterType;

    public abstract getRandomRarityFilter(): ItemCardFilterType;

    public abstract getRandomCardRarityQuantity(rarity: ItemCardRarity, accountLevel: number): number;

    public abstract getCardRarityPrice(rarity: ItemCardRarity): number;

    public abstract getCardAccountUnlockLevel(num: number): number;

    public abstract refreshData(): void;

    public abstract checkFullCollectedSet(): void;

    public abstract showCollectedSetDialog(dialogManager: ee.DialogManager): Promise<boolean>;

    public abstract getCollectedSetByName(setName: string): ItemCard[];

    public abstract getAllCardsFromDeck(deck: ItemCardDeck): ItemCard[];
    public abstract setCurrentDeck(index: number): void;

    public abstract triggerUpgradeCardEvent(level: number) : void;

    public abstract get maxLevelCollectedCard(): number;
    public abstract get maxLevelCollectedSetCard(): number;
}