import * as ee from '../../libraries/ee/index';
import {DataManager} from "../data/DataManager";
import {ShopManager} from '../shop/ShopManager';
import {VipManager} from '../vip/VipManager';
import {IapResult, StoreItem, StoreManager, StoreManagerParser,} from "./StoreManager";
import {PiggyBankManager} from "../../scenes/PiggyBank/PiggyBankManager";
import {PlayPassManager} from "../../scenes/play_pass/PlayPassManager";

enum Key {
    Store = 'store',
}

interface Data {
    [key: string]: number | undefined;
}

export class SimulatedStoreManager extends StoreManager {
    /** Used to set time out. */
    private readonly node: cc.Node;

    /** Custom configs. */
    private refreshMarketItemsTimeOut: number;
    private restorePurchasesTimeOut: number;
    private buyItemTimeOut: number;

    public constructor(private readonly dataManager: DataManager) {
        super();
        this.node = new cc.Node();
        this.dataManager = dataManager;
        this.refreshMarketItemsTimeOut = 1.0;
        this.restorePurchasesTimeOut = 1.0;
        this.buyItemTimeOut = 1.0;
        const items = [
            StoreItem.Ruby,
            StoreItem.Gold,
            StoreItem.Energy,
            StoreItem.Ticket,
            StoreItem.PvpTicket,
        ];
        dataManager.addHandler('store', {
            load: data => {
                const _data = data[Key.Store] || {};
                items.forEach(item => this.setItemBalance(item, _data[item] || 0));
            },
            save: data => {
                data[Key.Store] = data[Key.Store] || {};
                items.forEach(item => data[Key.Store][item] = this.getItemBalance(item));
            },
        });
    }

    public destroy(): void {
        this.dataManager.removeHandler('store');
        this.node.destroy();
    }

    public setRefreshMarketItemsTimeOut(timeOut: number): this {
        this.refreshMarketItemsTimeOut = timeOut;
        return this;
    }

    public setRestorePurchasesTimeOut(timeOut: number): this {
        this.restorePurchasesTimeOut = timeOut;
        return this;
    }

    public setBuyItemTimeOut(timeOut: number): this {
        this.buyItemTimeOut = timeOut;
        return this;
    }

    public refreshMarketItems(): Promise<void> {
        return new Promise((executor, reject) => {
            cc.director.getScheduler().schedule(() => {
                executor();
            }, this.node, this.refreshMarketItemsTimeOut);
        });
    }

    public restorePurchases(): Promise<void> {
        return new Promise((executor, reject) => {
            cc.director.getScheduler().schedule(() => {
                executor();
            }, this.node, this.restorePurchasesTimeOut);
        });
    }

    public getMarketPrice(id: string): number {
        return 1000;
    }

    public getMarketPriceAndCurrency(id: string): string {
        return '$1000';
    }

    public getMarketPriceMicros(id: string): number {
        return 1000;
    }

    public buyItem(id: string): Promise<IapResult> {
        return new Promise((executor, reject) => {
            cc.director.getScheduler().schedule(() => {
                const price = this.getItemPrice('');
                const basePrice = this.getBaseItemPrice();
                const point = price / basePrice;
                const vipMgr = ee.ServiceLocator.resolve(VipManager);
                vipMgr.addVipPoint(point);
                let ruby = 1000;
                if (id === "piggy_bank") {
                    ruby = 0;
                } else {
                    const shopManager = ee.ServiceLocator.resolve(ShopManager);
                    const rubyPackArr = shopManager.getRubyPacks();
                    for (const pack of rubyPackArr) {
                        if (pack.name === id) {
                            ruby = pack.ruby;
                            break;
                        }
                    }
                }

                this.addItemBalance(StoreItem.Ruby, ruby);

                executor(IapResult.Succeeded);

                // cập nhật điều kiện mua iap của piggy bank, play pass
                const piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
                piggyBankManager.buyIap();
                const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
                playPassManager.buyIap();


            }, this.node, 1, 0, this.buyItemTimeOut, false);
        });
    }

    private parserStoreItemString(item: StoreItem): string {
        const dict: { [key: number]: string } = {
            [StoreItem.Ruby /*      */]: "ruby",
            [StoreItem.Gold /*      */]: "gold",
            [StoreItem.Energy /*    */]: "energy",
            [StoreItem.Ticket /*    */]: "ticket",
            [StoreItem.PvpTicket /* */]: "pvpTicket",
        };

        return dict[item];
    }

    public getItemBalance(item: StoreItem): number {
        const id = this.parserStoreItemString(item);
        return this.dataManager.getValue('store_' + id, 0);
    }

    public setItemBalance(item: StoreItem, value: number): void {
        const id = this.parserStoreItemString(item);
        this.dataManager.setValue('store_' + id, value);
        this.dispatch(async observer =>
            observer.onBalanceChanged && observer.onBalanceChanged(item, value, 0));
    }

    public addItemBalance(item: StoreItem, value: number): void {
        const id = this.parserStoreItemString(item);
        const currentValue = this.getItemBalance(item);
        this.dataManager.setValue('store_' + id, currentValue + value);
        this.dispatch(async observer =>
            observer.onBalanceChanged && observer.onBalanceChanged(item, currentValue, value));

        // cập nhật điều kiện sink ruby của piggy bank, play pass
        if (value < 0 && item === StoreItem.Ruby) {
            const piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
            piggyBankManager.sinkRuby(-value);
            const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
            playPassManager.sinkRuby();
        }
    }

    public getBaseItemPrice(): number {
        return 1;
    }

    private getItemPrice(itemId: string): number {
        return 1;
    }

    public handlePromoIAP(packId: string): void {
        // do nothing
    }

    public onPurchaseUpdated(packId: string): void {
        // do nothing
    }

    public setWaitingPromotionCode(value: boolean): void {
        // do nothing
    }

    public createParser(data: {
        [Key.Store]: Data | undefined,
    }): StoreManagerParser {
        const _data = data[Key.Store] || {};
        const parser = {
            ruby: _data[StoreItem.Ruby] || 0,
            gold: _data[StoreItem.Gold] || 0,
        };
        return parser;
    }
}