import {IapResult, <PERSON><PERSON>tem, StoreManager, StoreManagerParser} from "./StoreManager";

class IapItem {
    public constructor(
        private readonly _name: string,
        private readonly _productId: string) {
    }

    public get name(): string {
        return this._name;
    }

    public get productId(): string {
        return this._productId;
    }
}

export class NativeStoreManager extends StoreManager {
    private nativeBridge: NativeIAPBridge;

    addItemBalance(item: StoreItem, value: number): void {
    }

    buyItem(id: string): Promise<IapResult> {
        return Promise.resolve(undefined);
    }

    createParser(data: any): StoreManagerParser {
        return undefined;
    }

    destroy(): void {
    }

    getBaseItemPrice(): number {
        return 0;
    }

    getItemBalance(item: StoreItem): number {
        return 0;
    }

    getMarketPrice(id: string): number {
        return 0;
    }

    getMarketPriceAndCurrency(id: string): string {
        return "";
    }

    getMarketPriceMicros(id: string): number {
        return 0;
    }

    handlePromoIAP(packId: string): void {
    }

    onPurchaseUpdated(packId: string): void {
    }

    refreshMarketItems(): Promise<void> {
        return Promise.resolve(undefined);
    }

    restorePurchases(): Promise<void> {
        return Promise.resolve(undefined);
    }

    setItemBalance(item: StoreItem, value: number): void {
    }

    setWaitingPromotionCode(value: boolean): void {
    }

}