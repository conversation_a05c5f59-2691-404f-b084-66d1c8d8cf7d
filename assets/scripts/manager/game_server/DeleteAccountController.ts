import {IErrResponse, IGameServer, ILogger} from "@senspark/ee-x";
import {Commands} from "./GameServerManager";
import {checkInternetAndShowDialog} from "../../server/InternetChecking";
import {DataManager} from "../data/DataManager";
import CancelDeleteAccountDialog from "../../dialog/CancelDeleteAccountDialog";
import * as ee from "../../libraries/ee/index";
import {DialogManager} from "../../libraries/ee/index";

export interface IDeleteAccountController {
    
    isDeleting(): Promise<boolean>;

    deleteAccount(): Promise<boolean>;

    cancelDeleting(): Promise<boolean>;
    
    deleteComplete(): Promise<boolean>;
}

export const DeletingKeyStorage = "isDeleting" 
export default class DeleteAccountController implements IDeleteAccountController {
    private readonly _logger: ILogger;
    private readonly _bridge: IGameServer;
    private readonly _dataManager: DataManager
    private _isDeleting: boolean = false
    private _isFirstTime: boolean = true

    constructor(bridge: IGameServer,dataManager: DataManager, logger: ILogger) {
        this._bridge = bridge;
        this._logger = logger;
        this._dataManager = dataManager
    }

    async isDeleting(): Promise<boolean> {
        if (this._isFirstTime) {
            this._isFirstTime = false
            await this._bridge.getServerInfo().then(
                value => {
                    this._isDeleting = value.isDeleting
                }
            )
        }
        return this._isDeleting;
    }

    async deleteAccount(): Promise<boolean> {
        const hasInternet = await checkInternetAndShowDialog()
        if (!hasInternet) return false;
        const res = await this._bridge.send({
            method: `POST`,
            command: Commands.DELETE,
            data: ""
        })
        this._logger.info("Delete account respond: " + res)
        this._isDeleting = !(res == null || (res as IErrResponse).errCode);
        this._dataManager.setValue(DeletingKeyStorage, this._isDeleting)
        if (this._isDeleting)
        {
            CancelDeleteAccountDialog.create().then((dialog) => {
                dialog.show(ee.ServiceLocator.resolve(DialogManager))
            })
        }
        return this._isDeleting
        
    }

    async cancelDeleting(): Promise<boolean> {
        const hasInternet = await checkInternetAndShowDialog()
        if (!hasInternet) return false;
        const res = await this._bridge.send({
            method: `POST`,
            command: Commands.CANCEL_DELETE,
            data: ""
        })
        const cancelState = !(res == null || (res as IErrResponse).errCode);
        this._isDeleting = !cancelState
        this._dataManager.setValue(DeletingKeyStorage, this._isDeleting)
        return cancelState
    }

    async deleteComplete (): Promise<boolean> {
        return !(await this.isDeleting()) && this._dataManager.getValue(DeletingKeyStorage, false)
    }

}

