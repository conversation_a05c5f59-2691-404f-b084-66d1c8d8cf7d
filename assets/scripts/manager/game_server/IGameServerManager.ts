import { IErrResponse } from '@senspark/ee-x/dist/game_server/IGameServer';
import * as ee from '../../libraries/ee/index';
import {LeaderBoardOption, TeamRankingData, UserRankingData} from "../leaderboard/LeaderboardManager";
import {GameMode} from "../level/LevelManager";
import {TeamInfo} from "../../scenes/team/TeamLayer";

@ee.service('IGameServerManager')
export abstract class IGameServerManager implements ee.Service {
    public abstract initialize(): Promise<void>;

    public abstract saveGame(): void;

    /**
     * Download game data from server.
     *
     * @returns `IDownloadDataResponse` if load successfully,
     * `IErrResponse` if load failed because of server error,
     * `null` if load failed because of client error.
     */
    public abstract downloadGameData(): Promise<IDownloadDataResponse | IErrResponse | null>;

    public abstract completeGameLevel(level: number, score: number, highestStar: number, latestStar: number, areaType: GameMode): Promise<void>;

    public abstract downloadChecksum(): Promise<ChecksumType | null>;

    public abstract downloadLevels(levelsNames: string[]): Promise<ILevelDownloadResponseData[]>;

    public abstract askToReloadCloudSaveData(data: IDownloadDataResponse | null): Promise<void>;

    public abstract forceReloadCloudSaveData(data: IDownloadDataResponse | IErrResponse | null): Promise<void>;

    public abstract getLevelsOverride(): Promise<LevelOverride>;

    public abstract getUserLeaderBoard(options?: LeaderBoardOption): Promise<UserRankingData[]>;

    public abstract getTeamLeaderBoard(options?: LeaderBoardOption): Promise<TeamRankingData[]>;

    public abstract getAndReplaceData(): Promise<boolean>

    public abstract getMyTeamProfile(): Promise<TeamInfo | null>;

    public abstract searchTeamByName(teamName: string): Promise<any>;

    public abstract destroy(): void;
}

export interface ILevelDownloadResponseData {
    lvName: string,
    data: string,
}

export type ChecksumType = Record<string, string>;

export type LevelOverride = Record<string, string>;

export interface IDownloadDataResponse {
    savedData: string
    clientDataVersion: number
    playTimeSeconds: number
    modifiedAt: string
}