import * as ee_x from "@senspark/ee-x";
import {AccountInfo, IErrResponse, ILogger, IServerConnectionInfo, Logger, Platform} from "@senspark/ee-x";
import {
    ChecksumType,
    IDownloadDataResponse,
    IGameServerManager,
    ILevelDownloadResponseData,
    LevelOverride,
} from "./IGameServerManager";
import {DataManager} from "../data/DataManager";
import CocosScheduler from "../time/CocosScheduler";
import {DefaultSchedulerId, IScheduler} from "../time/IScheduler";
import SimpleGameClientRequest from "./SimpleGameClientRequest";
import CloudSaveLoadGameController, {CloudSaveStatus} from "./CloudSaveLoadGameController";
import {IAuthenManager} from "../authentication/IAuthenManager";
import DeleteAccountController, {IDeleteAccountController} from "./DeleteAccountController";
import {TextDecoder, TextEncoder} from 'text-encoder';
import {LeaderBoardOption, TeamRankingData, UserRankingData} from "../leaderboard/LeaderboardManager";
import {PiggyBankManager} from "../../scenes/PiggyBank/PiggyBankManager";
import {ServiceLocator} from "../../libraries/ee/index";
import {UserProfile} from "../../scenes/profile/UserProfileView";
import {AreaInfo, GameMode, LevelInfo} from "../level/LevelManager";
import {TeamInfo} from "../../scenes/team/TeamLayer";
import {MemberInfo} from "../../scenes/team/MemberItem";
import {ChatCommands} from "../../team/chat/Manager/WebSocketChatNetworkManager";
import {RequestJoin} from "../../team/chat/Manager/ChatManager";

// Define JoinRequestStatus type to match the RequestJoin interface
export enum JoinRequestStatus {
    PENDING = "PENDING",
    APPROVED = "APPROVED",
    REJECTED = "REJECTED",
    CANCELLED = "CANCELLED",
    EXPIRED = "EXPIRED",
    BANNED = "BANNED",
}

export const Commands = {
    SIGN_IN: 'login',
    UPLOAD_DATA: 'save-data',
    DOWNLOAD_DATA: 'load-data',
    KEEP_ALIVE: 'ping',
    DELETE: 'delete-account',
    CANCEL_DELETE: 'cancel-delete-account',
    DOWNLOAD_CHECKSUM: 'download-checksum',
    DOWNLOAD_LEVELS: 'download-levels',
    GET_LEVELS_OVERRIDE: 'get-level-override',
    COMPLETE_GAME_LEVEL: 'complete-game-level',
    GET_LEADERBOARD: 'get-leaderboard',
    CAN_LINK_ACCOUNT: 'can-link',
    LINK_ACCOUNT: 'link-account',
    GET_REWARD_INFO: 'get-rewards',
    CLAIM_REWARD: 'claim-reward',
    RELOAD_LEADERBOARD: 'admin/cheat/reset-leaderboard',
    RESET_WEEKLY_SEASON: 'admin/cheat/reset-weekly-season',
    GET_USER_PROFILE: "get-user-profile",
    DELETE_IMMEDIATELY: 'delete-account/now',
    GET_HIGHEST_SCORE: 'game-levels/highest-score',

    /** Team */
    CREATE_TEAM: 'teams/create',
    UPDATE_TEAM_INFO: 'teams/update-info',
    JOIN_TEAM: 'teams/join/v2',
    LEAVE_TEAM: 'teams/leave',
    DELETE_TEAM: 'teams/delete',
    SEARCH_TEAM: 'teams/search',
    GET_TEAM_INFO: 'teams/profile',
    GET_TEAM_MEMBERS: 'teams/team-members',
    GET_MY_TEAM_PROFILE: 'teams/my-team-profile',
    DEMOTE_MEMBER: 'teams/demote-member',
    PROMOTE_MEMBER: 'teams/promote-member',
    KICK_MEMBER: 'teams/kick-member',

    /** Team close */
    GET_MY_TEAM_REQUESTS: 'teams/join-close/my-requests',
    GET_TEAM_REQUESTS: 'teams/join-close/team-requests',
    REJECT_JOIN_REQUEST: 'teams/join-close/reject',
    ACCEPT_JOIN_REQUEST: 'teams/join-close/accept',
    CANCEL_JOIN_REQUEST: 'teams/join-close/cancel',
    ACCEPT_ALL_JOIN_REQUEST: 'teams/join-close/accept/all',

    // BroadcastMessage
    BROADCAST_NEW_MESSAGE: 'chat/broadcast-new-message',
    BROADCAST_UPDATED_MESSAGE: 'chat/broadcast-update-message',
    BROADCAST_UPDATE_JOIN_REQUEST: 'teams/join-close/update-request',
    BROADCAST_NEW_JOIN_REQUEST: 'teams/join-close/new-request',
    BROADCAST_UPDATE_TEAM_INFO: 'teams/update-info',

    // Cheat commands
    ADD_BOT_MEMBER_TO_TEAM: 'cheat/team/add-bot-member',
    ADD_VIRTUAL_TEAM: 'cheat/team/add-virtual-team',
    ADD_RANDOM_CHAT_TEXT: 'cheat/chat/add-random-chat-text',
    ADD_RANDOM_REQUEST: 'cheat/chat/add-random-request',
    HELP_NEAREST_REQUEST: 'cheat/chat/help-nearest-request', 
    CLEAR_ALL_USER_BANS: "cheat/team/clear-all-user-bans",


};
let PingServerIntervalSeconds = 30;
let SaveDataIntervalSeconds = 30;
let GameVersion = 1;

export class GameServerManager extends IGameServerManager {

    private readonly _bridge: ee_x.IGameServer
    private _scheduler: IScheduler = new CocosScheduler();
    private readonly _logger: ILogger = new Logger('[GSM]');
    private readonly _simpleGameClientRequest: SimpleGameClientRequest;
    public readonly _cloudSaveLoadGameController: CloudSaveLoadGameController;
    public readonly deleteAccountController: IDeleteAccountController;
    private _saveDataScheduleId: number = DefaultSchedulerId;
    private _pingServerScheduleId: number = DefaultSchedulerId;
    private _dataHashed: number = -1;
    private _isSaving: boolean = false;
    private _cloudData: IDownloadDataResponse | null = null;
    private _saveDataIntervalSeconds: number = SaveDataIntervalSeconds

    public constructor(
        private readonly _serverConfig: IServerConnectionInfo,
        private readonly _dataManager: DataManager,
        private readonly _authenManager: IAuthenManager
    ) {
        super();
        this._bridge = ee_x.PluginManager.createPlugin<ee_x.IGameServer>(ee_x.Plugin.GameServer);
        this._simpleGameClientRequest = new SimpleGameClientRequest(_serverConfig.host.replace("ws", "http"), _serverConfig.gameClientJwt);
        this._cloudSaveLoadGameController = new CloudSaveLoadGameController(GameVersion, _dataManager, this._logger);
        this.deleteAccountController = new DeleteAccountController(this._bridge, _dataManager, this._logger);

    }

    public registerBroadcastMessageListener(command: string, objectId: string, handler: (message: string) => void) {
        this._bridge.registerReceivedBroadcastMessage(command, objectId, handler);
    }

    public unregisterBroadcastMessageListener(command: string, objectId: string): void {
        this._bridge.unregisterReceivedBroadcastMessage(command, objectId);
    }

    public destroy(): void {
        this._scheduler.unSchedule(this._saveDataScheduleId);
        this._scheduler.unSchedule(this._pingServerScheduleId);
        this._bridge.destroy();
    }

    public async initialize(): Promise<void> {
        try {
            const accountInfo = await this._authenManager.getUserInfo();
            accountInfo ? await this.initGGAccount(accountInfo) : await this.initDeviceAccount();
            this._saveDataIntervalSeconds = Math.max(SaveDataIntervalSeconds, await this.getServerInterval());
            this.autoKeepAlive();
        } catch (error) {
            console.error(error);
            this._logger.error(`Failed to initialize GameServerManager: ${error}`);
        }
    }

    public async initGGAccount(accountInfo: AccountInfo) {
        // Disable all schedule
        this._cloudData = null
        if (this._saveDataScheduleId != DefaultSchedulerId) {
            this._scheduler.unSchedule(this._saveDataScheduleId)
        }

        // if (this._pingServerScheduleId != DefaultSchedulerId) {
        //     this._scheduler.unSchedule(this._pingServerScheduleId)
        // }

        // initialize _bridge again
        await this._bridge.initialize(this._dataManager, {
            ...this._serverConfig,
            userUniqueId: accountInfo.uid,
        });

    }

    public async initDeviceAccount() {
        this._cloudData = null
        const userUniqueId = await Platform.getDeviceIdentifier();
        await this._bridge.initialize(this._dataManager, {
            ...this._serverConfig,
            userUniqueId: userUniqueId,
        });

    }

    public async saveGame(): Promise<boolean> {
        this._dataHashed = -1
        return await this.sendSaveData()
    }

    public async getAndReplaceData(): Promise<boolean> {
        const cloudSaveStatus = this._cloudSaveLoadGameController.getCloudSaveStatus();
        const serverAlive = await this.sendKeepAliveRequest()
        if (!serverAlive) {
            return false
        }
        if (cloudSaveStatus == CloudSaveStatus.Unknown || await this.deleteAccountController.deleteComplete()) {
            const cloudData = await this.downloadGameData()
            await this.forceReloadCloudSaveData(cloudData)
            if (cloudData != null) {
                this._cloudSaveLoadGameController.setCloudSaveStatus(CloudSaveStatus.LoadedFromCloud)
            } else {
                this._cloudSaveLoadGameController.setCloudSaveStatus(CloudSaveStatus.NewGame)
            }
            return true
        }
        this.autoSave()
        return false
    }

    public async askToReloadCloudSaveData(data: IDownloadDataResponse | null) {
        if (data == null) {
            const cloudSaveStatus = this._cloudSaveLoadGameController.getCloudSaveStatus();
            if (cloudSaveStatus === CloudSaveStatus.Unknown || await this.deleteAccountController.deleteComplete()) {
                const cloudData = await this.downloadGameData()
                await this._cloudSaveLoadGameController.askToLoadGame(cloudData)
            }
        } else {
            await this._cloudSaveLoadGameController.askToLoadGame(data);
        }
        this.autoSave();
    }

    public async downloadGameData(): Promise<IDownloadDataResponse | IErrResponse | null> {
        try {

            if (this._cloudData != null) return this._cloudData;

            const data = JSON.stringify({
                client_data_version: GameVersion,
                play_time: 1,
            });
            const res = await this._bridge.send<IDownloadDataResponse | IErrResponse>({
                command: Commands.DOWNLOAD_DATA,
                method: 'POST',
                data: data
            });
            if (!res) {
                return null;
            }
            if ((res as IErrResponse).errCode) {
                return res;
            }
            const r = res as IDownloadDataResponse;
            r.savedData = this.base64Decode(r.savedData)
            this._cloudData = r;
            return r;
        } catch (e) {
            this._logger.error(`Failed to load data: ${e}`);
            return null;
        }
    }

    public async downloadLevels(levelsNames: string[]): Promise<ILevelDownloadResponseData[]> {
        try {
            const res = await this._bridge.sendGameClientRequest<ILevelDownloadResponseData[]>({
                command: Commands.DOWNLOAD_LEVELS,
                data: JSON.stringify(levelsNames)
            });
            return res
        } catch (e) {
            this._logger.error(`SYNCER Failed to download levels: ${e}`);
            return [];
        }
    }

    public async forceReloadCloudSaveData(data: IDownloadDataResponse | IErrResponse | null, callBackAfterReload?: () => Promise<void>) {

        const piggyBankManager = ServiceLocator.resolve(PiggyBankManager);
        piggyBankManager.autoCashBack = false;

        await this._cloudSaveLoadGameController.forceLoadGame(data, callBackAfterReload)
        this.autoSave();
        piggyBankManager.autoCashBack = true;

    }

    public async completeGameLevel(level: number, score: number, highestStar: number, latestStar: number, areaType: GameMode): Promise<void> {
        try {
            const payLoad = {
                level: level,
                score: score,
                highestStar: highestStar,
                latestStar: latestStar,
                mode: GameMode[areaType]
            }
            // Use sendWithoutResponse instead of send to avoid waiting for response
            this._bridge.sendWithoutResponse({
                method: 'GET',
                command: Commands.COMPLETE_GAME_LEVEL,
                data: JSON.stringify(payLoad)
            });
        } catch (e) {
            this._logger.error(`${e}`);
        }
    }

    public downloadChecksum(): Promise<ChecksumType | null> {
        return this._simpleGameClientRequest.downloadChecksum();
    }

    public getLevelsOverride(): Promise<LevelOverride> {
        return this._simpleGameClientRequest.getLevelOverride();
    }

    private async sendSaveData() {
        try {
            if (this._isSaving) {
                return false;
            }
            const newHashed = this._dataManager.getDataHashed();
            if (this._dataHashed === newHashed) {
                return false;
            }
            this._isSaving = true;
            this._dataHashed = newHashed;
            const data = JSON.stringify(
                {
                    client_data_version: 1,
                    play_time: 1,
                    // gửi base64 cho gọn
                    data: this.base64Encode(this._dataManager.exportData()),
                } as IUploadDataRequest
            );
            const res = await this._bridge.send<IErrResponse>({
                command: Commands.UPLOAD_DATA,
                method: 'POST',
                data: data
            });
            return res == null;

        } catch (e) {
            this._logger.error(`Failed to save game: ${e}`);
            return false
        } finally {
            this._isSaving = false;
        }
    }

    private async sendKeepAliveRequest(): Promise<boolean> {
        try {
            const res = await this._bridge.send<null | IErrResponse>({
                command: Commands.KEEP_ALIVE,
                method: 'GET',
            });
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to send keep alive request: ${e}`);
            return false
        }
    }

    public autoSave() {
        if (this._saveDataScheduleId != DefaultSchedulerId) {
            this._scheduler.unSchedule(this._saveDataScheduleId)
        }
        // this.saveGame().then()
        this._saveDataScheduleId = this._scheduler.scheduleLoop(() => {
            this.sendSaveData().then();
        }, this._saveDataIntervalSeconds, this._saveDataIntervalSeconds);
    }

    private autoKeepAlive() {
        if (this._pingServerScheduleId != DefaultSchedulerId) {
            this._scheduler.unSchedule(this._pingServerScheduleId)
        }
        this._pingServerScheduleId = this._scheduler.scheduleLoop(() => {
            this.sendKeepAliveRequest().then();
        }, PingServerIntervalSeconds, PingServerIntervalSeconds);
    }

    private async getServerInterval(): Promise<number> {
        const serverInfo = await this._bridge.getServerInfo();
        return serverInfo.timeInterval;
    }

    private base64Encode(str: string): string {
        const bytes: Uint8Array = new TextEncoder().encode(str);
        const binString = Array.from(bytes, (byte) =>
            String.fromCodePoint(byte),
        ).join("");
        return btoa(binString);
    }

    private base64Decode(str: string): string {
        const binString = atob(str);
        const bytes: Uint8Array = Uint8Array.from(binString, (m) => m.codePointAt(0));
        return new TextDecoder().decode(bytes);
    }

    public getUserLeaderBoard(options?: LeaderBoardOption): Promise<UserRankingData[]> {
        return this._bridge.send<UserRankingData[]>({
            method: 'GET',
            command: Commands.GET_LEADERBOARD,
            data: JSON.stringify(options)
        });
    }

    public getTeamLeaderBoard(options?: LeaderBoardOption): Promise<TeamRankingData[]> {
        return this._bridge.send<TeamRankingData[]>({
            method: 'GET',
            command: Commands.GET_LEADERBOARD,
            data: JSON.stringify(options)
        });
    }

    public async canLinkAccount(deviceId: string, googleId: string): Promise<boolean> {
        const res = await this._bridge.send<null | IErrResponse>({
            method: 'GET',
            command: Commands.CAN_LINK_ACCOUNT,
            data: JSON.stringify({
                device_id: deviceId,
                google_id: googleId
            })
        });
        return !(res == null || (res as IErrResponse).errCode);
    }

    public async linkAccount(deviceId: string, googleId: string): Promise<boolean> {
        const res = await this._bridge.send<null | IErrResponse>({
            method: 'POST',
            command: Commands.LINK_ACCOUNT,
            data: JSON.stringify({
                device_id: deviceId,
                google_id: googleId
            })
        });
        return !(res == null || (res as IErrResponse).errCode);
    }

    public async getRewardInfo(): Promise<IGetRewardInfoResponse[]> {
        const res = await this._bridge.send<IGetRewardInfoResponse[] | IErrResponse | null>({
            method: 'GET',
            command: Commands.GET_REWARD_INFO,
        });
        if (res == null || (res as IErrResponse).errCode) {
            return [];
        }
        return res as IGetRewardInfoResponse[];
    }

    public async claimReward(rewardType: string): Promise<boolean> {
        const res = await this._bridge.send<IErrResponse | null>({
            method: 'POST',
            command: Commands.CLAIM_REWARD,
            data: JSON.stringify({reward_type: rewardType})
        });
        return !(res == null || (res as IErrResponse).errCode);
    }

    // Cheat for testing
    public reloadLeaderboard() {
        const headers = new Headers();
        headers.append("Authorization", this._serverConfig.gameClientJwt);

        const requestOptions = {
            method: "POST",
            headers: headers,
        };

        fetch(`${this._serverConfig.host.replace("ws", "http")}/${Commands.RELOAD_LEADERBOARD}`, requestOptions)
            .then((response) => response.text())
            .then((result) => console.log(result))
            .catch((error) => console.error(error));
    }

    // Cheat for testing
    public resetWeeklySeason() {
        const headers = new Headers();
        headers.append("Authorization", this._serverConfig.gameClientJwt);

        const requestOptions = {
            method: "POST",
            headers: headers,

        };

        fetch(`${this._serverConfig.host.replace("ws", "http")}/${Commands.RESET_WEEKLY_SEASON}`, requestOptions)
            .then((response) => response.text())
            .then((result) => console.log(result))
            .catch((error) => console.error(error));
    }

    public async deleteAccountImmediately() {
        const respond = await this._bridge.send({
            method: 'POST',
            command: Commands.DELETE_IMMEDIATELY,
            data: ''
        })

        await this.forceReloadCloudSaveData(null)
        this._cloudSaveLoadGameController.setCloudSaveStatus(CloudSaveStatus.NewGame)
        return respond
    }

    public async getUserInfo(id: string): Promise<IErrResponse | UserProfile | null> {
        const res = await this._bridge.send<IErrResponse | UserProfile | null>({
            method: 'GET',
            command: Commands.GET_USER_PROFILE,
            data: JSON.stringify({
                id: id
            })
        })

        cc.log(res);

        return res
    }

    public async getUId(): Promise<string> {
        return (await this._bridge.getServerInfo()).uid
    }

    async getHighestScore(areaInfo: AreaInfo, levelInfo: LevelInfo): Promise<number> {
        const level = areaInfo.index * 20 + levelInfo.index + 1;
        let mode: string;
        switch (areaInfo.areaType) {
            case GameMode.STORY:
                mode = "STORY";
                break;
            case GameMode.EVENT:
                mode = "EVENT";
                break;
            case GameMode.CHARACTER:
                mode = "CHARACTER";
                break;
        }
        const data = JSON.stringify({
            level: level,
            mode: mode
        })

        interface HighestScoreResponse {
            highestScore: number;
            levelId: number;
            mode: string;
        }

        const res = await this._bridge.send<IErrResponse | HighestScoreResponse | null>({
            method: 'GET',
            command: Commands.GET_HIGHEST_SCORE,
            data: data
        })
        if (res == null || (res as IErrResponse).errCode) {
            return 0;
        }
        return (res as HighestScoreResponse).highestScore
    }

    public async searchTeamByName(teamName: string): Promise<TeamInfo[]> {
        try {
            const payload = teamName ? {team_name: teamName} : {};
            const res = await this._bridge.send<IErrResponse | TeamInfo[] | null>({
                method: 'GET',
                command: Commands.SEARCH_TEAM,
                data: JSON.stringify(payload),
            });

            if (!res || (res as IErrResponse).errCode) {
                return [];
            }

            return res as TeamInfo[];
        } catch (e) {
            this._logger.error(`Failed to search team: ${e}`);
            return [];
        }
    }


    public async createTeam(
        name: string,
        avatar: string,
        teamType: string,
        desc: string,
        minLevelRequire: number
    ): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null | TeamInfoResponse>({
                method: 'POST',
                command: Commands.CREATE_TEAM,
                data: JSON.stringify({
                    teamName: name,
                    avatar: avatar,
                    teamType: teamType,
                    description: desc,
                    minimumLevelRequired: minLevelRequire
                }),
            });
            cc.log(res)
            if (!res || (res as IErrResponse).errCode) {
                this._logger.error(`Failed to create team: ${res}`);
                return false;
            }

            return true;
        } catch (e) {
            this._logger.error(`Failed to create team: ${e}`);
            return false;
        }
    }


    public async updateTeamInfo(teamInfo: TeamInfoResponse): Promise<TeamInfoResponse | null> {
        try {
            const res = await this._bridge.send<IErrResponse | TeamInfoResponse | null>({
                method: 'POST',
                command: Commands.UPDATE_TEAM_INFO,
                data: JSON.stringify({
                    id: teamInfo.teamId,
                    avatar: teamInfo.avatar,
                    teamName: teamInfo.teamName,
                    description: teamInfo.description,
                    teamType: teamInfo.teamType,
                    minimumLevelRequired: teamInfo.minimumLevelRequired
                }),
            });

            if (!res || (res as IErrResponse).errCode) {
                this._logger.error(`Failed to update team info: ${res}`);
                return null;
            }

            return res as TeamInfoResponse;
        } catch (e) {
            this._logger.error(`Failed to update team info: ${e}`);
            return null;
        }
    }

    //get team profile
    public async getTeamProfile(teamId: string): Promise<TeamInfo> {
        try {
            const res = await this._bridge.send<IErrResponse | TeamInfo | null>({
                method: 'GET',
                command: Commands.GET_TEAM_INFO,
                data: JSON.stringify({
                    id: teamId
                })
            })
            cc.log(res);
            if (res == null || (res as IErrResponse).errCode) {
                this._logger.error(`Failed to get team profile: ${JSON.stringify(res)}`);
                return null;
            }
            return res as TeamInfo;
        } catch (e) {
            this._logger.error(`Failed to get team profile: ${e}`);
            return null;
        }
    }

    //get all team members
    public async getTeamMembers(teamId: string): Promise<MemberInfo[]> {
        try {
            const res = await this._bridge.send<IErrResponse | MemberInfo[] | null>({
                method: 'GET',
                command: Commands.GET_TEAM_MEMBERS,
                data: JSON.stringify({
                    id: teamId
                })
            })
            cc.log(res);
            if (res == null || (res as IErrResponse).errCode) {
                this._logger.error(`Failed to get team members: ${res}`);
                return [];
            }
            return res as MemberInfo[];
        } catch (e) {
            this._logger.error(`Failed to get team members: ${e}`);
            return [];
        }
    }

    /**
     * Attempts to join a team.
     *
     * @param teamId - The ID of the team to join.
     * @returns A tuple [status, errorCode]:
     * - `status`: JoinRequestStatus if successful, null if failed.
     * - `errorCode`: null if successful, numeric error code if failed.
     */
    public async joinTeam(teamId: string): Promise<[JoinRequestStatus | null, number | null]> {
        try {
            const res = await this._bridge.send<IErrResponse | {status: JoinRequestStatus} | null>({
                method: 'POST',
                command: Commands.JOIN_TEAM,
                data: JSON.stringify({
                    teamId: teamId
                })
            });
            // Check if response is an error
            if (res && (res as IErrResponse).errCode) {
                const errCode = (res as IErrResponse).errCode;
                return [null, errCode];
            }

            // Check if response is null
            if (!res) {
                return [null, -1];
            }

            // Success case - extract status from response
            const successResponse = res as {status: JoinRequestStatus};
            return [successResponse.status, null];
        } catch (e) {
            this._logger.error(`Failed to join team: ${e}`);
            return [null, -1];
        }
    }


    /**
     * Leave team
     * @param teamId
     * @returns true if success, false if error
     */
    public async leaveTeam(teamId: string): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null>({
                method: 'POST',
                command: Commands.LEAVE_TEAM,
                data: JSON.stringify({
                    teamId: teamId
                })
            })
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to leave team: ${e}`);
            return false;
        }
    }

    //** CHAT */
    public async sendChatCommand(command: string, dataJson: string = ""): Promise<string | null> {
        if ([ChatCommands.SEND_CHAT_TEXT, ChatCommands.SEND_REQUEST_ENERGY, ChatCommands.HELP_ENERGY, ChatCommands.RESET_TIME_FOR_NEXT_REQUEST ].includes(command)) {
            this._bridge.sendWithoutResponse({
                method: 'POST',
                command: command,
                data: dataJson
            })
            return "Send"
        }
        const res = await this._bridge.send<IErrResponse | string | null>({
            method: 'POST',
            command: command,
            data: dataJson
        });
        if (res == null || (res as IErrResponse).errCode) {
            this._logger.error(`Failed to send chat command: ${res}`);
            return null;
        }
        return JSON.stringify(res);
    }

    async getMyTeamProfile(): Promise<TeamInfo> {
        try {
            const res = await this._bridge.send<IErrResponse | TeamInfo | null>({
                method: 'GET',
                command: Commands.GET_MY_TEAM_PROFILE,
                data: ""
            })
            if (res == null || (res as IErrResponse).errCode) {
                return null;
            }
            return res as TeamInfo;
        } catch (e) {
            this._logger.error(`Failed to get my team profile: ${e}`);
            return null;
        }
    }

    public async demoteMemberWithId(memberId: string, fromTeamId: string): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null>({
                method: 'POST',
                command: Commands.DEMOTE_MEMBER,
                data: JSON.stringify({
                    userId: memberId,
                    teamId: fromTeamId
                })
            })
            cc.log(res);
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to demote member: ${e}`);
            return false;
        }
    }

    public async kickMemberWithId(memberId: string, fromTeamId: string): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null>({
                method: 'POST',
                command: Commands.KICK_MEMBER,
                data: JSON.stringify({
                    userId: memberId,
                    teamId: fromTeamId
                })
            })
            cc.log(res);
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to kick member: ${e}`);
            return false;
        }
    }

    public async promoteMemberWithId(memberId: string, fromTeamId: string): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null>({
                method: 'POST',
                command: Commands.PROMOTE_MEMBER,
                data: JSON.stringify({
                    userId: memberId,
                    teamId: fromTeamId,
                    toRole: "coleader"
                })
            })
            cc.log(res);
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to promote member: ${e}`);
            return false;
        }
    }

    addBotToTeam(teamId: string, amount: number) {
        this._bridge.sendWithoutResponse({
            method: 'POST',
            command: Commands.ADD_BOT_MEMBER_TO_TEAM,
            data: JSON.stringify({
                teamId: teamId,
                botCount: amount,
            })
        })
    }

    addVirtualTeam() {
        this._bridge.sendWithoutResponse({
            method: 'POST',
            command: Commands.ADD_VIRTUAL_TEAM,
            data: ""
        })
    }

    addChatText(teamId: string) {
        this._bridge.sendWithoutResponse({
            method: 'POST',
            command: Commands.ADD_RANDOM_CHAT_TEXT,
            data: JSON.stringify({
                teamId: teamId,
            })
        })
    }

    addRequest(teamId: string) {
        this._bridge.sendWithoutResponse({
            method: 'POST',
            command: Commands.ADD_RANDOM_REQUEST,
            data: JSON.stringify({
                teamId: teamId,
            })
        })
    }

    helpNearestRequest(teamId: string) {
        this._bridge.sendWithoutResponse({
            method: 'POST',
            command: Commands.HELP_NEAREST_REQUEST,
            data: JSON.stringify({
                teamId: teamId,
            })
        })
    }

    /** Team Close API Methods */

    /**
     * Accept all join requests for a team
     * @param teamId - The ID of the team
     * @returns true if success, false if error
     */
    public async acceptAllJoinRequests(teamId: string): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null>({
                method: 'POST',
                command: Commands.ACCEPT_ALL_JOIN_REQUEST,
                data: JSON.stringify({
                    teamId: teamId
                })
            });
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to accept all join requests: ${e}`);
            return false;
        }
    }

    /**
     * Reject a join request
     * @param requestId - The ID of the request to reject
     * @returns true if success, false if error
     */
    public async rejectJoinRequest(requestId: string): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null>({
                method: 'POST',
                command: Commands.REJECT_JOIN_REQUEST,
                data: JSON.stringify({
                    requestId: requestId
                })
            });
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to reject join request: ${e}`);
            return false;
        }
    }

    /**
     * Get team join requests
     * @param teamId - The ID of the team
     * @returns Array of RequestJoin objects
     */
    public async getTeamRequests(teamId: string): Promise<RequestJoin[]> {
        try {
            const res = await this._bridge.send<IErrResponse | RequestJoin[] | null>({
                method: 'GET',
                command: Commands.GET_TEAM_REQUESTS,
                data: JSON.stringify({
                    teamId: teamId
                })
            });
            if (res == null || (res as IErrResponse).errCode) {
                this._logger.error(`Failed to get team requests: ${res}`);
                return [];
            }
            return res as RequestJoin[];
        } catch (e) {
            this._logger.error(`Failed to get team requests: ${e}`);
            return [];
        }
    }

    /**
     * Get my team join requests
     * @returns Array of RequestJoin objects
     */
    public async getMyTeamRequests(): Promise<RequestJoin[]> {
        try {
            const res = await this._bridge.send<IErrResponse | RequestJoin[] | null>({
                method: 'GET',
                command: Commands.GET_MY_TEAM_REQUESTS,
                data: ""
            });
            if (res == null || (res as IErrResponse).errCode) {
                this._logger.error(`Failed to get my team requests: ${res}`);
                return [];
            }
            return res as RequestJoin[];
        } catch (e) {
            this._logger.error(`Failed to get my team requests: ${e}`);
            return [];
        }
    }

    /**
     * Cancel a join request
     * @param requestId - The ID of the request to cancel
     * @returns true if success, false if error
     */
    public async cancelJoinRequest(requestId: string): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null>({
                method: 'POST',
                command: Commands.CANCEL_JOIN_REQUEST,
                data: JSON.stringify({
                    requestId: requestId
                })
            });
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to cancel join request: ${e}`);
            return false;
        }
    }

    /**
     * Accept a join request
     * @param requestId - The ID of the request to accept
     * @returns true if success, false if error
     */
    public async acceptJoinRequest(requestId: string): Promise<boolean> {
        try {
            const res = await this._bridge.send<IErrResponse | null>({
                method: 'POST',
                command: Commands.ACCEPT_JOIN_REQUEST,
                data: JSON.stringify({
                    requestId: requestId
                })
            });
            return !(res == null || (res as IErrResponse).errCode);
        } catch (e) {
            this._logger.error(`Failed to accept join request: ${e}`);
            return false;
        }
    }

    public async cleanTeamBan() {
        return this._bridge.sendWithoutResponse({
            method: 'POST',
            command: Commands.CLEAR_ALL_USER_BANS,
            data: ""
        });
    }
}

interface IUploadDataRequest {
    client_data_version: number;
    play_time: number;
    data: string;
}

interface IGetRewardInfoResponse {
    type: string;
    rewardData: string;
}

export interface TeamInfoResponse {
    teamId: string;
    teamName: string;
    avatar: string;
    description: string;
    minimumLevelRequired: number;
    teamType: "OPEN" | "CLOSE";
}
