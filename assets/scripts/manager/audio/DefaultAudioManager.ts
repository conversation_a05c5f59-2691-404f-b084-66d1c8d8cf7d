import {DataManager} from "../data/DataManager";
import {AudioManager} from "./AudioManager";
import {SoundType} from "./SoundType";

/** Min volume must be > 0. */
const MINIMUM_VOLUME = 0.00001;

export class DefaultAudioManager extends AudioManager {
    /** Data. */
    private _isMusicEnabled = true;
    private _isSoundEnabled = true;
    private _musicVolume = MINIMUM_VOLUME;
    private _soundVolume = 0.8;

    /** Normalization factors for different sound types */
    private readonly soundNormalizationFactors: { [key: string]: number } = {
        [SoundType.ButtonPress]: 0.7,    // Giảm âm lượng của button press vì thường quá to
        [SoundType.Score]: 0.8,          // Giảm âm lượng của âm thanh điểm số
        [SoundType.GameOver]: 0.8,       // Giảm âm lượng của âm thanh game over
        [SoundType.Finish]: 0.8,         // Giảm âm lượng của âm thanh kết thúc
        [SoundType.Explosive]: 0.7,      // Giảm âm lượng của âm thanh nổ
        [SoundType.BreakStone]: 0.7,     // Giảm âm lượng của âm thanh đập đá
        [SoundType.Dig]: 0.7,            // Giảm âm lượng của âm thanh đào
        [SoundType.Fall]: 0.7,           // Giảm âm lượng của âm thanh rơi
        [SoundType.GhostHowling]: 0.6,   // Giảm âm lượng của âm thanh ma
        [SoundType.WolfRoar]: 0.6,       // Giảm âm lượng của âm thanh sói
        [SoundType.VultureAttack]: 0.6,  // Giảm âm lượng của âm thanh kền kền
        [SoundType.ThiefAttack]: 0.6,    // Giảm âm lượng của âm thanh trộm
        [SoundType.SlotRoll]: 0.7,       // Giảm âm lượng của âm thanh slot roll
        [SoundType.SlotStop]: 0.7,       // Giảm âm lượng của âm thanh slot stop
        [SoundType.SlotWin]: 0.7,        // Giảm âm lượng của âm thanh slot win
        [SoundType.LastSecond]: 0.7,     // Giảm âm lượng của âm thanh đếm ngược
        [SoundType.PreScore]: 0.7,       // Giảm âm lượng của âm thanh pre-score
        [SoundType.ValueHigh]: 0.7,      // Giảm âm lượng của âm thanh giá trị cao
        [SoundType.ValueLow]: 0.7,       // Giảm âm lượng của âm thanh giá trị thấp
        [SoundType.ValueNormal]: 0.7,    // Giảm âm lượng của âm thanh giá trị bình thường
    };

    /** Aux. */
    private audio = cc.audioEngine;

    private audioIds: { [key: string]: number | undefined } = {};
    private audioDict: { [key: string]: string } = {};
    private clips: { [key: string]: cc.AudioClip | undefined } = {};

    /** Updater. */
    private readonly node: cc.Node;
    private isMusicDataDirty = false;
    private isSoundDataDirty = false;
    private isMusicConfigDirty = false;
    private isSoundConfigDirty = false;

    public constructor(private readonly dataManager: DataManager) {
        super();
        // Data.
        this._isMusicEnabled = this.dataManager.getValue('music_enabled', true);
        this._isSoundEnabled = this.dataManager.getValue('sound_enabled', true);
        this._musicVolume = this.dataManager.getValue('music_volume', 0.2);
        this._soundVolume = this.dataManager.getValue('sound_volume', 0.8);
        this.isMusicConfigDirty = true;
        this.isSoundConfigDirty = true;

        // Aux.
        this.audioDict = {
            [SoundType.Australia]: "sound/bg_australia",
            // [SoundType.AustraliaReverse]: "sound/bg_australia_reverse",
            [SoundType.Arizona]: "sound/bg_arizona",
            // [SoundType.ArizonaReverse]: "sound/bg_arizona_reverse",
            [SoundType.BgMenu]: "sound/bg_menu",
            // [SoundType.BgMenuReverse]: "sound/bg_menu_reverse",
            [SoundType.BreakStone]: "sound/break-stone",
            [SoundType.ButtonPress]: "sound/button-press",
            [SoundType.CartFloat]: "sound/cart-floating",
            [SoundType.CartMove]: "sound/cart-move",
            [SoundType.CartStop]: "sound/cart-stop",
            // [SoundType.Confused]: "",
            [SoundType.Dig]: "sound/dig",
            [SoundType.Electric]: "sound/electric",
            [SoundType.Explosive]: "sound/explosive",
            [SoundType.Fall]: "sound/fall-to-water",
            [SoundType.Finish]: "sound/finish-game",
            [SoundType.GameOver]: "sound/game-over",
            [SoundType.GhostHowling]: "sound/ghost-howling",
            [SoundType.Gift]: "sound/gift_receive",
            [SoundType.Hawaii]: "sound/bg_hawaii",
            // [SoundType.HawaiiReverse]: "sound/bg_hawaii_reverse",
            [SoundType.LastSecond]: "sound/last-10s-sound",
            // [SoundType.MusselClose]: "",
            // [SoundType.MusselOpen]: "",
            [SoundType.PreScore]: "sound/pre-score",
            // [SoundType.PrizeSpin]: "sound/prize_spin", // Prize wheel is disabled.
            [SoundType.Pull]: "sound/pull",
            [SoundType.Reach1]: "sound/reach-goal1",
            [SoundType.Reach2]: "sound/reach-goal2",
            [SoundType.Reach3]: "sound/reach-goal3",
            [SoundType.Score]: "sound/score",
            [SoundType.SlotRoll]: "sound/slot-machine-rolling",
            [SoundType.SlotStop]: "sound/slot-machine-stop",
            [SoundType.SlotWin]: "sound/slot-machine-win",
            [SoundType.ThiefAttack]: "sound/thief-attack",
            [SoundType.Throw]: "sound/throw",
            [SoundType.TouchBarrier]: "sound/touch-barrier",
            [SoundType.ValueHigh]: "sound/value-high",
            [SoundType.ValueLow]: "sound/value-low",
            [SoundType.ValueNormal]: "sound/value-normal",
            [SoundType.Vegas]: "sound/bg_vegas",
            // [SoundType.VegasReverse]: "sound/bg_vegas_reverse",
            [SoundType.VultureAttack]: "sound/vulture-attack",
            [SoundType.VultureRedGrab]: "sound/vulture-grab-bad",
            [SoundType.RubyNoise]: "sound/ui-noise-ruby",
            [SoundType.VultureWhiteGrab]: "sound/vulture-grab-good",
            [SoundType.WolfRoar]: "sound/wolf-roar-and-attack",
            [SoundType.TempleDig]: "sound/HiddenTemple/dig",
            [SoundType.TempleOpenGate]: "sound/HiddenTemple/open_gate",
            [SoundType.TemplePutGem]: "sound/HiddenTemple/put_gem",
        };

        // Update.
        this.node = new cc.Node();
        cc.director.getScheduler().schedule(() => {
            if (this.isMusicDataDirty) {
                this.isMusicDataDirty = false;
                this.saveMusicData();
            }
            if (this.isSoundDataDirty) {
                this.isSoundDataDirty = false;
                this.saveSoundData();
            }
        }, this.node, 1 /* May cause low fps. */);
        cc.director.getScheduler().schedule(() => {
            if (this.isMusicConfigDirty) {
                this.isMusicConfigDirty = false;
                this.updateMusicConfig();
            }
            if (this.isSoundConfigDirty) {
                this.isSoundConfigDirty = false;
                this.updateSoundConfig();
            }
        }, this.node, 0.1);
    }

    public destroy(): void {
        this.node.destroy();
    }
    public override refreshData() {
        this._isMusicEnabled = this.dataManager.getValue('music_enabled', true);
        this._isSoundEnabled = this.dataManager.getValue('sound_enabled', true);
        this._musicVolume = this.dataManager.getValue('music_volume', 0.2);
        this._soundVolume = this.dataManager.getValue('sound_volume', 0.8);
        this.updateMusicConfig()
        this.updateSoundConfig()
    }

    private saveMusicData(): void {
        this.dataManager.setValue('music_enabled', this._isMusicEnabled);
        this.dataManager.setValue('music_volume', this._musicVolume);
    }

    private saveSoundData(): void {
        this.dataManager.setValue('sound_enabled', this._isSoundEnabled);
        this.dataManager.setValue('sound_volume', this._soundVolume);
    }

    private updateMusicConfig(): void {
        this.audio.setMusicVolume(Math.max(this.musicVolume, MINIMUM_VOLUME));
    }

    private updateSoundConfig(): void {
        this.audio.setEffectsVolume(Math.max(this.soundVolume, MINIMUM_VOLUME));
    }

    public async preload(): Promise<void> {
        const keys = Object.keys(this.audioDict).filter(item => this.audioDict[item].length > 0);
        const urls = keys.map(item => this.audioDict[item]);
        return await new Promise<void>((resolve, reject) => {
            cc.loader.loadResArray(urls, cc.AudioClip, (err: Error | null, clips: cc.AudioClip[]) => {
                if (err) {
                    reject();
                } else {
                    const targetCounter = clips.length;
                    let counter = 0;
                    clips.forEach((item, index) => {
                        const key = keys[index];
                        this.clips[key] = item;
                        // cc.loader.load doesn't call native function.
                        ++counter;
                        if (counter === targetCounter) {
                            resolve();
                        }
                    });
                }
            });
        });
    }

    public get isMusicEnabled(): boolean {
        return this._isMusicEnabled;
    }

    public set isMusicEnabled(value: boolean) {
        if (this._isMusicEnabled === value) {
            return;
        }
        this._isMusicEnabled = value;
        value && this.musicVolume === 0 && (this._musicVolume = 0.5);
        this.isMusicDataDirty = true;
        this.isMusicConfigDirty = true;
    }

    public get musicVolume(): number {
        return this.isMusicEnabled ? this._musicVolume : 0;
    }

    public set musicVolume(value: number) {
        if (this._musicVolume === value) {
            return;
        }
        this._musicVolume = value;
        this._isMusicEnabled = value > 0;
        this.isMusicDataDirty = true;
        this.isMusicConfigDirty = true;
    }

    public get isSoundEnabled(): boolean {
        return this._isSoundEnabled;
    }

    public set isSoundEnabled(value: boolean) {
        if (this._isSoundEnabled === value) {
            return;
        }
        this._isSoundEnabled = value;
        value && this.soundVolume === 0 && (this._soundVolume = 0.5);
        this.isSoundDataDirty = true;
        this.isSoundConfigDirty = true;
    }

    public get soundVolume(): number {
        return this.isSoundEnabled ? this._soundVolume : 0;
    }

    public set soundVolume(value: number) {
        if (this._soundVolume === value) {
            return;
        }
        this._soundVolume = value;
        this._isSoundEnabled = value > 0;
        this.isSoundDataDirty = true;
        this.isSoundConfigDirty = true;
    }

    public playSound(type: SoundType, loop?: boolean): number | undefined {
        const clip = this.clips[type];
        if (clip) {
            // Áp dụng hệ số chuẩn hóa âm lượng
            const normalizationFactor = this.soundNormalizationFactors[type] || 1.0;
            const normalizedVolume = this.soundVolume * normalizationFactor;

            // Lưu âm lượng gốc
            const originalVolume = this.audio.getEffectsVolume();

            // Tạm thời điều chỉnh âm lượng tổng thể
            this.audio.setEffectsVolume(normalizedVolume);

            // Phát âm thanh
            const audioId = this.audio.playEffect(clip, loop === undefined ? false : loop);

            // Khôi phục âm lượng gốc
            this.audio.setEffectsVolume(originalVolume);

            this.audioIds[type] = audioId;
            return audioId;
        }
        return undefined;
    }

    public playMusic(type: SoundType): void {
        const clip = this.clips[type];
        clip && this.audio.playMusic(clip, true);
    }

    public pauseMusic(): void {
        this.audio.pauseMusic();
    }

    public stopMusic(): void {
        this.audio.stopMusic();
    }

    public resumeMusic(): void {
        this.audio.resumeMusic();
    }

    public stopAllSounds(): void {
        // https://forum.cocos.com/t/android-audiodecodermp3/53010/9
        // Calling stop after play may cause deadlock.
        // E.g. playMusic then stopAllSounds.
        // this.audio.stopAllEffects();

        // Workaround:
        Object.keys(this.audioIds).forEach(key => {
            this.stopSound(key as any as SoundType);
        });
    }

    public stopAllSoundsNotFinish(): void {
        Object.keys(this.audioIds).forEach(key => {
            if (key as any as SoundType != SoundType.Finish) {
                this.stopSound(key as any as SoundType);
            }
        });
    }

    public pauseAllSounds(): void {
        // https://discuss.cocos2d-x.org/t/audiomix-anr-issue/42867/27
        this.audio.pauseAllEffects();
        this.audio.resumeAllEffects();
        this.audio.pauseAllEffects();
    }

    public resumeAllSounds(): void {
        this.audio.resumeAllEffects();
    }

    public stopSound(type: SoundType): void {
        const audioId = this.audioIds[type];
        audioId !== undefined && this.audio.stopEffect(audioId);
        delete this.audioIds[type];
    }

    public stopSoundById(id: number): void {
        this.audio.stopEffect(id);
    }

    public getNormalizationFactor(type: string): number {
        return this.soundNormalizationFactors[type] || 1.0;
    }

    public getSoundMeasurement(type: string): number {
        // Since we can't measure actual RMS, return the default measurement
        return this.getDefaultMeasurement(type);
    }

    private getDefaultMeasurement(type: string): number {
        // Default measurements based on sound type
        const measurements: { [key: string]: number } = {
            [SoundType.ButtonPress]: 0.8,
            [SoundType.Score]: 0.7,
            [SoundType.GameOver]: 0.8,
            [SoundType.Finish]: 0.8,
            [SoundType.Explosive]: 0.9,
            [SoundType.BreakStone]: 0.8,
            [SoundType.Dig]: 0.7,
            [SoundType.Fall]: 0.7,
            [SoundType.GhostHowling]: 0.9,
            [SoundType.WolfRoar]: 0.9,
            [SoundType.VultureAttack]: 0.8,
            [SoundType.ThiefAttack]: 0.8,
            [SoundType.SlotRoll]: 0.7,
            [SoundType.SlotStop]: 0.7,
            [SoundType.SlotWin]: 0.8,
            [SoundType.LastSecond]: 0.7,
            [SoundType.PreScore]: 0.7,
            [SoundType.ValueHigh]: 0.7,
            [SoundType.ValueLow]: 0.7,
            [SoundType.ValueNormal]: 0.7,
        };
        return measurements[type] || 0.7; // Default to 0.7 if not found
    }
}