import { ConfigManager } from "../config/ConfigManager";
import { DataManager } from "../data/DataManager";
import {
    RawReward,
    RewardInfo,
    RewardManager,
} from "../reward/RewardManager";
import { PvpManager } from "./PvpManager";

interface RawConfig {
    streakRewards?: RawReward[];
    leagueRewards?: RawReward[];
}

interface Config {
    streakRewards: RewardInfo[];
    leagueRewards: RewardInfo[];
}

class ConfigImpl implements Config {
    private _streakRewards: RewardInfo[];
    private _leagueRewards: RewardInfo[];

    public constructor(
        private readonly data: RawConfig,
        private readonly rewardManager: RewardManager) {
        this._streakRewards = (data.streakRewards || []).map(item => rewardManager.createReward(item));
        this._leagueRewards = (data.leagueRewards || []).map(item => rewardManager.createReward(item));
    }

    public get streakRewards(): RewardInfo[] {
        return this._streakRewards;
    }

    public get leagueRewards(): RewardInfo[] {
        return this._leagueRewards;
    }
}

interface RawData {
    claimed?: boolean;
    streak?: number;
}

interface Data {
    claimed: boolean;
    streak: number;
}

class DataImpl implements Data {
    public constructor(private readonly data: RawData) {
    }

    public get raw(): RawData {
        return this.data;
    }

    public get claimed(): boolean {
        if (this.data.claimed === undefined) {
            this.data.claimed = true;
        }
        return this.data.claimed;
    }

    public set claimed(value: boolean) {
        this.data.claimed = value;
    }

    public get streak(): number {
        if (this.data.streak === undefined) {
            this.data.streak = -1;
        }
        return this.data.streak;
    }

    public set streak(value: number) {
        this.data.streak = value;
    }
}

enum Key {
    Handler = 'pvp_info',
    Config = 'pvp_info',
    Data = 'pvp_info',
}

interface DataStorage {
    [Key.Data]: RawData | undefined;
}

export class DefaultPvpManager extends PvpManager {
    private data: Data;
    private config: Config;

    public constructor(
        configManager: ConfigManager,
        private readonly dataManager: DataManager,
        rewardManager: RewardManager) {
        super();

        // Config.
        const rawConfig = configManager.getValue<RawConfig>(Key.Config);
        this.config = new ConfigImpl(rawConfig, rewardManager);

        // Data.
        const rawData = dataManager.getValue<RawData>(Key.Data, {});
        this.data = new DataImpl(rawData);

        // Sync handler.
        dataManager.addHandler(Key.Handler, {
            load: (data: DataStorage) => {
                this.data = new DataImpl(data[Key.Data] || {});
                this.saveData();
            },
            save: (data: DataStorage) => {
                data[Key.Data] = (this.data as DataImpl).raw;
            },
        });
    }

    public override refreshData() {
        // Data.
        const rawData = this.dataManager.getValue<RawData>(Key.Data, {});
        this.data = new DataImpl(rawData);
    }

    public get isEnabled(): boolean {
        // TODO.
        return false;
    }

    public destroy(): void {
        this.dataManager.removeHandler(Key.Handler);
    }

    public getCurrentStreakIndex(): number {
        return this.data.streak;
    }

    public increaseStreak(): void {
        ++this.data.streak;
        this.data.claimed = false;

        this.saveData();
    }

    public resetStreak(): void {
        this.data = new DataImpl({});
        this.saveData();
    }

    public isStreakRewardAvailable(): boolean {
        return !this.data.claimed;
    }

    public claimStreakReward(): void {
        if (this.data.claimed) {
            return;
        }
        this.data.claimed = true;
        this.saveData();
    }

    public getStreakRewards(): RewardInfo[] {
        return this.config.streakRewards;
    }

    public getLeagueRewards(): RewardInfo[] {
        return this.config.leagueRewards;
    }

    /** Saves the current data. */
    private saveData(): void {
        this.dataManager.setValue(Key.Data, (this.data as DataImpl).raw);
    }
}
