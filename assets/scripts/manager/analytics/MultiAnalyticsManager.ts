import { AnalyticsBundle } from "./AnalyticsBundle";
import { AnalyticsManager } from "./AnalyticsManager";
import {AdRevenue, IapRevenue} from "@senspark/ee-x";

export class MultiAnalyticsManager extends AnalyticsManager {
    private readonly managers: AnalyticsManager[] = [];

    public addManager(manager: AnalyticsManager): void {
        this.managers.push(manager);
    }

    public destroy(): void {
        for (let i = 0, n = this.managers.length; i < n; ++i) {
            this.managers[i].destroy();
        }
    }

    public trackEvent(bundle: AnalyticsBundle): void {
        for (let i = 0, n = this.managers.length; i < n; ++i) {
            this.managers[i].trackEvent(bundle);
        }
    }

    public trackConversion(name: string): void {
        for (let i = 0, n = this.managers.length; i < n; ++i) {
            this.managers[i].trackConversion(name);
        }
    }

    logAdRevenue(adRevenue: AdRevenue): void {
        for (let i = 0, n = this.managers.length; i < n; ++i) {
            this.managers[i].logAdRevenue(adRevenue);
        }
    }

    logIapRevenue(iapRevenue: IapRevenue): void {
        for (let i = 0, n = this.managers.length; i < n; ++i) {
            this.managers[i].logIapRevenue(iapRevenue);
        }
    }

    popGameLevel(winGame: boolean, reason: string): void {
        for (let i = 0, n = this.managers.length; i < n; ++i) {
            this.managers[i].popGameLevel(winGame, reason);
        }
    }

    pushGameLevel(levelNo: string, levelMode: string): void {
        for (let i = 0, n = this.managers.length; i < n; ++i) {
            this.managers[i].pushGameLevel(levelNo, levelMode);
        }
    }
}