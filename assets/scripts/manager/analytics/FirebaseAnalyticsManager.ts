import { ConfigManager } from "../config/ConfigManager";
import { AnalyticsBundle } from "./AnalyticsBundle";
import { <PERSON><PERSON><PERSON> } from "./AnalyticsConfig";
import { AnalyticsManager } from "./AnalyticsManager";
import * as ee_x from "@senspark/ee-x";
import {AdRevenue, IapRevenue} from "@senspark/ee-x";

interface TestDeviceConfig {
    device: string[];
}

export class FirebaseAnalyticsManager extends AnalyticsManager {
    private analytics?: ee_x.IFirebaseAnalytics;
    private isTestDevice = false;
    private initialized = false;
    private pendingBundles: AnalyticsBundle[] = [];

    public constructor(configManager: ConfigManager, appIdentity: string) {
        super();
        
        const config = configManager.getValue<TestDeviceConfig>('test_device_ids');
        const deviceList = config.device;
        ee_x.Platform.getDeviceId().then((id: string) => {
            const arr = deviceList.filter(item => item === id);
            cc.log('device id: ' + id);
            if (arr.length !== 0) {
                this.isTestDevice = true;
                cc.log('this is test device => should skip all event');
            }
            this.initialize(appIdentity);
        });
    }

    public destroy(): void {
    }

    private initialize(appIdentity: string): void {
        if (!this.isTestDevice) {
            this.analytics = ee_x.PluginManager.createPlugin<ee_x.IFirebaseAnalytics>(ee_x.Plugin.FirebaseAnalytics);
            this.analytics.initialize(appIdentity).then();

            this.pendingBundles.forEach(bundle => {
                this.trackEvent(bundle);
            });
        }
        this.pendingBundles = [];
        this.initialized = true;
    }

    public trackEvent(bundle: AnalyticsBundle): void {
        if (!this.initialized) {
            this.pendingBundles.push(bundle);
            return;
        }
        if (this.isTestDevice || this.analytics === undefined) {
            return;
        }
        const data = bundle.getMapData();
        const eventName = data[TrackKey.Action];
        const dict: { [key: string]: string } = {};
        Object.keys(data).forEach(key => {
            if (key !== TrackKey.Action) {
                dict[key] = data[key];
            }
        });
        this.analytics.logEvent(eventName, dict);
    }

    public trackConversion(name: string): void {
        const bundle = new AnalyticsBundle().setData(TrackKey.Action, name);
        this.trackEvent(bundle);
    }

    public logAdRevenue(adRevenue: AdRevenue): void {
        this.analytics.logAdRevenue(adRevenue);
    }

    public logIapRevenue(iapRevenue: IapRevenue): void {
        this.analytics.logIapRevenue(iapRevenue);
    }

    public popGameLevel(winGame: boolean, reason: string): void {
        this.analytics.popGameLevel(winGame, reason);
    }

    public pushGameLevel(levelNo: string, levelMode: string): void {
        this.analytics.pushGameLevel(levelNo, levelMode);
    }
}