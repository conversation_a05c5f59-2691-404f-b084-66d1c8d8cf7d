import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ana<PERSON>} from "./DataManager";
import {DataStorage} from "./DataStorage";

export class DefaultDataManager extends DataManager {
    private handlers: { [key: string]: DataHandler | undefined } = {};

    /* Stop service này, ko cho tiếp tục ghi data nữa
    * */
    private _stopWriteData: boolean = false;

    private _dataHashed: number = 0;

    public constructor(private readonly storage: DataStorage) {
        super();
    }

    public destroy(): void {
        this._stopWriteData = true;
    }

    public getKeys(): string[] {
        return this.storage.getKeys();
    }

    public getValue<T>(key: string, defaultValue: T): T {
        const data = this.storage.getValue<T>(key);
        if (data === undefined) {
            return defaultValue;
        }
        return data;
    }

    public setValue<T>(key: string, value: T): void {
        if (this._stopWriteData) {
            return;
        }
        this._dataHashed++;
        this.storage.setValue<T>(key, value);
    }

    public addHandler(key: string, handler: DataHandler): boolean {
        if (this.handlers[key]) {
            return false;
        }
        this.handlers[key] = handler;
        return true;
    }

    public removeHandler(key: string): boolean {
        if (this.handlers[key]) {
            delete this.handlers[key];
            return true;
        }
        return false;
    }

    public load(data: any): void {
        Object.keys(this.handlers).forEach(item => {
            const handler = this.handlers[item];
            handler && handler.load(data);
        });
    }

    public save(data: any): void {
        if (this._stopWriteData) {
            return;
        }
        Object.keys(this.handlers).forEach(item => {
            const handler = this.handlers[item];
            handler && handler.save(data);
        });
    }

    public clear(): void {
        this.storage.clear();
    }

    public exportData(): string {
        return this.storage.exportData();
    }

    public getDataHashed(): number {
        return this._dataHashed;
    }

    public refreshData(): void {
        this.storage.retrieveStorage()
    }
}
