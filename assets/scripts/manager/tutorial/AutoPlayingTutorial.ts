import { TutorialDialog } from '../../scenes/game_scene/dialogs/TutorialDialog';
import { GameScene } from '../../scenes/game_scene/GameScene';
import { AutoMineManager } from '../auto_mine/AutoMineManager';
import { Tutorial } from "./Tutorial";
import {
    TutorialManager,
    TutorialType,
} from "./TutorialManager";

/* nhanc18: Ko dùng Tutorial này nữa (16/11/2023)
* */
export class AutoPlayingTutorial implements Tutorial {
    private readonly type: TutorialType;
    private isStarted = false;
    private isDialogHidden = false;

    public constructor(
        private readonly tutorialManager: TutorialManager,
        private readonly autoMineManager: AutoMineManager,
        private readonly scene: GameScene) {
        this.type = TutorialType.AutoPlay;
    }

    public start(): void {
        if (this.isStarted) {
            return;
        }
        this.isStarted = true;
        this.isDialogHidden = false;
        TutorialDialog.create().then(dialog => {
            this.scene.controller.pauseLevel();
            dialog.setTutorial(this.type);
            dialog.onDidHide(() => {
                this.scene.controller.resumeLevel();
                this.scene.playAnimationAutoButton();
                this.isDialogHidden = true;
            });
            dialog.show(this.scene.dialogManager);
        });

        // Give free auto-mine duration to avoid displaying auto-mine dialog.
        this.autoMineManager.duration = this.autoMineManager.getAdsDuration();
    }

    public finish(): void {
        if (!this.isStarted) {
            return;
        }
        this.isStarted = false;
        this.scene.stopAnimationAutoButton();
        this.tutorialManager.completeTutorial(this.type);

        // Refresh duration.
        this.autoMineManager.duration = this.autoMineManager.getAdsDuration();
    }

    public checkStart(): boolean {
        const shouldStart = !this.isStarted
            && !this.tutorialManager.isTutorialComplete(this.type)
            && this.scene.levelView.config.area === 0
            && this.scene.levelView.config.level === 9;
        return shouldStart;
    }

    public checkFinish(): boolean {
        return this.isStarted && this.isDialogHidden && this.scene.autoToggled;
    }
}