import * as gm from '../../engine/gm_engine';
import { TutorialDialog } from '../../scenes/game_scene/dialogs/TutorialDialog';
import { GameScene } from '../../scenes/game_scene/GameScene';
import { Tutorial } from './Tutorial';
import {
    TutorialManager,
    TutorialType,
} from './TutorialManager';

export class CollectibleTutorial implements Tutorial {
    private readonly collectibleTypes: { [key: number]: gm.CollectionType };
    private isStarted = false;
    private isDialogHidden = false;

    public constructor(
        private readonly tutorialManager: TutorialManager,
        private readonly scene: GameScene,
        private readonly type: TutorialType) {
        this.collectibleTypes = {
            [TutorialType.Gem]: gm.CollectionType.Gem,
            [TutorialType.Chip]: gm.CollectionType.Chip,
            [TutorialType.Mask]: gm.CollectionType.Mask,
            [TutorialType.Quart]: gm.CollectionType.Quart,
        };
    }

    private getCollectibleType(): gm.CollectionType {
        return this.collectibleTypes[this.type];
    }

    public start(): void {
        if (this.isStarted) {
            return;
        }
        this.isStarted = true;
        this.isDialogHidden = false;
        const entityManager = this.scene.levelView.entityView.entityManager;
        TutorialDialog.create().then(dialog => {
            this.scene.controller.pauseLevel();
            dialog.setTutorial(this.type);
            dialog.onDidHide(() => {
                this.scene.controller.resumeLevel();
                const items = entityManager.findComponents(gm.Collectible);
                for (let i = items.length - 1; i >= 0; --i) {
                    const item = items[i];
                    const blinkable = item.getComponent(gm.Blinkable);
                    blinkable && blinkable.blink(true);
                    const pullable = item.getComponent(gm.Pullable);
                    pullable && pullable.onPulled(() => {
                        blinkable && blinkable.stopBlink();
                    });
                }
                this.isDialogHidden = true;
            });
            dialog.show(this.scene.dialogManager);
        });
    }

    public finish(): void {
        this.tutorialManager.completeTutorial(this.type);
    }

    public checkStart(): boolean {
        const entityManager = this.scene.levelView.entityView.entityManager;
        const items = entityManager.findComponents(gm.Collectible);
        return !this.isStarted
            && !this.tutorialManager.isTutorialComplete(this.type)
            && items.length > 0
            && this.getCollectibleType() === items[0].type;
    }

    public checkFinish(): boolean {
        return this.isStarted
            && this.isDialogHidden /* Is this condition necessary since
                                      collectionVIew.isCompleted implies dialog is already hidden. */
            && this.scene.levelView.collectionView.isCompleted();
    }
}