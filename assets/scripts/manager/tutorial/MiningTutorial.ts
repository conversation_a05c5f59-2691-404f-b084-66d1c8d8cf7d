import { TutorialDialog } from '../../scenes/game_scene/dialogs/TutorialDialog';
import { GameScene } from '../../scenes/game_scene/GameScene';
import { Tutorial } from "./Tutorial";
import {
    TutorialManager,
    TutorialType,
} from "./TutorialManager";

export class MiningTutorial implements Tutorial {
    private readonly type: TutorialType;
    private isStarted = false;
    private isDialogHidden = false;

    public constructor(
        private readonly tutorialManager: TutorialManager,
        private readonly scene: GameScene) {
        this.type = TutorialType.Mining;
    }

    public start(): void {
        if (this.isStarted) {
            return;
        }
        this.isStarted = true;
        this.isDialogHidden = false;
        this.scene.stopAnimationMiningArea();
        this.scene.controller.pauseLevel();

        TutorialDialog.create().then(dialog => {
            dialog.setTutorial(TutorialType.Mining);
            dialog.onDidHide(() => {
                this.scene.controller.resumeLevel();
                this.scene.playAnimationMiningArea();
                this.isDialogHidden = true;
            });
            dialog.show(this.scene.dialogManager);
        });
    }

    public finish(): void {
        this.tutorialManager.completeTutorial(this.type);
        this.scene.stopAnimationMiningArea();
    }

    public checkStart(): boolean {
        const shouldStart = !this.isStarted
            && !this.tutorialManager.isTutorialComplete(this.type)
            && this.scene.levelView.config.area === 0
            && this.scene.levelView.config.level === 0;
        return shouldStart;
    }

    public checkFinish(): boolean {
        return this.isStarted
            && this.isDialogHidden
            && (this.scene.levelView.timeView.time > 10
                || this.scene.didDigging);
    }
}