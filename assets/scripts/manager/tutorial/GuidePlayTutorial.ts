const {ccclass, property} = cc._decorator;

@ccclass
export default class GuidePlayTutorial extends cc.Component{
    @property(cc.Node)
    overlay: cc.Node = null;

    private handAnimation: sp.Skeleton = null;

    protected onLoad() {
        this.handAnimation = this.getComponent(sp.Skeleton);
    }

    onEnable() {
        this.overlay.active = true;
        this.handAnimation.setAnimation(0, 'hand_click', true);
    }

    onDisable() {
        this.overlay.active = false;
        this.handAnimation.clearTracks();
    }
}
