import * as gm from '../../engine/gm_engine';
import { GameScene } from '../../scenes/game_scene/GameScene';
import { Tutorial } from "./Tutorial";
import {
    TutorialManager,
    TutorialType,
} from "./TutorialManager";

export class SpeedBoosterTutorial implements Tutorial {
    private readonly type: TutorialType;
    private isStarted = false;

    public constructor(
        private readonly tutorialManager: TutorialManager,
        private readonly boosterManager: gm.BoosterManager,
        private readonly gameScene: GameScene) {
        this.type = TutorialType.BoosterSpeed;
        this.gameScene.stopAnimationMovingArea();
    }

    public start(): void {
        if (this.isStarted) {
            return;
        }
        this.isStarted = true;
        this.gameScene.playAnimationMovingArea();
    }

    public finish(): void {
        this.gameScene.stopAnimationMovingArea();
        this.tutorialManager.completeTutorial(this.type);
    }

    public checkStart(): boolean {
        const waterLevel = this.gameScene.levelView.config.waterLevel;
        const shouldStart = !waterLevel
            && !this.isStarted
            && !this.tutorialManager.isTutorialComplete(this.type)
            && this.boosterManager.isBoosterEnabled(gm.BoosterType.Speed);
        return shouldStart;
    }

    public checkFinish(): boolean {
        return this.isStarted && this.gameScene.didMoving;
    }
}