import * as gm from '../../engine/gm_engine';
import { TutorialDialog } from "../../scenes/game_scene/dialogs/TutorialDialog";
import { GameScene } from "../../scenes/game_scene/GameScene";
import { Tutorial } from "./Tutorial";
import {
    TutorialManager,
    TutorialType,
} from "./TutorialManager";

export class NieceStuckTutorial implements Tutorial {
    private readonly type = TutorialType.NieceStuck;
    private started = false;
    private isDialogHidden = false;

    public constructor(
        private readonly tutorialManager: TutorialManager,
        private readonly scene: GameScene) {
    }

    public start(): void {
        if (this.started) {
            return;
        }
        this.started = true;
        this.isDialogHidden = false;
        const entityManager = this.scene.levelView.entityView.entityManager;
        const items = entityManager.findEntities(gm.Niece).filter(item => item.isTrapped());
        TutorialDialog.create().then(dialog => {
            this.scene.controller.pauseLevel();
            dialog.setTutorial(this.type);
            dialog.onDidHide(() => {
                for (let i = items.length - 1; i >= 0; --i) {
                    const item = items[i];
                    const blinkable = item.getComponent(gm.Blinkable);
                    blinkable.blink(true);
                }
                this.scene.controller.resumeLevel();
                this.isDialogHidden = true;
            });
            dialog.show(this.scene.dialogManager);
        });
    }

    public finish(): void {
        const entityManager = this.scene.levelView.entityView.entityManager;
        const items = entityManager.findEntities(gm.Niece).filter(item => !item.isGivingDiamond());
        items.forEach(item => {
            const blinkable = item.getComponent(gm.Blinkable);
            blinkable.stopBlink();
        });
        this.tutorialManager.completeTutorial(this.type);
    }

    public checkStart(): boolean {
        if (this.started) {
            return false;
        }
        if (this.tutorialManager.isTutorialComplete(this.type)) {
            return false;
        }
        const entityManager = this.scene.levelView.entityView.entityManager;
        const items = entityManager.findEntities(gm.Niece);
        for (let i = items.length - 1; i >= 0; --i) {
            const item = items[i];
            if (item.isTrapped()) {
                return true;
            }
        }
        return false;
    }

    public checkFinish(): boolean {
        if (!this.started) {
            return false;
        }
        if (!this.isDialogHidden) {
            return false;
        }
        const miner = this.scene.levelView.entityView.controllers[0].item;
        const capturedItem = miner.claw.getCapturedItem();
        return capturedItem instanceof gm.Niece;
    }
}