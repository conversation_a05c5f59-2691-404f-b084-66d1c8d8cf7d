import * as ee from '../../libraries/ee/index';
import { LevelUpDialog } from "../../scenes/game_scene/dialogs/LevelUpDialog";
import { SceneName, TrackResultItem, TrackSourceType, } from '../analytics/AnalyticsConfig';
import { TrackingManager } from '../analytics/TrackingManager';
import { CardManager, ItemCardRarity, } from '../card/CardManager';
import { ConfigManager } from '../config/ConfigManager';
import { DataManager } from '../data/DataManager';
import { LevelManager } from '../level/LevelManager';
import {
    EventProfileInfo,
    ExperienceInfo,
    ProfileInfo,
    ProfileManagerParser,
    StoryProfileInfo,
    TemporaryItem,
    UserProfileManager,
    UserStatsInfo,
} from './UserProfileManager';
import { AudioManager } from "../audio/AudioManager";
import { SoundType } from "../audio/SoundType";
import { SocialUser } from "../social/SocialManager";
import { DialogManager, ServiceLocator } from "../../libraries/ee/index";
import { PlayPassManager } from "../../scenes/play_pass/PlayPassManager";
import { NameGenerator } from "../../utils/UserProfileUtils";
export const LIST_PICTURES = [
    "0char_default",
    "1char_miner",
    "2char_Julie",
    "3char_Hansel",
    "4char_cowboy",
    "5char_Saleman",
    "6char_Juliered",
    "7char_thief",
    "8char_Jaki",
    "9char_Goku",
    "10char_cactus",
    "11char_Thailen",
    "12char_Ariana",
    "13char_Spider",
    "14char_ironminer"];
export const LIST_FRAMES = [
    "Frame_Blue",
    "Frame_Bronze",
    "Frame_Green",
    "Frame_Light_Blue",
    "Frame_Orange",
    "Frame_Pink",
    "Frame_Rare_Gold",
    "Frame_Rare_Rock",
    "Frame_Rare_Wood",
    "Frame_Red",
    "Frame_ruby",
    "Frame_Yellow",
    "Frame_Avatar_Top_Rank_Bronze",
    "Frame_Avatar_Top_Rank_Silver",
    "Frame_Avatar_Top_Rank_Gold",
    "Frame_play_pass"

];

export const DEFAULT_AVATAR_ID = (() => {
    const avatars = ["1char_miner", "2char_Julie", "3char_Hansel", "4char_cowboy", 
        "5char_Saleman", "6char_Juliered", "7char_thief", "8char_Jaki"];
    const randomIndex = Math.floor(Math.random() * avatars.length);
    return avatars[randomIndex];
})();

export const DEFAULT_FRAME_ID = (() => {
    const frames = ["Frame_Blue", "Frame_Bronze", "Frame_Green", "Frame_Light_Blue",
         "Frame_Orange", "Frame_Pink", "Frame_Red", "Frame_Yellow"];
    const randomIndex = Math.floor(Math.random() * frames.length);
    return frames[randomIndex];
})();

export const DEFAULT_NAME = NameGenerator.generate();

interface ExperienceConfig {
    constants: {
        storyXpBase: number;
        storyLoseMultiplier: number;
        pvpMultiplier: number;
        pvpGoldWinMultiplier: number;
        pvpGoldDrawMultiplier: number;
        pvpGoldLoseMultiplier: number;
        pvpBonus1: number;
        pvpBonus2: number;
    };
    level_profile_xp: number[];
    card_xp_reward: {
        common: number[];
        rare: number[];
        epic: number[];
        legend: number[];
    };
    reward: {
        gold: number[];
        energy: number[];
    };
}

/** Used by ExperienceInfoImpl. */
interface RawExperienceInfo {
    level?: number;
    exp?: number;
}

class ExperienceInfoImpl implements ExperienceInfo {
    public constructor(
        private readonly data: RawExperienceInfo,
        private readonly profileManager: DefaultUserProfileManager) {
    }

    public get level(): number {
        return this.data.level || 0;
    }

    public set level(value: number) {
        if (this.data.level === value) {
            return;
        }
        this.data.level = value;
        this.profileManager.markDataDirty();
    }

    public get exp(): number {
        return this.data.exp || 0;
    }

    public set exp(value: number) {
        if (this.data.exp === value) {
            return;
        }
        this.data.exp = value;
        this.profileManager.markDataDirty();
    }
}

/** Used by UserStatsInfoImpl. */
interface RawUserStatsInfo {
    chestAcquiredCount?: number;
    starCount?: number;
    medalCount?: number;
}

class UserStatsInfoImpl implements UserStatsInfo {
    public constructor(
        private readonly data: RawUserStatsInfo,
        private readonly profileManager: DefaultUserProfileManager) {
    }

    public get openedChest(): number {
        return this.data.chestAcquiredCount || 0;
    }

    public set openedChest(value: number) {
        if (this.data.chestAcquiredCount === value) {
            return;
        }
        this.data.chestAcquiredCount = value;
        this.profileManager.markDataDirty();
    }

    public get starCount(): number {
        return this.profileManager.getStarCount();
    }

    public get medalCount(): number {
        return this.profileManager.getMedalCount();
    }
}

/** Used by StoryProfileInfoImpl. */
interface RawStoryProfileInfo {
    firstTryWins?: number;
    levelFullClear?: number;
    playCount?: number;
}

class StoryProfileInfoImpl implements StoryProfileInfo {
    public constructor(
        private readonly data: RawStoryProfileInfo,
        private readonly profileManager: DefaultUserProfileManager) {
    }

    public get firstTryWins(): number {
        return this.data.firstTryWins || 0;
    }

    public set firstTryWins(value: number) {
        if (this.data.firstTryWins === value) {
            return;
        }
        this.data.firstTryWins = value;
        this.profileManager.markDataDirty();
    }

    public get fullClearedLevelCount(): number {
        return this.data.levelFullClear || 0;
    }

    public set fullClearedLevelCount(value: number) {
        if (this.data.levelFullClear === value) {
            return;
        }
        this.data.levelFullClear = value;
        this.profileManager.markDataDirty();
    }

    public get playCount(): number {
        return this.data.playCount || 0;
    }

    public set playCount(value: number) {
        if (this.data.playCount === value) {
            return;
        }
        this.data.playCount = value;
        this.profileManager.markDataDirty();
    }
}

class EventProfileInfoImpl implements EventProfileInfo {
    public constructor(
        private readonly data: RawStoryProfileInfo,
        private readonly profileManager: DefaultUserProfileManager) {
    }

    public get firstTryWins(): number {
        return this.data.firstTryWins || 0;
    }

    public set firstTryWins(value: number) {
        if (this.data.firstTryWins === value) {
            return;
        }
        this.data.firstTryWins = value;
        this.profileManager.markDataDirty();
    }

    public get fullClearedLevelCount(): number {
        return this.data.levelFullClear || 0;
    }

    public set fullClearedLevelCount(value: number) {
        if (this.data.levelFullClear === value) {
            return;
        }
        this.data.levelFullClear = value;
        this.profileManager.markDataDirty();
    }

    public get playCount(): number {
        return this.data.playCount || 0;
    }

    public set playCount(value: number) {
        if (this.data.playCount === value) {
            return;
        }
        this.data.playCount = value;
        this.profileManager.markDataDirty();
    }
}

export interface RawProfileInfo {
    name?: string;
    experience?: RawExperienceInfo;
    stats?: RawUserStatsInfo;
    storyInfo?: RawStoryProfileInfo;
    eventInfo?: RawStoryProfileInfo;
    timesPlayed?: number;
    levelPlayCount?: { [key: number]: number | undefined };
    eventAreaPlayCount?: { [area: number]: number[] | undefined };
    socialUser?: SocialUser
    temporaryItems?: TemporaryItem[];
}

class ProfileInfoImpl implements ProfileInfo {
    public constructor(
        private readonly data: RawProfileInfo,
        private readonly profileManager: DefaultUserProfileManager) {
    }

    public get temporaryItems(): TemporaryItem[] {
        return this.data.temporaryItems || [];
    }

    public set temporaryItems(value: TemporaryItem[]) {
        this.data.temporaryItems = value;
        this.profileManager.markDataDirty();
    }

    public get raw(): RawProfileInfo {
        return this.data;
    }

    public get name(): string {
        return this.data.name || '';
    }

    public get experience(): ExperienceInfo {
        this.data.experience = this.data.experience || {};
        return new ExperienceInfoImpl(this.data.experience, this.profileManager);
    }

    public get stats(): UserStatsInfo {
        this.data.stats = this.data.stats || {};
        return new UserStatsInfoImpl(this.data.stats, this.profileManager);
    }

    public get storyInfo(): StoryProfileInfo {
        this.data.storyInfo = this.data.storyInfo || {};
        return new StoryProfileInfoImpl(this.data.storyInfo, this.profileManager);
    }

    public get eventInfo(): EventProfileInfo {
        this.data.eventInfo = this.data.eventInfo || {};
        return new EventProfileInfoImpl(this.data.eventInfo, this.profileManager);
    }

    public get timesPlayed(): number {
        return this.data.timesPlayed || 0;
    }

    public set timesPlayed(value: number) {
        if (this.data.timesPlayed === value) {
            return;
        }
        this.data.timesPlayed = value;
        this.profileManager.markDataDirty();
    }

    public get socialUser(): SocialUser {
        return this.data.socialUser;
    }

    public set socialUser(value: SocialUser) {
        this.data.socialUser = value;
        this.profileManager.markDataDirty();
    }

    public getStoryLevelPlayCount(level: number): number {
        const playCounts = this.data.levelPlayCount = this.data.levelPlayCount || {};
        return playCounts[level] || 0;
    }

    public setStoryLevelPlayCount(level: number, value: number): void {
        const playCounts = this.data.levelPlayCount = this.data.levelPlayCount || {};
        if (playCounts[level] === value) {
            return;
        }
        playCounts[level] = value;
        this.profileManager.markDataDirty();
    }

    public getEventLevelPlayCount(area: number, level: number): number {
        const areaPlayCounts = this.data.eventAreaPlayCount = this.data.eventAreaPlayCount || {};
        const playCounts = areaPlayCounts[area] = areaPlayCounts[area] || [];
        return playCounts[level] || 0;
    }

    public setEventLevelPlayCount(area: number, level: number, value: number): void {
        const areaPlayCounts = this.data.eventAreaPlayCount = this.data.eventAreaPlayCount || {};
        const playCounts = areaPlayCounts[area] = areaPlayCounts[area] || [];
        if (playCounts[level] === value) {
            return;
        }
        playCounts[level] = value;
        this.profileManager.markDataDirty();
    }
}

export enum Key {
    ProfileInfo = "profile_info",
}

interface DataStorage {
    [Key.ProfileInfo]: RawProfileInfo | undefined;
}

export class DefaultUserProfileManager extends UserProfileManager {

    /** Config. */
    private config: ExperienceConfig;

    /** Data. */
    private data: ProfileInfo;

    /** Update. */
    private readonly node: cc.Node;
    private isDataDirty = false;
    private pendingReward: {
        gold: number;
        energy: number;
        level: number;
    };

    public constructor(
        configManager: ConfigManager,
        private readonly dataManager: DataManager,
        private readonly levelManager: LevelManager,
        private readonly playPassManager: PlayPassManager) {
        super();
        // Config.
        this.config = configManager.getValue<ExperienceConfig>("experience");

        // Data.
        let rawData = dataManager.getValue<RawProfileInfo>(Key.ProfileInfo, {});
        try {
            if ('pvpInfo' in rawData) {
                const { pvpInfo, ...rest } = rawData;
                let newData: RawProfileInfo = {
                    ...rest,
                    eventInfo: {
                        firstTryWins: 0,
                        levelFullClear: 0
                    }
                }
                rawData = newData;
                dataManager.setValue(Key.ProfileInfo, newData);
            }
        } catch (error) {
            console.error('Error parsing localStorage data:', error);
        }

        this.data = new ProfileInfoImpl(rawData, this);

        // Sync handler.
        dataManager.addHandler('user_profile', {
            load: (data: DataStorage) => {
                this.data = new ProfileInfoImpl(data[Key.ProfileInfo] || {}, this);
                this.saveData();
            },
            save: (data: DataStorage) => {
                data[Key.ProfileInfo] = (this.data as ProfileInfoImpl).raw;
            },
        });

        // Update.
        this.node = new cc.Node();
        cc.director.getScheduler().schedule(() => {
            if (this.isDataDirty) {
                this.isDataDirty = false;
                this.saveData();
            }
        }, this.node, 0);

        // Get Nation Code
        if (this.data.socialUser) {

            if (this.data.socialUser.nationCode == null
                || this.data.socialUser.nationCode == ""
                || this.data.socialUser.nationCode == "unknown"
                || this.data.socialUser.nationCode == undefined
                || this.data.socialUser.nationCode == "null") {
                this.getNationCode().then(code => {
                    this.socialUser = {
                        ...this.socialUser,
                        nationCode: code
                    };
                });
            }

            if (this.data.socialUser.name == null || this.data.socialUser.name == "You") {
                const reNamed: string = NameGenerator.generate();
                this.socialUser = {
                    ...this.socialUser,
                    name: reNamed
                };
            }
        } else {
            this.socialUser = this.socialUser;
        }
        this.pendingReward = null;

        // Remove expired temporary items
        this.updateTemporaryItems();
        // Remove play pass frame khi hết hạn gold
        this.updatePlayPassFrame();
    }

    public override refreshData() {
        const rawData = this.dataManager.getValue<RawProfileInfo>(Key.ProfileInfo, {});
        this.data = new ProfileInfoImpl(rawData, this);
        this.getProfileInfo().timesPlayed++;
        this.updateTemporaryItems();
        this.updatePlayPassFrame();

    }

    public destroy(): void {
        this.dataManager.removeHandler('user_profile');
        this.node.destroy();
    }

    private saveData(): void {
        this.dataManager.setValue(Key.ProfileInfo, (this.data as ProfileInfoImpl).raw);
    }

    public markDataDirty(): void {
        this.isDataDirty = true;
    }

    public createParser(_data: DataStorage): ProfileManagerParser {
        const data = new ProfileInfoImpl(_data[Key.ProfileInfo] || {}, this);
        const parser = {
            level: data.experience.level
        };
        return parser;
    }

    public getProfileInfo(): ProfileInfo {
        return this.data;
    }

    public getStoryProfileInfo(): StoryProfileInfo {
        return this.data.storyInfo;
    }

    public getEventProfileInfo(): EventProfileInfo {
        return this.data.eventInfo;
    }

    public getCurrentLevel(): number {
        return this.data.experience.level;
    }

    public getCurrentExp(): number {
        return this.data.experience.exp;
    }

    public playStoryLevel(worldLevel: number): void {
        const count = this.data.getStoryLevelPlayCount(worldLevel);
        this.data.setStoryLevelPlayCount(worldLevel, count + 1);
        ++this.data.storyInfo.playCount;
    }

    public playEventLevel(area: number, level: number): void {
        const count = this.data.getEventLevelPlayCount(area, level);
        this.data.setEventLevelPlayCount(area, level, count + 1);
        ++this.data.eventInfo.playCount;
    }

    public completeStoryLevel(worldLevel: number, isFullCleared: boolean): void {
        const count = this.data.getStoryLevelPlayCount(worldLevel);
        if (count === 1) {
            ++this.data.storyInfo.firstTryWins;
        }
        if (isFullCleared) {
            ++this.data.storyInfo.fullClearedLevelCount;
        }
    }

    public completeEventLevel(area: number, level: number, isFullCleared: boolean): void {
        const count = this.data.getEventLevelPlayCount(area, level);
        if (count === 1) {
            ++this.data.eventInfo.firstTryWins;
        }
        if (isFullCleared) {
            ++this.data.eventInfo.fullClearedLevelCount;
        }
    }

    public getStoryWinExp(worldLevel: number): number {
        const storyXpBase = this.config.constants.storyXpBase;
        const value = worldLevel + 1 + storyXpBase;
        return value;
    }

    public getStoryFailExp(worldLevel: number): number {
        const storyXpBase = this.config.constants.storyXpBase;
        const storyLoseMultiplier = this.config.constants.storyLoseMultiplier;
        const value = (worldLevel + 1 + storyXpBase) * storyLoseMultiplier;
        return value;
    }

    public getStoryFailGold(area: number): number {
        const value = (50 + (20 * (area + 1)));
        return value;
    }

    public getPvpWinExp(progress: number, isTimeOut: boolean): number {
        const pvpBonus1 = this.config.constants.pvpBonus1;
        const pvpBonus2 = this.config.constants.pvpBonus2;
        const pvpMultiplier = this.config.constants.pvpMultiplier;
        const value = progress * pvpMultiplier + (isTimeOut ? pvpBonus1 : pvpBonus2);
        return value;
    }

    public getPvpFailExp(progress: number): number {
        const pvpMultiplier = this.config.constants.pvpMultiplier;
        const value = progress * pvpMultiplier;
        return value;
    }

    public getPvpWinGold(progress: number): number {
        const value = progress * this.config.constants.pvpGoldWinMultiplier;
        return value;
    }

    public getPvpFailGold(progress: number): number {
        const value = progress * this.config.constants.pvpGoldLoseMultiplier;
        return value;
    }

    public getPvpDrawGold(progress: number): number {
        const value = progress * this.config.constants.pvpGoldDrawMultiplier;
        return value;
    }

    public addExp(value: number, scene: SceneName): void {
        if (scene != null) {
            ee.ServiceLocator.resolve(TrackingManager).trackEventSourceItem(
                scene, TrackResultItem.Achieve, TrackSourceType.Xp, '', value);
        }
        const experience = this.data.experience;
        experience.exp += value;
        this.dispatch(async observer => observer.expChanged && observer.expChanged(this, experience.exp));
    }

    public addPvpExp(
        value: number, knockout: boolean, scene: SceneName): void {
        const pvpMultiplier = this.config.constants.pvpMultiplier;
        value = value * pvpMultiplier
            + (knockout
                ? this.config.constants.pvpBonus2
                : this.config.constants.pvpBonus1);
        this.addExp(value, scene);
    }

    public canProcessExp(): boolean {
        const currentExp = this.data.experience.exp;
        const currentLevel = this.getCurrentLevel();
        if (currentExp >= this.getExpForLevel(currentLevel)) {
            return true;
        }
        return false;
    }

    public async processExp(dialogManager: ee.DialogManager): Promise<void> {
        const experience = this.data.experience;
        let currentExp = experience.exp;
        let currentLevel = this.getCurrentLevel();
        let levelUp = false;
        let goldValue = 0;
        let energyValue = 0;
        while (true) {
            const nextExp = this.getExpForLevel(currentLevel);
            if (currentExp < nextExp) {
                break;
            }
            levelUp = true;
            currentExp -= nextExp;
            goldValue += this.getGoldForLevel(currentLevel);
            energyValue += this.getEnergyForLevel(currentLevel);
            currentLevel++;
        }
        if (levelUp) {
            ee.ServiceLocator.resolve(TrackingManager).trackConversionProfileLevelUp(currentLevel + 1);
            experience.level = currentLevel;
            experience.exp = currentExp;
            // await this.dispatch(async observer => observer.expChanged && observer.expChanged(this, experience.exp));
            const cardManager = ee.ServiceLocator.resolve(CardManager);
            let dialog = await LevelUpDialog.create();
            dialog.setGold(goldValue)
                .setAdsGold(goldValue)
                .setEnergy(energyValue)
                .setLevel(currentLevel + 1)
                .setDeck(cardManager.getCurrentDeck())
                .animateUpdateInfo()
            dialog.onDidShow(() => {
                ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.Finish);
            })
            dialog.show(dialogManager);

            return await new Promise<void>(resolve => {
                dialog.onDidHide(async () => {
                    await dialog.animateCurrency();
                    resolve();
                });
            });
        }
    }

    public getExpForLevel(level: number): number {
        return this.config.level_profile_xp[level];
    }

    public openChest(count: number): void {
        this.data.stats.openedChest += count;
    }

    public getStarCount(): number {
        const maxArea = this.levelManager.getStoryAreas();
        let starCount = 0;
        for (let i = 0; i < maxArea; i++) {
            const area = this.levelManager.getStoryAreaInfo(i);
            const levelArr = area.levels;
            for (const lv of levelArr) {
                starCount = starCount + lv.highestStar;
            }
        }
        for (let m = 0; m < this.levelManager.getEventAreas(); m++) {
            const eventArea = this.levelManager.getEventAreaInfo(m);
            const eventLevelArr = eventArea.levels;
            for (const lv of eventLevelArr) {
                starCount = starCount + lv.highestStar;
            }
        }
        return starCount;
    }

    public getMedalCount(): number {
        const lastUnlockedArea = this.levelManager.getLastUnlockedArea();
        let count = 0;
        for (let i = 0; i <= lastUnlockedArea; i++) {
            const areaInfo = this.levelManager.getStoryAreaInfo(i);
            const levelArr = areaInfo.levels;
            for (const level of levelArr) {
                if (level.highestStar > 0) {
                    count += level.score;
                }
            }
        }
        for (let m = 0; m < this.levelManager.getEventAreas(); m++) {
            const eventArea = this.levelManager.getEventAreaInfo(m);
            for (let n = 0; n < eventArea.unlockedLevels; n++) {
                const eventLevel = eventArea.levels[n];
                count += eventLevel.score;
            }
        }
        return count;
    }

    public getXpFromCardUpgrade(rarity: ItemCardRarity, level: number): number {
        switch (rarity) {
            case ItemCardRarity.Common: {
                return this.config.card_xp_reward.common[level];
            }
            case ItemCardRarity.Rare: {
                return this.config.card_xp_reward.rare[level];
            }
            case ItemCardRarity.Epic: {
                return this.config.card_xp_reward.epic[level];
            }
            case ItemCardRarity.Legendary: {
                return this.config.card_xp_reward.legend[level];
            }
        }
        return 0;
    }

    public getGoldForLevel(level: number): number {
        return this.config.reward.gold[level];
    }

    public getEnergyForLevel(level: number): number {
        return this.config.reward.energy[level];
    }

    public set socialUser(value: SocialUser) {
        this.data.socialUser = value;
        this.dispatch(async observer =>
            observer.socialUserChange && observer.socialUserChange(this, this.data.socialUser
            )).then();
    }

    public get socialUser(): SocialUser {
        if (this.data.socialUser == null) {
            this.data.socialUser = {
                id: "",
                name: DEFAULT_NAME,
                picture: DEFAULT_AVATAR_ID,
                frame: DEFAULT_FRAME_ID,
                changedNameTimes: 0,
                nationCode: "unknown",
            };
            this.getNationCode().then(code => {
                this.data.socialUser.nationCode = code;
            });
        } else {
            const pictures = LIST_PICTURES
            const frames = LIST_FRAMES
            const user = this.data.socialUser;
            if (user.name === "") this.data.socialUser.name = "You";
            if (!pictures.includes(user.picture)) this.data.socialUser.picture = DEFAULT_AVATAR_ID;
            if (!frames.includes(user.frame)) this.data.socialUser.frame = DEFAULT_FRAME_ID;
            if (user.nationCode === "unknown" || user.nationCode === undefined) {
                this.getNationCode().then(code => {
                    this.data.socialUser.nationCode = code;
                });
            }
        }
        return this.data.socialUser;
    }

    private async getNationCode(): Promise<string> {
        try {
            const response = await fetch("https://geolocation-db.com/json/");
            const data = await response.json();
            return data.country_code;
        } catch (error) {
            console.error("Error fetching geolocation:", error);
            return "unknown";
        }
    }

    public async receivePendingRewards(): Promise<void> {

        if (this.canProcessExp()) {
            await this.processExp(ee.ServiceLocator.resolve(DialogManager));
            await this.dispatch(async observer => observer.expChanged && observer.expChanged(this, this.data.experience.exp));
        }

        // if (!this.pendingReward) return;
        // const cardManager = ee.ServiceLocator.resolve(CardManager);
        // const dialog = await LevelUpDialog.create();
        // const { gold, energy, level } = this.pendingReward;
        // dialog.setGold(gold)
        //     .setAdsGold(gold)
        //     .setEnergy(energy)
        //     .setLevel(level + 1)
        //     .setDeck(cardManager.getCurrentDeck())
        //     .animateUpdateInfo()
        // dialog.onDidShow(() => {
        //     this.pendingReward = null;
        //     ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.Finish);
        // })
        // dialog.show(ee.ServiceLocator.resolve(DialogManager));
        //
        // return await new Promise<void>(resolve => {
        //     dialog.onDidHide(async () => {
        //         await dialog.animateCurrency();
        //         resolve();
        //     });
        // });
    }
    public addTemporaryItem(item: TemporaryItem): void {
        if (!this.data.temporaryItems) {
            this.data.temporaryItems = [];
        }
        const index = this.data.temporaryItems.findIndex(i => item.itemType === i.itemType && item.itemName === i.itemName);
        if (index !== -1) {
            this.data.temporaryItems[index] = item;
            this.data.temporaryItems = [...this.data.temporaryItems]
        } else {
            this.data.temporaryItems = [...this.data.temporaryItems, item]
        }
        this.markDataDirty()
    }
    public getTemporaryItems(): TemporaryItem[] {
        return this.data.temporaryItems || [];
    }
    private updateTemporaryItems(): void {
        this.data.temporaryItems = this.data.temporaryItems.filter(item => {
            if (item.expiredTime === "unlimited") return true;
            const expiredTime = new Date(item.expiredTime); // Ensure proper Date conversion
            const now = new Date();

            if (expiredTime < now) {
                if (item.itemType === "frame" && this.socialUser.frame === item.itemName) {
                    this.socialUser.frame = DEFAULT_FRAME_ID;
                }
                return false;
            }
            return true;
        });
    }
    private updatePlayPassFrame() {
        if (this.playPassManager.isBuyFramePlayPass) {
            return;
        }
        if (this.socialUser.frame === "Frame_play_pass") {
            this.socialUser.frame = DEFAULT_FRAME_ID;
        }
    }
}
