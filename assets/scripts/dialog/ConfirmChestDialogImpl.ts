import * as gm from '../engine/gm_engine';
import * as ee from '../libraries/ee/index';
import {
    ChestManager,
    ChestUtils,
    CrashlyticManager,
    crashlytics,
    StoreItem,
} from "../manager/gm_manager";
import { ChestView } from '../scenes/common/ChestView';
import {
    ConfirmChestDialog,
    ConfirmChestDialogController,
} from './ConfirmChestDialog';
import {PrefabUtils} from '../utils/PrefabUtils'

const { ccclass, disallowMultiple, property } = cc._decorator;

/** Used in editor. */
@ccclass
@disallowMultiple
class ConfirmChestDialogImpl extends ConfirmChestDialog {
    @property({ type: cc.Sprite, visible: true })
    private readonly _light: cc.Sprite | null = null;

    @property([cc.SpriteFrame])
    private readonly lightSpriteFrames: cc.SpriteFrame[] = [];

    @ee.nest(ChestView)
    private readonly _chestView: ChestView | null = null;

    @property({ type: ee.LanguageComponent, visible: true })
    private readonly _chestLabel: ee.LanguageComponent | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _rubyAndBoosterLayer: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _rubyLayer: cc.Node | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _rubyLabel: cc.Label | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _boosterLayer: cc.Node | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _boosterLabel: cc.Label | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _goldLayer: cc.Node | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _goldLabel: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _totalCardLabel: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _rareCardLabel: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _epicCardLabel: cc.Label | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _legendCardLayer: cc.Node | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _legendCardLabel: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _priceLabel: cc.Label | null = null;

    @property({ type: cc.Sprite, visible: true })
    private readonly _currencyIcon: cc.Sprite | null = null;

    @property([cc.SpriteFrame])
    private readonly currencyIconSpriteFrames: cc.SpriteFrame[] = [];

    @property({ type: cc.Node, visible: true })
    private readonly _buttonLayer: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _videoButton: cc.Node | null = null;
    
    private get light(): cc.Sprite {
        return gm.retrieveNull(this._light);
    }

    private get chestView(): ChestView {
        return gm.retrieveNull(this._chestView);
    }

    private get chestLabel(): ee.LanguageComponent {
        return gm.retrieveNull(this._chestLabel);
    }

    private get rubyAndBoosterLayer(): cc.Node {
        return gm.retrieveNull(this._rubyAndBoosterLayer);
    }

    private get rubyLayer(): cc.Node {
        return gm.retrieveNull(this._rubyLayer);
    }

    private get rubyLabel(): cc.Label {
        return gm.retrieveNull(this._rubyLabel);
    }

    private get boosterLayer(): cc.Node {
        return gm.retrieveNull(this._boosterLayer);
    }

    private get boosterLabel(): cc.Label {
        return gm.retrieveNull(this._boosterLabel);
    }

    private get goldLayer(): cc.Node {
        return gm.retrieveNull(this._goldLayer);
    }

    private get goldLabel(): cc.Label {
        return gm.retrieveNull(this._goldLabel);
    }

    private get totalCardLabel(): cc.Label {
        return gm.retrieveNull(this._totalCardLabel);
    }

    private get rareCardLabel(): cc.Label {
        return gm.retrieveNull(this._rareCardLabel);
    }

    private get epicCardLabel(): cc.Label {
        return gm.retrieveNull(this._epicCardLabel);
    }

    private get legendCardLayer(): cc.Node {
        return gm.retrieveNull(this._legendCardLayer);
    }

    private get legendCardLabel(): cc.Label {
        return gm.retrieveNull(this._legendCardLabel);
    }

    private get priceLabel(): cc.Label {
        return gm.retrieveNull(this._priceLabel);
    }

    private get currencyIcon(): cc.Sprite {
        return gm.retrieveNull(this._currencyIcon);
    }

    private get buttonLayer(): cc.Node {
        return gm.retrieveNull(this._buttonLayer);
    }

    private get videoButton(): cc.Node {
        return gm.retrieveNull(this._videoButton);
    }

    public controller: ConfirmChestDialogController = {};

    private _chestType = gm.ChestType.Free;
    private _price = 0;
    private _currencyType = StoreItem.Gold;
    private _isButtonEnabled = false;
    private _isVideoButtonEnabled = false;

    private isInitialized = false;

    @crashlytics
    protected onLoad(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        super.onLoad();
        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });

        this.isInitialized = true;
        this.updateChestType();
        this.updatePrice();
        this.updateCurrencyType();
        this.updateButton();
        this.updateVideoButton();
        
    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }


    public get chestType(): gm.ChestType {
        return this._chestType;
    }

    public set chestType(value: gm.ChestType) {
        if (this._chestType === value) {
            return;
        }

        this._chestType = value;
        this.isInitialized && this.updateChestType();
    }

    public get price(): number {
        return this._price;
    }

    public set price(value: number) {
        if (this._price === value) {
            return;
        }
        this._price = value;
        this.isInitialized && this.updatePrice();
    }

    public get currencyType(): StoreItem {
        return this._currencyType;
    }

    public set currencyType(value: StoreItem) {
        if (this._currencyType === value) {
            return;
        }
        this._currencyType = value;
        this.isInitialized && this.updateCurrencyType();
    }

    public get isButtonEnabled(): boolean {
        return this._isButtonEnabled;
    }

    public set isButtonEnabled(value: boolean) {
        if (this._isButtonEnabled === value) {
            return;
        }
        this._isButtonEnabled = value;
        this.updateButton();
    }

    public get isVideoButtonEnabled(): boolean {
        return this._isVideoButtonEnabled;
    }

    public set isVideoButtonEnabled(value: boolean) {
        if (this._isVideoButtonEnabled === value) {
            return;
        }
        this._isVideoButtonEnabled = value;
        this.updateVideoButton();
    }

    private updateChestType(): void {
        this.chestView.type = this.chestType;

        const chestManager = ee.ServiceLocator.resolve(ChestManager);
        const config = chestManager.getChestConfig(this.chestType);
        const dict: { [key: number]: number } = {
            [gm.ChestType.Free  /*   */]: 0,
            [gm.ChestType.Copper  /* */]: 1,
            [gm.ChestType.Silver  /* */]: 2,
            [gm.ChestType.Gold    /* */]: 3,
            [gm.ChestType.Diamond /* */]: 4,
            [gm.ChestType.Star    /* */]: 5,
        };
        const index = dict[this.chestType];
        this.light.spriteFrame = this.lightSpriteFrames[index];

        this.chestLabel.key = ChestUtils.getChestNameTextKey(this.chestType);

        // First row.
        const rubyConfig = config.rewards.ruby;
        const boosterConfig = config.rewards.booster;
        if ((rubyConfig === undefined || rubyConfig.max === 0) &&
            (boosterConfig === undefined || boosterConfig.max === 0)) {
            this.rubyAndBoosterLayer.active = false;
        } else {
            this.rubyAndBoosterLayer.active = true;
            if (rubyConfig === undefined || rubyConfig.max === 0) {
                this.rubyLayer.active = false;
            } else {
                this.rubyLayer.active = true;
                this.rubyLabel.string = `${rubyConfig.min} - ${rubyConfig.max}`;
            }
            if (boosterConfig === undefined || boosterConfig.max === 0) {
                this.boosterLayer.active = false;
            } else {
                this.boosterLayer.active = true;
                this.boosterLabel.string = `${boosterConfig.min} - ${boosterConfig.max}`;
            }
            this.rubyAndBoosterLayer.getComponent(cc.Layout).updateLayout();
        }

        // Second row.
        // Nếu mở rương bằng Gold thì ko cho nhận thưởng Gold nữa
        this.goldLayer.active = this.currencyType !== StoreItem.Gold
        this.goldLabel.string = `${config.rewards.gold.min} - ${config.rewards.gold.max}`;

        // Third row.
        const cardConfig = config.rewards.card;
        this.totalCardLabel.string = `${cardConfig.total}`;

        const applyLabel = (label: cc.Label, layer: cc.Node | null, guarantee: number, bonus: number) => {
            if (guarantee === 0 && bonus === 0) {
                layer && (layer.active = false);
            } else {
                layer && (layer.active = true);
                label.string = bonus === 0 || guarantee >= 2
                    ? `${guarantee}`
                    : `${guarantee}-${guarantee + 1}`;
            }
        };
        applyLabel(this.rareCardLabel, null, cardConfig.rare_guarantee, cardConfig.rare_bonus);
        applyLabel(this.epicCardLabel, null, cardConfig.epic_guarantee, cardConfig.epic_bonus);
        applyLabel(this.legendCardLabel, this.legendCardLayer, cardConfig.legend_guarantee, cardConfig.legend_bonus);
    }

    private updatePrice(): void {
        this.priceLabel.string = this.price.toString();
    }

    private updateCurrencyType(): void {
        const dict: { [key: number]: number } = {
            [StoreItem.Ruby  /* */]: 0,
            [StoreItem.Gold  /* */]: 1,
        };
        const index = dict[this.currencyType];
        this.currencyIcon.spriteFrame = this.currencyIconSpriteFrames[index];
    }

    private updateButton(): void {
        this.buttonLayer.active = this.isButtonEnabled;
    }

    private updateVideoButton(): void {
        this.videoButton.active = this.isVideoButtonEnabled;
    }

    /** Registered in editor */
    @crashlytics
    private onPressedVideo(): void {
        if (!this.isActive()) {
            return;
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onPressedVideo, `${this.uuid}`);
        this.onDidHide(() => {
            this.controller.pressedVideo && this.controller.pressedVideo();
        });
        this.hide();
    }

    /** Registered in editor */
    @crashlytics
    private onPressedOpenByCurrency(): void {
        if (!this.isActive()) {
            return;
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onPressedOpenByCurrency, `${this.uuid}`);
        this.onDidHide(() => {
            this.controller.pressedOpenByCurrency && this.controller.pressedOpenByCurrency();
        });
        this.hide();
    }
}
