import requireComponent = cc._decorator.requireComponent;
import * as ee from "../libraries/ee/index";
import { UserProfileManager } from "../manager/profile/UserProfileManager";
import { VipManager } from "../manager/vip/VipManager";

const { ccclass, property } = cc._decorator;

export enum ConditionType {
    NON_LOCK = 0,
    LOCK_BY_VIP,
    LOCK_BY_ACCOUNT,
}

@ccclass
@requireComponent(cc.Toggle)
export default class AvatarFrameSelector extends cc.Component {
    @property(cc.Sprite)
    private avatarSprite: cc.Sprite = null;

    @property(cc.Sprite)
    private frameSprite: cc.Sprite = null;

    @property(cc.Node)
    private iconLock: cc.Node = null;

    @property(cc.Node)
    private popupDescription: cc.Node = null;

    @property(cc.Node)
    private tickCheckmark: cc.Node = null;

    @property(cc.Node)
    private tickCheckmarkSimulator: cc.Node = null;

    @property(cc.Label)
    private timeRemaining: cc.Label = null;

    @property(cc.Node)
    private timeRemainingContainer: cc.Node = null;
    private _index: number = 0;
    private _conditionUnlocked: () => boolean = null;
    private _onSelect: (index: number) => void = null;
    private _isReviewFrame: boolean = false;
    private _conditionMap: {
        [key in ConditionType]?: {
            localize: string;
            conditionCallback: () => boolean;
        }
    } = {};

    protected onLoad() {
        this.node.on(cc.Node.EventType.TOUCH_END, this.handleSelect, this);
        this.popupDescription.active = false;
    }

    public create() {
        const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
        const vipManager = ee.ServiceLocator.resolve(VipManager);

        this._conditionMap[ConditionType.NON_LOCK] = {
            localize: "",
            conditionCallback: () => false
        }

        this._conditionMap[ConditionType.LOCK_BY_VIP] = {
            localize: "unlock_vip",
            conditionCallback: () => vipManager.getCurrentVipLevel() + 1 < 4
        }

        this._conditionMap[ConditionType.LOCK_BY_ACCOUNT] = {
            localize: "unlock_account",
            conditionCallback: () => profileManager.getCurrentLevel() + 1 < 4
        }
        return this;
    }

    private handleSelect(): void {
        this._onSelect(this._index);
    }


    private onMouseLeave(): void {
        if (this._conditionUnlocked()) {
            this.popupDescription.active = false;
        }
    }

    public setIndex(index: number) {
        this._index = index;
        return this;
    }

    public setIsReviewFrame(isReviewFrame: boolean) {
        this._isReviewFrame = isReviewFrame;
        return this;
    }

    public setAvatar(image: cc.SpriteFrame) {
        this.avatarSprite.spriteFrame = image;
        return this;
    }
    public get Avatar(): cc.SpriteFrame {
        return this.avatarSprite.spriteFrame;
    }

    public setFrame(image: cc.SpriteFrame) {
        this.frameSprite.spriteFrame = image;
        return this;
    }
    public get Frame(): cc.SpriteFrame {
        return this.frameSprite.spriteFrame;
    }

    public get isLocked(): boolean {
        return this._conditionUnlocked();
    }

    public showMarkSimulator(): void {
        this.tickCheckmarkSimulator.active = true;
    }

    public hideMarkSimulator(): void {
        this.tickCheckmarkSimulator.active = false;
    }

    public setCondition(condition: ConditionType) {
        this._conditionUnlocked = this._conditionMap[condition].conditionCallback;
        let description = this.popupDescription.getComponentInChildren(ee.LanguageComponent)
        description.key = this._conditionMap[condition].localize;
        description.paramValues = [`${4}`];
        return this;
    }

    public setOnSelect(callback: (index: number) => void) {
        this._onSelect = callback;
        return this;
    }

    public build() {
        cc.log("build");
        const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
        let isLocked = this._conditionUnlocked && this._conditionUnlocked();
        this.iconLock.active = isLocked;
        let toggle = this.getComponent(cc.Toggle);
        if (isLocked) {
            toggle.checkMark = this.popupDescription.getComponent(cc.Sprite);
        } else {
            toggle.checkMark = this.tickCheckmark.getComponent(cc.Sprite);
        }
        if (this._isReviewFrame) {
            toggle.isChecked = profileManager.socialUser.frame === this.frameSprite.spriteFrame.name;
            this.avatarSprite.spriteFrame = null;
        } else {
            toggle.isChecked = profileManager.socialUser.picture === this.avatarSprite.spriteFrame.name;
            this.frameSprite.spriteFrame = null;
        }
        return this;
    }

    public setTimeRemaining(timeRemaining: string) {
        if (timeRemaining === "" || timeRemaining === null) {
            this.timeRemainingContainer.active = false;
            return this;
        }
        this.timeRemaining.string = timeRemaining;
        this.timeRemainingContainer.active = true;
        return this;
    }


}
