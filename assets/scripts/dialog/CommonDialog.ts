import * as ee from '../libraries/ee/index';
import { DialogUtils } from './DialogUtils';
import {TransformHelper} from "../engine/utils/TransformHelper";
import DialogHelper from "./DialogHelper";
import {Dialog} from "../libraries/ee/index";

const { ccclass, disallowMultiple } = cc._decorator;

@ccclass
@disallowMultiple
export class CommonDialog extends ee.Dialog {
    private touchOutsideEnabled: boolean = true;
    private _outsideTouched: boolean = false;

    protected constructor() {
        super();
        DialogUtils.addFadeInBackground(this);
        DialogUtils.addPopOutTransition(this);
        DialogUtils.addPopInTransition(this);
        DialogUtils.addFadeOutBackground(this);
    }

    protected onLoad(): void {
        super.onLoad();
    }

    public show(manager: ee.DialogManager, scale: number = 1): void {
        const container = new cc.Node(this.name);
        container.setContentSize(cc.winSize);
        container.addChild(this.node);
        container.addComponent(cc.BlockInputEvents);
        container.on(cc.Node.EventType.TOUCH_END, this.touchOutOfDialog, this);
        container.setScale(scale)
        this.manager = manager;

        // manager.pushDialog(this);
        const helper = DialogHelper.getInstance();
        helper.pushDialog(this, manager)
    }

    public getRubyBarPosition(posNode: cc.Node): cc.Vec3 {
        const parent = posNode.parent.parent.parent.parent;
        // posNode(background)->level_collection->collectionPrefab->hub->storyView
        const transformHelper = new TransformHelper(posNode, parent);
        const worldPosition =  cc.v3(transformHelper.convertTo(cc.Vec2.ZERO));
        if (!posNode.parent.parent.active) {
            worldPosition.x -= 150;
        }
        return worldPosition;
    }

    private touchOutOfDialog(event: cc.Event.EventTouch): void {
        if (!this.touchOutsideEnabled) {
            return;
        }

        const box = this.node.getBoundingBox();
        const touchPos = event.touch.getLocation();
        const pos = this.node.parent.convertToNodeSpaceAR(touchPos);

        if (!box.contains(pos) && this.isActive()) {
            this._outsideTouched = true;
            this.hide();
        }
    }

    public get outsideTouched(){
        return this._outsideTouched;
    }

    protected setTouchOutsideEnabled(enabled: boolean): void {
        this.touchOutsideEnabled = enabled;
    }
}