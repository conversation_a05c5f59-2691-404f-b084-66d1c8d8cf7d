import {CommonDialog} from "./CommonDialog";
import * as ee from '../libraries/ee/index';
import {PrefabUtils} from "../utils/PrefabUtils";
import {
    AudioManager,
    CrashlyticManager,
    crashlytics, SceneName, SoundType, StoreItem, StoreManager, TrackingManager, UserProfileManager,
} from '../manager/gm_manager';
import {GameServerManager} from "../manager/game_server/GameServerManager";
import BadgeView from "../team/BadgeView";
import {ITeamLayerListener, TeamInfo} from "../scenes/team/TeamLayer";
import {MyTeamManager} from "../manager/team/MyTeamManager";

enum TeamType {
    CLOSE,
    OPEN,
}

const {ccclass, property} = cc._decorator;

@ccclass
export class UpdateTeamProfileDialog extends CommonDialog {
    public static create(): Promise<UpdateTeamProfileDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/team/edit_team_dialog');
    }

    @property(cc.Label)
    private teamNameText: cc.Label = null;

    @property(cc.EditBox)
    private teamDescriptionEditBox: cc.EditBox = null;

    @property(cc.PageView)
    private teamTypePageView: cc.PageView = null;

    @property(cc.Label)
    private requiredAccountLevelLabel: cc.Label = null;

    buttons
    @property(cc.Button)
    private previousTeamTypeButton: cc.Button = null;

    @property(cc.Button)
    private nextTeamTypeButton: cc.Button = null;

    @property(cc.Button)
    private btnRequiredLevelUp: cc.Button = null;

    @property(cc.Button)
    private btnRequiredLevelDown: cc.Button = null;

    @property(cc.Animation)
    private toast: cc.Animation = null;

    @property(BadgeView)
    private teamBadge: BadgeView = null;

    private _teamType: TeamType;
    private _requiredAccountLevel: number = 3;
    private _currentAccountLevel: number;
    private updateCallback: (updatedTeamInfo:  TeamInfo) => void;

    public get requiredAccountLevel(): number {
        return this._requiredAccountLevel;
    }

    public set requiredAccountLevel(value: number) {
        this._requiredAccountLevel = value;
        this.updateRequiredAccountLevel();
    }

    public setUpdateCallback(callback: (updatedTeamInfo: TeamInfo) => void): this {
        this.updateCallback = callback;
        return this;
    }

    @crashlytics
    protected onLoad(): void {
        super.onLoad();
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        const profileMgr = ee.ServiceLocator.resolve(UserProfileManager);
        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });
        this._currentAccountLevel = profileMgr.getCurrentLevel() + 1;
    }

    protected start() {
        // get team profile from my Team manager
        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        const teamProfile = myTeamManager.getTeamInfo();

        this.teamNameText.string = teamProfile.teamName;
        this.teamDescriptionEditBox.string = teamProfile.description;
        this._teamType = teamProfile.teamType === "OPEN" ? TeamType.OPEN : TeamType.CLOSE;
        this.teamTypePageView.scrollToPage(this._teamType, 0.3);
        // updateTeamTypePageView
        this.updateTeamTypePageView();

        this.teamBadge.badgeID = teamProfile.avatar;
        this.requiredAccountLevel = teamProfile.minimumLevelRequired;
    }

    @crashlytics
    private onPreviousTeamTypeButtonClicked() {
        // add sound
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        const currentPageIndex = this.teamTypePageView.getCurrentPageIndex();
        this.teamTypePageView.scrollToPage(currentPageIndex - 1, 0.3);
        this.updateTeamTypePageView();
    }

    @crashlytics
    private onNextTeamTypeButtonClicked() {
        // same with previous
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        const currentPageIndex = this.teamTypePageView.getCurrentPageIndex();
        this.teamTypePageView.scrollToPage(currentPageIndex + 1, 0.3);
        this.updateTeamTypePageView();
    }

    private updateTeamTypePageView() {
        const currentPageIndex = this.teamTypePageView.getCurrentPageIndex();
        this.previousTeamTypeButton.node.active = currentPageIndex === 1
        this.nextTeamTypeButton.node.active = currentPageIndex === 0;
    }

    @crashlytics
    private onRequiredAccountLevelUpPressed() {
        this.requiredAccountLevel++;
    }

    @crashlytics
    private onRequiredAccountLevelDownPressed() {
        this.requiredAccountLevel--;
    }

    private updateRequiredAccountLevel() {
        this.requiredAccountLevelLabel.string = this._requiredAccountLevel.toString();
        this.btnRequiredLevelDown.node.active = this._requiredAccountLevel > 3;
    }

    @crashlytics
    private onEditTeamButtonClicked() {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        const trackingMgr = ee.ServiceLocator.resolve(TrackingManager);
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);

        trackingMgr.trackEventClick(SceneName.SceneTeam, 'btn_edit_team');

        const desc = this.teamDescriptionEditBox.string.trim();
        const rubyBalance = ee.ServiceLocator.resolve(StoreManager).getItemBalance(StoreItem.Ruby);

        let isInvalid: string = null;

        if (this._requiredAccountLevel > this._currentAccountLevel) {
            isInvalid = `error_team_account_level`;
        } else {
            const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
            const teamInfo = myTeamManager.getTeamInfo();
            let newTeamInfo = {...teamInfo};

            gameServerManager.updateTeamInfo({
                teamId: teamInfo.teamId,
                avatar: teamInfo.avatar,
                teamName: teamInfo.teamName,
                description: desc,
                teamType: this.teamTypePageView.getCurrentPageIndex() === 1 ? "OPEN" : "CLOSE",
                minimumLevelRequired: this._requiredAccountLevel
            })
                .then(res => {
                    if (res) {
                        newTeamInfo.description = res.description;
                        newTeamInfo.minimumLevelRequired = res.minimumLevelRequired;
                        newTeamInfo.teamType = res.teamType;
                        myTeamManager.setTeamInfo(newTeamInfo);
                        
                        // Call the callback with the updated team info
                        if (this.updateCallback) {
                            this.updateCallback(newTeamInfo);
                        }
                        
                        this.hide();
                    }
                })
            return;
        }

        let text = this.toast.getComponentInChildren(ee.LanguageComponent);
        text.key = isInvalid;
        this.toast.node.active = true;
        this.toast.play('error_toast');
    }
}
