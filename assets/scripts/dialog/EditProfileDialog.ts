import { CommonDialog } from "./CommonDialog";
import { PrefabUtils } from "../utils/PrefabUtils";
import * as ee from '../libraries/ee/index';
import {DialogManager, ServiceLocator} from '../libraries/ee/index';
import {
    crashlytics,
    RewardUtils,
    SceneName,
    SocialUser,
    TrackingManager,
    UserProfileManager,
    VipManager,
} from "../manager/gm_manager";
import EditProfileNameDialog from "./EditProfileNameDialog";
import { DefaultUserAvatarView } from "../scenes/leaderboard/UserAvatarView";
import AvatarFrameSelector, { ConditionType } from "./AvatarFrameSelector";
import { ResourcesUtils } from "../utils/ResourcesUtils";
import { TimeUtils } from "../utils/TimeUtis";
import {PlayPassManager} from "../scenes/play_pass/PlayPassManager";

const { ccclass, property } = cc._decorator;

@ccclass
export class EditProfileDialog extends CommonDialog {
    public static create(): Promise<EditProfileDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/profile/edit_user_profile_dialog');
    }

    @property(cc.Label)
    private lblUserName: cc.Label = null;

    @property(cc.Node)
    private avatarContainNode: cc.Node = null;

    @property(cc.Node)
    private frameContainNode: cc.Node = null;

    @property(DefaultUserAvatarView)
    private avatarReview: DefaultUserAvatarView = null;

    @property([cc.SpriteFrame])
    private avatarSpriteFrames: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    private frameSpriteFrames: cc.SpriteFrame[] = [];

    @property(cc.Prefab)
    private avatarFramePrefab: cc.Prefab = null;

    @property(cc.Node)
    private editNameButton: cc.Node = null;

    @property(cc.Button)
    private saveButton: cc.Button = null;

    private userProfileManager: UserProfileManager = null;
    private _currentUser: SocialUser = null;

    private prevAvatar: AvatarFrameSelector = null;
    private prevFrame: AvatarFrameSelector = null;

    @crashlytics
    protected onLoad() {
        super.onLoad();
        this.userProfileManager =  ee.ServiceLocator.resolve(UserProfileManager);
        const socialUser = this.userProfileManager.socialUser;
        this._currentUser = {
            id: socialUser.id,
            name: socialUser.name,
            picture: socialUser.picture,
            frame: socialUser.frame,
            nationCode: socialUser.nationCode,
            changedNameTimes: socialUser.changedNameTimes,
        };
        this.lblUserName.string = this._currentUser.name;
        this.editNameButton.active = socialUser.changedNameTimes < 3;
        this.setData();
        this.saveButton.interactable = false;

    }

    private setData() {
        const avatarUnlockConditionDict: { [key: number]: ConditionType } = {
            [0]: ConditionType.NON_LOCK,
            [1]: ConditionType.NON_LOCK,
            [2]: ConditionType.NON_LOCK,
            [3]: ConditionType.NON_LOCK,
            [4]: ConditionType.NON_LOCK,
            [5]: ConditionType.NON_LOCK,
            [6]: ConditionType.NON_LOCK,
            [7]: ConditionType.NON_LOCK,
            [8]: ConditionType.LOCK_BY_ACCOUNT,
            [9]: ConditionType.LOCK_BY_ACCOUNT,
            [10]: ConditionType.LOCK_BY_ACCOUNT,
            [11]: ConditionType.LOCK_BY_ACCOUNT,
            [12]: ConditionType.LOCK_BY_VIP,
            [13]: ConditionType.LOCK_BY_VIP,
        };
        // avatar scroll
        {
            this.avatarContainNode.removeAllChildren();
            for (let i = 0; i < this.avatarSpriteFrames.length; i++) {
                let item = cc.instantiate(this.avatarFramePrefab);
                let selector = item.getComponent(AvatarFrameSelector).create()
                    .setIndex(i)
                    .setIsReviewFrame(false)
                    .setAvatar(this.avatarSpriteFrames[i])
                    .setCondition(avatarUnlockConditionDict[i])
                    .setOnSelect(() => {
                        this.onSelectAvatar(selector);
                    })
                    .build();
                this.avatarContainNode.addChild(selector.node);
                if (this._currentUser.picture === selector.Avatar.name) {
                    this.prevAvatar = selector;
                }
            }
        }

        const frameUnlockConditionDict: { [key: number]: ConditionType } = {
            [0]: ConditionType.NON_LOCK,
            [1]: ConditionType.NON_LOCK,
            [2]: ConditionType.NON_LOCK,
            [3]: ConditionType.NON_LOCK,
            [4]: ConditionType.NON_LOCK,
            [5]: ConditionType.NON_LOCK,
            [6]: ConditionType.NON_LOCK,
            [7]: ConditionType.NON_LOCK,
            [8]: ConditionType.LOCK_BY_VIP,
            [9]: ConditionType.LOCK_BY_VIP,
            [10]: ConditionType.LOCK_BY_VIP,
            [11]: ConditionType.LOCK_BY_VIP,
        };

        {
            this.frameContainNode.removeAllChildren();
            for (let i = 0; i < this.frameSpriteFrames.length; i++) {
                let item = cc.instantiate(this.avatarFramePrefab);
                let selector = item.getComponent(AvatarFrameSelector).create()
                    .setIndex(i)
                    .setIsReviewFrame(true)
                    .setFrame(this.frameSpriteFrames[i])
                    .setCondition(frameUnlockConditionDict[i])
                    .setOnSelect(() => {
                        this.onSelectFrame(selector);
                    })
                    .build();
                this.frameContainNode.addChild(selector.node);
                if (this._currentUser.frame === selector.Frame.name) {
                    this.prevFrame = selector;
                }
            }
        }

        // Show temporary frame
        const temporaryItems = this.userProfileManager.getTemporaryItems().filter(item => item.itemType === "frame");
        const loadTemporaryFrames = Promise.all(
            temporaryItems.map(async (info, index) => {
                let item = cc.instantiate(this.avatarFramePrefab);
                let expiredTime = -1;
                if (info.expiredTime != "unlimited") {
                    expiredTime = new Date(info.expiredTime).getTime() - Date.now();
                }
                let selector = item.getComponent(AvatarFrameSelector).create()
                    .setIndex(index)
                    .setIsReviewFrame(true)
                    .setFrame(await ResourcesUtils.loadFrame(info.itemName))
                    .setCondition(ConditionType.NON_LOCK)
                    .setTimeRemaining(expiredTime > 0 ? TimeUtils.getRemainString(expiredTime / 1000) : "")
                    .setOnSelect(() => {
                        this.onSelectFrame(selector);
                    })
                    .build();

                this.frameContainNode.addChild(selector.node);
                if (this._currentUser.frame === selector.Frame.name) {
                    this.prevFrame = selector;
                }
            })
        );

        // Show play pass frame after all temporary frames are loaded
        loadTemporaryFrames.then(() => {
            const playPassManager = ServiceLocator.resolve(PlayPassManager);
            if (playPassManager.isBuyFramePlayPass) {
                let item = cc.instantiate(this.avatarFramePrefab);
                ResourcesUtils.loadFrame("Frame_play_pass").then((frame) => {
                    let selector = item.getComponent(AvatarFrameSelector).create()
                        .setIndex(this.frameSpriteFrames.length + temporaryItems.length)
                        .setIsReviewFrame(true)
                        .setFrame(frame)
                        .setCondition(ConditionType.NON_LOCK)
                        .setOnSelect(() => {
                            this.onSelectFrame(selector);
                        })
                        .build();
                    this.frameContainNode.addChild(selector.node);
                    if (this._currentUser.frame === selector.Frame.name) {
                        this.prevFrame = selector;
                    }
                });
            }
        });
    }

    private onSelectAvatar(selector: AvatarFrameSelector) {
        if (selector.isLocked) {
            this.prevAvatar.showMarkSimulator();
            return;
        }
        this.prevAvatar.hideMarkSimulator();
        this.prevAvatar = selector;
        this._currentUser.picture = selector.Avatar.name
        this.avatarReview.updateAvatar(<SocialUser>{ picture: selector.Avatar.name })
        this.updateButtonSave()
    }

    private onSelectFrame(selector: AvatarFrameSelector) {
        if (selector.isLocked) {
            this.prevFrame.showMarkSimulator();
            return;
        }
        this.prevFrame.hideMarkSimulator();
        this.prevFrame = selector;
        this._currentUser.frame = selector.Frame.name;
        this.avatarReview.updateFrame(<SocialUser>{ frame: selector.Frame.name });
        this.updateButtonSave();
    }

    private onSubmitChangePressed() {
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.SceneEditProfile, 'btn_save');
        let userProfileManager = ee.ServiceLocator.resolve(UserProfileManager);
        userProfileManager.socialUser = this._currentUser;
    }

    /** Registered in editor. */
    private onEditNameButtonClicked() {
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.SceneEditProfile, 'btn_edit_name');
        EditProfileNameDialog.create().then((dialog) => {
            dialog.setInfo(this.lblUserName.string, (newName) => {
                this.nameChanged(newName);
            })
            dialog.show(ee.ServiceLocator.resolve(DialogManager));
        })
    }

    private nameChanged(newName: string) {
        this.lblUserName.string = newName;
        this._currentUser.name = newName;
        this._currentUser.changedNameTimes += 1;
        this.updateButtonSave();
    }

    private updateButtonSave() {
        const prevUser = this.userProfileManager.socialUser;
        if (prevUser.name !== this._currentUser.name ||
            prevUser.picture !== this._currentUser.picture ||
            prevUser.frame !== this._currentUser.frame) {
            this.saveButton.interactable = true;
            return;
        }
        this.saveButton.interactable = false;
    }
}
