import {CommonDialog} from "./CommonDialog";
import * as ee from '../libraries/ee/index';
import {Callback} from "../manager/ads/WatchAdsHelper";
import {CrashlyticManager, crashlytics} from "../manager/gm_manager";
import Button = cc.Button;
import {PrefabUtils} from "../utils/PrefabUtils";

const {ccclass, property} = cc._decorator;

@ccclass
export default class TeamConfirmDialog extends CommonDialog {

    @property({type: Button})
    private readonly yesButton: Button = null;

    @property({type: Button})
    private readonly noButton: Button = null;

    @property({type: ee.LanguageComponent, visible: true})
    private readonly _content: ee.LanguageComponent | null = null;

    @property({type: ee.LanguageComponent, visible: true})
    private readonly _title: ee.LanguageComponent | null = null;

    private yesCallback: Callback = () => {
    };

    private noCallback: Callback = () => {
    };

    public static create(): Promise<TeamConfirmDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/team/team_confirm_dialog');
    }

    public initialize(yesCallBack: Callback, noCallBack: Callback = null): void {
        this.yesCallback = yesCallBack;
        if (noCallBack) {
            this.noCallback = noCallBack;
        }
        this.setTouchOutsideEnabled(false);
    }
    public setKeyContent(key: string): void {
        if (this._content)
        {
            this._content._key = key
        }
    }

    public setParamContent(params: string[]): void {
        if (this._content)
        {
            this._content.paramValues = params
        }
    }

    public setKeyTitle(key: string): void {
        if (this._title)
        {
            this._title._key = key
        }
    }

    @crashlytics
    public onLoad() {
        super.onLoad();
        ee.ServiceLocator.resolve(CrashlyticManager)?.logFunction(this, this.onLoad, this.uuid);
    }

    private pressedYes = false;

    public onYesButtonPress() {
        if (this.pressedYes) {
            return;
        }
        this.pressedYes = true;
        this.yesCallback();
        this.isActive() && this.hide();
    }

    private pressedNo = false;

    public onNoButtonPress() {
        if (this.pressedNo) {
            return;
        }
        this.pressedNo = true;
        this.noCallback();
        this.isActive() && this.hide();
    }
}
