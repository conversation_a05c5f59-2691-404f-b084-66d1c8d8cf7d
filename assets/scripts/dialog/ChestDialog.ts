import * as gm from '../engine/gm_engine';
import * as ee from '../libraries/ee/index';
import { PrefabUtils } from "../utils/PrefabUtils";

export abstract class ChestDialog extends ee.Dialog {
    public static create(): Promise<ChestDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/chest_reward/chest_dialog');
    }

    /** Gets or sets the chest reward type. */
    public abstract chestType: gm.ChestType;

    /** Gets or sets the chest quantity. */
    public abstract chestQuantity: number;

    /**
     * Id của Chest slot
     */
    public abstract chestSlotId?: number;


    /** Có thưởng Gold hay ko? **/
    public abstract isRewardGold :boolean;
}
