import { RewardInfo, crashlytics, CrashlyticManager } from "../manager/gm_manager";
import { DailyRewardItem } from "../scenes/daily_reward/DailyRewardItem";
import { Controller, IAPPack, IAPPurchaseDialog } from "./IAPPurchaseDialog";
import * as ee from "../libraries/ee/index";
import { ResourcesUtils } from "../utils/ResourcesUtils";

const {ccclass, property, disallowMultiple} = cc._decorator;

@ccclass
@disallowMultiple
export class OfferDialogImpl extends IAPPurchaseDialog {
    
    @property({type: cc.Prefab, visible: true})
    private readonly commonReward: cc.Prefab = null;

    @property({type: cc.ScrollView, visible: true})
    protected readonly rewardLayer: cc.ScrollView = null;

    @property({type: cc.Label, visible: true})
    private readonly priceLabel: cc.Label = null;

    @property({type: cc.Sprite, visible: true})
    private readonly background: cc.Sprite = null;

    @property({type: ee.LanguageComponent, visible: true})
    private readonly title: ee.LanguageComponent = null;

    private controller: Controller;
    private _rewards: RewardInfo[] = [];

    @crashlytics
    public async onItemPressed(): Promise<void> {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onItemPressed, `${this.uuid}`);
        this.controller.onItemPressedCallback && this.controller.onItemPressedCallback();
    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    setController(controller: Controller): this {
        this.controller = controller;
        return this;
    }

    setRewardInfo(rewardInfo: RewardInfo[]): this {
        this._rewards = rewardInfo;
        this.rewardLayer.content.removeAllChildren();
        this._rewards.forEach((reward: RewardInfo) => {
            const item = cc.instantiate(this.commonReward);
            const rewardItem = item.getComponent(DailyRewardItem);
            rewardItem.setQuantity(reward.quantity);
            rewardItem.setIcon(reward.raw);
            this.rewardLayer.content.addChild(rewardItem.node);
        })
        return this;
    }

    protected setPrice(price: string): this {
        this.priceLabel && (this.priceLabel.string = price);
        return this;
    }

    protected setBackground(packType: IAPPack): void {
        const dict: { [key: number]: string } = {
            [IAPPack.BEGINNER_OFFER]: 'beginner_offer_banner',
            [IAPPack.CARD_OFFER]: 'card_offer_banner',
            [IAPPack.ENERGY_OFFER]: 'energy_offer_banner',
            [IAPPack.GOLDBAR_OFFER]: 'gold_offer_banner',
            [IAPPack.RUBY_OFFER]: 'ruby_offer_banner',
            [IAPPack.VIP_OFFER]: 'VIP_offer_banner',
            [IAPPack.BOOSTER_OFFER]: 'booster_offer_banner',
        };
        ResourcesUtils.loadResources(`images/offer/${dict[packType]}`, cc.SpriteFrame).then(spriteFrame => {
            if(!this.isValid) return;
            this.background!.spriteFrame = spriteFrame;
        })
    }
    
    protected setTitle(packType: IAPPack): void {
        const dict: { [key: number]: string } = {
            [IAPPack.BEGINNER_OFFER]: 'beginner_offer_pack',
            [IAPPack.CARD_OFFER]: 'beginner_offer_pack',
            [IAPPack.ENERGY_OFFER]: 'beginner_offer_pack',
            [IAPPack.GOLDBAR_OFFER]: 'beginner_offer_pack',
            [IAPPack.RUBY_OFFER]: 'beginner_offer_pack',
            [IAPPack.VIP_OFFER]: 'beginner_offer_pack',
            [IAPPack.BOOSTER_OFFER]: 'beginner_offer_pack',
        };
        this.title.key = dict[packType];
    }
}