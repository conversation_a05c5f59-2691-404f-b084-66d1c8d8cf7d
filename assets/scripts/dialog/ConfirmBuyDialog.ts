import * as gm from '../engine/gm_engine';
import * as ee from '../libraries/ee/index';
import {
    AudioManager,
    CrashlyticManager,
    crashlytics,
    ItemCard,
    ItemCardRarity,
    SoundType,
    StoreItem,
} from "../manager/gm_manager";
import { ItemCardAvatarView } from '../scenes/character/ItemCardAvatarView';
import { ItemCardProgressView } from '../scenes/character/ItemCardProgressView';
import { PrefabUtils } from "../utils/PrefabUtils";
import { CommonDialog } from "./CommonDialog";
import {GameStatic} from "../utils/GameStatic";

const { ccclass, disallowMultiple, property } = cc._decorator;

type Callback = () => void;

@ccclass
@disallowMultiple
export class ConfirmBuyDialog extends CommonDialog {
    public static create(): Promise<ConfirmBuyDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/confirm_buy_dialog/confirm_buy_dialog');
    }

    /** Displays the title. */
    @property({ type: ee.LanguageComponent, visible: true })
    private readonly _titleLabel: ee.LanguageComponent | null = null;

    /** Displays the purchase item. */
    @property({ type: ee.NestedPrefab, visible: true })
    private readonly _itemIcon: ee.NestedPrefab | null = null;

    @ee.nest(ItemCardAvatarView)
    private readonly _itemCardAvatarView: ItemCardAvatarView | null = null;

    @ee.nest(ItemCardProgressView)
    private readonly _itemCardProgressView: ItemCardProgressView | null = null;

    /** Displays the purchase amount. */
    @property({ type: cc.Label, visible: true })
    private readonly _amountLabel: cc.Label | null = null;

    /** Displays the unlimited duration (for energy packs). */
    @property({ type: ee.LanguageComponent, visible: true })
    private readonly _unlimitedLabel: ee.LanguageComponent | null = null;

    /** Displays purchase cost. */
    @property({ type: cc.Label, visible: true })
    private readonly _costLabel: cc.Label | null = null;

    /** Displays purchase cost type (gold or ruby). */
    @property({ type: ee.NestedPrefab, visible: true })
    private readonly _costIcon: ee.NestedPrefab | null = null;

    @property([cc.Prefab])
    private readonly storeItemPrefabs: cc.Prefab[] = [];

    private get titleLabel(): ee.LanguageComponent {
        return gm.retrieveNull(this._titleLabel);
    }

    private get itemIcon(): ee.NestedPrefab {
        return gm.retrieveNull(this._itemIcon);
    }

    private get itemCardAvatarView(): ItemCardAvatarView {
        return gm.retrieveNull(this._itemCardAvatarView);
    }

    private get itemCardProgressView(): ItemCardProgressView {
        return gm.retrieveNull(this._itemCardProgressView);
    }

    private get amountLabel(): cc.Label {
        return gm.retrieveNull(this._amountLabel);
    }

    private get unlimitedLabel(): ee.LanguageComponent {
        return gm.retrieveNull(this._unlimitedLabel);
    }

    private get costLabel(): cc.Label {
        return gm.retrieveNull(this._costLabel);
    }

    private get costIcon(): ee.NestedPrefab {
        return gm.retrieveNull(this._costIcon);
    }

    private callback: Callback = () => { };

    @crashlytics
    protected onLoad(): void {
        super.onLoad();
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);

        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });
    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    public setItemType(type: StoreItem): this {
        const keys: { [key: string]: string } = {
            [StoreItem.Gold]: "confirm_buy_gold",
            [StoreItem.Energy]: "confirm_buy_energy",
            [StoreItem.PvpTicket]: "confirm_buy_ticket_pvp",
            [StoreItem.Card]: "confirm_buy_card",
        };
        this.titleLabel.key = keys[type];
        if (type === StoreItem.Card) {
            this.itemIcon.node.active = false;
        } else {
            this.itemIcon.node.active = true;
            this.itemIcon.prefab = this.getStoreItemPrefab(type);
        }
        return this;
    }

    public setAmount(value: number): void {
        if (value === 0) {
            // Ignore.
            return;
        }
        this.amountLabel.node.active = true;
        this.amountLabel.string = value.toString();
        this.unlimitedLabel.node.active = false;
    }

    public setUnlimited(value: number): void {
        if (value === 0) {
            // Ignore.
            return;
        }
        this.amountLabel.node.active = false;
        this.unlimitedLabel.node.active = true;
        this.unlimitedLabel.paramValues = [`${value}`];
    }

    public setCost(cost: number): void {
        this.costLabel.string = cost.toString();
    }

    public setCostType(type: StoreItem): void {
        this.costIcon.prefab = this.getStoreItemPrefab(type);
    }

    public setCardView(itemCard: ItemCard): this {
        this.itemCardAvatarView.node.parent.active = true;
        this.itemCardProgressView.node.parent.active = true;
        this.itemCardProgressView.progress = itemCard.progress;
        this.itemCardProgressView.next = itemCard.next;

        itemCard.iconLoader.load().then(value => {
            this.isValid && (this.itemCardAvatarView.icon = value);
        });
        this.itemCardAvatarView.rarity = itemCard.rarity;
        this.itemCardAvatarView.new = !itemCard.collected;
        this.itemCardAvatarView.level = itemCard.level;

        this.itemCardAvatarView.node.color = GameStatic.getCardColorByRarity(itemCard.rarity);
        return this;
    }

    public onPressed(callback: Callback): this {
        this.callback = callback;
        return this;
    }

    private getStoreItemPrefab(type: StoreItem): cc.Prefab {
        const dict: { [key: string]: cc.Prefab } = {
            [StoreItem.Energy]: this.storeItemPrefabs[0],
            [StoreItem.Gold]: this.storeItemPrefabs[1],
            [StoreItem.Ruby]: this.storeItemPrefabs[2],
            [StoreItem.PvpTicket]: this.storeItemPrefabs[3],
        };
        return dict[type];
    }

    /** Register in editor */
    @crashlytics
    private onBuyButtonPressed(): void {
        if (!this.isActive()) {
            return;
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onBuyButtonPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        this.callback();
    }
}
