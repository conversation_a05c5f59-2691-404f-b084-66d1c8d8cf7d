import {CommonDialog} from "./CommonDialog";
import * as ee from '../libraries/ee/index';
import * as gm from '../engine/gm_engine';
import {PrefabUtils} from '../utils/PrefabUtils';
import {
    BoosterInfo,
    CrashlyticManager,
    crashlytics,
} from "../manager/gm_manager";
import BadgeView, {BadgeInfo} from "../team/BadgeView";
import {AutoScalePolicy, AutoScaleWidget} from "../scenes/widget/AutoScaleWidget";
import {CloseDialogButton} from "../ui/CloseDialogButton";


const {ccclass, property} = cc._decorator;
const DEFAULT_BADGE = JSON.stringify({
    "backgroundID": "badge_bg_violet",
    "iconID": "badge_icon_star2",
    "iconColor": "#2F5EB2"
});

@ccclass
export class ChooseBadgeDialog extends CommonDialog {
    public static create(): Promise<ChooseBadgeDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/team/choose_badge_dialog');
    }

    @property({type: cc.Node, visible: true})
    private readonly _layout: cc.Node | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _itemPrefab: cc.Prefab | null = null;

    @property(cc.JsonAsset)
    private badgeData: cc.JsonAsset | null = null;

    @property(CloseDialogButton)
    private btnCloseDialog: CloseDialogButton = null;

    private get layout(): cc.Node {
        return gm.retrieveNull(this._layout);
    }

    private get itemPrefab(): cc.Prefab {
        return gm.retrieveNull(this._itemPrefab);
    }

    private _badges: BadgeInfo[] = [];
    public selectedBadgeID: string = DEFAULT_BADGE;

    @crashlytics
    protected onLoad(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        super.onLoad();
        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });
        this.btnCloseDialog.replaceCallback(() => {
            this.isActive() && this.hide();
        });
        this.setBadges();
    }

    public setBadges() {
        this._badges = this.badgeData.json.data;
        this.updateDisplay();
    }

    public setInitialBadge(id: string){
        if(id){
            this.selectedBadgeID = id;
        }
    }

    private updateDisplay(): void {
        this.layout.removeAllChildren();
        // this.selectedBadgeID = DEFAULT_BADGE;
        this._badges.forEach(badge => {
            const container = new cc.Node();
            container.setContentSize(210, 210);
            const item = cc.instantiate(this.itemPrefab);
            const badgeData = JSON.stringify(badge);
            item.getComponent(BadgeView).badgeID = badgeData;
            container.addChild(item);

            let autoScale = item.addComponent(AutoScaleWidget);
            autoScale.policy = AutoScalePolicy.FitContent;
            this.layout.addChild(container);

            item.on(cc.Node.EventType.TOUCH_END, () => {
                    this.selectedBadgeID = badgeData;
                this.hide();
            })
        })
    }
}
