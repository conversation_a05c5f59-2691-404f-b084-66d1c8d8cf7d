import {CommonDialog} from "./CommonDialog";
import {PrefabUtils} from "../utils/PrefabUtils";
import * as ee from "../libraries/ee/index";
import {GameServerManager} from "../manager/game_server/GameServerManager";
import {TrackingManager} from "../manager/analytics/TrackingManager";
import {SceneName} from "../manager/analytics/AnalyticsConfig";

const {ccclass, property} = cc._decorator;

@ccclass
export default class CancelDeleteAccountDialog extends CommonDialog {
    public static create(): Promise<CancelDeleteAccountDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/server/cancel_delete_account_dialog');
    }
    @property({ type: cc.Button, visible: true })
    private _cancelButton: cc.Button | null = null;
    protected onLoad() {
        super.onLoad();
        this._cancelButton.interactable = true
        this.setTouchOutsideEnabled(false);
    }


    public onPressCancel(): void {
        if (this.isActive()) {
            this._cancelButton.interactable = false
            ee.ServiceLocator.resolve(GameServerManager).deleteAccountController.cancelDeleting().then(rs => {
                if (rs) {
                    this.isActive() && this.hide();
                }
                this._cancelButton.interactable = true
            })
        }
        const trackingMgr = ee.ServiceLocator.resolve(TrackingManager);
        trackingMgr.trackEventClick(SceneName.DialogAccountDeleting, "btn_cancel");
    }
}
