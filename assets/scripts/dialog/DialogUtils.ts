import * as ee from '../libraries/ee/index';
import { ParticleUtils } from '../utils/ParticleUtils';
import { ResourcesUtils } from "../utils/ResourcesUtils";

export class DialogUtils {
    // Must be larger than zero otherwise matrix transformation would be incorrect.
    private static TRANSFORM_SCALE = 0.0001;

    public static addPopInTransition(dialog: ee.Dialog): void {
        dialog.onWillHide(() => {
            const node = dialog.node;
            const scale = this.TRANSFORM_SCALE;
            const transition = cc.targetedAction(node, cc.scaleTo(0.2, scale)).easing(cc.easeElasticIn(0.9));
            dialog.addHidingTransition(transition);
        });
    }

    public static addPopOutTransition(dialog: ee.Dialog): void {
        dialog.onWillShow(() => {
            const node = dialog.node;
            const scale = this.TRANSFORM_SCALE;
            node.scale = scale;
            const transition = cc.targetedAction(node, cc.scaleTo(0.3, 1.0)).easing(cc.easeElasticOut(0.9));
            dialog.addShowingTransition(transition);
        });
    }

    public static addFadeInBackground(dialog: ee.Dialog): void {
        dialog.onWillShow(() => {
            const path = `images/other/white`;
            ResourcesUtils.loadResources(path, cc.Texture2D).then(texture => {
                const node = new cc.Node("background");
                const sprite = node.addComponent(cc.Sprite);
                node.color = cc.Color.BLACK;
                node.opacity = 150;
                sprite.type = cc.Sprite.Type.SLICED;
                sprite.spriteFrame = new cc.SpriteFrame(texture);
                const container = dialog.getContainer();
                container.addChild(node, -1);
                node.setContentSize((container.getContentSize()));
                (container as any).__fade_background = node;
            });
        });
    }

    public static addFadeOutBackground(dialog: ee.Dialog): void {
        dialog.onWillHide(() => {
            dialog.addHidingTransition(cc.callFunc(() => {
                const container = dialog.getContainer();
                const node = (container as any).__fade_background as cc.Node;
                node.removeFromParent(true);
                node.destroy();
            }));
        });
    }

    public static createConfettiParticle(duration: number, isLeft: boolean): cc.Node {
        const node = new cc.Node("confetti");
        const n = `particles/dialog/confetti_outro_${isLeft ? 'left' : 'right'}`;
        node.addChild(ParticleUtils.createParticle({
            file: n,
            duration,
        }));
        return node;
    }

    public static createLightAndConfetti(): cc.Node {
        const winSize = cc.winSize;
        const node = new cc.Node("light");

        // Create light
        /*const leftLight = ParticleUtils.createSprite(`images/other/light_big`);
        leftLight.name = "light1";
        leftLight.anchorX = 0.5;
        leftLight.anchorY = 0;
        leftLight.position = cc.v3(-winSize.width / 1.8, -winSize.height / 1.8, 0);
        leftLight.scale = 4;
        leftLight.angle = 90;

        const rightLight = ParticleUtils.createSprite(`images/other/light_big`);
        rightLight.position = cc.v3(winSize.width / 1.8, - winSize.height / 1.8, 0);
        rightLight.anchorX = 0.5;
        rightLight.anchorY = 0;
        rightLight.scale = 4;
        rightLight.angle = -90;

        leftLight.runAction(cc.repeatForever(cc.sequence(cc.rotateBy(1.5, 90), cc.rotateBy(1.5, -90))));
        rightLight.runAction(cc.repeatForever(cc.sequence(cc.rotateBy(1.5, -90), cc.rotateBy(1.5, 90))));
        node.addChild(leftLight);
        node.addChild(rightLight);
         */

        // Create confetti
        const leftConfetti = this.createConfettiParticle(5, true);
        leftConfetti.position = cc.v3(-winSize.width / 2, -winSize.height / 2, 0);
        const rightConfetti = this.createConfettiParticle(5, false);
        rightConfetti.position = cc.v3(winSize.width / 2, -winSize.height / 2, 0);
        node.addChild(leftConfetti);
        node.addChild(rightConfetti);

        node.setAnchorPoint(0.5, 0.5);
        return node;
    }
}