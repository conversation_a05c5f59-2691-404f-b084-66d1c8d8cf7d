import * as ee from '../../libraries/ee/index';
import ChatItemView from "./ChatItemView";
import {ChatMessage, ChatMessageType} from "./Manager/ChatManager";
import {ChatUserInfo} from "./Manager/ChatUserCacheManager";
import {ServiceLocator} from "../../libraries/ee/index";
import {UserProfileManager} from "../../manager/profile/UserProfileManager";

const {ccclass, property} = cc._decorator;

@ccclass
export default class SystemItemView extends ChatItemView {

    @property({ type: ee.LanguageComponent, visible: true })
    private readonly content: ee.LanguageComponent | null = null;

    public setData(offset: number, chatMessage: ChatMessage, chatUserInfo: ChatUserInfo) {
        this.chatMessage = chatMessage;
        if (chatMessage.type === ChatMessageType.system) {
            this.content.key = chatMessage.content;
        }

        if (chatUserInfo.userName == null || chatUserInfo.userName == "") {
            this.playerName.node.active = false;
        } else {
            if (chatMessage.isSelf) {
                this.updateUiLocal(offset);
            } else {
                this.updateUi(offset, chatUserInfo);
            }
        }
        this.scheduleOnce(() => {
            this.updateHeight();
        }, 0);
    }

    protected updateUi(offset: number, chatUserInfo: ChatUserInfo) {
        if (chatUserInfo.userName == null || chatUserInfo.userName == "") {
            this.playerName.node.active = false;
        } else {
            this.playerName.string = chatUserInfo.userName;
            this.playerName.node.active = true;
        }
    }

    protected updateUiLocal(offset: number) {
        const profileManager = ServiceLocator.resolve(UserProfileManager);
        const socialUser = profileManager.socialUser;
        if (socialUser) {
            this.playerName.string = socialUser.name;
        }
    }
}
