import * as ee from "../../libraries/ee/index";
import * as gm from '../../engine/gm_engine';
import {GenericOptimizer} from "../../utils/Clipper";
import JoinRequestTeamView from "./JoinRequestTeamView";
import {ChatNetworkManager} from "./Manager/ChatNetworkManager";
import {ChatManager, ChatMessage, RequestJoin} from "./Manager/ChatManager";
import {PrefabUtils} from "../../utils/PrefabUtils";
import {CommonDialog} from "../../dialog/CommonDialog";
import {NotificationView} from "../../scenes/common/NotificationView";
import {TimeUtils} from "../../utils/TimeUtis";
import {FloatingText} from "../../scenes/team/FloatingText";

const {ccclass, property} = cc._decorator;

export interface joinRequestListener {
    /** Occurs when Reject a requestId. */
    onReject?(requestId: string): void;
    /** Occurs when accept a requestId */
    onAccept?(requestId: string): void;
    /** Occurs when accept all requestIds */
    onAcceptAll?(): void;
    /** Occurs when scroll at bottom to append more */
    onAppendMore?(): void;
}

@ccclass
export default class JoinRequestDialog extends CommonDialog {

    public static create(): Promise<JoinRequestDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/team/chat/join_requests_dialog');
    }

    @property({type: cc.Prefab, visible: true})
    private readonly joinRequestPrefab: cc.Prefab | null = null;

    @property({type: cc.ScrollView, visible: true})
    private readonly scrollView: cc.ScrollView | null = null;

    @property({type: cc.Layout, visible: true})
    private readonly layout: cc.Layout | null = null; // Node Content trong ScrollView

    @property({type: cc.Node, visible: true})
    private readonly noPending: cc.Node | null = null; // Node Content trong ScrollView

    @property({type: cc.Node, visible: true})
    private readonly waiting: cc.Node | null = null; // Node Content trong ScrollView

    @property({type: FloatingText, visible: true})
    private floatingText: FloatingText | null = null;

    @property({type: NotificationView, visible: true})
    private readonly mailNotification: NotificationView | null = null;

    public listener: joinRequestListener;

    private chatNetworkManager: ChatNetworkManager;
    private itemList = [];
    private workItemList = [];
    private _optimizer?: GenericOptimizer<JoinRequestTeamView>;

    private _isLoadingNew = false;
    public set isLoadingNew(value: boolean) {
        if (value) {
            this.waiting.opacity = 255;
        }
        else {
            cc.tween(this.waiting)
                .to(1, {opacity: 0})
                .start();
            this._isLoadingNew = value;
        }
    }

    private get optimizer(): GenericOptimizer<JoinRequestTeamView> {
        if (this._optimizer === undefined) {
            this._optimizer = new GenericOptimizer<JoinRequestTeamView>(this.layout.node)
                .setCreator(() => {
                    const node = cc.instantiate(this.joinRequestPrefab);
                    return node;
                })
                .setUpdater((index, view, model) => {
                    const item = view.getComponent(JoinRequestTeamView);
                    this.setItemInfo(index, item);
                });
        }
        return this._optimizer;
    }

    protected onLoad() {
        this.chatNetworkManager = ee.ServiceLocator.resolve(ChatNetworkManager);
        this.scrollView.node.on('scrolling', this.onScrolling, this);

    }

    public updateAlignment() {
        this.scrollView.content.getComponent(cc.Widget)?.updateAlignment();
    }

    public setRequestList(requestList: RequestJoin[], workRequestList: RequestJoin[]): void {
        this.itemList = requestList;
        this.workItemList = workRequestList;
        this.optimizer.setModels(this.itemList);
        this.layout.updateLayout();
        this.updateRendering();
        this.scheduleOnce(()=>{
            this.updateMailNotification();
            cc.tween(this.waiting)
                .to(1, {opacity: 0})
                .start();
        })
    }

    public updateRequestJoinsView(): void {
        this.optimizer.updateActiveViews();
        this.scheduleOnce(()=>{
            this.updateMailNotification();
        })
    }

    public showFloatingText(key: string) {
        this.floatingText.content.key = key;
        this.floatingText.show();
    }

    public removeJoinRequest(requestId: string): void {
        let index = this.itemList.findIndex(it => it.requestId === requestId);
        if (index >= 0 && !this.itemList[index].deleted) { // đã xóa rồi thì không xóa nữa.
            this.itemList[index].deleted = true;
            this.updateMailNotification();

            // update lastRequestId
            if (index === 0) {
                for (let i = 0; i < this.itemList.length; ++i) {
                    if (!this.itemList[i].deleted) {
                        ee.ServiceLocator.resolve(ChatManager).lastRequestId = this.itemList[i].requestId;
                        break;
                    }
                }
            }

            // animate remove item
            const children = this.layout.node.children;
            children.some((child) => {
                const item = child.getComponent(JoinRequestTeamView);
                if (item.requestId === requestId) {
                    this.animateRemoveJoinRequest(item).then(() => {
                    });
                    return true;
                }
                return false;
            });

        }

        // Cập nhật deleted trong workItemList.
        index = this.workItemList.findIndex(it => it.requestId === requestId);
        if (index >= 0 && !this.workItemList[index].deleted) { // đã xóa rồi thì không xóa nữa.
            this.workItemList[index].deleted = true;
            this.updateMailNotification();
        }
    }

    public getRequestCount(): number {
        const count1 = this.workItemList.filter((item) => !item.deleted).length;
        const count2 = this.itemList.filter((item) => !item.deleted).length;
        const count = count1 + count2
        this.noPending.active = count <= 0;
        return count;
    }

    public hideMailNotification(): void {
        this.mailNotification.node.active = false;
    }

    private updateMailNotification(): void {
        const amount = this.getRequestCount();
        this.mailNotification.setValue(amount);
        this.mailNotification.node.active = amount > 0;
    }

    private async animateRemoveJoinRequest(item: JoinRequestTeamView): Promise<void> {
        await item.removeOut();

        const steps = 10;
        let originalHeight = item.node.height;
        let childHeights = [];
        for (const child of item.node.children) {
            if (child.height !== undefined) {
                childHeights.push(child.height);
            }
        }

        // animate giảm chiều cao.
        for (let i = steps; i >= 0; i--) {
            const factor = i / steps;
            item.node.height = originalHeight * factor;
            for (const child of item.node.children) {
                if (child.height !== undefined) {
                    child.height = child.height * factor;
                }
            }
            this.layout.updateLayout();
            await TimeUtils.sleep(10);
        }

        item.node.active = false;
        // Trả lại chiều cao ban đàu.
        item.node.height = originalHeight;
        for (const child of item.node.children) {
            if (child.height !== undefined) {
                child.height = childHeights.shift();
            }
        }
        item.node.opacity = 255;

        this.layout.updateLayout();

        // sau khi giảm kiểm tra lại để appendMore nếu cần.
        if (this.isAtBottom()) {
            this.listener.onAppendMore && this.listener.onAppendMore();
        }
    }

    public removeAllRequests(): void {
        this.itemList.forEach(it => {
            it.deleted = true;
        })
        this.workItemList.forEach(it => {
            it.deleted = true;
        })
        const children = this.layout.node.children;
        children.forEach(it => {
            it.active = false;
        })
        this.updateMailNotification();
    }

    private onAcceptAllClicked() {
        this.listener.onAcceptAll && this.listener.onAcceptAll();
    }

    private setItemInfo(index: number, item: JoinRequestTeamView): void {
        if (!this.chatNetworkManager) {
            this.chatNetworkManager = ee.ServiceLocator.resolve(ChatNetworkManager);
        }
        const requestJoint = this.itemList[index] as RequestJoin;
        this.chatNetworkManager.getUserInfo(requestJoint.userId).then((userInfo) => {
            item.setData(requestJoint, userInfo);
            item.setListener(this.listener)
        });
    }

    private updateRendering(): void {
        const mask = this.scrollView.content.parent;
        const helper = new gm.TransformHelper(mask, this.layout.node);
        const anchorX = mask.anchorX;
        const from = helper.convertTo(cc.v2(mask.width * -anchorX, 0)).x;
        const to = helper.convertTo(cc.v2(mask.width * (1 - anchorX), 0)).x;
        this.optimizer.process(from, to, false);
    }

    private isAtBottom(threshold: number = 0): boolean {
        const offsetY = this.scrollView.getScrollOffset().y;
        const maxY = this.scrollView.getMaxScrollOffset().y;

        const contentHeight = this.scrollView.content.height;
        const viewHeight = this.scrollView.node.getContentSize().height;

        // Nếu nội dung không đủ để cuộn => đã ở "bottom"
        if (contentHeight <= viewHeight) return true;

        return (maxY - offsetY) <= threshold;
    }


    private async onScrolling() {
        if (this._isLoadingNew) {
            return;
        }

        if (this.workItemList.length === 0) {
            return;
        }

        if (this.isAtBottom()) {
            this.listener.onAppendMore && this.listener.onAppendMore();
        }
    }
}
