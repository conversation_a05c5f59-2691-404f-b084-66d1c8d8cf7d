import * as ee from "../../libraries/ee/index";
import {RequestJoin} from "./Manager/ChatManager";
import {ChatNetworkManager} from "./Manager/ChatNetworkManager";
import {ChatUserInfo} from "./Manager/ChatUserCacheManager";
import {UserProfileManager} from "../../manager/profile/UserProfileManager";
import {ResourcesUtils} from "../../utils/ResourcesUtils";
import {
    DEFAULT_AVATAR_ID,
    DEFAULT_FRAME_ID,
    LIST_FRAMES,
    LIST_PICTURES
} from "../../manager/profile/DefaultUserProfileManager";
import {joinRequestListener} from "./JoinRequestsDialog";

const {ccclass, property} = cc._decorator;

@ccclass
export default class JoinRequestTeamView extends cc.Component {

    @property({type: cc.Sprite, visible: true})
    protected readonly avatarSprite: cc.Sprite | null = null;

    @property({type: cc.Sprite, visible: true})
    protected readonly frame: cc.Sprite | null = null;

    @property({type: cc.Node, visible: true})
    protected readonly waitingIcon: cc.Node | null = null;

    @property({type: cc.Label, visible: true})
    protected readonly playerName: cc.Label | null = null;

    @property({type: cc.Node, visible: true})
    private readonly new: cc.Node | null = null;

    @property({type: cc.Button, visible: true})
    private readonly rejectButton: cc.Button | null = null;

    @property({type: cc.Button, visible: true})
    private readonly acceptButton: cc.Button | null = null;

    private _listener: joinRequestListener;
    private _requestId: string;
    public get requestId(): string {
        return this._requestId;
    }

    public setData(requestJoin: RequestJoin, chatUserInfo: ChatUserInfo) {
        this._requestId = requestJoin.requestId;
        const userId = ee.ServiceLocator.resolve(ChatNetworkManager).userId;
        if (requestJoin.userId === userId) {
            this.updateUiLocal()
        } else {
            this.updateUi(chatUserInfo);
        }
        this.waitingIcon.active = false;
        this.new.active = requestJoin.new;
        this.node.active = !requestJoin.deleted;
    }

    public async removeOut(): Promise<void> {
        return await new Promise<void>(resolve => {
            cc.tween(this.node)
                .to(0.3, {opacity: 0})
                .call(()=>{
                    resolve();
                })
                .start();
        });
    }

    public setListener(listener: joinRequestListener) {
        this._listener = listener;
    }

    private updateUi(chatUserInfo: ChatUserInfo) {
        this.loadFrame(chatUserInfo.frame)
        this.loadAvatar(chatUserInfo.avatar);
        this.playerName.string = chatUserInfo.userName;
    }

    private updateUiLocal() {
        const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
        const socialUser = profileManager.socialUser;

        if (socialUser) {
            this.loadFrame(socialUser.frame)
            this.loadAvatar(socialUser.picture);
            this.playerName.string = socialUser.name;
        }
    }

    private getAvailableAvatarId(avatarId: string): string {
        if (LIST_PICTURES.includes(avatarId)) {
            return avatarId;
        }
        return DEFAULT_AVATAR_ID;
    }

    private loadAvatar(avatarName: string) {
        avatarName = this.getAvailableAvatarId(avatarName);
        ResourcesUtils.loadAvatar(avatarName).then(avatar => {
            this.avatarSprite.node.opacity = 255;
            this.avatarSprite.spriteFrame = avatar;
        })
    }

    private getAvailableFrameId(frameId: string) {
        if (LIST_FRAMES.includes(frameId)) {
            return frameId;
        }
        return DEFAULT_FRAME_ID;
    }

    private loadFrame(frameName: string) {
        frameName = this.getAvailableFrameId(frameName);
        ResourcesUtils.loadFrame(frameName).then(frame => {
            this.frame.spriteFrame = frame;
        })
    }

    public onRejectButtonClicked() {
        this._listener.onReject && this._listener.onReject(this.requestId);
    }

    public onAcceptButtonClicked() {
        this._listener.onAccept && this._listener.onAccept(this.requestId);
    }

}
