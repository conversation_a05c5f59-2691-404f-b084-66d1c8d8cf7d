import * as gm from '../../engine/gm_engine';
import * as ee from "../../libraries/ee/index";
import ActionView from "./ActionView";
import MessageView from "./MessageView";
import {ChatManager, ChatMessage, ChatMessageType, RequestJoin} from "./Manager/ChatManager";
import {ChatNetworkManager} from "./Manager/ChatNetworkManager";
import {ChatSyncManager} from "./Manager/ChatSyncManager";
import {checkInternetConnection} from "../../server/InternetChecking";
import {MyTeamManager, ROLE} from "../../manager/team/MyTeamManager";
import JoinRequestDialog from "./JoinRequestsDialog";
import {DialogManager} from "../../libraries/ee/index";
import {TeamInfoResponse} from "../../manager/game_server/GameServerManager";

const {ccclass, property} = cc._decorator;

export interface chatViewListener {
    onShowUserInfo?: (userId: string) => void;
    onViewMyTeamProfile?: () => void;
    onKickOff?: () => void;
}

@ccclass
export default class ChatView extends cc.Component {

    public listener: chatViewListener;

    @ee.nest(MessageView)
    private readonly _messageView: MessageView;

    @ee.nest(ActionView)
    private readonly _actionView: ActionView | null = null;

    private get messageView(): MessageView {
        return gm.retrieveNull(this._messageView);
    }

    private get actionView(): ActionView {
        return gm.retrieveNull(this._actionView);
    }

    @property({type: cc.Node, visible: true})
    private readonly noInternetLayer: cc.Node | null = null;

    private chatNetworkManager: ChatNetworkManager;
    private chatManager: ChatManager;
    private chatSyncManager: ChatSyncManager;
    private myTeamManager: MyTeamManager;

    private joinRequestDialog: JoinRequestDialog;
    private fullRequestJoins: RequestJoin[] = [];
    private workRequestJoins: RequestJoin[] = [];
    private requestJoins: RequestJoin[] = [];

    public onLoad(): void {
        this.chatNetworkManager = ee.ServiceLocator.resolve(ChatNetworkManager);
        this.chatManager = ee.ServiceLocator.resolve(ChatManager);
        this.chatSyncManager = new ChatSyncManager(this.chatNetworkManager, this.chatManager);
        this.myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);

        this.chatNetworkManager.listener = {
            onKickoff: this.kickOff.bind(this),
            onDemoted: this.demoted.bind(this),
            onPromoted: this.promoted.bind(this),
            onUpdateTeamInfo: this.updateTeamInfo.bind(this)
        }

        this.chatManager.listener = {
            onNewMessage: this.appendMessage.bind(this),
            onMessageUpdated: this.updateMessage.bind(this),
            onSystemMessage: this.appendMessage.bind(this),
            onUpdateJoinRequest: this.updateJoinRequest.bind(this),
            onNewJoinRequest: this.newJoinRequest.bind(this)
        }

        this.actionView.listener = {
            onRequest: this.sendRequest.bind(this),
            onMessage: this.sendMessage.bind(this),
            onViewMyTeamProfile: this.viewMyTeamProfile.bind(this),
            onViewJoinRequests: this.viewJoinRequests.bind(this),
        }

        this.messageView.listener = {
            onResponse: this.response.bind(this),
            onUserInfo: this.showUserInfo.bind(this),
            onFetchUp: this.onFetchUp.bind(this)
        }

        this.chatNetworkManager.connect();

    }

    protected onEnable() {
        this.schedule(this.checkInternet, 3);
        this.messageView.updateAlignment();
        this.scheduleOnce(()=>{
            this.onEnterChat().then(()=> {
                this.scheduleOnce(() => {
                    this.scheduleOnce(() => {
                        this.scheduleOnce(() => {
                            this.scheduleOnce(() => {
                                this.messageView.scrollBottom();
                            });
                        });
                    });
                });
            });
        });
    }

    protected onDisable() {
        this.unschedule(this.checkInternet);
    }

    protected onDestroy() {
        this.chatNetworkManager.clearRoomPreview();
        this.chatNetworkManager.listener = null;
        this.chatManager.listener = null;
    }

    private async onEnterChat() {
        //Kiểm tra lấy thưởng energy khi vào game.
        const requestId = this.chatManager.getRequestId();
        await this.chatSyncManager.synchronizeRequest(requestId);

        // lấy danh sách join requests
        await this.getJoinRequest();

        //sync 20 tin nhắn cuối từ server.
        const newFetch = await this.chatSyncManager.synchronizeLast();
        this.messageView.setFullItemList(newFetch);
        this.messageView.moveLastToMessageList(10);
    }

    private async getJoinRequest(): Promise<void> {
        await this.myTeamManager.loadTeamMembers();
        const myRole = this.myTeamManager.getMyRole();
        if (myRole !== ROLE.MEMBER) {
            const teamId = this.myTeamManager.getTeamInfo().teamId;
            this.fullRequestJoins = await this.chatNetworkManager.fetchTeamRequests(teamId);

            // check new
            const lastRequestId = this.chatManager.lastRequestId;
            let newAmount = 0;
            let index = -1;
            if (lastRequestId != null) {
                index = this.fullRequestJoins.findIndex(it => it.requestId === this.chatManager.lastRequestId);
            }
            if (index < 0) index = this.fullRequestJoins.length;
            for (let i = index - 1; i >= 0; i--) {
                this.fullRequestJoins[i].new = true;
                newAmount++;
            }
            this.actionView.setNotification(newAmount);
        }
        this.actionView.updateMyRole(this.myTeamManager.isClosed(), myRole);
    }

    private async onFetchUp() {
        const newFetch = await this.chatSyncManager.synchronizeUp();
        this.messageView.prependFullList(newFetch);
    }

    private kickOff() {
        this.actionView.closeEditMessageDialog();
        this.closeJoinRequestDialog();
        this.listener.onKickOff && this.listener.onKickOff();
    }

    private demoted() {
        this.myTeamManager.loadTeamMembers().then(()=>{
            const myRole = this.myTeamManager.getMyRole();
            if (myRole === ROLE.MEMBER) {
                this.closeJoinRequestDialog();
                this.actionView.updateMyRole(this.myTeamManager.isClosed(), myRole);
            }
        })
    }

    private promoted() {
        this.getJoinRequest().then();
    }

    private updateTeamInfo(teamInfo: TeamInfoResponse) {
        const myRole = this.myTeamManager.getMyRole();
        this.closeJoinRequestDialog();
        this.getJoinRequest().then();
    }

    private appendMessage(msg: ChatMessage) {
        this.messageView.addMessage(msg);
    }

    private updateMessage(msg: ChatMessage, subType: string, amount: number = 0): void {
        this.messageView.updateMessage(msg, subType, amount);
    }

    private sendRequest() {
        this.chatManager.setTimeForNextRequest(4 * 60 * 60); // tạm set sẽ cập nhật sau khi nhận broadcast
        this.chatNetworkManager.sendRequest();
    }

    private sendMessage(message: string): void {
        const content = message;
        this.chatNetworkManager.sendMessage({ content });
    }

    private updateJoinRequest(joinRequest: RequestJoin) {
        const myRole = this.myTeamManager.getMyRole();
        if (myRole === ROLE.MEMBER) {
            return;
        }

        // status không thay đổi.
        if (joinRequest.status === 'PENDING') {
            return;
        }

        if (this.joinRequestDialog) {
            this.joinRequestDialog.removeJoinRequest(joinRequest.requestId)
        } else {
            const index = this.fullRequestJoins.findIndex(it => it.requestId === joinRequest.requestId);
            if (index >= 0) {
                this.fullRequestJoins[index].deleted = true;
                this.actionView.addNotification(-1);

                // update lastRequestId
                if (index === 0) {
                    for (let i = 0; i < this.fullRequestJoins.length; ++i) {
                        if (!this.fullRequestJoins[i].deleted) {
                            ee.ServiceLocator.resolve(ChatManager).lastRequestId = this.fullRequestJoins[i].requestId;
                            break;
                        }
                    }
                }
            }
        }
    }

    private newJoinRequest(joinRequest: RequestJoin) {
        const myRole = this.myTeamManager.getMyRole();
        if (myRole === ROLE.MEMBER) {
            return;
        }
        joinRequest.new = true;
        this.chatManager.lastRequestId = joinRequest.requestId;
        this.fullRequestJoins.unshift(joinRequest);
        this.requestJoins.unshift(joinRequest);

        if (this.joinRequestDialog) {
            // Đang mở danh sách Join Request => cập nhật lại UI
            this.joinRequestDialog.updateRequestJoinsView();
        } else {
            // Đang đóng danh sách Join Request => cập nhật notìfy nút mail
            this.actionView.addNotification(1);
        }
    }

    private viewMyTeamProfile(): void {
        this.listener.onViewMyTeamProfile && this.listener.onViewMyTeamProfile();
    }

    private async viewJoinRequests(): Promise<void> {
        this.joinRequestDialog = await JoinRequestDialog.create();
        this.joinRequestDialog.listener = {
            onReject: this.reject.bind(this),
            onAccept: this.accept.bind(this),
            onAcceptAll: this.acceptAll.bind(this),
            onAppendMore: this.appendMore.bind(this)
        }
        this.joinRequestDialog
            .onWillShow(() => {
                this.chatManager.lastRequestId = this.fullRequestJoins.length > 0 ? this.fullRequestJoins[0].requestId : undefined;
                this.actionView.setNotification(0);
                this.joinRequestDialog.hideMailNotification();

                this.workRequestJoins = [...this.fullRequestJoins];
                this.requestJoins = this.workRequestJoins.splice(0, 5);
                this.joinRequestDialog && this.joinRequestDialog.setRequestList(this.requestJoins, this.workRequestJoins);
            })
            .onDidHide(() => {
                this.fullRequestJoins.forEach((request) => {
                    request.new = false;
                })
                this.joinRequestDialog = null;
            })

        this.joinRequestDialog.show(ee.ServiceLocator.resolve(DialogManager));
    }

    private closeJoinRequestDialog() {
        if (this.joinRequestDialog) {
            this.joinRequestDialog.hide();
            this.joinRequestDialog = null;
        }
    }

    private appendMore() {
        if (!this.joinRequestDialog) {
            return;
        }
        const appendList = this.workRequestJoins.splice(0, 5);
        this.requestJoins.push(...appendList);
        this.joinRequestDialog.updateRequestJoinsView();
        this.scheduleOnce(()=>{
            if (this.joinRequestDialog) {
                this.joinRequestDialog.isLoadingNew = false;
            }
        })
    }

    private reject(requestId: string): void {
        this.chatNetworkManager.rejectJoinRequest(requestId).then((result)=> {
            if (result) {
                if (this.joinRequestDialog) {
                    this.joinRequestDialog.removeJoinRequest(requestId)
                }
            }
        })
    }

    private accept(requestId: string): void {
        const teamSize = this.myTeamManager.getTeamInfo().teamSize;
        const teamMax = 30;
        const capacity = teamMax - teamSize;

        if (capacity <= 0) {
            this.joinRequestDialog && this.joinRequestDialog.showFloatingText("team_not_enough_spots");
            return;
        }

        this.chatNetworkManager.acceptJoinRequest(requestId).then((result)=> {
            if (result) {
                this.joinRequestDialog && this.joinRequestDialog.removeJoinRequest(requestId)
            }
        })
    }

    private acceptAll(): void {
        const requestCount = this.joinRequestDialog ? this.joinRequestDialog.getRequestCount() : 0;
        if (requestCount === 0) {
            return;
        }

        const teamSize = this.myTeamManager.getTeamInfo().teamSize;
        const teamMax = 30;
        const capacity = teamMax - teamSize;

        if (capacity < requestCount) {
            this.joinRequestDialog && this.joinRequestDialog.showFloatingText("team_not_enough_spots");
            return;
        }

        const teamId = this.myTeamManager.getTeamInfo().teamId;
        this.chatNetworkManager.acceptAllJoinRequest(teamId).then((result) => {
            if (result) {
                this.joinRequestDialog && this.joinRequestDialog.removeAllRequests()
            }
        })
    }

    private response(id: string): void {
        this.chatNetworkManager.response(id, this.chatNetworkManager.userId);
    }

    private showUserInfo(userId: string): void {
        this.listener.onShowUserInfo && this.listener.onShowUserInfo(userId);
    }

    private checkInternet() {
        checkInternetConnection((hasInternet) => {
            this.noInternetLayer.active = !hasInternet
        });
    }
}
