import * as ee from "../../libraries/ee/index";
import {DialogManager} from "../../libraries/ee/index";
import EditMessageDialog from "./EditMessageDialog";
import BadgeView from "../BadgeView";
import {TeamInfo} from "../../scenes/team/TeamLayer";
import {MyTeamManager, ROLE} from "../../manager/team/MyTeamManager";
import {NotificationView} from "../../scenes/common/NotificationView";

const {ccclass, property} = cc._decorator;

export interface chatActionListener {
    /** Occurs when Send Request. */
    onRequest?(): void;

    /** Occurs when Send Message. */
    onMessage?(message: string): void;

    /** Occurs when click on View Team button */
    onViewMyTeamProfile?(): void;

    /** Occurs when click on Mail button */
    onViewJoinRequests?(): void;
}


@ccclass
export default class ActionView extends cc.Component {

    @property(BadgeView)
    private badge: BadgeView = null;

    @property(cc.Label)
    private lblName: cc.Label = null;

    @property(cc.Button)
    private buttonRequest: cc.Button = null;

    @property(cc.Button)
    private buttonMail: cc.Button = null;

    @property(NotificationView)
    private readonly mailNotification: NotificationView = null;

    public listener: chatActionListener;

    private myTeamManager: MyTeamManager;
    private editMessageDialog: EditMessageDialog = null;

    protected onLoad() {
        this.myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
    }

    protected onEnable() {
        this.setTeamInfo(this.myTeamManager.getTeamInfo());
        this.updateMyRole(this.myTeamManager.isClosed(), this.myTeamManager.getMyRole());
        this.hideNotification();
    }

    public setTeamInfo(teamProfile: TeamInfo) {
        this.lblName.string = teamProfile.teamName;
        this.badge.badgeID = teamProfile.avatar;
    }

    public updateMyRole(teamIsClosed: boolean, myRole: ROLE) {
        this.buttonMail.node.active = teamIsClosed && myRole !== ROLE.MEMBER
    }

    public hideNotification(): void {
        this.mailNotification.node.active = false;
    }

    public setNotification(quantity: number) {
        this.mailNotification.setValue(quantity);
        this.mailNotification.node.active = quantity > 0;
    }

    public addNotification(quantity: number) {
        this.mailNotification.addValue(quantity);
        this.mailNotification.node.active = this.mailNotification.value > 0;
    }

    public onViewMyTeamProfileButtonClicked(): void {
        this.listener.onViewMyTeamProfile && this.listener.onViewMyTeamProfile();
    }

    public onMailButtonClicked(): void {
        this.listener.onViewJoinRequests && this.listener.onViewJoinRequests();
    }

    public onRequestButtonClicked() {
        this.buttonRequest.interactable = false;
        this.listener.onRequest && this.listener.onRequest();
    }

    public onMessageButtonClicked() {
        this.editMessage();
    }

    public closeEditMessageDialog(): void {
        if (this.editMessageDialog) {
            this.editMessageDialog.hide();
            this.editMessageDialog = null;
        }
    }

    private async editMessage() {
        this.editMessageDialog = await EditMessageDialog.create();
        this.editMessageDialog.setCallback((message) => {
            this.listener.onMessage?.(message);
        })
        this.editMessageDialog.show(ee.ServiceLocator.resolve(DialogManager));
        this.editMessageDialog.onDidHide(()=>{
            this.editMessageDialog = null;
        })
    }
}
