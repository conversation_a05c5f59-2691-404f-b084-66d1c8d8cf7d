import * as ee from "../../../libraries/ee/index";
import {IRefreshable} from "../../../manager/IRefreshable";

export enum Key {
    Data = `chat_data`
}

/** Chat Message Server */
export interface RequestMessageEntity {
    userRequest: string,
    helper: string[],
}

export interface ChatMessageEntity {
    sender: string,
    content: string,
}

export interface NotifyData {
    userId?: string,
    teamId?: string,
    leaderId?: string,
}

export interface NotificationMessageEntity {
    type: 'ROOM_CREATED' | `USER_JOIN` | `USER_LEAVE` | `KICK_USER` |
          `PROMOTION_TO_CO_LEADER` | `PROMOTION_TO_LEADER` | `DEMOTION_TO_MEMBER`
    data: string
}

/** Room */
export interface ChatRoomEntity {
    id: number,
    title: string,
    creator: string,
    createAt: number,
    updateAt: number,
    otherCreator?: string
    firstMessageId: number,
    lastMessageId: number
}

export interface MessageEntity {
    id: number,
    roomId: number,
    type: `TEXT` | `REQUEST` | `NOTIFY`,
    notificationMessage?: NotificationMessageEntity,
    requestMessage?: RequestMessageEntity,
    chatMessage?: ChatMessageEntity,
    createAt: number
}

export interface RoomPreview {
    room: ChatRoomEntity,
    lastMessage: MessageEntity,
    lastReadMessageId?: number
}

//** Request */
export interface RemainTimeRequest {
    remainingTimeSeconds: number;
}

//** Request Join */
export interface RequestJoin {
    requestId: string,
    teamId: string,
    userId: string,
    status: `PENDING` | `APPROVED` | `REJECTED` | `CANCELLED` | `EXPIRED`;
    new: boolean
    deleted: boolean
}

/** chat local */
export interface ChatConfig {
    maxEnergy: number; // số energy nhận đủ cho 1 request
    energyGiven: number; // số energy tặng cho 1 lần response request
    rubyHelper: number; // số ruby helper được nhận
}

export interface ChatData {
    messages: ChatMessage[];
    lastRequestId: string;
}

export enum ChatMessageType {
    message = "message",
    request = "request",
    system = "system",
}

export interface ChatMessage {
    id: string;
    senderId: string;
    content: string;
    type: ChatMessageType;
    timestamp: number;
    updatedAt: number;
    isSelf: boolean;
    deleted: boolean

    helperId?: string;
    responseCount?: number;
    responseUserId?: string[];
}

export interface chatManagerListener {
    onNewMessage?(message: ChatMessage): void;
    onMessageUpdated?(message: ChatMessage, subType: string, amount: number): void;
    onSystemMessage?(message: ChatMessage): void;
    onUpdateJoinRequest?:(joinRequest: RequestJoin) => void;
    onNewJoinRequest?:(joinRequest: RequestJoin) => void;
}

@ee.service('ChatManager')
export abstract class ChatManager implements ee.Service, IRefreshable {
    public abstract userId: string;
    public abstract refreshData(): void;
    public abstract destroy(): void;

    public abstract energyGiven: number;
    public abstract maxEnergy: number;
    public abstract requestTime: number;
    public abstract lastRequestId: string;

    public abstract setTimeForNextRequest(remainTimeSeconds: number): void;
    public abstract canSendRequest(): boolean;
    public abstract getRemainString(): string;
    public abstract getRequestId(): string;
    public abstract syncRequestEnergy(requestId: string, helpers: string[]): void;
    public abstract syncLatestMessages(newMessage: ChatMessage[]): void;
    public abstract syncOlderMessages(newMessage: ChatMessage[]): void;
    public abstract getMessages(): ChatMessage[];

    public abstract listener: chatManagerListener;

    public abstract addMessage(msg: ChatMessage): void;
    public abstract addSystemMessage(msg: ChatMessage): void;
    public abstract updateMessage(partial: Partial<ChatMessage> & { id: string }): boolean;

}
