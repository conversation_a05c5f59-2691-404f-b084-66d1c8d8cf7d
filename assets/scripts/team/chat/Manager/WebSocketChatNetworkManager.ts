import {chatNetworkListener, Chat<PERSON>workManager} from "./ChatNetworkManager";
import {
    Chat<PERSON>anager,
    ChatMessage,
    ChatMessageType,
    MessageEntity,
    NotificationMessageEntity,
    NotifyData,
    RemainTimeRequest,
    RequestJoin,
    RoomPreview
} from "./ChatManager";
import {Commands, GameServerManager, TeamInfoResponse} from "../../../manager/game_server/GameServerManager";
import {ChatUserCacheManager, ChatUserInfo} from "./ChatUserCacheManager";
import {LanguageManager} from "../../../libraries/ee/index";

export const ChatCommands = {
    /** Chat */
    GET_CHAT_ROOM_PREVIEW: `chat/get-chat-room-preview`,
    GET_CHAT: `chat/get-chat`,
    SEND_CHAT_TEXT: `chat/send-chat-text`,
    SEND_REQUEST_ENERGY: `chat/send-request-energy`,
    HELP_ENERGY: `teams/help-energy-request`,
    GET_USER_INFO: `get-short-info`,
    GET_ENERGY_HELPERS: `teams/energy-helpers`,
    GET_TIME_FOR_NEXT_REQUEST: `teams/time-for-next-request`,
    RESET_TIME_FOR_NEXT_REQUEST: `cheat/chat/reset-time-for-next-request`,
    GET_TEAM_REQUESTS: `teams/join-close/team-requests`,
    REJECT_JOIN_REQUEST: `teams/join-close/reject`,
    ACCEPT_JOIN_REQUEST: `teams/join-close/accept`,
    ACCEPT_ALL_JOIN_REQUEST: `teams/join-close/accept-all`,

};

export default class WebSocketChatNetworkManager extends ChatNetworkManager {

    public listener: chatNetworkListener;

    private _userId = "local-user";
    private _roomPreview: RoomPreview;

    public constructor(
        private readonly uuid: string,
        private readonly gameServer: GameServerManager,
        private readonly chatManager: ChatManager,
        private readonly userCacheManager: ChatUserCacheManager
        ) {
        super();
    }

    public get userId() {
        return this._userId
    }

    public connect(): void {
        this.gameServer.getUId().then((id: string) => {
            this._userId = id;
            this.chatManager.userId = id;

            this.gameServer.registerBroadcastMessageListener(Commands.BROADCAST_NEW_MESSAGE, this.uuid, this.handleNewMessage.bind(this));
            this.gameServer.registerBroadcastMessageListener(Commands.BROADCAST_UPDATED_MESSAGE, this.uuid, this.handleUpdatedMessage.bind(this));
            this.gameServer.registerBroadcastMessageListener(Commands.BROADCAST_UPDATE_JOIN_REQUEST, this.uuid, this.handleUpdateJoinRequest.bind(this));
            this.gameServer.registerBroadcastMessageListener(Commands.BROADCAST_NEW_JOIN_REQUEST, this.uuid, this.handleNewJoinRequest.bind(this));
            this.gameServer.registerBroadcastMessageListener(Commands.BROADCAST_UPDATE_TEAM_INFO, this.uuid, this.handleUpdateTeamInfo.bind(this));
        })
        this.loadTimeForNextRequest().then();
    }

    public clearRoomPreview(): void {
        this._roomPreview = null;
    }

    public destroy(): void {
        this.gameServer.unregisterBroadcastMessageListener(Commands.BROADCAST_NEW_MESSAGE, this.uuid);
        this.gameServer.unregisterBroadcastMessageListener(Commands.BROADCAST_UPDATED_MESSAGE, this.uuid);
        this.gameServer.unregisterBroadcastMessageListener(Commands.BROADCAST_UPDATE_JOIN_REQUEST, this.uuid);
        this.gameServer.unregisterBroadcastMessageListener(Commands.BROADCAST_NEW_JOIN_REQUEST, this.uuid);
        this.gameServer.unregisterBroadcastMessageListener(Commands.BROADCAST_UPDATE_TEAM_INFO, this.uuid);
    }

    private handleNewMessage(message: string): void {
        const msg = JSON.parse(message).msg;
        let isJoinOrLeftMe = false;
        this.convertMessageEntityToChatMassage(msg).then(async (chatMessage: ChatMessage) => {
            if (chatMessage.type === ChatMessageType.system) {
                const notificationMessage = msg.notificationMessage;

                const kickMeOff = await this.isKickMeOffNotify(notificationMessage)
                if (kickMeOff) {
                    this.clearRoomPreview();
                    this.listener?.onKickoff && this.listener?.onKickoff();
                    return;
                }

                const isDemoted = await this.isDemoteMe(notificationMessage);
                if (isDemoted) {
                    this.listener?.onDemoted && this.listener?.onDemoted();
                }

                const isPromoted = await this.isPromoteMe(notificationMessage);
                if (isPromoted) {
                    this.listener?.onPromoted && this.listener?.onPromoted();
                }

                isJoinOrLeftMe = await this.isJoinOrLeftMe(notificationMessage)
            }

            if (chatMessage.type === ChatMessageType.request) {
                if (chatMessage.senderId === this.userId) {
                    this.loadTimeForNextRequest().then();
                }
            }

            // Nếu là thông báo bản thân vào/ra team thi không báo. (tránh nhá dòng này do reset khi mở chat)
            if (!isJoinOrLeftMe) {
                this.chatManager.addMessage(chatMessage);
            }
        })
    }

    private handleUpdatedMessage(message: string): void {
        const msg = JSON.parse(message) as MessageEntity;
        this.convertMessageEntityToChatMassage(msg).then(async (chatMessage: ChatMessage) => {
            const updated = this.chatManager.updateMessage({
                id: chatMessage.id,
                helperId: chatMessage.helperId,
                updatedAt: Date.now(),
                responseCount: chatMessage.responseCount,
                responseUserId: chatMessage.responseUserId
            });
            if (updated) {
                const helper = await this.getUserInfo(chatMessage.helperId);
                const receiver = await this.getUserInfo(chatMessage.senderId);

                const languageManager = LanguageManager.getInstance()
                const options: any = {};
                options["0"] = helper.userName;
                options["1"] = receiver.userName;
                const content = languageManager.parseFormat("chat_send_energy", options);
                this.sendSystemMessage(content);
            }
        })
    }

    private handleUpdateJoinRequest(message: string): void {
        const joinRequest = JSON.parse(message) as RequestJoin;
        this.chatManager.listener.onUpdateJoinRequest && this.chatManager.listener.onUpdateJoinRequest(joinRequest);
    }

    private handleNewJoinRequest(message: string): void {
        const joinRequest = JSON.parse(message) as RequestJoin;
        this.chatManager.listener.onNewJoinRequest && this.chatManager.listener.onNewJoinRequest(joinRequest);
    }

    private handleUpdateTeamInfo(message: string): void {
        const teamInfo = JSON.parse(message) as TeamInfoResponse;
        this.listener.onUpdateTeamInfo && this.listener.onUpdateTeamInfo(teamInfo);
    }

    public async fetchRequestMessage(requestId: number[]): Promise<string[]> {
        const dataJson = JSON.stringify({
            requestIds: requestId
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.GET_ENERGY_HELPERS, dataJson)
        const obj = JSON.parse(res) as Record<string, string[]>;
        const helpers = new Map<string, string[]>(Object.entries(obj));
        if (helpers && helpers.has(requestId[0].toString())) {
            const result = helpers.get(requestId[0].toString());
            return result;
        }
        return []
    }

    public async fetchUsers(usersIds: string[]): Promise<ChatUserInfo[]> {
        const dataJson = JSON.stringify({
            usersIds: usersIds
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.GET_USER_INFO, dataJson)
        const obj = JSON.parse(res) as Record<string, ChatUserInfo>;
        const userMap = new Map<string, ChatUserInfo>(Object.entries(obj));
        return  Array.from(userMap?.values() ?? []);
    }

    public async getUserInfo(userId: string): Promise<ChatUserInfo> {
        // get user info from cache
        let user = await this.userCacheManager.getUserCache(userId);
        if (user) {
            return user;
        }

        // fetch user info from server
        const dataJson = JSON.stringify({
            usersIds: [userId],
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.GET_USER_INFO, dataJson)
        const obj = JSON.parse(res) as Record<string, ChatUserInfo>;
        const userMap = new Map<string, ChatUserInfo>(Object.entries(obj));
        const userList = Array.from(userMap?.values() ?? []);
        for (const it of userList) {
            if (it && it.id === userId) {
                user = it;
                break;
            }
        }

        if (user) {
            this.userCacheManager.cacheUser(user);
            return user;
        }

        console.warn(`userId: ${userId} - res: ${res} => unknown`)
        // return unknown user
        return {
            id: userId,
            userName: "unknown",
            avatar: "",
            frame: ""
        }
    }

    public async sendMessage(msg: Partial<ChatMessage> & { content: string }): Promise<void> {
        const roomPreview = await this.getRoomPreview();
        const dataJson = JSON.stringify({
            roomId: roomPreview.room.id,
            message: msg.content
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.SEND_CHAT_TEXT, dataJson)
        //this.chatManager.addMessage(this.createSendingMessages(msg.content));
    }

    public async sendRequest(): Promise<void> {
        const roomPreview = await this.getRoomPreview();
        const dataJson = JSON.stringify({
            roomId: roomPreview.room.id,
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.SEND_REQUEST_ENERGY, dataJson)
        //this.chatManager.addMessage(this.createSendingRequest());
    }

    public sendSystemMessage(message: string): void {
        const msg: ChatMessage = {
            id: `msg-${Date.now()}`,
            senderId: `system`,
            content: message,
            type: ChatMessageType.system,
            timestamp: Date.now(),
            updatedAt: Date.now(),
            isSelf: false,
            deleted: false
        };
        this.chatManager.addSystemMessage(msg);
    }

    public async acceptJoinRequest(requestId: string): Promise<boolean> {
        const roomPreview = await this.getRoomPreview();
        if (!roomPreview) {
            return false
        }
        const dataJson = JSON.stringify({
            requestId: requestId,
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.ACCEPT_JOIN_REQUEST, dataJson);
        return res != null;
    }

    public async rejectJoinRequest(requestId: string): Promise<boolean> {
        const roomPreview = await this.getRoomPreview();
        if (!roomPreview) {
            return false
        }
        const dataJson = JSON.stringify({
            requestId: requestId,
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.REJECT_JOIN_REQUEST, dataJson);
        return res != null;
    }

    public async acceptAllJoinRequest(teamId: string): Promise<boolean> {
        const roomPreview = await this.getRoomPreview();
        if (!roomPreview) {
            return false
        }
        const dataJson = JSON.stringify({
            teamId: teamId
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.ACCEPT_ALL_JOIN_REQUEST, dataJson);
        return res != null;
    }

    public async response(messageId: string, userId: string): Promise<void> {
        const dataJson = JSON.stringify({
            requestId: messageId,
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.HELP_ENERGY, dataJson)
    }

    private sleep(ms: number) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    public async loadTimeForNextRequest(): Promise<void> {
        let remainTimeSeconds= 0;
        const res = await this.gameServer.sendChatCommand(ChatCommands.GET_TIME_FOR_NEXT_REQUEST);
        if (res) {
            const remainTimeRequest = JSON.parse(res) as RemainTimeRequest
            remainTimeSeconds =  remainTimeRequest.remainingTimeSeconds
        }
        this.chatManager.setTimeForNextRequest(remainTimeSeconds);
    }

    public async resetTimeForNextRequest(): Promise<void> {
        await this.gameServer.sendChatCommand(ChatCommands.RESET_TIME_FOR_NEXT_REQUEST);
        this.chatManager.setTimeForNextRequest(0);
    }

    public async getRoomPreview(): Promise<RoomPreview> {
        if (this._roomPreview == null) {
            let attempt = 0;
            let resRoom: string | null = null;
            while (attempt < 3) {
                resRoom = await this.gameServer.sendChatCommand(ChatCommands.GET_CHAT_ROOM_PREVIEW);
                if (resRoom) {
                    const roomPreviews = JSON.parse(resRoom);
                    this._roomPreview = roomPreviews[0];
                    break;
                }
                attempt++;
                if (attempt < 3) {
                    await this.sleep(1000);
                }
            }
        }
        if (this._roomPreview == null) {
            console.warn("room preview not found");
            this.clearRoomPreview();
            this.listener?.onKickoff && this.listener?.onKickoff();
        }
        return this._roomPreview ?? null;
    }

    public async fetchLastMessages(): Promise<ChatMessage[]> {
        const roomPreview = await this.getRoomPreview();
        if (!roomPreview) {
            return []
        }
        const dataJson = JSON.stringify({
            roomId: roomPreview.room.id,
        })
        return await this.fetchMessages(dataJson);
    }

    public async fetchMessagesFrom(fromMessageId: string, direction: number): Promise<ChatMessage[]> {
        const roomPreview = await this.getRoomPreview();
        if (!roomPreview) {
            return []
        }
        const dataJson = JSON.stringify({
            roomId: roomPreview.room.id,
            option: {
                fromMessageId: fromMessageId,
                direction: direction
            }
        })
        return await this.fetchMessages(dataJson);
    }

    private async fetchMessages(dataJson: string): Promise<ChatMessage[]> {
        const res = await this.gameServer.sendChatCommand(ChatCommands.GET_CHAT, dataJson);
        if (!res) {
            console.warn("chat: get chat error");
            return []
        }
        const messages = JSON.parse(res) as MessageEntity[];

        await this.updateUserCache(messages);

        const chatMessages = [];
        for (const message of messages) {
            const chatMessage = await this.convertMessageEntityToChatMassage(message)
            chatMessages.push(chatMessage);
        }
        return chatMessages;
    }

    public async fetchTeamRequests(teamId: string): Promise<RequestJoin[]> {
        const roomPreview = await this.getRoomPreview();
        if (!roomPreview) {
            return []
        }
        const dataJson = JSON.stringify({
            teamId: teamId
        })
        const res = await this.gameServer.sendChatCommand(ChatCommands.GET_TEAM_REQUESTS, dataJson);
        if (!res) {
            console.warn("chat: get team requests error");
            return []
        }
        return JSON.parse(res) as RequestJoin[];
    }

    private async convertMessageEntityToChatMassage(msg: MessageEntity): Promise<ChatMessage> {
        let sender = ``;
        let content = ``;
        let type = ChatMessageType.message;
        let helper = [];
        switch (msg.type) {
            case `TEXT`:
                sender = msg.chatMessage.sender;
                content = msg.chatMessage.content;
                type = ChatMessageType.message;
                break;
            case "REQUEST":
                sender = msg.requestMessage.userRequest;
                type = ChatMessageType.request;
                helper = msg.requestMessage.helper;
                break;
            case "NOTIFY":
                const [userId, contentKey] = await this.getNotifyContent(msg.notificationMessage);
                sender = userId;
                content = contentKey;
                type = ChatMessageType.system;
                break;
        }
        return {
            id: `${msg.id}`,
            senderId: sender,
            content: content,
            type: type,
            timestamp: msg.createAt,
            helperId: helper.length > 0 ? helper[helper.length-1] : undefined,
            responseCount: (helper.length),
            responseUserId: helper,
            updatedAt: msg.createAt,
            isSelf: sender === this._userId,
            deleted: false
        };
    }

    private async updateUserCache(messages: MessageEntity[]) {
        if (!messages || messages.length === 0) {
            return;
        }
        const userIdList = Array.from(
            messages.reduce((set, message) => {
                if (message.type === "TEXT") {
                    set.add(message.chatMessage.sender);
                } else if (message.type === "REQUEST") {
                    set.add(message.requestMessage.userRequest);
                }
                return set;
            }, new Set<string>())
        );
        const newUserIdList = userIdList.filter(userId => !this.userCacheManager.has(userId));
        if (newUserIdList && newUserIdList.length > 0) {
            const userList = await this.fetchUsers(newUserIdList);
            if (userList != null && userList.length > 0) {
                for (const user of userList) {
                    if (user) {
                        this.userCacheManager.cacheUser(user);
                    }
                }
            }
        }
    }

    private async isKickMeOffNotify(notify: NotificationMessageEntity): Promise<boolean> {
        if (notify.type === `KICK_USER`) {
            const notifyData = JSON.parse(notify.data) as NotifyData
            return notifyData.userId === this._userId;
        }
        return false;
    }

    private async isDemoteMe(notify: NotificationMessageEntity): Promise<boolean> {
        if (notify.type === `DEMOTION_TO_MEMBER`) {
            const notifyData = JSON.parse(notify.data) as NotifyData
            return notifyData.userId === this._userId;
        }
        return false;
    }

    private async isPromoteMe(notify: NotificationMessageEntity): Promise<boolean> {
        if (notify.type === `PROMOTION_TO_CO_LEADER` || notify.type === `PROMOTION_TO_LEADER`) {
            const notifyData = JSON.parse(notify.data) as NotifyData
            return notifyData.userId === this._userId;
        }
        return false;
    }

    private async isJoinOrLeftMe(notify: NotificationMessageEntity): Promise<boolean> {
        if (notify.type === `USER_JOIN` || notify.type === `USER_LEAVE` || notify.type === `KICK_USER`) {
            const notifyData = JSON.parse(notify.data) as NotifyData
            return notifyData.userId === this._userId;
        }
        return false;
    }

    private async getNotifyContent(notify: NotificationMessageEntity): Promise<[string, string]> {
        const notifyData = JSON.parse(notify.data) as NotifyData
        let content = "";
        switch (notify.type) {
            case `USER_JOIN`:
                content = `chat_had_join`
                break;
            case `USER_LEAVE`:
                content = `chat_had_left`
                break;
            case `KICK_USER`:
                content = `chat_was_remove`
                break;
            case `PROMOTION_TO_CO_LEADER`:
                content = 'chat_had_promoted_co_leader'
                break;
            case `DEMOTION_TO_MEMBER`:
                content = `chat_has_demoted`;
                break;
            case `PROMOTION_TO_LEADER`:
                content = `chat_had_promoted_leader`
                break;
            default:
                content = `${notify.type}`
        }
        return [notifyData.userId, content];
    }

    private createSendingMessages(content: string): ChatMessage {
        return {
            id: `${Date.now()}`,
            senderId: this._userId,
            content: content,
            type: ChatMessageType.message,
            timestamp: 0,
            responseCount: 0,
            responseUserId: [],
            updatedAt: 0,
            isSelf: true,
            deleted: false
        };
    }

    private createSendingRequest(): ChatMessage {
        return {
            id: `${Date.now()}`,
            senderId: this._userId,
            content: ``,
            type: ChatMessageType.request,
            timestamp: 0,
            responseCount: 0,
            responseUserId: [],
            updatedAt: 0,
            isSelf: true,
            deleted: false
        };
    }
}
