import * as ee from "../../../libraries/ee/index";
import {IRefreshable} from "../../../manager/IRefreshable";
import {ChatMessage, NotificationMessageEntity, RequestJoin, RoomPreview} from "./ChatManager";
import {ChatUserInfo} from "./ChatUserCacheManager";
import {TeamInfoResponse} from "../../../manager/game_server/GameServerManager";

export interface chatNetworkListener {
    onKickoff?: () => void;
    onDemoted?: () => void;
    onPromoted?: () => void;
    onUpdateTeamInfo?: (teamInfo: TeamInfoResponse) => void;
}

@ee.service('ChatNetworkManager')
export abstract class ChatNetworkManager implements ee.Service, IRefreshable {
    refreshData(): void {
    }

    public abstract destroy(): void;

    public abstract listener: chatNetworkListener;
    public abstract userId: string;
    public abstract connect(): void;
    public abstract loadTimeForNextRequest(): Promise<void>
    public abstract resetTimeForNextRequest(): Promise<void>;
    public abstract getRoomPreview(): Promise<RoomPreview>
    public abstract clearRoomPreview(): void;
    public abstract getUserInfo(userId: string): Promise<ChatUserInfo>;
    public abstract sendMessage(msg: Partial<ChatMessage> & {content: string}): void;
    public abstract sendRequest(): void
    public abstract response(messageId: string, userId: string): void;
    public abstract sendSystemMessage(message: string): void;

    public abstract acceptJoinRequest(requestId: string): Promise<boolean>;
    public abstract rejectJoinRequest(requestId: string): Promise<boolean>;
    public abstract acceptAllJoinRequest(teamId: string): Promise<boolean>;

    public abstract fetchRequestMessage(requestId: number[]): Promise<string[]>;
    public abstract fetchUsers(usersIds: string[]): Promise<ChatUserInfo[]>;
    public abstract fetchLastMessages(): Promise<ChatMessage[]>;
    public abstract fetchMessagesFrom(fromMessageId: string, direction: number): Promise<ChatMessage[]>;
    public abstract fetchTeamRequests(teamId: string): Promise<RequestJoin[]>;

}

