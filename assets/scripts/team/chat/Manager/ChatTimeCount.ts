import * as ee from '../../../libraries/ee/index';
import {TimeUtils} from "../../../utils/TimeUtis";
import {ChatMessage} from "./ChatManager";

const {ccclass, property} = cc._decorator;

@ccclass
export default class ChatTimeCount extends cc.Component {

    @property({type: ee.LanguageComponent, visible: true})
    private readonly messageTime: ee.LanguageComponent | null = null;

    private _chatMessage: ChatMessage | null = null;
    public set chatMessage(value: ChatMessage) {
        this._chatMessage = value;
    }

    protected onEnable() {
        this.updateTimeText(0);
        this.schedule(this.updateTimeText, 1);
    }

    protected onDisable() {
        this.unschedule(this.updateTimeText);
    }

    protected updateTimeText(dt: number) {
        if (this._chatMessage) {
            if (this._chatMessage.timestamp === 0) {
                this.unschedule(this.updateTimeText);
                this.messageTime.key = "txt_send"
                return;
            }
            TimeUtils.formatTimeAgo(this.messageTime, this._chatMessage.timestamp);
        }
    }
}
