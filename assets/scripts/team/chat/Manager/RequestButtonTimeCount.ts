import * as gm from '../../../engine/gm_engine';
import {ChatManager} from "./ChatManager";
import * as ee from "../../../libraries/ee/index";

const {ccclass, property} = cc._decorator;

@ccclass
export default class RequestButtonTimCount extends cc.Component {

    @property({ type: cc.Button, visible: true })
    private readonly requestButton: cc.Button | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly remainTime: cc.Node | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly remainText: cc.Label | null = null;

    private chatManager: ChatManager;

    protected onLoad() {
        this.chatManager = ee.ServiceLocator.resolve(ChatManager);
    }

    protected onEnable() {
        this.updateRemainText(0);
        this.schedule(this.updateRemainText, 1);
    }

    protected onDisable() {
        this.unschedule(this.updateRemainText);
    }

    private updateRemainText(dt: number) {
        if (this.chatManager.requestTime == 0) {
            this.remainTime.active = false;
            this.requestButton.interactable = true;
            return;
        }

        if (this.chatManager.canSendRequest()) {
            this.chatManager.setTimeForNextRequest(0);
            this.remainTime.active = false;
            this.requestButton.interactable = true;
            return;
        }

        this.requestButton.interactable = false;
        this.remainText.string = this.chatManager.getRemainString();
        this.remainTime.active = true;
    }
}
