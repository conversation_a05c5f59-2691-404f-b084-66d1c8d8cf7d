import { CommonDialog } from "../../dialog/CommonDialog";
import * as ee from '../../libraries/ee/index';
import * as gm from "../../engine/gm_engine";
import {
    CrashlyticManager,
    crashlytics,
    PvpTicketManager,
    SceneName,
    StoreItem,
    StoreManager,
    TrackingManager,
    TrackResultIap,
    TrackResultSoftCurrency,
    TrackResultWatch,
    TrackSinkType,
    TrackSourceType,
} from "../../manager/gm_manager";
import { EffectHelper } from '../../utils/EffectHelper';
import { PrefabUtils } from "../../utils/PrefabUtils";

interface PvpTicketListener {
    moreRuby(): void;
    buyPvpTicket(): void;
}

const { ccclass, disallowMultiple, property } = cc._decorator;

@ccclass
@disallowMultiple
export class MorePvpTicketDialog extends CommonDialog {
    public static create(): Promise<MorePvpTicketDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/more_currency_dialog/more_pvp_ticket_dialog');
    }

    @property({ type: cc.Button, visible: true })
    private readonly _refillNowButton: cc.Button | null = null;

    private get refillNowButton(): cc.Button {
        if (this._refillNowButton === null) {
            throw Error('Item not registered');
        }
        return this._refillNowButton;
    }

    @property({ type: cc.Label, visible: true })
    private readonly _labelRubyNumber: cc.Label | null = null;

    private get labelRubyNumber(): cc.Label {
        if (this._labelRubyNumber === null) {
            throw Error('Item not registered');
        }
        return this._labelRubyNumber;
    }

    @property({ type: cc.Label, visible: true })
    private readonly _labelPvpTicketNumberByRuby: cc.Label | null = null;

    private get labelPvpTicketNumberByRuby(): cc.Label {
        if (this._labelPvpTicketNumberByRuby === null) {
            throw Error('Item not registered');
        }
        return this._labelPvpTicketNumberByRuby;
    }

    @property({ type: cc.Label, visible: true })
    private _rubyLeftQuantity: cc.Label | null = null;

    private get rubyLeftQuantity(): cc.Label {
        if (this._rubyLeftQuantity === null) {
            throw Error('Item not registered');
        }
        return this._rubyLeftQuantity;
    }

    @property({ type: cc.Label, visible: true })
    private _pvpTicketLeftQuantity: cc.Label | null = null;

    

    

    private get pvpTicketLeftQuantity(): cc.Label {
        if (this._pvpTicketLeftQuantity === null) {
            throw Error('Item not registered');
        }
        return this._pvpTicketLeftQuantity;
    }

    private listener: PvpTicketListener = {
        moreRuby: () => { },
        buyPvpTicket: () => { },
    };

    private _pvpTicketManager?: PvpTicketManager;
    private canClick = true;

    private get pvpTicketManager(): PvpTicketManager {
        if (!this._pvpTicketManager) {
            this._pvpTicketManager = ee.ServiceLocator.resolve(PvpTicketManager);
        }
        return this._pvpTicketManager;
    }

    @crashlytics
    protected onLoad(): void {
        super.onLoad();
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);

        const storeManager = ee.ServiceLocator.resolve(StoreManager);
        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });
        this.pvpTicketLeftQuantity.string = `${this.pvpTicketManager.getAvailable()}`;
        this.rubyLeftQuantity.string = `${storeManager.getItemBalance(StoreItem.Ruby)}`;
        this.labelPvpTicketNumberByRuby.string = `${this.pvpTicketManager.getMaxPvpTicket()}`;
        this.setRubyNumber(this.pvpTicketManager.getRefillPrice());
        this.canClick = true;
        
    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    @crashlytics
    private pressRefillPvpTicketByRuby(): void {
        if (!this.canClick) {
            return;
        }
        this.canClick = false;

        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.pressRefillPvpTicketByRuby, `${this.uuid}`);

        const winSize = cc.winSize;
        const dstNode = new cc.Node();
        dstNode.position = new cc.Vec2(winSize.width * 4 / 5, winSize.height);
        const storeManager = ee.ServiceLocator.resolve(StoreManager);
        const price = this.pvpTicketManager.getRefillPrice();

        const trackingMgr = ee.ServiceLocator.resolve(TrackingManager);
        trackingMgr.trackEventClick(SceneName.DialogNotEnoughPvpTicket, 'btn_refill_by_ruby');

        const pvpTicketCnt = this.pvpTicketManager.getMaxPvpTicket();
        if (storeManager.getItemBalance(StoreItem.Ruby) >= price) {
            storeManager.addItemBalance(StoreItem.Ruby, -price);
            EffectHelper.showFlyingPvpTicketAnimation(this.refillNowButton.node, dstNode,
                this.pvpTicketManager.getMaxPvpTicket()).then(() => {
                    this.pvpTicketManager.givePvpTicket(pvpTicketCnt).then(() => {
                        this.listener.buyPvpTicket && this.listener.buyPvpTicket();
                        this.hide();
                    });
                });

            trackingMgr.trackEventSourceSoftCurrency(SceneName.DialogNotEnoughPvpTicket,
                TrackResultSoftCurrency.Bought, TrackSourceType.PvpTicket, pvpTicketCnt);
            trackingMgr.trackEventSinkIap(SceneName.DialogNotEnoughPvpTicket,
                TrackResultIap.Bought, TrackSinkType.Ruby, 'more_pvp_ticket_dialog_refill', price);
        } else {
            this.canClick = true;
            this.listener.buyPvpTicket && this.listener.moreRuby();
            trackingMgr.trackEventSourceSoftCurrency(SceneName.DialogNotEnoughPvpTicket,
                TrackResultSoftCurrency.Error, TrackSourceType.PvpTicket, pvpTicketCnt);
        }
    }

    public setListener(listener: PvpTicketListener): void {
        this.listener = listener;
    }

    public setRubyNumber(rubyNumber: number): void {
        this.labelRubyNumber.string = `${rubyNumber}`;
    }
}
