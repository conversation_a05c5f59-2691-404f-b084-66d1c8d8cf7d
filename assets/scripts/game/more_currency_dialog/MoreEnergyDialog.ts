import {CommonDialog} from '../../dialog/CommonDialog';
import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {
    AudioManager,
    CrashlyticManager,
    crashlytics,
    SoundType,
} from "../../manager/gm_manager";
import {PrefabUtils} from "../../utils/PrefabUtils";
import {IapShopCategory} from "../../scenes/IapShopCategory";
import {MenuController, MenuScene} from "../../scenes/MenuScene";

const findMenuController = () => {
    const scene = cc.director.getScene();
    const menu = scene.getComponentInChildren(MenuScene);
    if (menu !== null) {
        return menu.controller;
    }
    return undefined;
};

type MoreRubyCallBack = () => void;

const {ccclass, disallowMultiple} = cc._decorator;

@ccclass
@disallowMultiple
export class MoreEnergyDialog extends CommonDialog {
    public static create(): Promise<MoreEnergyDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/more_currency_dialog/more_energy_dialog');
    }

    private _controller?: MenuController;
    private callback?: MoreRubyCallBack;

    public get controller(): MenuController {
        return gm.retrieveUndefined(this._controller);
    }

    public set controller(value: MenuController) {
        this._controller = value;
    }

    @crashlytics
    protected onLoad(): void {
        super.onLoad();
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });

    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    public setMoreRubyCallback(callback: MoreRubyCallBack): void {
        this.callback = callback;
    }

    /** Registered in editor. */
    private onShopButtonPressed(): void {
        if (!this.isActive()) {
            return;
        }
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        const controller = this._controller || findMenuController();
        controller && this.onDidHide(() => {
            controller.showShopCategory(IapShopCategory.Energy);
        });
        this.hide();
    }

}
