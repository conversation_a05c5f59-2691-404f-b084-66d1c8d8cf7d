import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import { StoreItem } from '../../manager/gm_manager';

const { ccclass, disallowMultiple, property } = cc._decorator;

export abstract class StoreItemView extends cc.Component {
    /** Gets or sets the store item type. */
    public abstract type: StoreItem;
}

const nameToType: { [key: string]: StoreItem | undefined } = {
    ['icon_gold']: StoreItem.Gold,
    ['icon_ruby']: StoreItem.Ruby,
};

@ccclass
@disallowMultiple
class StoreItemViewImpl extends StoreItemView {
    @property({ type: cc.Sprite, visible: true })
    private readonly _iconSprite: cc.Sprite | null = null;

    @property([cc.SpriteFrame])
    private readonly spriteFrames: cc.SpriteFrame[] = [];

    private get iconSprite(): cc.Sprite {
        return gm.retrieveNull(this._iconSprite);
    }

    private _type = StoreItem.Gold;
    private isDirty = false;

    /** Maps store item type to sprite frame. */
    private typeToSpriteFrame: { [key: string]: cc.SpriteFrame | null } = {};

    protected onLoad(): void {
        for (let i = 0, n = this.spriteFrames.length; i < n; ++i) {
            const frame = this.spriteFrames[i];
            const type = nameToType[frame.name];
            if (type === undefined) {
                assert(false);
                continue;
            }
            this.typeToSpriteFrame[type] = frame;
        }

        this.updateDisplay();
    }

    protected update(delta: number): void {
        if (this.isDirty) {
            this.isDirty = false;
            this.updateDisplay();
        }
    }

    public get type(): StoreItem {
        return this._type;
    }

    public set type(value: StoreItem) {
        if (this._type !== value) {
            this._type = value;
            this.isDirty = true;
        }
    }

    private updateDisplay(): void {
        const frame = this.typeToSpriteFrame[this.type];
        frame && (this.iconSprite.spriteFrame = frame);
    }
}
