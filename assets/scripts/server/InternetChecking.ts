import {AlertDialog} from "../dialog/AlertDialog";
import {Dialog, DialogManager} from "../libraries/ee/index";
import * as ee from '../libraries/ee/index';
declare const CC_JSB: boolean;

//// Hàm này báo lỗi "The response type isn't supported!" trên Android
// export async function internetAvailable(): Promise<boolean> {
//     try {
//         if (!CC_JSB) {
//             return navigator.onLine;
//         }
//
//         const response = await fetch('https://www.google.com/favicon.ico', {
//             method: 'HEAD',
//             cache: 'no-cache',
//         });
//         return response.ok;
//     } catch (error) {
//         return false;
//     }
// }

export async function checkInternetAndShowDialog(): Promise<boolean> {
    //  if (await internetAvailable() === false) {
    //     const dialog = await AlertDialog.create()
    //     dialog.setKey("no_internet_notify")
    //     dialog.show(ee.ServiceLocator.resolve(DialogManager))
    //     return false
    // }
    // return true;

    return await new Promise<boolean>(resolve => {
        checkInternetConnection(async (hasInternet) => {
            if (hasInternet) {
                resolve(true);
                return;
            }
            const dialog = await AlertDialog.create()
            dialog.setKey("no_internet_notify")
            dialog.show(ee.ServiceLocator.resolve(DialogManager))
            resolve(false);
        });
    });
}

export function checkInternetConnection(callback: { (hasInternet: boolean): void }, timeOut: number = 3000) {
    if (!CC_JSB) {
        callback(navigator.onLine);
        return;
    }
    const xhr = new XMLHttpRequest();
    xhr.open("GET", "https://www.google.com", true);
    xhr.timeout = timeOut; //
    xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
            callback(true); // Internet is available
        } else {
            callback(false); // HTTP error
        }
    };
    xhr.onerror = () => callback(false); // Network error
    xhr.ontimeout = () => callback(false); // Request timed out
    xhr.send();
}



