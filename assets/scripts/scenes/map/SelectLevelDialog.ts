import {CommonDialog} from "../../dialog/CommonDialog";
import {NoAdsDialog} from '../../dialog/NoAdsDialog';
import * as gm from '../../engine/gm_engine';
import {BoosterPanel} from '../../game/booster_panel/BoosterPanel';
import * as ee from "../../libraries/ee/index";
import {DialogManager} from "../../libraries/ee/index";
import {
    AreaInfo,
    AudioManager,
    BoosterStorageManager,
    CrashlyticManager,
    crashlytics,
    DataManager,
    EnergyManager,
    FeatureManager,
    GameMode,
    LevelInfo,
    LevelManager,
    SceneName,
    SoundType,
    StoreItem,
    TrackAdsRewardWatch,
    TrackingManager,
    TrackResultSoftCurrency,
    TrackSourceType,
    UserProfileManager,
    WatchAdsHelper,
} from '../../manager/gm_manager';
import {EffectHelper} from '../../utils/EffectHelper';
import {LevelLabel} from '../common/LevelLabel';
import {StarBar} from "../common/StarBar";
import {LevelTargetView} from '../game_scene/LevelTargetView';
import {MenuController} from '../MenuScene';
import {SceneUtils} from "../../manager/scene/SceneUtils";
import {BoosterPanelImpl} from "../../game/booster_panel/BoosterPanelImpl";
import {PrefabUtils} from "../../utils/PrefabUtils";
import {NotEnoughCurrencyDialog} from "../../game/more_currency_dialog/NotEnoughCurrencyDialog";
import {MoreCurrencyUtils} from "../../game/more_currency_dialog/MoreCurrencyUtils";
import {OfferManager} from "../../manager/offer/OfferManager";
import {OfferDialog} from "../../dialog/OfferDialog";


const {ccclass, disallowMultiple, property} = cc._decorator;

export abstract class SelectLevelDialog extends CommonDialog {

    public static create(): Promise<SelectLevelDialog> {
        const path = `prefabs/select_level_dialog/select_level_dialog`;
        return PrefabUtils.createPrefab(this, path);
    }

    /** Sets the controller. */
    public abstract setController(controller: MenuController): void;

    //
    // public abstract get closeButton(): CloseDialogButton;

    /** Sets the level info. */
    public abstract setInfo(info: {
        areaInfo: AreaInfo,
        levelInfo: LevelInfo,
        fromHome: boolean,
    }): void;

    public abstract setTryAgain(tryAgain: boolean): void;
}


@ccclass
@disallowMultiple
class SelectLevelDialogImpl extends SelectLevelDialog {
    @ee.nest(StarBar)
    private readonly _starBar: StarBar | null = null;

    /** Displays the current level. */
    @ee.nest(LevelLabel)
    private readonly _levelLabel: LevelLabel | null = null;

    @ee.nest(LevelLabel)
    private levelTryAgainLabel: LevelLabel | null = null;


    /** Displays the level score targets. */
    @ee.nest(LevelTargetView)
    private readonly _levelTarget: LevelTargetView | null = null;

    /** Displays the booster selection panel. */
    @property({type: BoosterPanelImpl, visible: true})
    private readonly _boosterPanel: BoosterPanelImpl | null = null;

    /** Displays energy cost label. */
    @property({type: cc.Label, visible: true})
    private readonly _energyLabel: cc.Label | null = null;

    @property({type: cc.Toggle, visible: false})
    private readonly _recordButton: cc.Toggle | null = null;

    /** Plays the level by consuming energy. */
    @property(cc.Button)
    private playButton: cc.Button | null = null;

    /** Displayed when not enough energy to play. */
    @property({type: cc.Node, visible: true})
    private readonly _adsLayer: cc.Node | null = null;

    /** Plays the level by watching an ads. */
    @property({type: cc.Button, visible: true})
    private readonly _playAdsButton: cc.Button | null = null;

    @property({type: cc.Label, visible: true})
    private readonly _playAdsLabel: cc.Label | null = null;

    @property([cc.Node])
    private UIWin: cc.Node[] = [];

    @property([cc.Node])
    private UITryAgain: cc.Node[] = [];

    @property({type: OfferDialog, visible: true})
    private _boosterOffer: OfferDialog = null;

    private get starBar(): StarBar {
        return gm.retrieveNull(this._starBar);
    }

    private get levelLabel(): LevelLabel {
        return gm.retrieveNull(this._levelLabel);
    }

    private get levelTarget(): LevelTargetView {
        return gm.retrieveNull(this._levelTarget);
    }

    private get boosterPanel(): BoosterPanel {
        return gm.retrieveNull(this._boosterPanel);
    }

    private get energyLabel(): cc.Label {
        return gm.retrieveNull(this._energyLabel);
    }

    private get adsLayer(): cc.Node {
        return gm.retrieveNull(this._adsLayer);
    }

    private get playAdsButton(): cc.Button {
        return gm.retrieveNull(this._playAdsButton);
    }

    private isInitialized = false;

    /** Current controller. */
    private controller: MenuController = {} as MenuController;

    /** Current info. */
    private area: number;
    private level = 0;

    private _areaInfo?: AreaInfo;
    private _levelInfo?: LevelInfo;

    private isEvent: boolean;

    /** Select Level Dialog được gọi từ HomeLayer ? */
    private _fromHome: boolean = false;

    private get areaInfo(): AreaInfo {
        // return this._areaInfo || (this._areaInfo = (() => {
        //     const levelManager = ee.ServiceLocator.resolve(LevelManager);
        //     return this.isEvent
        //         ? levelManager.getEventAreaInfo(this.area, this._isCharacterEvent)
        //         : levelManager.getStoryAreaInfo(this.area);
        // })());
        return this._areaInfo;
    }

    private get levelInfo(): LevelInfo {
        return this._levelInfo;
    }

    private isPlayLevelTutorial: boolean = false;

    /** Whether or not to record gameplay. */
    private isRecordingEnabled = false;

    /** Is locked due to nested dialog. */
    private isLocked = false;

    private levelManager: LevelManager;

    private get clickable(): boolean {
        return this.isActive() && !this.isLocked;
    }

    @crashlytics
    protected onLoad(): void {
        super.onLoad();

        this.levelManager = ee.ServiceLocator.resolve(LevelManager);
        this.levelManager.isLoadingGameScene = false;

        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        ee.ServiceLocator.resolve(TrackingManager).trackEventOpen(SceneName.DialogSelectLevel);

        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });

        this.schedule(this.updateEnergy.bind(this), 1);

        this.onDidShow(() => {
            // Requirements: isEvent + area + level must be set.
            if (this.isEvent) {
                return;
            }
            const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
            const count = profileManager.getProfileInfo().getStoryLevelPlayCount(this.levelInfo.worldIndex);
            if (count === 0) {
                this.boosterPanel.showBoosterPopUpInfoAtUnlockedLevelFirstTime(
                    this.areaInfo.index, this.levelInfo.index);
            }

            // auto active booster pickup if unlocked
            this.boosterPanel.autoActiveUnlockedBooster(this.areaInfo.index, this.levelInfo.index, gm.BoosterType.PickUp);
        });
        this.boosterPanel.setSceneName(SceneName.DialogSelectLevel);

        this.updateInfo();
        this.updateEnergy();
        this.updateRecordButton();
        this.setTouchOutsideEnabled(false);
        this.isInitialized = true;
    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
        this.unschedule(this.updateEnergy.bind(this));
    }

    public setController(controller: MenuController): void {
        this.controller = controller;
    }

    public setInfo(options: {
        areaInfo: AreaInfo,
        levelInfo: LevelInfo,
        fromHome: boolean,
    }): void {
        this._fromHome = options.fromHome;
        this._areaInfo = options.areaInfo;
        this._levelInfo = options.levelInfo;
        this.area = options.areaInfo.index;
        this.level = options.levelInfo.index;
        this.isEvent = this._areaInfo.areaType === GameMode.EVENT || this._areaInfo.areaType === GameMode.CHARACTER;
        if (this.isInitialized) {
            this.updateInfo();
            this.updateEnergy();
        }
    }

    private updateInfo(): void {
        this.levelLabel.level = this.levelInfo.worldIndex;
        this.levelTryAgainLabel.level = this.levelInfo.worldIndex;
        this.levelTarget.target = this.levelInfo.targets[0];
        this.levelTarget.yourHighScore = this.levelInfo.score;
        ee.ServiceLocator.resolve(LevelManager).getHighestScore(this._areaInfo, this._levelInfo).then(score => {
            this.levelTarget.serverHighScore = score > this.levelInfo.score ? score : this.levelInfo.score;
        });
        this.starBar.stars = this.levelInfo.highestStar;

        // Update boosters.
        const boosterStorageManager = ee.ServiceLocator.resolve(BoosterStorageManager);
        if (this.isEvent) {
            this.boosterPanel.boosters = boosterStorageManager.getEventBoosters();
        } else {
            const levelManager = ee.ServiceLocator.resolve(LevelManager);
            const lastUnlockedArea = levelManager.getLastUnlockedArea();
            const info = levelManager.getStoryAreaInfo(lastUnlockedArea);
            this.boosterPanel.boosters = boosterStorageManager.getStoryBoosters(
                this.area, this.level, lastUnlockedArea, info.unlockedLevels - 1);
        }

        // Update energy display.
        const energyManager = ee.ServiceLocator.resolve(EnergyManager);
        const energyCost = this.isEvent
            ? energyManager.getEventEnergyCost(this.area)
            : energyManager.getStoryEnergyCost(this.area);
        this.energyLabel.string = `${energyCost}`;
        this._playAdsLabel.string = `${energyManager.getRefillWatchAdsCount()}`;
    }

    private updateEnergy(): void {
        const energyManager = ee.ServiceLocator.resolve(EnergyManager);
        const energyCost = this.isEvent
            ? energyManager.getEventEnergyCost(this.area)
            : energyManager.getStoryEnergyCost(this.area);
        const hasEnergy = energyManager.getAvailable() >= energyCost;
        const isUnlimited = energyManager.getUnlimitedDuration() > 0;
        const hasEnoughEnergy = hasEnergy || isUnlimited;
        this.playButton.node.active = hasEnoughEnergy;
        this.adsLayer.active = !hasEnoughEnergy;
    }

    /** Registered in editor. */
    @crashlytics
    private onRecordButtonPressed(): void {
        // cmt to fix noti 
        // if (!this.clickable) {
        //     return;
        // }
        // if (this.isPlayLevelTutorial) {
        //     return;
        // }
        // ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onRecordButtonPressed, `${this.uuid}`);
        // ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        // if (this.isRecordingEnabled) {
        //     this.isRecordingEnabled = false;
        // } else {
        //     // No need to check permissions.
        //     // const recorderManager = ee.ServiceLocator.resolve(RecorderManager);
        //     // if (recorderManager.checkRecordingPermission()) {
        //     this.isRecordingEnabled = true;
        //     // }
        // }
        // this.updateRecordButton();

        // const btnName = 'btn_record_' + this.isRecordingEnabled ? 'on' : 'off';
        // ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.DialogSelectLevel, btnName);
    }

    private updateRecordButton(): void {
        // const recorderManager = ee.ServiceLocator.resolve(RecorderManager);
        // if (recorderManager.isSupported()) {
        //     this.recordButton.node.active = true;
        // } else {
        //     this.recordButton.node.active = false;
        //     this.recordButton.isChecked = this.isRecordingEnabled;
        // }
    }

    /** Registered in editor. */
    /** Registered in editor. */
    @crashlytics
    private onPlayButtonPressed(): void {
        if (!this.clickable) {
            return;
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onPlayButtonPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        let eventName = "-"
        if (this._areaInfo.areaType === GameMode.EVENT) {
            eventName = "_7_deadly_sins_";
        } else if (this.areaInfo.areaType === GameMode.CHARACTER) {
            eventName = "_jaki_";
        }
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.DialogSelectLevel, `btn_play${eventName}level_${this.levelInfo.worldIndex + 1}`);

        const energyManager = ee.ServiceLocator.resolve(EnergyManager);
        const isUnlimited = energyManager.getUnlimitedDuration() > 0;
        if (!isUnlimited) {
            energyManager.consumeEnergy(this.area, SceneName.DialogSelectLevel, this.isEvent);
        }

        const boosters = this.boosterPanel.getSelectedBoosters();
        const areaInfo = this.areaInfo;
        const levelInfo = this.levelInfo;
        this.onDidHide(() => {
            if (this.isEvent) {
                // Only play music if area changes:
                // FIX: E/libOpenSLES: frameworks/wilhelm/src/android/AudioPlayer_to_android.cpp:886:
                // pthread_mutex_lock_timeout_np returned 110
                const dict: { [key: number]: number } = {
                    [0]: SoundType.BgMenuReverse,
                    [1]: SoundType.HawaiiReverse,
                    [2]: SoundType.Australia,
                    [3]: SoundType.VegasReverse,
                    [4]: SoundType.Arizona,
                    [5]: SoundType.ArizonaReverse,
                    [6]: SoundType.AustraliaReverse,
                };
                const soundName = dict[areaInfo.index];
                ee.ServiceLocator.resolve(AudioManager).playMusic(soundName);
            } else {
                const area = Math.floor(areaInfo.index / 3);
                const dict: { [key: number]: number } = {
                    [0]: SoundType.Australia,
                    [1]: SoundType.Hawaii,
                    [2]: SoundType.Vegas,
                    [3]: SoundType.Arizona,
                };
                const soundName = dict[area];
                ee.ServiceLocator.resolve(AudioManager).playMusic(soundName);
            }

            this.levelManager.isLoadingGameScene = true;
            SceneUtils.loadGameScene(areaInfo, levelInfo, boosters, this.isRecordingEnabled, this._fromHome).then();
        });
        this.hide();
    }

    /** Registered in editor. */
    @crashlytics
    private onPlayAdsButtonPressed(): void {
        if (!this.clickable) {
            return;
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onPlayAdsButtonPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        this.isLocked = true;
        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
        trackingManager.trackEventClick(SceneName.DialogSelectLevel, 'btn_refill_energy_by_ads');

        (new WatchAdsHelper(SceneName.DialogSelectLevel, TrackAdsRewardWatch.EnergyBonusStory)).onSuccess(() => {
            const winSize = cc.winSize;
            const dstNode = new cc.Node("effect_dst");
            dstNode.position = new cc.Vec3(winSize.width * 4 / 5, winSize.height, 0);

            const energyManager = ee.ServiceLocator.resolve(EnergyManager);
            this.isLocked = false;
            const refillCount = energyManager.getRefillWatchAdsCount();
            EffectHelper.showFlyingEnergyAnimation(this.playAdsButton.node, dstNode,
                energyManager.getRefillWatchAdsCount()).then(() => {
                energyManager.giveEnergy(energyManager.getRefillWatchAdsCount());
                //this.hide();
            });
            trackingManager.trackEventSourceSoftCurrency(SceneName.DialogSelectLevel,
                TrackResultSoftCurrency.Bought, TrackSourceType.Energy, refillCount);
        }).onNoAds(() => {
            this.showNoAdsDialog();
            this.isLocked = false;
        }).start();
    }

    /** Registered in editor. */
    @crashlytics
    private onMoreEnergyButtonPressed(): void {
        if (!this.clickable) {
            return;
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onMoreEnergyButtonPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        let dialogManager = ee.ServiceLocator.resolve(DialogManager);

        this.isLocked = true;
        if (ee.ServiceLocator.resolve(FeatureManager).moreCurrencyIsPack) { // Done
            MoreCurrencyUtils.show({
                dialogManager: dialogManager,
                sceneName: SceneName.DialogStoryBooster,
                storeItem: StoreItem.Energy,
                needed: 3
            }).then(() => this.isLocked = false);
        } else {
            NotEnoughCurrencyDialog.create().then(dialog => {
                dialog.setInfo(StoreItem.Energy, null);
                dialog.onDidHide(() => {
                    this.isLocked = false;
                })
                dialog.show(dialogManager);
            })
        }

        // MoreEnergyDialog.create().then((dialog) => {
        //     dialog.setMoreRubyCallback(() => {
        //         MoreRubyDialog.create().then((moreRubyDialog) => {
        //             dialog.showDialog(moreRubyDialog);
        //         });
        //     });
        //     dialog.onDidHide(() => {
        //         this.updateEnergy();
        //         this.isLocked = false;
        //     });
        //     this.showDialog(dialog);
        // });
    }

    public getPlayButton(): cc.Button {
        return this.playButton;
    }

    public setIsPlayTutorialEnabled(enabled: boolean): this {
        this.isPlayLevelTutorial = enabled;
        if (this.isPlayLevelTutorial) {
            this.setTouchOutsideEnabled(false);
        }
        return this;
    }

    private showNoAdsDialog(): void {
        NoAdsDialog.create().then((dialog) => {
            this.showDialog(dialog);
        });
    }

    //
    // public get closeButton(): CloseDialogButton {
    //     return this._closeDialogButton;
    // }

    setTryAgain(tryAgain: boolean): void {
        this.UITryAgain.forEach(items => items.active = tryAgain);
        tryAgain && ee.ServiceLocator.resolve(OfferManager).setupOfferDialog(this._boosterOffer)
        this.UIWin.forEach(items => items.active = !tryAgain);
    }
}
