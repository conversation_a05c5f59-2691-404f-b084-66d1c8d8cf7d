import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {
    AudioManager,
    SoundType,
} from '../../manager/gm_manager';
import { UserAvatarView } from '../leaderboard/UserAvatarView';

const { ccclass, disallowMultiple, property } = cc._decorator;

interface Listener {
    onPressed?(sender: LevelMarker): void;
}

@ccclass
@disallowMultiple
export class LevelMarker extends cc.Component {
    @property({ type: cc.Sprite, visible: true })
    private _background: cc.Sprite | null = null;

    @ee.nest(UserAvatarView)
    private _avatarView: UserAvatarView | null = null;

    private _isBackgroundActive = true;
    private _isBackgroundFlipped = false;

    public listener: Listener = {};

    public get isBackgroundActive(): boolean {
        return this._isBackgroundActive;
    }

    public set isBackgroundActive(value: boolean) {
        if (this._isBackgroundActive !== value) {
            this._isBackgroundActive = value;
            this.background.node.opacity = value ? 255 : 127;
        }
    }

    public get isBackgroundFlipped(): boolean {
        return this._isBackgroundFlipped;
    }

    public set isBackgroundFlipped(value: boolean) {
        if (this._isBackgroundFlipped !== value) {
            this._isBackgroundFlipped = value;
            this.background.node.scaleY = value ? -1 : 1;
        }
    }

    private get background(): cc.Sprite {
        return gm.retrieveNull(this._background);
    }

    private get avatarView(): UserAvatarView {
        return gm.retrieveNull(this._avatarView);
    }

    public loadAvatar(url: string): Promise<void> {
        return this.avatarView.loadItem(url);
    }

    private onPressed(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        this.listener.onPressed && this.listener.onPressed(this);
    }
}