import assert = require('assert');
import * as ee from '../../libraries/ee/index';
import {
    AreaInfo,
    GameMode,
    LevelInfo,
    LevelManager,
    RewardUtils,
    SceneName,
    UserProfileManager,
} from "../../manager/gm_manager";
import {TopHud} from '../common/TopHud';
import {AreaCompleteDialog} from '../game_scene/dialogs/AreaCompleteDialog';
import {MenuController} from "../MenuScene";
import {SelectLevelDialog} from "./SelectLevelDialog";

export class MapUtils {
    public static findNextStoryLevel(
        levelManager: LevelManager,
        area: number,
        level: number): [number, number] {
        const info = levelManager.getStoryAreaInfo(area);
        let [nextArea, nextLevel] = [area, level];
        if (nextLevel + 1 >= info.levels.length) {
            // Complete the last level of the current area.
            if (nextArea + 1 >= levelManager.getStoryAreas()) {
                // Complete the last area.
            } else {
                ++nextArea;
                nextLevel = 0;
            }
        } else {
            ++nextLevel;
        }
        return [nextArea, nextLevel];
    }

    public static findNextEventLevel(
        levelManager: LevelManager,
        area: number,
        level: number
    ): number {
        const info = levelManager.getEventAreaInfo(area);
        let nextLevel = level;
        if (nextLevel + 1 >= info.levels.length) {
            // Complete the last level.
        } else {
            ++nextLevel;
        }
        return nextLevel;
    }

    public static findNextCharacterLevel(
        levelManager: LevelManager,
        level: number
    ): number {
        const info = levelManager.getCharacterAreaInfo();
        let nextLevel = level;
        if (nextLevel + 1 >= info.levels.length) {
            // Complete the last level.
        } else {
            ++nextLevel;
        }
        return nextLevel;
    }

    public static async showSelectLevelDialog(
        prefab: cc.Prefab,
        dialogManager: ee.DialogManager,
        controller: MenuController,
        areaInfo: AreaInfo,
        levelInfo: LevelInfo,
        fromHome: boolean = false,
    ): Promise<void> {
        const dialog = cc.instantiate(prefab).getComponent(SelectLevelDialog);
        dialog.setController(controller);
        dialog.setInfo({
            areaInfo: areaInfo,
            levelInfo: levelInfo,
            fromHome: fromHome
        });
        dialog.show(dialogManager);
        await new Promise<void>(resolve => dialog.onDidHide(() => {
            resolve();
        }));
    }

    public static async showAreaCompleteDialog(
        dialogManager: ee.DialogManager,
        levelManager: LevelManager,
        profileManager: UserProfileManager,
        topHud: TopHud,
        area: number,
        areaType: GameMode): Promise<void> {
        // Area completed dialog.
        const gold = levelManager.getGoldForArea(area);
        let xp = 0;
        if (areaType === GameMode.STORY) xp = levelManager.getExpForStoryArea(area);
        if (areaType === GameMode.EVENT) xp = levelManager.getExpForEventArea(area);
        if (areaType === GameMode.CHARACTER) xp = levelManager.getExpForCharacterArea(area);
        const dialog = await AreaCompleteDialog.create();
        dialog.setAreaType(areaType)
            .setArea(area)
            .setGold(gold)
            .setXP(xp)
            .setTopHud(topHud)
            .buildDialog()
            .show(dialogManager);
        await new Promise<void>(resolve => dialog.onWillHide(() => resolve()));
        await Promise.all([
            dialog.collectAllRewards(),
            new Promise<void>(resolve => dialog.onDidHide(() => resolve())),
        ]);
        // Chest dialog.
        let info: AreaInfo;
        if (areaType === GameMode.STORY) info = levelManager.getStoryAreaInfo(area);
        if (areaType === GameMode.EVENT) info = levelManager.getEventAreaInfo(area);
        if (areaType === GameMode.CHARACTER) info = levelManager.getCharacterAreaInfo();
        const chestDialogs = await RewardUtils.openChestDialog([info.reward]);
        await new Promise<void>(resolve => chestDialogs[0].onDidHide(() => resolve()));

        // Level up dialog.
        const _dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        assert(dialogManager === _dialogManager);
        profileManager.addExp(xp, SceneName.DialogStoryAreaClear);
        //await profileManager.processExp(dialogManager);
    }
}