import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import * as ee from "../../libraries/ee/index";
import {
    AreaInfo,
    AudioManager,
    GameMode,
    LeaderboardManager,
    LevelManager,
    SceneName,
    SocialManager,
    SoundType,
    TrackingManager,
    UserProfileManager,
} from "../../manager/gm_manager";
import {TopHud} from "../common/TopHud";
import {GameClearDialog} from '../game_scene/dialogs/GameClearDialog';
import {UserAvatarView} from '../leaderboard/UserAvatarView';
import {MenuController} from "../MenuScene";
import {AreaListView} from './AreaListView';
import {AreaPageView} from './AreaPageView';
import {LevelMarker} from './LevelMarker';
import {MapUtils} from './MapUtils';
import {PlayButton} from "../../ui/PlayButton";
import AreaPageView_v2 from "./AreaPageView_v2";
import {BottomHud} from "../common/BottomHud";

const {ccclass, disallowMultiple, property} = cc._decorator;

export abstract class MapLayer extends cc.Component {

    /** Dùng để animate event reward bay vào nút */
    public abstract blockMenu(value: boolean): void;
    public abstract eventButtonContainer: cc.Node;
    public abstract topButtonContainer: cc.Node;
    public abstract bottomButtonContainer: cc.Node;
    public abstract hiddenTempleButton: cc.Node;
    public abstract piggyBankButton: cc.Node;
    public abstract playPassButton: cc.Node;

    /** Gets or sets the menu controller. */
    public abstract controller: MenuController;

    /** Gets or sets the dialog manager. */
    public abstract dialogManager: ee.DialogManager;

    /** Gets or sets the associated top hud. */
    public abstract topHud: TopHud;

    public abstract bottomHud: BottomHud;

    public abstract init(): void;

    /**
     * Finishes the specified area and level.
     * Usually called after finishing a level.
     */
    public abstract handleDialogs(options?: {
        area?: number,
        areaInfo?: AreaInfo,
        level?: number,
        animateMarker?: boolean,
        goToNextLevel?: boolean,
        selectLevel?: boolean,
    }): Promise<void>;

    /**
     * Scrolls to the specified area and level.
     * Usually called when navigating from the home scene.
     */
    public abstract scrollToLevel(options?: {
        /** Desired area, default is the last unlocked area. */
        area?: number,

        /** Desired level, default is the last unlocked level. */
        level?: number,

        /** Whether to show select dialog or not. */
        selectLevel?: boolean,

        markerLevel?: number,

        duration?: number,
    }): Promise<void>;

    public abstract showSelectLevelDialog(area: number, level: number): Promise<void>;
}

/** Used in editor. */
@ccclass
@disallowMultiple
class MapLayerImpl extends MapLayer {
    @ee.nest(AreaListView)
    private readonly _areaListView: AreaListView | null = null;

    @property({type: AreaPageView_v2, visible: true})
    private readonly _areaPageView: AreaPageView | null = null;

    @ee.nest(LevelMarker)
    private readonly _backwardMarker: LevelMarker | null = null;

    @property(PlayButton)
    private playButton: PlayButton = null;

    @ee.nest(LevelMarker)
    private readonly _forwardMarker: LevelMarker | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _selectLevelDialogPrefab: cc.Prefab | null = null;

    @property({ type: cc.BlockInputEvents, visible: true })
    private readonly _blocker: cc.BlockInputEvents | null = null;
    @property({type: cc.Node, visible: true})
    private readonly _eventButtonContainer: cc.Node | null = null;
    @property({type: cc.Node, visible: true})
    private readonly _topButtonContainer: cc.Node | null = null;
    @property({type: cc.Node, visible: true})
    private readonly _bottomButtonContainer: cc.Node | null = null;
    @property({type: cc.Node, visible: true})
    private readonly _hiddenTempleButton: cc.Node | null = null;
    @property({type: cc.Node, visible: true})
    private readonly _piggyBankButton: cc.Node | null = null;
    @property({type: cc.Node, visible: true})
    private readonly _playPassButton: cc.Node | null = null;

    private get areaListView(): AreaListView {
        return gm.retrieveNull(this._areaListView);
    }

    private get areaPageView(): AreaPageView {
        return gm.retrieveNull(this._areaPageView);
    }

    private get backwardMarker(): LevelMarker {
        return gm.retrieveNull(this._backwardMarker);
    }

    private get forwardMarker(): LevelMarker {
        return gm.retrieveNull(this._forwardMarker);
    }

    private get selectLevelDialogPrefab(): cc.Prefab {
        return gm.retrieveNull(this._selectLevelDialogPrefab);
    }

    private get blocker(): cc.BlockInputEvents {
        return gm.retrieveNull(this._blocker);
    }
    public get eventButtonContainer(): cc.Node {
        return gm.retrieveNull(this._eventButtonContainer);
    }
    public get topButtonContainer(): cc.Node {
        return gm.retrieveNull(this._topButtonContainer);
    }
    public get bottomButtonContainer(): cc.Node {
        return gm.retrieveNull(this._bottomButtonContainer);
    }
    public get hiddenTempleButton(): cc.Node {
        return gm.retrieveNull(this._hiddenTempleButton);
    }
    public get piggyBankButton(): cc.Node {
        return gm.retrieveNull(this._piggyBankButton);
    }
    public get playPassButton(): cc.Node {
        return gm.retrieveNull(this._playPassButton);
    }

    public blockMenu(value: boolean) {
        this.blocker.enabled = value;
    }

    private _controller?: MenuController;

    public get controller(): MenuController {
        return gm.retrieveUndefined(this._controller);
    }

    public set controller(value: MenuController) {
        this._controller = value;
    }

    private _dialogManager?: ee.DialogManager;

    public get dialogManager(): ee.DialogManager {
        return gm.retrieveUndefined(this._dialogManager);
    }

    public set dialogManager(value: ee.DialogManager) {
        this._dialogManager = value;
    }

    private _topHud: TopHud | null = null;

    public get topHud(): TopHud {
        return gm.retrieveNull(this._topHud);
    }

    public set topHud(value: TopHud) {
        this._topHud = value;
    }

    private _bottomHud: BottomHud | null = null;
    public get bottomHud(): BottomHud {
        return gm.retrieveNull(this._bottomHud);
    }

    public set bottomHud(value: BottomHud) {
        this._bottomHud = value;
    }

    private currentArea = -1;

    protected onLoad(): void {

        let levelManager = ee.ServiceLocator.resolve(LevelManager);

        const level = levelManager.getUnlockedStoryLevelsCount();
        this.playButton.setNextLevel_v2(level);
        this.playButton.setPressedCallback(() => {
            ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
            ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.SceneStoryMap, `btn_play_level_${level}`)

            let lastUnlockedStoryArea = levelManager.getLastUnlockedArea();
            let lastUnlockedStoryLevel = levelManager.getStoryLastUnlockedLevel(lastUnlockedStoryArea);

            this.showSelectLevelDialog(lastUnlockedStoryArea, lastUnlockedStoryLevel).then();
        })
    }

    public init(): void {
        ee.AsyncManager.getInstance().add(MapLayerImpl.name, async () => {
            await this.initialize();
            this.blocker.enabled = false;
        });
    }

    /** Asynchronously initializes. */
    private async initialize(): Promise<void> {
        assert(this._areaListView !== null);
        assert(this._areaPageView !== null);
        this.initializeAreaList();
        this.initializeAreaPage();
        this.initializeMarkers();
    }

    /** Initializes the area list view. */
    private initializeAreaList(): void {
        this.areaListView.init();
        this.areaListView.listener = {
            select: (sender, area, level) => {
                this.showSelectLevelDialog(area, level);
            },
            markerStateChanged: (sender, isOnScreen, isForward) => {
                this.backwardMarker.node.active = !isOnScreen && !isForward;
                this.forwardMarker.node.active = !isOnScreen && isForward;
            },
        };
    }

    /** Initializes the area page view. */
    private initializeAreaPage(): void {

        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const areas = levelManager.getStoryAreas();
        const infos: AreaInfo[] = [];
        for (let i = 0; i < areas; ++i) {
            infos.push(levelManager.getStoryAreaInfo(i));
        }
        this.areaPageView.setInfo(infos);
        this.areaPageView.listener = {
            select: async (sender, index, interact) => {
                if (interact) {
                    //this.controller.setAllButtonEnabled(false);
                    await this.scrollToLevelInstantly(index, 0, 0);
                    await this.showMarker(index);
                    this.updateNavigateMarkers(index);
                    //this.controller.setAllButtonEnabled(true);

                    // In case user quit after completing the last level of this area.
                    await this.handleDialogsForArea(index);
                }
            },
        };
        this.areaPageView.init(this.topHud, this.bottomHud);
    }

    private initializeMarkers(): void {
        const markers = [
            this.backwardMarker,
            this.forwardMarker,
        ];
        const socialManager = ee.ServiceLocator.resolve(SocialManager);
        if (socialManager.isLoggedIn) {
            markers.forEach(item => item.loadAvatar(socialManager.user.picture));
        } else {
            markers.forEach(item => item.loadAvatar(UserAvatarView.DEFAULT_AVATAR_URL));
        }

        this.backwardMarker.isBackgroundFlipped = false;
        this.forwardMarker.isBackgroundFlipped = true;

        markers.forEach(item => {
            item.isBackgroundActive = true;
            item.listener = {
                onPressed: sender => {
                    this.scrollToLevel({
                        duration: 0.3,
                    });
                },
            };
        });

        this.backwardMarker.node.active = false;
        this.forwardMarker.node.active = false;
    }

    public async handleDialogs(options?: {
        area?: number,
        areaInfo?: AreaInfo,
        level?: number,
        animateMarker?: boolean,
    }): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        if (!options.areaInfo) {
            options.areaInfo = levelManager.getStoryAreaInfo(options.area);
        }

        const area = options && options.area !== undefined
            ? options.area
            : levelManager.getLastUnlockedArea();
        const level = options && options.level !== undefined
            ? options.level
            : options.areaInfo.unlockedLevels - 1;
        const animateMarker = options && options.animateMarker !== undefined
            ? options.animateMarker
            : false;
        // if (options.areaInfo.shouldReward) {
        //     await this.handleDialogsForArea(area);
        // }
        await this.handleDialogsForArea(area);
    }

    public async scrollToLevel(options?: {
        area?: number,
        level?: number,
        markerLevel?: number,
        duration?: number,
        isEventMode?: boolean,
    }): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const area = options && options.area !== undefined
            ? options.area
            : levelManager.getLastUnlockedArea();
        const isEventMode = options && options.isEventMode !== undefined
            ? options.isEventMode
            : false;
        let level;
        if (isEventMode) {
            level = options && options.level !== undefined
                ? options.level
                : levelManager.getEventAreaInfo(area).unlockedLevels - 1;
        } else {
            level = options && options.level !== undefined
                ? options.level
                : levelManager.getStoryAreaInfo(area).unlockedLevels - 1;
        }
        const duration = area === this.currentArea && options && options.duration !== undefined
            ? options.duration
            : 0;
        const markerLevel = options && options.markerLevel;

        // Update the area header instantly.
        await this.areaPageView.scrollToArea(area, 0.001);

        await this.scrollToLevelInstantly(area, level, duration);
        await this.showMarker(area, markerLevel);
        this.updateNavigateMarkers(area);
        this.playButton.setNextLevel_v2(levelManager.getUnlockedStoryLevelsCount());
    }

    /** Internal. */
    private async scrollToLevelInstantly(area: number, level: number, duration: number): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        this.fetchLeaderboardScores(area);

        // Update page view.
        await this.areaPageView.scrollToArea(area, 0.001); // Must not be zero.

        // Load the desired area.
        const info = levelManager.getStoryAreaInfo(area);
        await this.areaListView.loadArea(info);

        // Update current area.
        this.currentArea = area;

        // Scroll to the desired level.
        await this.areaListView.scrollToLevel(level, duration);
    }

    private updateNavigateMarkers(area: number): void {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const lastUnlockedArea = levelManager.getLastUnlockedArea();
        if (area > lastUnlockedArea) {
            this.forwardMarker.node.active = false;
            this.backwardMarker.node.active = true;
        } else if (area < lastUnlockedArea) {
            this.forwardMarker.node.active = true;
            this.backwardMarker.node.active = false;
        } else {
            // Handled in listener.
        }
    }

    /** Immediately shows the level marker. */
    private async showMarker(area: number, level?: number): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const lastUnlockedArea = levelManager.getLastUnlockedArea();
        if (area === lastUnlockedArea) {
            this.areaListView.setMarkerVisible(true);
            const info = levelManager.getStoryAreaInfo(area);
            if (level === undefined) {
                // Use the last unlocked level.
                level = info.unlockedLevels - 1;
            }
            await this.areaListView.animateMarker(level, 0);
        } else {
            // Not the last area.
            this.areaListView.setMarkerVisible(false);
        }
    }

    /** Animates the marker from the last completed level to the next level. */
    private async animateMarker(area: number): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const lastUnlockedArea = levelManager.getLastUnlockedArea();
        if (area === lastUnlockedArea) {
            const info = levelManager.getStoryAreaInfo(area);
            const maxLevel = info.levels.length;
            let lastCompletedLevel = -1;
            for (let i = 0; i < maxLevel; i++) {
                if (levelManager.isStoryLevelCompleted(area, i)) {
                    lastCompletedLevel = i;
                } else {
                    break;
                }
            }
            if (lastCompletedLevel === -1) {
                this.areaListView.setMarkerVisible(false);
            } else {
                this.areaListView.setMarkerVisible(true);
                const nextLevel = Math.min(lastCompletedLevel + 1, maxLevel - 1);
                await this.areaListView.animateMarker(lastCompletedLevel, 0);
                await this.areaListView.animateMarker(nextLevel, 1.0);
            }
        } else {
            // Not the last area.
            this.areaListView.setMarkerVisible(false);
        }
    }

    private async handleDialogsForLevel(area: number, level: number): Promise<void> {

    }

    private async handleDialogsForArea(area: number): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const rewarded = await this.handleAreaClearDialog(area);
        if (rewarded) {
            // Open the next area.
            if (area + 1 >= levelManager.getStoryAreas()) {
                // Complete the last area.
                await this.showGameClearDialog();
            }
        }
    }

    /** Handles case where the area clear dialog should show or not. */
    private async handleAreaClearDialog(area: number): Promise<boolean> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const allAreas = Array.from({length: area + 1}, (_, index) => index); // Create array [0, 1, ..., area]

        // Filter areas that have `shouldReward` set to true
        const areasToReward = allAreas.filter(index => levelManager.getStoryAreaInfo(index).shouldReward);

        if (areasToReward.length === 0) {
            return false; // No area requires reward
        }

        // Show complete dialog for each area sequentially
        for (const areaIndex of areasToReward) {
            const info = levelManager.getStoryAreaInfo(areaIndex);
            info.shouldReward = false; // Mark as rewarded
            await this.showAreaCompleteDialog(areaIndex);
        }

        return true; // At least one area was rewarded
    }

    /** Shows the area complete dialog for the specified area. */
    private async showAreaCompleteDialog(area: number): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
        return await MapUtils.showAreaCompleteDialog(
            this.dialogManager,
            levelManager,
            profileManager,
            this.topHud,
            area,
            GameMode.STORY,
        );
    }

    /** Shows the game clear dialog. */
    private async showGameClearDialog(): Promise<void> {
        const dialog = await GameClearDialog.create();
        dialog.show(this.dialogManager);
        await new Promise<void>(resolve => dialog.onDidHide(() => resolve()));
    }

    /** Shows the select level dialog for the specified area and level. */
    public async showSelectLevelDialog(area: number, level: number): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        let areaInfo = levelManager.getStoryAreaInfo(area);
        if (level >= areaInfo.levels.length) return;
        let levelInfo = areaInfo.levels[level];
        await MapUtils.showSelectLevelDialog(
            this.selectLevelDialogPrefab,
            this.dialogManager,
            this.controller,
            areaInfo, levelInfo);
    }

    private async fetchLeaderboardScores(area: number): Promise<void> {
        // cc.log(`fetchLevelLeaderboard: begin area = ${area}`);
        // const leaderboardManager = ee.ServiceLocator.resolve(LeaderboardManager);
        // const levelManager = ee.ServiceLocator.resolve(LevelManager);
        // const areaInfo = levelManager.getStoryAreaInfo(area);
        // const promises: Array<Promise<void>> = [];
        // const lastLevel = Math.min(
        //     areaInfo.levels.length,
        //     areaInfo.unlockedLevels + 1 /* Fetch one more level forward. */);
        // for (let i = 0; i < lastLevel; ++i) {
        //     promises.push((async () => {
        //         try {
        //             await leaderboardManager.fetchLevelLeaderboard(area, i, 10, false);
        //         } catch (ex) {
        //         }
        //     })());
        // }
        // await Promise.all(promises);
        // cc.log(`fetchLevelLeaderboard: end area = ${area}`);
    }
}
