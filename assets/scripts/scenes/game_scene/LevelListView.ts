import assert = require('assert');
import { DefaultPrefabLoader, PrefabLoader } from '../../utils/PrefabLoader';

const { ccclass, disallowMultiple, property } = cc._decorator;

interface Listener {
    loadLevel?(sender: LevelListView, loader: PrefabLoader): void;
}

export abstract class LevelListView extends cc.Component {
    public abstract listener: Listener;
    public abstract readLevels(dir: string): void;
}

@ccclass
@disallowMultiple
class LevelListViewImpl extends LevelListView {
    @property(cc.Node)
    private readonly listView: cc.Node | null = null;

    public listener: Listener = {};

    protected onLoad(): void {
        assert(this.listView !== null);
    }

    public readLevels(dir: string): void {
        cc.loader.loadResDir(dir, (err, res, urls) => {
            // Only interested in urls.
            const configs: Array<[string, string]> = [];
            urls.forEach(url => {
                const index = url.indexOf(dir);
                const display = url.substr(index + dir.length + 1);
                const path = url;
                configs.push([display, path]);
            });
            this.buildListView(configs);
        });
    }

    private buildListView(configs: Array<[string, string]>): void {
        const listView = this.listView!;
        listView.removeAllChildren();

        configs.forEach(([display, path]) => {
            const node = new cc.Node();
            node.width = 300;
            node.height = 30;

            const sprite = node.addComponent(cc.Sprite);
            const button = node.addComponent(cc.Button);
            node.on(cc.Node.EventType.TOUCH_END, () => {
                // FIXME: workaround.
                cc.sys.localStorage.setItem('test_scene_last_level', path);
                this.listener.loadLevel && this.listener.loadLevel(this, new DefaultPrefabLoader(path));
            });

            const labelNode = new cc.Node();
            labelNode.anchorX = 0;

            const label = labelNode.addComponent(cc.Label);
            label.fontSize = 20;
            label.string = display;
            label.overflow = cc.Label.Overflow.NONE;

            const widget = labelNode.addComponent(cc.Widget);
            widget.isAlignLeft = true;
            widget.left = 0;

            node.addChild(labelNode);
            listView.addChild(node);
        });
    }
}