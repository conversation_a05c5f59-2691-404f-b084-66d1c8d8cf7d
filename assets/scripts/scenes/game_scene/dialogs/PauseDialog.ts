import * as  ee from '../../../libraries/ee/index';
import { LevelController } from '../LevelController';
import { StoryLevelConfig } from '../StoryLevelConfig';
import {GameMode} from "../../../manager/level/LevelManager";

export abstract class PauseDialog extends ee.Dialog {
    /** Sets the level controller. */
    public abstract setController(controller: LevelController): void;

    public abstract setConfig(config: StoryLevelConfig, gameMode: GameMode): void;
}