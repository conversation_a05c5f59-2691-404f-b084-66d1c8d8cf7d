import { CommonDialog } from '../../../dialog/CommonDialog';
import { DialogUtils } from '../../../dialog/DialogUtils';
import * as ee from "../../../libraries/ee/index";
import { PrefabUtils } from '../../../utils/PrefabUtils';
import * as gm from '../../../engine/gm_engine';

const { ccclass, disallowMultiple, property } = cc._decorator;



@ccclass
@disallowMultiple
export class GameClearDialog extends CommonDialog {
    public static create(): Promise<GameClearDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/menu_scene/map/game_clear_dialog');
    }

    protected onLoad(): void {
        super.onLoad();

        this.animateEffect();
        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });
        
    }

    public animateEffect(): void {
        // Generate and set light show in place
        this.node.addChild(DialogUtils.createLightAndConfetti(), -1);
    }
}