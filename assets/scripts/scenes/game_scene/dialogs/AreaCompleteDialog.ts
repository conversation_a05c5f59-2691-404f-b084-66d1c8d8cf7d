import assert = require('assert');
import {CommonDialog} from '../../../dialog/CommonDialog';
import {DialogUtils} from '../../../dialog/DialogUtils';
import {NoAdsDialog} from '../../../dialog/NoAdsDialog';
import * as gm from '../../../engine/gm_engine';
import * as ee from "../../../libraries/ee/index";
import {LanguageComponent} from "../../../libraries/ee/index";
import {
    AreaInfo,
    AudioManager,
    CrashlyticManager,
    crashlytics,
    GameMode,
    LevelManager,
    RewardInfo, RewardManager, RewardUtils,
    SceneName,
    ShareHelper,
    SocialManager,
    SoundType,
    StoreItem,
    StoreManager,
    TrackAdsRewardWatch,
    TrackingManager,
    TrackResultSoftCurrency,
    TrackShareType,
    TrackSourceType,
    WatchAdsHelper,
} from '../../../manager/gm_manager';
import {EffectHelper} from '../../../utils/EffectHelper';
import {PrefabUtils} from '../../../utils/PrefabUtils';
import {TopHud} from '../../common/TopHud';
import {RewardFrame} from "../../../dialog/RewardFrame";
import {ResourceBar} from "../../common/ResourceBar";
import {VibrateManager} from "../../../manager/vibrate/VibrateManager";

const {ccclass, disallowMultiple, property} = cc._decorator;

interface Controller {
    pressedShare: () => void;
}

@ccclass
@disallowMultiple
export class AreaCompleteDialog extends CommonDialog {
    public static create(): Promise<AreaCompleteDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/menu_scene/map/area_completed_dialog');
    }

    @property({type: cc.Node, visible: true})
    private readonly _areaHudNode: cc.Node | null = null;

    private get areaHudNode(): cc.Node {
        return gm.retrieveNull(this._areaHudNode);
    }

    @property({type: cc.Node, visible: true})
    private readonly _eventAreaHud: cc.Node | null = null;

    private get eventAreaHud(): cc.Node {
        return gm.retrieveNull(this._eventAreaHud);
    }

    @property({type: cc.Layout, visible: true})
    private _layout: cc.Layout | null = null;

    private get layout(): cc.Layout {
        if (this._layout === null) {
            throw new Error('layout is null');
        }
        return this._layout;
    }

    @property(cc.Label)
    private readonly label: cc.Label | null = null;

    @property(ResourceBar)
    private adsRewardHud: ResourceBar | null = null;

    @property({type: [cc.SpriteFrame], visible: true})
    private readonly _areaSpriteFrames: cc.SpriteFrame[] = [];

    private get areaSpriteFrames(): cc.SpriteFrame[] {
        return gm.retrieveNull(this._areaSpriteFrames);
    }

    @property({type: cc.Sprite, visible: true})
    private readonly _areaBackgroundSprite: cc.Sprite | null = null;

    private get areaBackgroundSprite(): cc.Sprite {
        return gm.retrieveNull(this._areaBackgroundSprite);
    }

    @ee.nest(RewardFrame)
    private readonly _rewardFrame: RewardFrame | null = null;

    private get rewardFrame(){
        return gm.retrieveNull(this._rewardFrame);
    }

    private area: number = 1;
    private controller: Controller;
    private xp: number = 0;
    private gold: number = 0;
    private adsGold: number = 0;
    private chestReward?: RewardInfo;
    private dirty: boolean = false;
    private watchedVideo: boolean = false;
    private topHud: TopHud | null = null;
    private clickable: boolean = true;
    private areaType: GameMode;

    private rewards: RewardInfo[] = [];

    public constructor() {
        super();
        this.controller = {
            pressedShare: () => {
            },
        };
    }

    @crashlytics
    protected onLoad(): void {
        super.onLoad();
        ee.ServiceLocator.resolve(VibrateManager).makeVibrate();
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        ee.ServiceLocator.resolve(TrackingManager).trackEventOpen(SceneName.DialogStoryAreaClear);

        assert(this.label !== null);
        assert(this.adsRewardHud !== null);
        assert(this._areaHudNode !== null);
        assert(this._eventAreaHud !== null);
        assert(this._layout !== null);
        assert(this._areaBackgroundSprite !== null);
        assert(this._areaSpriteFrames !== null);

        this.animateEffect();
        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });

        ee.AsyncManager.getInstance().add(AreaCompleteDialog.name, async () => {
            await this.initialize();
            this.rewardFrame.setRewards(this.rewards);
        })

    }

    private async initialize(): Promise<void> {
        // Nothing.
    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    public collectAllRewards(): Promise<void> {
        return new Promise((executor, reject) => {
            let dstNode = this.topHud!.getNodeByItem(StoreItem.Gold);
            const storeManager = ee.ServiceLocator.resolve(StoreManager);
            if (this.areaType === GameMode.EVENT || this.areaType === GameMode.CHARACTER) {
                if(this.watchedVideo){
                    EffectHelper.showFlyingGoldAnimation(this.rewardFrame.node, dstNode, this.adsGold).then(() => {
                        storeManager.addItemBalance(StoreItem.Gold, this.adsGold);
                    });
                }
                executor();
                return;
            }

            const gold = this.gold + (this.watchedVideo ? this.adsGold : 0);
            EffectHelper.showFlyingGoldAnimation(this.rewardFrame.node, dstNode, this.gold).then(() => {
                storeManager.addItemBalance(StoreItem.Gold, gold);
            });
            dstNode = this.topHud!.getNodeByItem(StoreItem.Xp);
            EffectHelper.showFlyingXpAnimation(this.rewardFrame.node, dstNode, this.xp).then(() => {
                executor();
            });
            executor();
            const trackingMgr = ee.ServiceLocator.resolve(TrackingManager);
            trackingMgr.trackEventSourceSoftCurrency(SceneName.DialogStoryAreaClear,
                TrackResultSoftCurrency.Bought, TrackSourceType.GoldBar, gold);
        });
    }

    public setArea(area: number): this {
        this.area = area;
        return this;
    }

    public setXP(xp: number): this {
        this.xp = xp;
        return this;
    }

    public setGold(gold: number): this {
        this.gold = gold;
        this.adsGold = gold > 10000 ? 10000 : gold;
        this.adsRewardHud.value = this.adsGold;
        return this;
    }

    public setTopHud(top: TopHud): this {
        this.topHud = top;
        return this;
    }

    public setAreaType(areaType: GameMode): this {
        this.areaType = areaType;
        return this;
    }

    public buildDialog(): this {
        this.dirty = true;
        this.updateDisplay();
        return this;
    }

    @crashlytics
    private pressedShare(): void {
        if (!this.isActive()) {
            return;
        }
        if (!this.clickable) {
            return;
        }
        this.clickable = false;

        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.pressedShare, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        this.controller.pressedShare();

        const trackingMgr = ee.ServiceLocator.resolve(TrackingManager);
        trackingMgr.trackEventClick(SceneName.DialogStoryAreaClear, 'btn_share');

        const socialManager = ee.ServiceLocator.resolve(SocialManager);
        const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        (new ShareHelper(socialManager, dialogManager).shareScreenshot().then(trackResult => {
            trackingMgr.trackEventShare(SceneName.DialogStoryAreaClear, trackResult, TrackShareType.Image);
            this.clickable = true;
        }));

    }

    @crashlytics
    private pressedAds(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.pressedAds, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        const trackingMgr = ee.ServiceLocator.resolve(TrackingManager);
        const rewardManager = ee.ServiceLocator.resolve(RewardManager);
        trackingMgr.trackEventClick(SceneName.DialogStoryAreaClear, 'btn_video');

        (new WatchAdsHelper(SceneName.DialogStoryAreaClear, TrackAdsRewardWatch.GoldAreaComplete)).onSuccess(() => {
            this.adsRewardHud!.node.active = false;
            this.watchedVideo = true;
            // animate gold
            const value = this.gold > 10000 ? 10000 : this.gold;
            let bonusReward = [...this.rewards,rewardManager.createReward({
                type: 'store',
                subType: 'gold',
                value: value
            })]
            this.rewardFrame.setRewards(RewardUtils.compactRewards(bonusReward));
        }).onNoAds(() => {
            this.adsRewardHud!.node.active = true;
            NoAdsDialog.create().then(dialog => this.showDialog(dialog));
        }).start();
    }

    public setController(controller: Controller): this {
        this.controller = controller;
        return this;
    }

    public animateEffect(): void {
        // Generate and set light show in place
        this.node.addChild(DialogUtils.createLightAndConfetti(), -1);
    }

    public updateDisplay(): void {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const rewardManager = ee.ServiceLocator.resolve(RewardManager);
        let areaInfo: AreaInfo;
        if (this.areaType === GameMode.STORY) areaInfo = levelManager.getStoryAreaInfo(this.area);
        if (this.areaType === GameMode.EVENT) areaInfo = levelManager.getEventAreaInfo(this.area);
        if (this.areaType === GameMode.CHARACTER) areaInfo = levelManager.getCharacterAreaInfo();

        this.rewards = [areaInfo.reward];

        this.areaHudNode.active = false;
        this.eventAreaHud.active = true;
        this.eventAreaHud.getComponentInChildren(LanguageComponent).key = this.areaType === GameMode.EVENT ? `text_event_area_clear` : `text_jaki_event_area_clear`;

        if(areaInfo.areaType === GameMode.STORY){
            let goldReward = rewardManager.createReward({
                type: 'store',
                subType: 'gold',
                value: this.gold,
            })

            let expReward = rewardManager.createReward({
                type: 'store',
                subType: 'xp',
                value: this.xp,
            })

            this.rewards.push(goldReward,expReward);

            this.areaHudNode.active = true;
            this.eventAreaHud.active = false;

            this.label!.string = areaInfo.name;
            this.areaBackgroundSprite.spriteFrame = this.areaSpriteFrames[this.area];
        }

        this.layout.updateLayout();
    }
}