import { LevelConfig } from './LevelConfig';
import { LevelEntityView } from './LevelEntityView';
import { LevelTimeView } from './LevelTimeView';
import { SimulationManager } from './strategy/StepStrategy';

export enum LevelStatus {
    /** Loading a level or no level is loaded. */
    Invalid,

    /** Loaded and playing. */
    Playing,

    /** Loaded and playing and failed (story mode) or lost (pvp mode). */
    Failed,

    /** Loaded and playing and completed (story mode) or won (pvp mode). */
    Completed,
}

export interface LevelViewListener {
    onDig?(): void;
    onMove?(): void;
}

export abstract class LevelView extends cc.Component {
    /** Gets the controller index. */
    public abstract controllerIndex: number;

    /** Gets the currently using config. */
    public abstract config: LevelConfig;

    /** Gets or sets the listener. */
    public abstract listener: LevelViewListener;

    /** Gets the simulation manager. */
    public abstract simulationManager: SimulationManager;

    /** Gets the associated level entity view. */
    public abstract entityView: LevelEntityView;

    /** Gets the level time view. */
    public abstract timeView: LevelTimeView;

    /** Gets the level status. */
    public abstract getStatus(): LevelStatus;

    /**
     * Changes money by the specified amount.
     * @param index Controller index.
     * @param value Changed amount.
     */
    public abstract changeMoney(index: number, value: number): Promise<void>;
}
