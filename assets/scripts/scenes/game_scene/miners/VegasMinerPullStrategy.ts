import * as bt from '@senspark/behavior-tree';
import * as gm from '../../../engine/gm_engine';
import {
    AudioManager,
    SoundType,
} from '../../../manager/gm_manager';
import { VegasMiner } from './VegasMiner';
import {
    createVegasMinerPullingTree,
    createVegasMinerPullTree,
} from "./VegasMinerAction";

export class VegasMinerPullStrategy implements gm.MinerPullStrategy {
    private prePullTree = createVegasMinerPullTree();
    private pullingTree = createVegasMinerPullingTree();
    private puller: gm.MinerUpdater;
    private syncer: gm.MinerApplier;
    private audioId?: number;

    public constructor(
        private readonly audioManager: AudioManager,
        private readonly boosterManager: gm.BoosterManager,
        private readonly entityManager: gm.EntityManager) {
        this.puller = new gm.LinearMinerPuller();
        this.syncer = new gm.RopeSyncer();
    }

    public pull(miner: gm.Miner): void {
        this.prePullTree.resetTask();
        this.audioId = this.audioManager.playSound(SoundType.Pull, true);
    }

    public update(miner: VegasMiner, delta: number): void {
        const claw = miner.claw;
        const entity = claw.getCapturedItem();
        if (entity !== null && this.prePullTree.getStatus() !== bt.TaskStatus.Succeeded) {
            this.prePullTree.setObject({
                miner,
                character: miner.character,
                claw: miner.claw,
                entity,
                delta,
                boosterManager: this.boosterManager,
            });
            this.prePullTree.step();
        } else {
            if (entity !== null) {
                this.pullingTree.resetTask();
                this.pullingTree.setObject({
                    claw: miner.claw,
                    entity,
                    entityManager: this.entityManager,
                });
                this.pullingTree.step();
            }
            this.puller.update(miner, delta);
        }
        this.syncer.apply(miner);
    }

    public isDone(miner: gm.Miner): boolean {
        return miner.rope.length < 40;
    }

    public finish(miner: gm.Miner): void {
        this.audioId && this.audioManager.stopSoundById(this.audioId);
    }
}