import * as bt from '@senspark/behavior-tree';
import * as gm from '../../../engine/gm_engine';
import {
    BoosterStorageManager,
    LogManager,
} from '../../../manager/gm_manager';
import { VegasMinerTakeListener } from './VegasListener';
import { VegasMiner } from './VegasMiner';
import { createVegasMinerTakeTree } from './VegasMinerAction';

export class VegasMinerTakeStrategy implements gm.MinerTakeStrategy {
    private tree = createVegasMinerTakeTree();
    private entity: gm.Entity | null;

    public constructor(
        private readonly boosterManager: gm.BoosterManager,
        private readonly boosterStorageManager: BoosterStorageManager,
        private readonly entityManager: gm.EntityManager,
        private readonly logManager: LogManager,
        private readonly sceneView: cc.Node,
        private readonly listener: VegasMinerTakeListener) {
        this.entity = null;
    }

    public take(miner: gm.Miner, entity: gm.Entity | null): void {
        this.tree.resetTask();
        this.entity = entity;
        entity && entity.kill();
    }

    public update(miner: <PERSON>Miner, delta: number): void {
        if (this.entity === null) {
            return;
        }
        this.tree.setObject({
            logManager: this.logManager,
            boosterManager: this.boosterManager,
            boosterStorageManager: this.boosterStorageManager,
            miner,
            character: miner.character,
            entity: this.entity,
            entityManager: this.entityManager,
            sceneView: this.sceneView,
            money: 0,
            delta,
            collectionSlot: -1,
            collectionType: -1,
            collectedChest: -1,
        });
        this.tree.step();
        const money = this.tree.getObject().money;
        if (money > 0) {
            this.listener.moneyChanged && this.listener.moneyChanged(miner, money);
        }
        const collectionSlot = this.tree.getObject().collectionSlot;
        const collectionType = this.tree.getObject().collectionType;
        if (collectionSlot > -1 && collectionType > -1) {
            this.listener.itemCollected && this.listener.itemCollected(miner, collectionType, collectionSlot);
        }

        const collectedChest = this.tree.getObject().collectedChest;
        if (collectedChest > -1) {
            this.listener.chestCollected && this.listener.chestCollected(collectedChest);
        }
    }

    public isDone(miner: gm.Miner): boolean {
        if (this.entity === null) {
            return true;
        }
        return this.tree.getStatus() !== bt.TaskStatus.Running;
    }
}