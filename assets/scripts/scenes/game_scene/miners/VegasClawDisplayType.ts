export enum VegasClawDisplayType {
    // special type
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,

    <PERSON><PERSON>,
    <PERSON>act<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON>,

    /** Rare */
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>,

    /** Epic */
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,

    /** Legendary */
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
}