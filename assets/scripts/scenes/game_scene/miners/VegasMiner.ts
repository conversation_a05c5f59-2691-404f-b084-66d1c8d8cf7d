import assert = require('assert');
import * as gm from '../../../engine/gm_engine';
import * as ee from '../../../libraries/ee/index';
import {ResourcesUtils} from '../../../utils/ResourcesUtils';
import {MinerEffect} from './MinerEffect';
import {CharacterAnimation, VegasCharacter} from './VegasCharacter';
import {VegasClaw} from './VegasClaw';
import {Type, VegasPet} from './VegasPet';
import {VegasRope} from './VegasRope';
import {Entity} from "../../../engine/gm_engine";

const { ccclass, disallowMultiple, menu, property } = cc._decorator;

interface Listener {
    throwItem?(): Promise<boolean>;
    pickItem?(item: Entity): void;
}

interface GameSceneListener {
    updateDynamiteCount?(entity: Entity): void;
}

@ccclass
@disallowMultiple
@menu('vegas/Vegas Miner')
export class VegasMiner extends gm.DefaultMiner {
    @ee.nest(VegasCharacter)
    private readonly _character: VegasCharacter | null = null;

    @ee.nest(VegasRope)
    private readonly _rope: VegasRope | null = null;

    @ee.nest(VegasClaw)
    private readonly _claw: VegasClaw | null = null;

    @ee.nest(VegasPet)
    private readonly _pet: VegasPet | null = null;

    @property({ type: cc.Prefab, visible: true })
    private readonly _effectPrefab: cc.Prefab | null = null;

    // @property({ type: cc.Label, visible: true })
    // private readonly _dynamiteLabel: cc.Label | null = null;

    private get effectPrefab(): cc.Prefab {
        return gm.retrieveNull(this._effectPrefab);
    }

    // private get dynamiteLabel(): cc.Label {
    //     return gm.retrieveNull(this._dynamiteLabel);
    // }

    private tutorialHand: cc.Node | null = null;

    public listener: Listener = {};
    public gameSceneListener: GameSceneListener = {};

    /** Bonus gold to items, in range [0, +]% */
    private _bonusStat = 0;

    public get bonusStat(): number {
        return this._bonusStat;
    }

    public set bonusStat(value: number) {
        value = Math.max(value, 0);
        this._bonusStat = value;
    }

    /** More luck when grab bag items, in range [0, 100]% */
    private _luckStat = 0;

    public get luckStat(): number {
        return this._luckStat;
    }

    public set luckStat(value: number) {
        value = Math.min(value, 1);
        value = Math.max(value, 0);
        this._luckStat = value;
    }

    public get entityId(): string {
        return 'vegas_miner';
    }

    public onLoad(): void {
        super.onLoad();

        assert(this._character !== null);
        assert(this._rope !== null);
        assert(this._claw !== null);
        assert(this._pet !== null);
        assert(this._effectPrefab !== null);
        // assert(this._dynamiteLabel !== null);

        this.character.playAnimation(CharacterAnimation.Wait, true);
    }

    public get character(): VegasCharacter {
        return gm.retrieveNull(this._character);
    }

    public get rope(): VegasRope {
        return gm.retrieveNull(this._rope);
    }

    public get claw(): VegasClaw {
        return gm.retrieveNull(this._claw);
    }

    public get pet(): VegasPet {
        return gm.retrieveNull(this._pet);
    }

    public createEffect(): MinerEffect {
        const node = cc.instantiate(this.effectPrefab);
        return node.getComponent(MinerEffect);
    }

    public async pressedThrowDynamite() {
        if(this.listener.throwItem){
            try {
                return await this.listener.throwItem();
            }catch (error){
                console.error("Error while throwing dynamite:", error);
            }
        }
        this._pet.type
    }

    public setPetJaki(){
        this.pet.type = Type.Jaki
    }

    public setDynamiteCount(entity: Entity): void {
        this.gameSceneListener.updateDynamiteCount && this.gameSceneListener.updateDynamiteCount(entity);
        // this.dynamiteLabel.string = value.toString();
    }

    public playAnimationTutorialThrowDynamite(): void {
        const pointer = new cc.Node();
        const ske = pointer.addComponent(sp.Skeleton);
        ResourcesUtils.loadResources('spine/hand/hand', sp.SkeletonData).then(skeletonData => {
            if (!pointer.isValid) {
                return;
            }
            ske.skeletonData = skeletonData;
            ske.setAnimation(0, "hand_click", true);
        });
        // this.dynamiteLabel.node.addChild(pointer);
        pointer.scale = 0.5;
        this.tutorialHand = pointer;
    }

    public stopAnimationTutorialThrowDynamite(): void {
        if (this.tutorialHand) {
            this.tutorialHand.removeFromParent(true);
            this.tutorialHand.destroy();
            this.tutorialHand = null;
        }
    }
}
