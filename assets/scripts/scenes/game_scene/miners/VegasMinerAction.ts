import * as bt from '@senspark/behavior-tree';
import {TaskStatus} from '@senspark/behavior-tree';
import * as gm from '../../../engine/gm_engine';
import {BoxType, MusselType} from '../../../engine/gm_engine';
import * as ee from '../../../libraries/ee/index';
import {AudioManager, BoosterStorageManager, CountType, LogManager, SoundType,} from '../../../manager/gm_manager';
import {StrangerCharacter, StrangerCharacterAnimation} from './StrangerCharacter';
import {CharacterAnimation, VegasCharacter} from './VegasCharacter';
import {VegasClaw} from './VegasClaw';
import {VegasMiner} from './VegasMiner';
import {VegasStrengthEngine} from './VegasStrengthEngine';
import assert = require('assert');
import {VibrateManager} from "../../../manager/vibrate/VibrateManager";

interface LogManagerData {
    logManager: LogManager;
}

interface BoosterManagerData {
    boosterManager: gm.BoosterManager;
}

interface BoosterStorageManagerData {
    boosterStorageManager: BoosterStorageManager;
}

interface StrangerCharacterData {
    character: StrangerCharacter;
}

interface VegasCharacterData {
    character: VegasCharacter;
}

interface VegasClawData {
    claw: VegasClaw;
}

interface SceneViewData {
    sceneView: cc.Node;
}

interface PullData {
    shouldPull: boolean;
}

interface MoneyData {
    money: number;
}

interface VegasMinerData {
    miner: VegasMiner;
}

interface CollectionData {
    collectionType: gm.CollectionType;
    collectionSlot: gm.CollectionSlot;
}

interface CollectedChestData {
    collectedChest: gm.ChestType;
}

/** Stores old parent and old position for the currently pulling item. */
class PullingComponent extends cc.Component {
    public parent: cc.Node | null;
    public position: cc.Vec2;
    // Thứ tự trong parent
    public siblingIndex: number;

    public constructor() {
        super();
        this.parent = null;
        this.position = cc.Vec2.ZERO;
        this.siblingIndex = 0;
    }
}

class AddMoneyFromItem<T extends MoneyData & gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const component = entity.getComponent(gm.Valuable);
        if (component === null) {
            return bt.TaskStatus.Failed;
        }
        data.money += component.getValue();
        return bt.TaskStatus.Succeeded;
    }
}

/** Adds bonus value from miner stats. */
class AddBonusValue<T extends MoneyData & gm.EntityData & VegasMinerData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const miner = data.miner;
        const component = entity.getComponent(gm.Valuable);
        if (component === null) {
            return bt.TaskStatus.Failed;
        }
        const bonus = miner.bonusStat;
        data.money += Math.floor(component.getValue() * bonus);
        return bt.TaskStatus.Succeeded;
    }
}

class vibrateTaken<T extends MoneyData & gm.EntityData & VegasMinerData> extends bt.LeafTask<T> {
    public constructor() {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const miner = data.miner;
        const component = entity.getComponent(gm.Valuable);
        if (component === null) {
            return bt.TaskStatus.Failed;
        }
        let money = component.getValue();
        const bonus = miner.bonusStat;
        money += Math.floor(component.getValue() * bonus);
        if (money >= 50) {
            ee.ServiceLocator.resolve(VibrateManager).makeVibrate();
        }
        return bt.TaskStatus.Succeeded;
    }
}

class SetShouldPull<T extends PullData> extends bt.LeafTask<T> {
    public constructor(private readonly shouldPull: boolean) {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        data.shouldPull = this.shouldPull;
        return bt.TaskStatus.Succeeded;
    }
}

/** Checks if the specified booster is enabled. */
class IfBoosterEnabled<T extends BoosterManagerData> extends bt.LeafTask<T> {
    public constructor(private readonly type: gm.BoosterType) {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const manager = data.boosterManager;

        if (manager.isBoosterEnabled(this.type)) {
            return bt.TaskStatus.Succeeded;
        }
        return bt.TaskStatus.Failed;
    }
}

class SaveItemPosition<T extends gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;

        // Store old parent and position for heavy item handler.
        const component = entity.addComponent(PullingComponent);
        component.parent = entity.node.parent;
        component.position = cc.v2(entity.node.position);
        component.siblingIndex = entity.node.getSiblingIndex();
        return bt.TaskStatus.Succeeded;
    }
}

class PutBackItem<T extends gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;

        // Restore old position.
        const component = entity.getComponent(PullingComponent);
        entity.node.position = cc.v3(component.position);
        entity.node.parent = component.parent!;
        entity.node.setSiblingIndex(component.siblingIndex);

        // Remove reference.
        component.parent = null;
        entity.node.removeComponent(PullingComponent);

        const pullable = entity.getComponent(gm.Pullable);
        pullable.release();

        return bt.TaskStatus.Succeeded;
    }
}

class ShowLog<T> extends bt.LeafTask<T> {
    public constructor(private readonly message: string) {
        super();
    }

    public execute(): TaskStatus {
        console.log("::::::Vegas Miner Action Log: ", this.message,);
        return bt.TaskStatus.Succeeded;
    }
}

class PlaySound<T> extends bt.LeafTask<T> {
    public constructor(
        private readonly soundType: SoundType,
        private readonly loop: boolean,
        private readonly delay: number = 0,
    ) {
        super();
    }

    public execute(): bt.TaskStatus {
        const node = new cc.Node();
        cc.director.getScheduler().schedule(() => {
            ee.ServiceLocator.resolve(AudioManager).playSound(this.soundType, this.loop);
        }, node, 0, 0, this.delay, false);
        return bt.TaskStatus.Succeeded;
    }
}

class makeVibrate<T> extends bt.LeafTask<T> {
    public constructor() {
        super();
    }

    public execute(): bt.TaskStatus {
        ee.ServiceLocator.resolve(VibrateManager).makeVibrate();
        return bt.TaskStatus.Succeeded;
    }
}


/** Plays character animations. */
class PlayCharacterAnimation<T extends VegasCharacterData> extends bt.LeafTask<T> {
    public constructor(private readonly animation: CharacterAnimation, private readonly loop: boolean) {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const character = data.character;
        character.playAnimation(this.animation, this.loop);
        return bt.TaskStatus.Succeeded;
    }
}

/** Plays character animation and wait until it finishes. */
class PlayCharacterAnimationAndWait<T extends VegasCharacterData & bt.TimeData> extends bt.LeafTask<T> {
    private remaining: number;

    public constructor(private readonly animation: CharacterAnimation, private readonly loop: boolean) {
        super();
        this.remaining = 0;
    }

    public start(): void {
        super.start();
        const data = this.getObject();
        const character = data.character;
        this.remaining = character.playAnimation(this.animation, this.loop);
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const delta = data.delta;
        this.remaining -= delta;
        if (this.remaining > 0) {
            return bt.TaskStatus.Running;
        }
        return bt.TaskStatus.Succeeded;
    }
}

class PlayStrangerCharacterAnimation<T extends StrangerCharacterData> extends bt.LeafTask<T> {
    public constructor(private readonly animation: StrangerCharacterAnimation, private readonly loop: boolean) {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const character = data.character;
        character.playAnimation(this.animation, this.loop);
        return bt.TaskStatus.Succeeded;
    }
}

class PlayStrangerAnimationAndWait<T extends StrangerCharacterData & bt.TimeData> extends bt.LeafTask<T> {
    private remaining: number;

    public constructor(private readonly animation: StrangerCharacterAnimation, private readonly loop: boolean) {
        super();
        this.remaining = 0;
    }

    public start(): void {
        super.start();
        const data = this.getObject();
        const character = data.character;
        this.remaining = character.playAnimation(this.animation, this.loop);
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const delta = data.delta;
        this.remaining -= delta;
        if (this.remaining > 0) {
            return bt.TaskStatus.Running;
        }
        return bt.TaskStatus.Succeeded;
    }
}

const randomizeWeight = (luckyCloverEnabled: boolean) => {
    if (luckyCloverEnabled) {
        const k = gm.MathUtils.getRandom(0, 3);
        if (k < 1) {
            // 33% that weight = [1, 7)
            return gm.MathUtils.getRandom(1, 7);
        }
        if (k < 2) {
            // 33% that weight = 1
            return 1;
        }
        // 33% that weight = 8
        return 8;
    }
    // weight = [4, 6)
    return gm.MathUtils.getRandom(4, 6);
};

class IfSafeBoxTypeIs<T extends gm.EntityData & LogManagerData> extends bt.LeafTask<T> {
    public constructor(private readonly type: BoxType) {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const component = entity.getComponent(gm.SafeBox);
        if (component === null || component.getType() !== this.type) {
            return bt.TaskStatus.Failed;
        }
        return bt.TaskStatus.Succeeded;
    }
}

class IsCoralDiamond<T extends gm.EntityData> extends bt.LeafTask<T> {
    public constructor() {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const component = entity.getComponent(gm.Mussel);
        if (component === null || component.type !== MusselType.CoralDiamond) {
            return bt.TaskStatus.Failed;
        }
        return bt.TaskStatus.Succeeded;
    }
}

class CalculateBagWeight<T extends gm.EntityData & BoosterManagerData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const manager = data.boosterManager;
        const component = entity.getComponent(gm.Bag);
        if (component === null || component.getType() !== gm.BagType.RandomItem) {
            return bt.TaskStatus.Failed;
        }
        const pullable = entity.getComponent(gm.Pullable);
        const weight = randomizeWeight(manager.isBoosterEnabled(gm.BoosterType.LuckyClover));
        pullable.setWeight(weight);
        return bt.TaskStatus.Succeeded;
    }
}

class RandomizeBagValue<T extends gm.EntityData & VegasMinerData & BoosterManagerData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const miner = data.miner;
        const manager = data.boosterManager;
        const component = entity.getComponent(gm.Bag);
        if (component === null) {
            return bt.TaskStatus.Failed;
        }
        if (component.getType() !== gm.BagType.RandomItem &&
            component.getType() !== gm.BagType.RandomGold) {
            return bt.TaskStatus.Failed;
        }
        // Randomize bag value.
        const randomizer = (minValue: number, maxValue: number) => {
            const baseValue = gm.MathUtils.getRandom(minValue, maxValue);
            const value = Math.floor(baseValue + (maxValue - baseValue) * miner.luckStat);
            return value;
        };
        if (component.getType() === gm.BagType.RandomGold) {
            const value = randomizer(111, 300);
            component.addComponent(gm.Valuable).setValue(value);
            return bt.TaskStatus.Succeeded;
        }

        assert(component.getComponent(gm.Valuable) === null);
        assert(component.getComponent(gm.ClawRewardable) === null);

        // Randomize reward.
        const hasLuckyClover = manager.isBoosterEnabled(gm.BoosterType.LuckyClover);
        const k = gm.MathUtils.getRandom(0, 100);
        if (hasLuckyClover) {
            if (k < 25) {
                // 25% got super claw.
                component.addComponent(gm.ClawRewardable).setType(gm.ClawType.Super);
            } else if (k < 50) {
                // 25% got spike claw.
                component.addComponent(gm.ClawRewardable).setType(gm.ClawType.Spike);
            } else {
                // 50% got 700 gold.
                component.addComponent(gm.Valuable).setValue(700);
            }
        } else {
            if (k < 90) {
                // 90% got [1, 600) gold.
                const value = randomizer(1, 600);
                component.addComponent(gm.Valuable).setValue(value);
            } else if (k < 92.5) {
                // 2.5% got super claw.
                component.addComponent(gm.ClawRewardable).setType(gm.ClawType.Super);
            } else if (k < 95) {
                // 2.5% got laser claw.
                component.addComponent(gm.ClawRewardable).setType(gm.ClawType.Laser);
            } else if (k < 97.5) {
                // 2.5% got spike claw.
                component.addComponent(gm.ClawRewardable).setType(gm.ClawType.Spike);
            } else {
                // 2.5% got 800.
                component.addComponent(gm.Valuable).setValue(800);
            }
        }
        return bt.TaskStatus.Succeeded;
    }
}

class CollectChest<T extends CollectedChestData & gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const component = entity.getComponent(gm.Chest);
        if (component === null) {
            return bt.TaskStatus.Failed;
        }

        const chestType = component.getType();
        data.collectedChest = chestType;
        return bt.TaskStatus.Succeeded;
    }
}

class CountEventTake<T extends LogManagerData> extends bt.LeafTask<T> {
    constructor(private type: CountType) {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const logManager = data.logManager;
        const dict: { [key: number]: number } = {
            [this.type]: 1,
        };
        logManager.countEvent(dict);
        return bt.TaskStatus.Succeeded;
    }
}

class AddDynamite<T extends BoosterStorageManagerData & VegasMinerData & gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;

        const dynamiteRewardable = entity.getComponent(gm.DynamiteRewardable);
        if (dynamiteRewardable === null) {
            return bt.TaskStatus.Failed;
        }

        const miner = data.miner;
        //const boosterStorageManager = data.boosterStorageManager;
        //const amount = dynamiteRewardable.getAmount();
        //boosterStorageManager.addBalance(gm.BoosterType.Dynamite, amount);
        miner.setDynamiteCount(entity);

        return bt.TaskStatus.Succeeded;
    }
}

/** Shows ant bitting animation for the current view. */
class ShowAntBites<T extends gm.MinerData & gm.EntityData & gm.EntityManagerData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const manager = data.entityManager;
        const miner = data.miner;
        const component = entity.getComponent(gm.Ant);
        if (component === null) {
            return bt.TaskStatus.Failed;
        }
        const effect = component.createEffect();
        const view = manager.getView().node;
        const helper = new gm.TransformHelper(miner.node, view);
        effect.node.position = helper.convertTo(cc.Vec2.ZERO);
        view.addChild(effect.node, gm.EntityOrder.Bug);
        manager.addEntity(effect);
        return bt.TaskStatus.Succeeded;
    }
}

class ShowChestOnMap<T extends gm.MinerData & gm.EntityData & gm.EntityManagerData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const manager = data.entityManager;
        const miner = data.miner;
        const component = entity.getComponent(gm.Chest);
        if (component === null) {
            return bt.TaskStatus.Failed;
        }
        const node = component.createEffect();
        const view = manager.getView().node;
        const helper = new gm.TransformHelper(miner.node, view);
        node.position = helper.convertTo(cc.Vec2.ZERO);
        view.addChild(node, gm.EntityOrder.ChestEffect);

        const effect = node.getComponent(gm.ChestEffect);
        effect.setType(component.getType());

        node.runAction(cc.sequence(
            cc.spawn(
                cc.scaleTo(0.5, 0.2),
                cc.jumpBy(0.5, -70, 130, 200, 1),
            ),
            cc.fadeOut(0.1),
            cc.removeSelf(),
        ));

        return bt.TaskStatus.Succeeded;
    }
}

class IfHedgehogRolling<T extends SceneViewData & gm.MinerData & gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const view = data.sceneView;
        const miner = data.miner;
        const hedgehog = data.entity.getComponent(gm.Hedgehog);
        if (hedgehog === null || !hedgehog.isRolling()) {
            return bt.TaskStatus.Failed;
        }
        return bt.TaskStatus.Succeeded;
    }
}

class ShowHedgehogAttack<T extends SceneViewData & gm.MinerData & gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {

        const data = this.getObject();
        const view = data.sceneView;
        const miner = data.miner;
        const hedgehog = data.entity.getComponent(gm.Hedgehog);
        if (hedgehog === null || !hedgehog.isRolling()) {
            return bt.TaskStatus.Failed;
        }
        const path = 'spine/level_items/hedgehog/hedgehod_tach';
        cc.loader.loadRes(path, sp.SkeletonData, (err: Error, skeletonData: sp.SkeletonData) => {
            // Create animation.
            const node = new cc.Node();
            const animation = node.addComponent(sp.Skeleton);
            animation.skeletonData = skeletonData;

            // Show.
            const helper = new gm.TransformHelper(miner.node, view);
            node.position = helper.convertTo(cc.Vec2.ZERO);
            view.addChild(node);

            // Remove when done.
            animation.setAnimation(0, 'atk', false);
            const duration = animation.findAnimation('atk').duration;
            node.runAction(cc.sequence(
                cc.delayTime(duration),
                cc.removeSelf(true)),
            );
        });
        return bt.TaskStatus.Succeeded;
    }
}

/** Shows the claw obtaining animation for the current view. */
class ShowObtainClaw<T extends SceneViewData & VegasMinerData & gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const chest = entity.getComponent(gm.ClawRewardable);
        if (chest === null) {
            return bt.TaskStatus.Failed;
        }

        const type = chest.getType();
        if (type === gm.ClawType.Trivia) {
            // Not apply.
            return bt.TaskStatus.Failed;
        }

        const view = data.sceneView;
        const miner = data.miner;

        miner.claw.type = type;

        const effect = miner.createEffect();
        const helper = new gm.TransformHelper(miner.node, view);
        effect.node.position = helper.convertTo(cc.Vec2.ZERO);
        view.addChild(effect.node);

        effect.playClawAnimation(type);
        return bt.TaskStatus.Succeeded;
    }
}

class SetRibTrayEnabled<T extends VegasMinerData> extends bt.LeafTask<T> {
    public constructor(private readonly enabled: boolean) {
        super();
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const miner = data.miner;
        (miner.strengthEngine as VegasStrengthEngine).setRibEnabled(this.enabled);
        return bt.TaskStatus.Succeeded;
    }
}

/** Shows the rib tray obtaining animation for the current view. */
class ShowRibTray<T extends SceneViewData & VegasMinerData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const view = data.sceneView;
        const miner = data.miner;

        const effect = miner.createEffect();
        const helper = new gm.TransformHelper(miner.node, view);
        effect.node.position = helper.convertTo(cc.Vec2.ZERO);
        view.addChild(effect.node);

        effect.playRibAnimation();
        return bt.TaskStatus.Succeeded;
    }
}

/** Shows the niece animation for the current view. */
class ShowNieceThanksAndWait<T extends SceneViewData &
    gm.MinerData & MoneyData & bt.TimeData & gm.EntityData & gm.EntityManagerData & BoosterManagerData>
    extends bt.LeafTask<T> {
    private remaining: number;

    public constructor() {
        super();
        this.remaining = 1000; // Wait before get duration from animation
    }

    public start(): bt.TaskStatus {
        const data = this.getObject();
        const view = data.sceneView;
        const miner = data.miner;
        const niece = data.entity.getComponent(gm.Niece);
        const entityManager = data.entityManager;
        if (niece === null) {
            return bt.TaskStatus.Failed;
        }
        const node = niece.createEffect();

        const helper = new gm.TransformHelper(miner.node, view);
        node.position = helper.convertTo(cc.Vec2.ZERO).add(cc.v2(300, 0));
        view.addChild(node);
        entityManager.addEntity(node.getComponent(gm.Entity));

        const component = niece.getComponent(gm.Niece);
        this.remaining = component.animation.getAnimationDuration('thanks');
        return bt.TaskStatus.Running;
    }

    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const delta = data.delta;
        this.remaining -= delta;
        if (this.remaining > 0) {
            return bt.TaskStatus.Running;
        }
        data.money += 1000;
        const boosterManager = data.boosterManager;
        if (boosterManager.isBoosterEnabled(gm.BoosterType.DiamondPolish)) {
            data.money += 300;
        }
        return bt.TaskStatus.Succeeded;
    }
}

/** Shows the ghost scaring animation for the current view. */
class ShowGhostScare<T extends SceneViewData & gm.MinerData & gm.EntityData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const view = data.sceneView;
        const miner = data.miner;
        const entity = data.entity;
        const ghost = entity.getComponent(gm.Ghost);
        const node = ghost.createEffect();
        const helper = new gm.TransformHelper(miner.node, view);
        node.position = helper.convertTo(cc.Vec2.ZERO);
        view.addChild(node);
        return bt.TaskStatus.Succeeded;
    }
}

/** Shows the niece animation for the current view. */
class ShowDynamiteAndWait<T extends SceneViewData & gm.MinerData & bt.TimeData & gm.EntityData & gm.EntityManagerData>
    extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const view = data.sceneView;
        const miner = data.miner;
        const entity = data.entity;
        const manager = data.entityManager;
        const component = entity.getComponent(gm.DynamiteRewardable);
        if (component === null) {
            return bt.TaskStatus.Failed;
        }
        if (entity instanceof gm.Bag) {
            (entity as gm.Bag).morphToDynamite();
        }
        component.playAnimation();
        const helper = new gm.TransformHelper(miner.node, view);
        entity.node.position = helper.convertTo(cc.Vec2.ZERO).add(cc.v2(150, 75));
        view.addChild(entity.node);
        manager.addEntity(entity);
        return bt.TaskStatus.Succeeded;
    }
}

/** (Pass through) Kills ants if possible. */
const handleAnt = <T extends gm.EntityData & gm.EntityManagerData & BoosterManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Ant),
        new IfBoosterEnabled<T>(gm.BoosterType.AntSpray),
        new gm.KillAnt<T>(),
    );

const handleBag = <T extends gm.EntityData & BoosterManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Bag),
        new CalculateBagWeight<T>(),
    );

/** (Pass through) Kills lava ghosts if possible. */
const handleLavaGhost = <T extends gm.EntityData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.LavaGhost),
        new gm.KillLavaGhost<T>(),
        new CountEventTake<T>(CountType.LavaGhost),
    );

/** (Pass through) Breaks stones if possible. */
const handleStones = <T extends gm.EntityData & gm.EntityManagerData & VegasClawData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Breakable),
        new gm.CanClawBreakStones<T>(),
        new CountEventTake<T>(CountType.BreakStone),
        new gm.BreakStone<T>(),
        new PlaySound<T>(SoundType.BreakStone, false),
    );

/** (Pass through) Drops carters. */
const handleCarter = <T extends gm.EntityData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Carter),
        new gm.DropCarter<T>(),
    );

/** (Pass through) Drops cartables. */
const handleCartable = <T extends gm.EntityData & gm.EntityManagerData>() =>
    new bt.Sequence(
        new gm.IfEntityHasComponent<T>(gm.Cartable),
        new gm.DropCartable<T>(),
    );

/** (Pass through) Drops consumables. */
const handleConsumable = <T extends gm.EntityData & gm.EntityManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Consumable),
        new gm.DropConsumable<T>(),
    );

/** (Pull) Pulls empty if collides borders. */
const handleBorder = <T extends gm.EntityData & PullData>() =>
    new gm.IfEntityHasComponent<T>(gm.Border);

/** (Pull) Explodes all tnt if touch detonator. */
const handleDetonator = <T extends gm.EntityData & gm.EntityManagerData & PullData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Detonator),
        new CountEventTake(CountType.Detonator),
        new gm.ActivateDetonator<T>(),
        new gm.ExplodeAllTnts<T>(),
    );

const handleThief = <T extends gm.ClawData & gm.EntityData & BoosterManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Thief),
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new IfBoosterEnabled<T>(gm.BoosterType.WantedPoster),
                new SaveItemPosition<T>(),
                new gm.CaptureItem<T>(),
            ),
            new gm.MakeThiefRunAway<T>(),
        ),
    );

const handleDynamiteThrower = <T extends gm.ClawData & gm.EntityData & BoosterManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.DynamiteThrower),
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new IfBoosterEnabled<T>(gm.BoosterType.WantedPoster),
                new SaveItemPosition<T>(),
                new gm.CaptureItem<T>(),
            ),
            new gm.MakeDynamiteThrowerRunAway<T>(),
        ),
    );

const handleStrangerBoat = <T extends gm.ClawData & gm.EntityData & gm.EntityManagerData & PullData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.StrangerBoat),
        new gm.KillStrangerBoat<T>(),
        new PlaySound<T>(SoundType.Fall, false, 0.7),
        new gm.ChangeToBoat<T>(),
        new gm.AddEntity<T>(),
        new SaveItemPosition<T>(),
        new gm.CaptureItem<T>(),
    );

const handleStrangerBoomerang = <T extends gm.EntityData & gm.EntityManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.StrangerBoomerang),
        new gm.KillStrangerBoomerang<T>(),
    );

const handleNiece = <T extends gm.ClawData & gm.EntityData & gm.EntityManagerData & PullData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Niece),
        new gm.ChangeToDiamond<T>(),
        new gm.AddEntity<T>(),
        new SaveItemPosition<T>(),
        new gm.CaptureItem<T>(),
    );

const handleExplosible = <T extends gm.ClawData & gm.EntityData & PullData & gm.EntityManagerData>() =>
    new bt.Sequence<T>(
        new gm.Explode<T>(),
        new bt.AlwaysSucceed<T>(
            new bt.Sequence<T>(
                new gm.IfEntityHasComponent<T>(gm.Tnt),
                new gm.ChangeToTntDebris<T>(),
                new gm.AddEntity<T>(),
                new SaveItemPosition<T>(),
                new gm.CaptureItem<T>(),
            ),
        ),
    );

const handleMussel = <T extends gm.ClawData & gm.EntityData & BoosterManagerData & PullData & gm.EntityManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Mussel),
        new gm.ChangeToPearl<T>(),
        new gm.AddEntity<T>(),
        new SaveItemPosition<T>(),
        new gm.CaptureItem<T>(),
        // Must place after finishing initialization of item.
        new bt.AlwaysSucceed<T>(
            new bt.Sequence<T>(
                new IfBoosterEnabled<T>(gm.BoosterType.PirateHat),
                new gm.AddValue<T>(+300),
            ),
        ),
    );

const handleElectricLine = <T extends gm.EntityData & VegasCharacterData & PullData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.ElectricLine),
        new gm.Electrocute<T>(),
        new PlayCharacterAnimation<T>(CharacterAnimation.Electrocuted, false),
        new PlaySound<T>(SoundType.Electric, false),
    );

const handleGhost = <T extends gm.ClawData & gm.EntityData & gm.EntityManagerData & BoosterManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Ghost),
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new gm.IfGhostColorIs<T>(gm.GhostColor.Blue),
                new gm.TransformRandomRedGhostToBlueGhost<T>(true),
                new bt.AlwaysSucceed<T>(
                    new bt.Sequence<T>(
                        new gm.IfGhostTypeIs<T>(gm.GhostType.Vulture),
                        new PlaySound<T>(SoundType.VultureWhiteGrab, false),
                    )),
            ),
            new bt.Sequence<T>(
                new bt.AlwaysSucceed<T>(
                    new bt.Sequence<T>(
                        new gm.IfGhostTypeIs<T>(gm.GhostType.Vulture),
                        new PlaySound<T>(SoundType.VultureRedGrab, false),
                    )),
                new bt.Selector<T>(
                    new bt.Sequence<T>(
                        new IfBoosterEnabled<T>(gm.BoosterType.SpiritJar),
                        new gm.AddValue<T>(+200),
                    ),
                    new bt.Sequence<T>(
                        new IfBoosterEnabled<T>(gm.BoosterType.OldHam),
                        new gm.AddValue<T>(+200),
                    ),
                ),
            ),
            new gm.TransformAllBlueGhostToRedGhost<T>(),
        ),
        new SaveItemPosition<T>(),
        new gm.CaptureItem<T>(),
    );

const handleMole = <T extends gm.EntityData & gm.ClawData & gm.EntityManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfMoleStandingUp<T>(),
        new gm.TakeItemFromMole<T>(),
        new SaveItemPosition<T>(),
        new gm.CaptureItem<T>(),
    );

const handlePullable = <T extends gm.ClawData & gm.EntityData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityIsAlive<T>(),
        new gm.IfEntityHasComponent<T>(gm.Pullable),
        new gm.IfPullable<T>(),
        new SaveItemPosition<T>(),
        new gm.CaptureItem<T>(),
    );

const handleSlotMachine = <T extends gm.ClawData & gm.EntityData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent(gm.SlotMachine),
        new gm.IfSlotMachineNotSpinning<T>(),
        new gm.SpinSlotMachine<T>(),
    );

/**
 * Creates a default collision behavior tree for vegas miners.
 * Multiple results:
 * - Pass through, i.e. not pull.
 * - Pull empty (pull nothing).
 * - Pull the collided item.
 */
const handleItem = <T extends PullData &
    VegasClawData &
    gm.EntityData &
    gm.EntityManagerData &
    BoosterManagerData &
    VegasCharacterData &
    LogManagerData>() =>
    new bt.Sequence<T>(
        new SetShouldPull<T>(false),
        new bt.AlwaysSucceed(handleAnt<T>()),
        new bt.AlwaysSucceed(handleBag<T>()),
        new bt.AlwaysSucceed(handleLavaGhost<T>()),
        new bt.AlwaysSucceed(handleStones<T>()),
        new bt.AlwaysSucceed(handleCarter<T>()),
        new bt.AlwaysSucceed(handleCartable<T>()),
        new bt.AlwaysSucceed(handleConsumable<T>()),
        new bt.Selector<T>(
            handleBorder<T>(),
            handleDetonator<T>(),
            handleThief<T>(),
            handleDynamiteThrower<T>(),
            handleStrangerBoat<T>(),
            handleStrangerBoomerang<T>(),
            handleNiece<T>(),
            handleExplosible<T>(),
            handleMussel<T>(),
            handleElectricLine<T>(),
            handleGhost<T>(),
            handleMole<T>(),
            handleSlotMachine<T>(),
            handlePullable<T>(),
        ),
        new SetShouldPull<T>(true),
    );

/** Used when the mine pulls an item using default behavior. */
const pullItem = <T extends gm.MinerData & gm.ClawData & VegasCharacterData &
    gm.EntityData & bt.TimeData & BoosterManagerData>() =>
    new bt.Sequence<T>(
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new bt.Invert<T>(new gm.IfValueLessThan<T>(500)),
                new PlaySound<T>(SoundType.ValueHigh, false),
            ),
            new bt.Sequence<T>(
                new bt.Invert<T>(new gm.IfValueLessThan<T>(50)),
                new PlaySound<T>(SoundType.ValueNormal, false),
            ),
            new PlaySound<T>(SoundType.ValueLow, false),
        ),
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new gm.IfWeightLessThan<T>(15),
                new bt.Selector<T>(
                    new bt.Sequence<T>(
                        new gm.IfWeightLessThan<T>(8),
                        new PlayCharacterAnimation<T>(CharacterAnimation.PullLight, true),
                    ),
                    new bt.Sequence<T>(
                        new PlayCharacterAnimation<T>(CharacterAnimation.PullHeavy, true),
                    ),
                ),
            ),
            new bt.Sequence<T>(
                new IfBoosterEnabled<T>(gm.BoosterType.TitanRope),
                new PlayCharacterAnimation<T>(CharacterAnimation.PullHeavy, true),
            ),
            new bt.Sequence<T>(
                new bt.AlwaysSucceed<T>(
                    new bt.Sequence<T>(
                        new bt.Invert<T>(new gm.CanClawPullHeavyItems<T>()),
                        new bt.DynamicGuardSelector<T>(
                            new bt.Sequence<T>(
                                new PlayCharacterAnimationAndWait<T>(CharacterAnimation.TooHeavy, false),
                                new gm.ReleaseItem<T>(),
                                new PutBackItem<T>(),
                            ).setGuard(new gm.IfEntityIsAlive<T>()),
                        ),
                    ),
                ),
                new PlayCharacterAnimation<T>(CharacterAnimation.PullLight, true),
            ),
        ),
    );

const pullingItem = <T extends gm.ClawData & gm.EntityData & gm.EntityManagerData>() =>
    new bt.Sequence<T>(
        new bt.Invert<T>(new gm.IfEntityIsAlive<T>()),
        new gm.ReleaseItem<T>(),
        new gm.ChangeToDebris<T>(),
        new gm.AddEntity<T>(),
        new gm.CaptureItem<T>(),
    );

const takeGold = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Gold),
    new CountEventTake<T>(CountType.Gold),
);

const takeScorpion = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Scorpion),
    new CountEventTake<T>(CountType.Scorpion),
);

const takeMouse = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Mouse),
    new CountEventTake<T>(CountType.Mouse),
);

const takeCoralDiamond = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Mussel),
    new IsCoralDiamond(),
    new CountEventTake<T>(CountType.Coral),
);

const takeCoral = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Coral),
    new CountEventTake<T>(CountType.Coral),
);

const takeBlackWolf = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Wolf),
    new CountEventTake<T>(CountType.Wolf),
);

const takeWhiteWolf = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.WhiteWolf),
    new CountEventTake<T>(CountType.Wolf),
);

const takePearl = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Pearl),
    new CountEventTake<T>(CountType.Pearl),
);

const takeStone = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Stone),
    new CountEventTake<T>(CountType.Stone),
);

const takeBone = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Bone),
    new CountEventTake<T>(CountType.Bone),
);

const takeColoradoFish = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Fish),
    new gm.IfFishTypeIs<T>(gm.FishType.Colorado),
    new CountEventTake<T>(CountType.Fish),
    new CountEventTake<T>(CountType.FishColorado),
);

const takeRedFish = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Fish),
    new gm.IfFishTypeIs<T>(gm.FishType.RedClown),
    new CountEventTake<T>(CountType.Fish),
    new CountEventTake<T>(CountType.FishRed),
);

const takeBlueFish = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Fish),
    new gm.IfFishTypeIs<T>(gm.FishType.BlueDory),
    new CountEventTake<T>(CountType.Fish),
    new CountEventTake<T>(CountType.FishBlue),
);

const takeBlueShark = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Fish),
    new gm.IfFishTypeIs<T>(gm.FishType.Shark),
    new CountEventTake<T>(CountType.Fish),
    new CountEventTake<T>(CountType.BlueShark),
)

const takeVioletFish = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Fish),
    new gm.IfFishTypeIs<T>(gm.FishType.Violet),
    new CountEventTake<T>(CountType.Fish),
    new CountEventTake<T>(CountType.FishPurple),
);

const takePufferFish = <T extends gm.EntityData & LogManagerData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent<T>(gm.Fish),
    new gm.IfFishTypeIs<T>(gm.FishType.Puffer),
    new CountEventTake<T>(CountType.FishPuffer),
);

const takeAnt = <T extends gm.MinerData
    & gm.EntityData & gm.EntityManagerData & VegasCharacterData & bt.TimeData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Ant),
        new bt.AlwaysSucceed<T>(
            new bt.Sequence<T>(
                new gm.IfBugTypeIs<T>(gm.BugType.Ant),
                new CountEventTake<T>(CountType.Ant)),
        ),
        new ShowAntBites<T>(),
        new PlayCharacterAnimationAndWait<T>(CharacterAnimation.AntBitten, false),
    );

const takeBag = <T extends gm.EntityData & VegasMinerData & BoosterManagerData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Bag),
        new CountEventTake<T>(CountType.MoneyBag),
        new RandomizeBagValue<T>(),
        new bt.AlwaysSucceed<T>(
            new bt.Sequence<T>(
                new gm.IfEntityHasComponent<T>(gm.ClawRewardable),
                new PlaySound<T>(SoundType.ValueHigh, false),
            ),
        ),
    );

const takeSafeBox = <T extends gm.EntityData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.SafeBox),
        new CountEventTake<T>(CountType.Safe),
    );

const takeMineral = <T extends gm.EntityData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.SafeBox),
        new IfSafeBoxTypeIs<T>(BoxType.Mineral),
        new CountEventTake<T>(CountType.Mineral),
    );


const takeRibTray = <T extends SceneViewData & VegasMinerData & gm.EntityData & VegasCharacterData & bt.TimeData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.RibTray),
        new SetRibTrayEnabled<T>(true),
        new ShowRibTray<T>(),
        new PlaySound<T>(SoundType.ValueHigh, false),
        new PlayCharacterAnimationAndWait<T>(CharacterAnimation.EatRib, false),
    );

const takeNiece = <T extends SceneViewData & gm.MinerData &
    gm.EntityData & MoneyData & bt.TimeData & gm.EntityManagerData & LogManagerData & BoosterManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Niece),
        new CountEventTake(CountType.Niece),
        new PlaySound<T>(SoundType.PreScore, false),
        new ShowNieceThanksAndWait<T>(),
        new CountEventTake<T>(CountType.Diamond),
        new CountEventTake<T>(CountType.BigDiamond),
        new PlaySound<T>(SoundType.Score, false),
    );

const takeClawChest = <T extends SceneViewData & VegasMinerData & gm.EntityData & VegasCharacterData & bt.TimeData & LogManagerData>() =>
    new bt.Sequence<T>(
        new ShowObtainClaw<T>(),
        new CountEventTake<T>(CountType.ClawChest),
        new PlaySound<T>(SoundType.ValueHigh, false),
        new PlayCharacterAnimationAndWait<T>(CharacterAnimation.Happy, false),
    );

const takeWhiteVulture = <T extends SceneViewData & LogManagerData & gm.EntityData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Ghost),
        new gm.IfGhostTypeIs<T>(gm.GhostType.Vulture),
        new gm.IfGhostColorIs<T>(gm.GhostColor.Blue),
        new CountEventTake<T>(CountType.VultureWhite),
    );

const takeGhost =
    <T extends SceneViewData & gm.MinerData & gm.EntityData & VegasCharacterData &
        bt.TimeData & LogManagerData & BoosterManagerData>() =>
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new gm.IfGhostColorIs<T>(gm.GhostColor.Blue),
                new CountEventTake<T>(CountType.BlueGhost),
            ),
            new bt.Sequence<T>(
                new gm.IfGhostColorIs<T>(gm.GhostColor.Red),
                new bt.Selector<T>(
                    new IfBoosterEnabled<T>(gm.BoosterType.SpiritJar),
                    new IfBoosterEnabled<T>(gm.BoosterType.OldHam),
                    new bt.Sequence<T>(
                        new bt.Selector<T>(
                            new bt.Sequence<T>(
                                new gm.IfGhostTypeIs<T>(gm.GhostType.Ghost),
                                new PlaySound<T>(SoundType.GhostHowling, false),
                            ),
                            new PlaySound<T>(SoundType.VultureAttack, false),
                        ),
                        new ShowGhostScare<T>(),
                        new PlayCharacterAnimationAndWait<T>(CharacterAnimation.Scare, false),
                    ),
                ),
            ));

const takeBigDiamond = <T extends gm.EntityData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Diamond),
        new bt.Sequence<T>(
            new bt.Invert<T>(new gm.IfValueLessThan<T>(1000)),
            new CountEventTake<T>(CountType.BigDiamond),
        ),
    );

const takeDiamond = <T extends gm.EntityData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Diamond),
        new CountEventTake<T>(CountType.Diamond),
    );

const takeChest = <T extends gm.MinerData & gm.EntityData & gm.EntityManagerData & CollectedChestData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Chest),
        new PlaySound<T>(SoundType.Gift, false),
        new makeVibrate<T>(),
        new ShowChestOnMap<T>(),
        new CollectChest<T>(),
    );

const takeMouseDiamond = <T extends gm.EntityData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.DiamondMouse),
        new CountEventTake<T>(CountType.Diamond),
    );

const takeMonkey = <T extends gm.EntityData & LogManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Monkey),
        new CountEventTake<T>(CountType.Monkey),
    );

const takeHasDiamond = <T extends BoosterManagerData & gm.EntityData>() =>
    new bt.Sequence<T>(
        new IfBoosterEnabled<T>(gm.BoosterType.DiamondPolish),
        new gm.AddDiamondValue<T>(),
    );

const takeTriviaItem = <T extends BoosterManagerData & gm.EntityData>() =>
    new bt.Sequence<T>(
        new IfBoosterEnabled<T>(gm.BoosterType.SpookyDoll),
        new gm.IfEntityHasComponent<T>(gm.TriviaItem),
        new gm.MultiplyValue<T>(4),
    );

const takeStrangerBoat = <T extends gm.EntityData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent(gm.Boat),
    new CountEventTake(CountType.StrangerBoat),
);

const takeThief = <T extends gm.EntityData>() => new bt.Sequence<T>(
    new gm.IfEntityHasComponent(gm.Thief),
    new CountEventTake(CountType.Thief),
);

const takeBoomerangDynamite = <T extends gm.EntityData & VegasCharacterData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.BoomerangDynamite),
        new CountEventTake(CountType.Boomerang),
    );

const takeDynamiteRewardable = <T extends BoosterStorageManagerData & gm.MinerData & VegasMinerData
    & gm.EntityData & VegasCharacterData & bt.TimeData & gm.EntityManagerData & SceneViewData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.DynamiteRewardable),
        new bt.Sequence<T>(
            new PlaySound<T>(SoundType.ValueHigh, false),
            new ShowDynamiteAndWait<T>(),
            new PlayCharacterAnimationAndWait<T>(CharacterAnimation.Happy, false),
            new AddDynamite<T>(),
        ),
    );

class ShowCollectableItemInCollection<T extends gm.EntityData & CollectionData> extends bt.LeafTask<T> {
    public execute(): bt.TaskStatus {
        const data = this.getObject();
        const entity = data.entity;
        const component = entity.getComponent(gm.Collectible);
        if (component !== null) {
            data.collectionSlot = component.slot;
            data.collectionType = component.type;
            return bt.TaskStatus.Succeeded;
        }
        return bt.TaskStatus.Failed;
    }
}

const takeCollectableItem = <T extends gm.EntityData & CollectionData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Collectible),
        new ShowCollectableItemInCollection<T>(),
    );

const takeValuable = <T extends gm.EntityData & MoneyData & VegasCharacterData & VegasMinerData & bt.TimeData>() =>
    new bt.Sequence<T>(
        new bt.Invert<T>(new gm.IfValueLessThan<T>(1)),
        new gm.IfEntityHasComponent<T>(gm.Valuable),
        new AddMoneyFromItem<T>(),
        new bt.AlwaysSucceed<T>(new AddBonusValue<T>()),
        new vibrateTaken<T>(),
        new PlaySound<T>(SoundType.PreScore, false),
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new bt.Invert<T>(new gm.IfValueLessThan<T>(50)),
                new PlayCharacterAnimationAndWait<T>(CharacterAnimation.Happy, false),
            ),
            new PlayCharacterAnimationAndWait<T>(CharacterAnimation.Sad, false),
        ),
        new PlaySound<T>(SoundType.Score, false),
    );

const takeHedgehog = <T extends SceneViewData &
    gm.MinerData &
    gm.EntityData &
    VegasCharacterData &
    BoosterManagerData &
    LogManagerData &
    bt.TimeData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Hedgehog),
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new IfBoosterEnabled<T>(gm.BoosterType.Glove),
                new IfHedgehogRolling<T>(),
                new gm.AddValue<T>(+200),
            ),
            new bt.Sequence<T>(
                new ShowHedgehogAttack<T>(),
                new PlayCharacterAnimationAndWait<T>(CharacterAnimation.AntBitten, false),
            ),
            new CountEventTake<T>(CountType.Hedgehog),
        ));

/** Used when the miner takes an item using default behavior. */
const takeItem = <T extends SceneViewData &
    VegasMinerData &
    gm.EntityData &
    gm.EntityManagerData &
    bt.TimeData &
    BoosterManagerData &
    BoosterStorageManagerData &
    CollectedChestData &
    VegasCharacterData &
    CollectionData &
    LogManagerData &
    MoneyData>() =>
    new bt.Sequence(
        new bt.AlwaysSucceed<T>(takeCollectableItem<T>()),
        new bt.AlwaysSucceed<T>(takeHasDiamond<T>()),
        new bt.AlwaysSucceed<T>(takeDiamond<T>()),
        new bt.AlwaysSucceed<T>(takeBigDiamond<T>()),
        new bt.AlwaysSucceed<T>(takeMouseDiamond<T>()),
        new bt.AlwaysSucceed<T>(takeGold<T>()),
        new bt.AlwaysSucceed<T>(takeBone<T>()),
        new bt.AlwaysSucceed<T>(takeStone<T>()),
        new bt.AlwaysSucceed<T>(takeMouse<T>()),
        new bt.AlwaysSucceed<T>(takePearl<T>()),
        new bt.AlwaysSucceed<T>(takeCoral<T>()),
        new bt.AlwaysSucceed<T>(takeCoralDiamond<T>()),
        new bt.AlwaysSucceed<T>(takeSafeBox<T>()),
        new bt.AlwaysSucceed<T>(takeBag<T>()),
        new bt.AlwaysSucceed<T>(takeBlackWolf<T>()),
        new bt.AlwaysSucceed<T>(takeWhiteWolf<T>()),
        new bt.AlwaysSucceed<T>(takeRedFish<T>()),
        new bt.AlwaysSucceed<T>(takeBlueFish<T>()),
        new bt.AlwaysSucceed<T>(takeColoradoFish<T>()),
        new bt.AlwaysSucceed<T>(takeVioletFish<T>()),
        new bt.AlwaysSucceed<T>(takePufferFish<T>()),
        new bt.AlwaysSucceed<T>(takeBlueShark<T>()),
        new bt.AlwaysSucceed<T>(takeScorpion<T>()),
        new bt.AlwaysSucceed<T>(takeMineral<T>()),
        new bt.AlwaysSucceed<T>(takeChest<T>()),
        new bt.AlwaysSucceed<T>(takeAnt<T>()),
        new bt.AlwaysSucceed<T>(takeHedgehog<T>()),
        new bt.AlwaysSucceed<T>(takeMonkey<T>()),
        new bt.AlwaysSucceed<T>(takeRibTray<T>()),
        new bt.AlwaysSucceed<T>(takeNiece<T>()),
        new bt.AlwaysSucceed<T>(takeClawChest<T>()),
        new bt.AlwaysSucceed<T>(takeGhost<T>()),
        new bt.AlwaysSucceed<T>(takeTriviaItem<T>()),
        new bt.AlwaysSucceed<T>(takeThief<T>()),
        new bt.AlwaysSucceed<T>(takeWhiteVulture<T>()),
        new bt.AlwaysSucceed<T>(takeBoomerangDynamite<T>()),
        new bt.AlwaysSucceed<T>(takeStrangerBoat<T>()),
        new bt.AlwaysSucceed<T>(takeValuable<T>()),
        new bt.AlwaysSucceed<T>(takeDynamiteRewardable<T>()),
    );

export const createVegasMinerCaptureTree = () =>
    new bt.BaseBehaviorTree<
        PullData &
        VegasClawData &
        gm.EntityData &
        gm.EntityManagerData &
        BoosterManagerData &
        VegasCharacterData &
        LogManagerData>(handleItem());

export const createVegasMinerPullTree = () =>
    new bt.BaseBehaviorTree<
        gm.MinerData &
        gm.ClawData &
        VegasCharacterData &
        gm.EntityData &
        BoosterManagerData &
        bt.TimeData>(pullItem());

export const createVegasMinerPullingTree = () =>
    new bt.BaseBehaviorTree<gm.ClawData & gm.EntityData & gm.EntityManagerData>(pullingItem());

export const createVegasMinerTakeTree = () =>
    new bt.BaseBehaviorTree<
        SceneViewData &
        VegasMinerData &
        gm.EntityData &
        gm.EntityManagerData &
        bt.TimeData &
        BoosterManagerData &
        BoosterStorageManagerData &
        LogManagerData &
        VegasCharacterData &
        CollectedChestData &
        CollectionData &
        MoneyData>(takeItem());

const takeStrangerItem = <T extends bt.TimeData &
    StrangerCharacterData>() =>
    new PlayStrangerAnimationAndWait<T>(StrangerCharacterAnimation.Happy, false);

export const createStrangerMinerTakeTree = () =>
    new bt.BaseBehaviorTree<
        bt.TimeData &
        StrangerCharacterData>(takeStrangerItem());

const strangerHandleElectricLine = <T extends gm.EntityData & StrangerCharacterData & PullData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.ElectricLine),
        new gm.Electrocute<T>(),
        new PlayStrangerCharacterAnimation<T>(StrangerCharacterAnimation.Pull, false),
    );

const strangerHandleGhost = <T extends gm.ClawData & gm.EntityData & gm.EntityManagerData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Ghost),
        new bt.Selector<T>(
            new bt.Sequence<T>(
                new gm.IfGhostColorIs<T>(gm.GhostColor.Blue),
                new gm.TransformRandomRedGhostToBlueGhost<T>(false),
            ),
            new gm.TransformAllBlueGhostToRedGhost<T>(),
        ),
        new SaveItemPosition<T>(),
        new gm.CaptureItem<T>(),
    );

const strangerHandleBag = <T extends gm.EntityData>() =>
    new bt.Sequence<T>(
        new gm.IfEntityHasComponent<T>(gm.Bag),
        new gm.SetWeight<T>(1),
    );

const strangerHandleItem = <T extends PullData &
    VegasClawData &
    gm.EntityData &
    gm.EntityManagerData &
    StrangerCharacterData &
    LogManagerData>() =>
    new bt.Sequence<T>(
        new SetShouldPull<T>(false),
        // new bt.AlwaysSucceed(handleAnt<T>()),
        new bt.AlwaysSucceed(strangerHandleBag<T>()),
        new bt.AlwaysSucceed(handleLavaGhost<T>()),
        new bt.AlwaysSucceed(handleStones<T>()),
        new bt.AlwaysSucceed(handleCarter<T>()),
        new bt.AlwaysSucceed(handleCartable<T>()),
        new bt.AlwaysSucceed(handleConsumable<T>()),
        new bt.Selector<T>(
            handleBorder<T>(),
            handleDetonator<T>(),
            // handleThief<T>(),
            handleStrangerBoat<T>(),
            handleNiece<T>(),
            handleExplosible<T>(),
            // handleMussel<T>(),
            strangerHandleElectricLine<T>(),
            strangerHandleGhost<T>(),
            handleMole<T>(),
            handleSlotMachine<T>(),
            handlePullable<T>(),
        ),
        new SetShouldPull<T>(true),
    );

const strangerPullItem = <T extends StrangerCharacterData>() =>
    new PlayStrangerCharacterAnimation<T>(StrangerCharacterAnimation.Pull, true);

export const createStrangerMinerCaptureTree = () =>
    new bt.BaseBehaviorTree<
        PullData &
        VegasClawData &
        gm.EntityData &
        gm.EntityManagerData &
        StrangerCharacterData &
        LogManagerData>(strangerHandleItem());

export const createStrangerMinerPullTree = () =>
    new bt.BaseBehaviorTree<StrangerCharacterData>(strangerPullItem());
