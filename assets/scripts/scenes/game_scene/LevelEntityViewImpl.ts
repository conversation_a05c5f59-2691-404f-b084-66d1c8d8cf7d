import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {
    AudioManager,
    BoosterStorageManager,
    ConfigManager,
    CrashlyticManager,
    crashlytics,
    ItemCardDeck,
    ItemCardStatsType,
    ItemCardType,
    LogManager,
    NullBoosterStorageManager,
    RewardUtils,
    SceneName,
    SoundType,
    TrackingManager,
    TrackResultBooster,
    TrackSinkType,
} from '../../manager/gm_manager';
import {PrefabLoader} from '../../utils/PrefabLoader';
import {LevelEntityView, MinerController, TutorialListener,} from './LevelEntityView';
import {MinerAndPet} from './miners/MinerAndPet';
import {ActivityManager, DefaultActivityManager,} from './miners/MinerBuilder';
import {StrangerCharacter} from './miners/StrangerCharacter';
import {CharacterType, VegasCharacter,} from './miners/VegasCharacter';
import {VegasClawDisplayType} from './miners/VegasClawDisplayType';
import {ActivityListener} from './miners/VegasListener';
import {VegasMiner} from './miners/VegasMiner';
import {PetType} from './miners/VegasPet';
import {RopeDisplayType} from './miners/VegasRope';
import {VegasSpeedEngine} from './miners/VegasSpeedEngine';
import {VegasStrengthEngine} from './miners/VegasStrengthEngine';
import {VegasSwingEngine} from './miners/VegasSwingEngine';
import {NodeLoader} from "../../utils/NodeLoader";
import {Entity} from "../../engine/gm_engine";

const { ccclass, disallowMultiple, property } = cc._decorator;

enum LoadStatus {
    /** No level is loading or loaded. */
    NotLoaded,

    /** There is a loading level. */
    Loading,

    /** A level is loaded. */
    Loaded,
}

const unnestPrefab = (entity: gm.Entity) => {
    const view = entity.node;
    const parent = view.parent;
    const prefab = parent.getComponent(ee.NestedPrefab);
    if (prefab === null) {
        return;
    }
    assert(prefab.view === view);
    const prefabView = prefab.node;
    view.scaleX = prefabView.scaleX;
    view.scaleY = prefabView.scaleY;
    view.position = prefabView.position;
    new gm.NodeAngle(view).angle = new gm.NodeAngle(prefabView).angle;
    view.parent = prefabView.parent;
    prefabView.destroy();
};

const parseCharacterId = (id: string): CharacterType => {
    const dict: { [key: string]: CharacterType } = {
        ["char_miner" /*     */]: CharacterType.Miner,
        ["char_pirate" /*    */]: CharacterType.Pirate,
        ["char_jaki" /*      */]: CharacterType.Jaki,
        ["char_cactus" /*    */]: CharacterType.Cactus,
        ["char_fighter" /*   */]: CharacterType.Fighter,
        ["char_ariana" /*    */]: CharacterType.Ariana,
        ["char_samurai" /*   */]: CharacterType.Samurai,
        ["char_thor" /*      */]: CharacterType.Thor,
        ["char_doll" /*      */]: CharacterType.Doll,
        ["char_goku" /*      */]: CharacterType.Saiyan,
        ["char_bat" /*       */]: CharacterType.Bat,
        ["char_iron" /*      */]: CharacterType.Iron,
        ["char_spider" /*    */]: CharacterType.Spider,
    };
    return dict[id];
};

const parseCarId = (id: string): CharacterType => {
    const dict: { [key: string]: CharacterType } = {
        ["car_miner" /*     */]: CharacterType.Miner,
        ["car_pirate" /*    */]: CharacterType.Pirate,
        ["car_jaki" /*      */]: CharacterType.Jaki,
        ["car_cactus" /*    */]: CharacterType.Cactus,
        ["car_fighter" /*   */]: CharacterType.Fighter,
        ["car_ariana" /*    */]: CharacterType.Ariana,
        ["car_samurai" /*   */]: CharacterType.Samurai,
        ["car_thor" /*      */]: CharacterType.Thor,
        ["car_doll" /*      */]: CharacterType.Doll,
        ["car_goku" /*      */]: CharacterType.Saiyan,
        ["car_bat" /*       */]: CharacterType.Bat,
        ["car_iron" /*      */]: CharacterType.Iron,
        ["car_spider" /*    */]: CharacterType.Spider,
    };
    return dict[id];
};

const parseClawId = (id: string): VegasClawDisplayType => {
    const dict: { [key: string]: VegasClawDisplayType } = {
        ["claw_miner" /*     */]: VegasClawDisplayType.Miner,
        ["claw_pirate" /*    */]: VegasClawDisplayType.Pirate,
        ["claw_jaki" /*      */]: VegasClawDisplayType.Jaki,
        ["claw_cactus" /*    */]: VegasClawDisplayType.Cactus,
        ["claw_fighter" /*   */]: VegasClawDisplayType.Fighter,
        ["claw_ariana" /*    */]: VegasClawDisplayType.Ariana,
        ["claw_samurai" /*   */]: VegasClawDisplayType.Samurai,
        ["claw_thor" /*      */]: VegasClawDisplayType.Thor,
        ["claw_doll" /*      */]: VegasClawDisplayType.Doll,
        ["claw_goku" /*      */]: VegasClawDisplayType.Saiyan,
        ["claw_bat" /*       */]: VegasClawDisplayType.Bat,
        ["claw_iron" /*      */]: VegasClawDisplayType.Iron,
        ["claw_spider" /*    */]: VegasClawDisplayType.Spider,
    };
    return dict[id];
};

const parseRopeId = (id: string): RopeDisplayType => {
    const dict: { [key: string]: RopeDisplayType } = {
        ["rope_miner" /*     */]: RopeDisplayType.Miner,
        ["rope_pirate" /*    */]: RopeDisplayType.Pirate,
        ["rope_jaki" /*      */]: RopeDisplayType.Jaki,
        ["rope_cactus" /*    */]: RopeDisplayType.Cactus,
        ["rope_fighter" /*   */]: RopeDisplayType.Fighter,
        ["rope_ariana" /*    */]: RopeDisplayType.Ariana,
        ["rope_samurai" /*   */]: RopeDisplayType.Samurai,
        ["rope_thor" /*      */]: RopeDisplayType.Thor,
        ["rope_doll" /*      */]: RopeDisplayType.Doll,
        ["rope_goku" /*      */]: RopeDisplayType.Saiyan,
        ["rope_bat" /*       */]: RopeDisplayType.Bat,
        ["rope_iron" /*      */]: RopeDisplayType.Iron,
        ["rope_spider" /*    */]: RopeDisplayType.Spider,
    };
    return dict[id];
};

const parsePetId = (id: string): PetType => {
    const dict: { [key: string]: PetType } = {
        ["pet_miner" /*     */]: PetType.Miner,
        ["pet_pirate" /*    */]: PetType.Pirate,
        ["pet_jaki" /*      */]: PetType.Jaki,
        ["pet_cactus" /*    */]: PetType.Cactus,
        ["pet_fighter" /*   */]: PetType.Fighter,
        ["pet_ariana" /*    */]: PetType.Ariana,
        ["pet_samurai" /*   */]: PetType.Samurai,
        ["pet_thor" /*      */]: PetType.Thor,
        ["pet_doll" /*      */]: PetType.Doll,
        ["pet_goku" /*      */]: PetType.Saiyan,
        ["pet_bat" /*       */]: PetType.Bat,
        ["pet_iron" /*      */]: PetType.Iron,
        ["pet_spider" /*    */]: PetType.Spider,
        ["pet_null" /*      */]: PetType.Null,
    };
    return dict[id];
};

/**
 * Used in editor.
 * Manages entity view, contains:
 * - Border.
 * - Level items.
 * - Miner character.
 * - Other players' miner character.
 * Has its own EntityManager.
 */
@ccclass
@disallowMultiple
export default class LevelEntityViewImpl extends LevelEntityView {
    /** Used to draw collision polygons. */
    @property({ type: cc.Graphics, visible: true })
    private readonly _debugDrawer: cc.Graphics | null = null;

    /**
     * Contains the entity view.
     * This is fixed.
     */
    @property({ type: gm.EntityView, visible: true })
    private readonly _entityView: gm.EntityView | null = null;

    /**
     * Contains the border prefabs.
     * This is fixed.
     */
    @property({ type: ee.NestedPrefab, visible: true })
    private readonly _borderPrefab: ee.NestedPrefab | null = null;

    /**
     * Contains miner prefabs.
     * These are fixed.
     */
    @property(ee.NestedPrefab)
    private readonly minerPrefabs: ee.NestedPrefab[] = [];

    private loadStatus: LoadStatus = LoadStatus.NotLoaded;

    private _boosterStorageManager?: BoosterStorageManager;
    private _entityManager?: gm.EntityManager;
    private _activityManager?: ActivityManager;
    private activities: gm.Activity[] = [];
    private _controllers?: MinerController[];

    private _dynamiteLoader?: PrefabLoader;

    private get dynamiteLoader(): PrefabLoader {
        return gm.retrieveUndefined(this._dynamiteLoader);
    }

    private get boosterStorageManager(): BoosterStorageManager {
        return gm.retrieveUndefined(this._boosterStorageManager);
    }

    public get entityManager(): gm.EntityManager {
        return gm.retrieveUndefined(this._entityManager);
    }

    public tutorialListener: TutorialListener = {};

    private get debugDrawer(): cc.Graphics {
        return gm.retrieveNull(this._debugDrawer);
    }

    private get entityView(): gm.EntityView {
        return gm.retrieveNull(this._entityView);
    }

    private get borderPrefab(): ee.NestedPrefab {
        return gm.retrieveNull(this._borderPrefab);
    }

    public get controllers(): MinerController[] {
        if (this._controllers) {
            return this._controllers;
        }
        const miners = this.miners;
        return this._controllers = miners.map(miner => ({
            get item(): VegasMiner {
                return miner;
            },
            applyBooster: (boosterManager: gm.BoosterManager) => {
                if (boosterManager.isBoosterEnabled(gm.BoosterType.TitanRope)) {
                    miner.rope.displayType = RopeDisplayType.Titanium;
                }
            },
            applyDeck: (deck: ItemCardDeck) => {
                // Testing a specific card code\

                /*deck.equip(ItemCardType.Character,'char_bat');
                deck.equip(ItemCardType.Car,'car_bat');
                deck.equip(ItemCardType.Claw,'claw_bat');
                deck.equip(ItemCardType.Rope,'rope_bat');
                deck.equip(ItemCardType.Pet,'pet_bat');

                const manager = ee.ServiceLocator.resolve(CardManager);
                manager.findCardById('char_bat').level = manager.getMaxLevel(ItemCardRarity.Legendary);
                manager.findCardById('car_bat').level = manager.getMaxLevel(ItemCardRarity.Legendary);
                manager.findCardById('claw_bat').level = manager.getMaxLevel(ItemCardRarity.Legendary);
                manager.findCardById('rope_bat').level = manager.getMaxLevel(ItemCardRarity.Legendary);
                manager.findCardById('pet_bat').level = manager.getMaxLevel(ItemCardRarity.Legendary);*/

                /*deck.equip(ItemCardType.Character,'char_miner');
                deck.equip(ItemCardType.Car,'car_miner');
                deck.equip(ItemCardType.Claw,'claw_miner');
                deck.equip(ItemCardType.Rope,'rope_miner');
                deck.equip(ItemCardType.Pet,'pet_miner');

                const manager = ee.ServiceLocator.resolve(CardManager);
                manager.findCardById('char_miner').level = manager.getMaxLevel(ItemCardRarity.Common);
                manager.findCardById('car_miner').level = manager.getMaxLevel(ItemCardRarity.Common);
                manager.findCardById('claw_miner').level = manager.getMaxLevel(ItemCardRarity.Common);
                manager.findCardById('rope_miner').level = manager.getMaxLevel(ItemCardRarity.Common);
                manager.findCardById('pet_miner').level = manager.getMaxLevel(ItemCardRarity.Common);*/

                const charId = deck.getItem(ItemCardType.Character).id;
                const carId = deck.getItem(ItemCardType.Car).id;
                const clawId = deck.getItem(ItemCardType.Claw).id;
                const ropeId = deck.getItem(ItemCardType.Rope).id;
                const petId = deck.getItem(ItemCardType.Pet).id;

                miner.character.type = parseCharacterId(charId);
                miner.character.carType = parseCarId(carId);
                miner.claw.displayType = parseClawId(clawId);
                miner.pet.type = parsePetId(petId);
                miner.rope.displayType = parseRopeId(ropeId);

                // Retrieve all stats.
                const values: { [key: number]: number } = {};
                const stats = deck.getStats();
                stats.forEach(item => values[item.type] = item.base * (1 + item.boost / 100));

                (miner.strengthEngine as VegasStrengthEngine).setBonusStrength(values[ItemCardStatsType.Strength]);
                (miner.speedEngine as VegasSpeedEngine).setBonusSpeed(values[ItemCardStatsType.Speed] / 100);
                (miner.swingEngine as VegasSwingEngine).setStability(values[ItemCardStatsType.Stability] / 100);
                miner.luckStat = values[ItemCardStatsType.Luck] / 100;
                miner.bonusStat = values[ItemCardStatsType.Bonus] / 100;
            },
            dig: async () => {
                return miner.processCommand(new gm.MinerDigCommand());
            },
            digAtPosition: async (position: cc.Vec2) => {
                const rope = miner.rope;
                const worldSrc = rope.node.convertToWorldSpaceAR(cc.Vec2.ZERO);
                const worldDst = this.node.convertToWorldSpaceAR(position);
                const difference = worldDst.sub(worldSrc);

                const angle = cc.misc.radiansToDegrees(Math.atan2(-difference.y, difference.x)) - 90;
                return miner.processCommand(new gm.MinerDigCommand(angle));
            },
            digByAngle: async (angle: number) => {
                return miner.processCommand(new gm.MinerDigCommand(angle));
            },
            move: async (position: cc.Vec2) => {
                const worldPosition = this.node.convertToWorldSpaceAR(position);
                const target = miner.node.parent.convertToNodeSpaceAR(worldPosition).x;
                return miner.processCommand(new gm.MinerMoveCommand(target));
            },
            throw: async () => {
                return await this.throwDynamite(miner);
            },
        }));
    }

    /** Gets all miners in the current level. */
    private get miners(): VegasMiner[] {
        return this.entityManager.findEntities(VegasMiner);
    }

    @crashlytics
    protected onLoad(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        assert(this._debugDrawer !== null);
        assert(this._entityView !== null);
        assert(this._borderPrefab !== null);
        assert(this.minerPrefabs.length === 2);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    public async loadLevel(
        miners: number,
        levelLoader: NodeLoader,
        dynamiteLoader: PrefabLoader,
    ): Promise<void> {
        if (this.loadStatus === LoadStatus.Loading) {
            // Ignore.
            throw Error('Loading in progress.');
        }
        if (miners !== 1 && miners !== 2) {
            throw Error('Invalid miner count.');
        }
        this.loadStatus = LoadStatus.Loading;

        // Clear the current level.
        // This will remove the loaded level and all added entities during the last level.
        this.entityView.node.removeAllChildren(true);

        // Release all captured item.
        this.minerPrefabs.forEach(item => {
            const minerView = item.view;
            if (minerView === null) {
                throw Error('Item not loaded.');
            }
            const minerAndPet = minerView.getComponent(MinerAndPet);
            minerAndPet.resetPosition();
            minerAndPet.getMiner().claw.releaseItem();
        });

        // Load new level view.
        const view = await levelLoader.load();

        // Add loaded level to the entity view.
        this.entityView.node.addChild(this.borderPrefab.node, gm.EntityOrder.Border);

        const asyncManager = new ee.AsyncManager();
        this.entityView.node.addChild(view, gm.EntityOrder.Entity);
        await asyncManager.flushAll({ size: 100 });
        asyncManager.destroy();

        // FIXME: should use dynamic distance.
        const minerDistance = 800;
        for (let i = 0; i < miners; ++i) {
            const item = this.minerPrefabs[i];
            item.node.x = -(minerDistance / 2) * (miners - 1) + minerDistance * i;
            this.entityView.node.addChild(item.node, gm.EntityOrder.Miner);
        }

        // ========== nhanc18 ========== start
        // Manual update alignment to ensure view is in correct position immediately.
        // Must be called after being added to scene.
        // view.getComponent(cc.Widget).updateAlignment();

        // Manual update alignment to ensure view is in correct position immediately.
        // Must be called after being added to scene.
        const viewWidget = view.addComponent(cc.Widget);
        viewWidget.top = viewWidget.right = viewWidget.bottom = viewWidget.left = 0;
        viewWidget.updateAlignment();
        // ========== nhanc18 ========== end

        // Unnest prefabs.
        view.getComponentsInChildren(gm.Entity)
            .filter(item => // Fix StrangerMiner.
                item.getComponent(VegasCharacter) === null &&
                item.getComponent(StrangerCharacter) === null &&
                item.getComponent(gm.Claw) === null &&
                item.getComponent(gm.Rope) === null)
            .forEach(unnestPrefab);

        this._dynamiteLoader = dynamiteLoader;
        this._controllers = undefined;
        this.loadStatus = LoadStatus.Loaded;
    }

    public initializeLevel(
        isWaterArea: boolean,
        sceneView: cc.Node,
        listener: ActivityListener,
        eventManager: gm.EventManager,
        boosterManager: gm.BoosterManager,
        boosterStorageManager: BoosterStorageManager,
        logManager: LogManager,
        configManager: ConfigManager
    ): void {
        const entityManager = new gm.DefaultEntityManager(this.entityView, eventManager);

        // Add all entities in the loaded level to the manager.
        const entities = this.entityView.getComponentsInChildren(gm.Entity);
        entities.forEach(entity => entityManager.addEntity(entity));

        this._boosterStorageManager = boosterStorageManager;
        this._entityManager = entityManager;

        const audioManager = ee.ServiceLocator.resolve(AudioManager);
        this._activityManager = new DefaultActivityManager(
            audioManager,
            boosterManager,
            boosterStorageManager,
            entityManager,
            eventManager,
            logManager,
            sceneView,
            listener,
            isWaterArea,
        );

        const [activities, listeners /* Unused */] = this._activityManager.create();
        this.activities = [...activities];

        if (CC_EDITOR && CC_DEBUG) {
            this.activities.push(new gm.DebugActivity(this.debugDrawer));
        }

        this.initializeTankForFish(entityManager);
        this.initializeWolf(entityManager);

        // Initialize miners.
        this.miners.forEach(miner => {
            miner.listener = {
                throwItem: async () => await this.throwDynamite(miner),
                pickItem(item: Entity) {
                    this.pickItem(miner, item);
                }
            };
            miner.claw.type = gm.ClawType.Trivia;
            miner.character.boatEnabled = isWaterArea;
            miner.pet.node.active = !isWaterArea;
        });
    }

    public destroyLevel(): void {
        // Clear all activities.
        this.activities = [];

        // Destroys the current activity manager.
        this._activityManager && this._activityManager.destroy();

        // Remove existing entities.
        if (this._entityManager) {
            [...this.entityManager.getEntities()].forEach(item => {
                this.entityManager.removeEntity(item);
            });
        }

        this._entityManager = new gm.NullEntityManager();
        this._boosterStorageManager = new NullBoosterStorageManager();
    }

    public step(delta: number): void {
        for (let i = 0, n = this.activities.length; i < n; ++i) {
            const activity = this.activities[i];
            activity.processUpdate(delta, this.entityManager);
        }
    }

    private async throwDynamite(miner: VegasMiner): Promise<boolean> {
        const prefab = await this.dynamiteLoader.load();
        assert(prefab !== null);
        const dynamite = cc.instantiate(prefab);
        const entity = dynamite.getComponent(gm.Entity);

        const canThrow = miner.throwItem(entity);
        if (!canThrow) {
            return false;
        }
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.Throw);

        const boosterName = RewardUtils.parserBoosterRewardSubType(gm.BoosterType.Dynamite);
        ee.ServiceLocator.resolve(TrackingManager).trackEventSinkBooster(SceneName.SceneStoryInGame,
            TrackResultBooster.Used, TrackSinkType.Booster, `booster_${boosterName}`, 1);

        this.tutorialListener.onThrowDynamite && this.tutorialListener.onThrowDynamite();

        const view = this.entityManager.getView().node;
        const rope = miner.rope.node;
        const position = new gm.TransformHelper(rope, view).convertTo(cc.Vec2.ZERO);
        dynamite.position = cc.v3(position);

        view.addChild(entity.node);

        entity.addComponent(gm.Throwable);
        this.entityManager.addEntity(entity);
        return true;
    }

    private pickItem(miner: VegasMiner, item: Entity) {
        miner.strategy.pickItem(item)
    }

    // private async throw(miner: VegasMiner): Promise<boolean> {
    //     //const boosterStorageManager = this.boosterStorageManager;
    //     const dynamiteQuantity = boosterStorageManager.getBalance(gm.BoosterType.Dynamite);
    //     if (dynamiteQuantity === 0) {
    //         return false;
    //     }
    //     const prefab = await this.dynamiteLoader.load();
    //     assert(prefab !== null);
    //     const dynamite = cc.instantiate(prefab);
    //     const entity = dynamite.getComponent(gm.Entity);
    //
    //     const canThrow = miner.throwItem(entity);
    //     if (!canThrow) {
    //         return false;
    //     }
    //     ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.Throw);
    //
    //     boosterStorageManager.addBalance(gm.BoosterType.Dynamite, -1);
    //     this.miners[0].setDynamiteCount();
    //
    //     const boosterName = RewardUtils.parserBoosterRewardSubType(gm.BoosterType.Dynamite);
    //     ee.ServiceLocator.resolve(TrackingManager).trackEventSinkBooster(SceneName.SceneStoryInGame,
    //         TrackResultBooster.Used, TrackSinkType.Booster, `booster_${boosterName}`, 1);
    //
    //     this.tutorialListener.onThrowDynamite && this.tutorialListener.onThrowDynamite();
    //
    //     const view = this.entityManager.getView().node;
    //     const rope = miner.rope.node;
    //     const position = new gm.TransformHelper(rope, view).convertTo(cc.Vec2.ZERO);
    //     dynamite.position = cc.v3(position);
    //
    //     // Add to scene first to finish initializing other entity components.
    //     view.addChild(entity.node);
    //
    //     // Must be added later to make sure checkComponents succeeds in Dynamite.ts.
    //     entity.addComponent(gm.Throwable);
    //
    //     // Must be added last to ensure cache entity manager works correctly.
    //     this.entityManager.addEntity(entity);
    //     return true;
    // }

    /** Initializes all fish tanks. */
    private initializeTankForFish(manager: gm.EntityManager): void {
        const fishes = manager.findEntities(gm.Fish);
        const tanks = manager.findEntities(gm.FishTank);
        fishes.forEach(fish => {
            const fishShape = fish.getComponent(gm.Collidable).getShape(null);
            tanks.some(tank => {
                const tankShape = tank.getComponent(gm.Collidable).getShape(null);
                if (tankShape.collides(fishShape)) {
                    fish.tank = tank;
                    return true;
                }
                return false;
            });
        });
    }

    /** Initializes audio listener for all wolf entities. */
    private initializeWolf(manager: gm.EntityManager): void {
        const audioManager = ee.ServiceLocator.resolve(AudioManager);
        const items = manager.findEntities(gm.Wolf);
        items.forEach(item => {
            item.listener = new (class implements gm.WolfListener {
                private audioId?: number;
                private stopSound(): void {
                    this.audioId && audioManager.stopSoundById(this.audioId);
                }
                private playSound(): void {
                    this.stopSound();
                    this.audioId = audioManager.playSound(SoundType.WolfRoar);
                }
                public onPulled(sender: gm.Wolf): void {
                    this.stopSound();
                }
                public onCarted(sender: gm.Wolf): void {
                    this.stopSound();
                }
                public onTargeting(sender: gm.Wolf): void {
                    this.playSound();
                }
                public onFinishConsuming(sender: gm.Wolf): void {
                    this.stopSound();
                }
                public onLostTarget(sender: gm.Wolf): void {
                    this.stopSound();
                }
                public onDestroyed(sender: gm.Wolf): void {
                    this.stopSound();
                }
            })();
        });
    }
}