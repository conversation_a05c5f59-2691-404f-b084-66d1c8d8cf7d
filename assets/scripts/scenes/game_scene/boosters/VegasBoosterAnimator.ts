import * as gm from "../../../engine/gm_engine";

import { BoosterUtils } from "./BoosterUtils";

export class VegasBoosterAnimator extends gm.MultipleBoosterAnimator {
    private sceneView: cc.Node;

    public constructor(sceneView: cc.Node) {
        super();
        this.sceneView = sceneView;
    }

    public play(doneCallback: () => void): void {
        // Add a fade layer.
        const view = BoosterUtils.createSprite('images/other/white');
        view.color = cc.color(0, 0, 0);
        view.opacity = 0;
        view.getComponent(cc.Sprite).sizeMode = cc.Sprite.SizeMode.CUSTOM;
        view.setContentSize(this.sceneView.getContentSize());
        view.runAction(cc.fadeTo(0.2, 150));
        this.sceneView.addChild(view);

        super.play(() => {
            view.removeFromParent(true);
            doneCallback();
        });
    }
}