import * as gm from '../../../engine/gm_engine';

import { ParticleUtils } from '../../../utils/ParticleUtils';
import { VegasMiner } from '../miners/VegasMiner';

export class BoosterUtils {
    public static createSprite(spriteName: string): cc.Node {
        const node = new cc.Node();
        const sprite = node.addComponent(cc.Sprite);
        cc.loader.loadRes(spriteName, cc.SpriteFrame, (err: Error | null, res: cc.SpriteFrame) => {
            sprite.spriteFrame = res;
        });
        return node;
    }

    public static createMinerEntry(manager: gm.EntityManager, sceneView: cc.Node): gm.BoosterAnimatorEntry[] {
        const items = manager.findEntities(VegasMiner);
        const entries = items.map(item => {
            const aabb = item.getComponent(gm.Collidable).getShape(sceneView).getAABB();
            const position = aabb.center;
            const callback = () => {
                const node = this.createSpookyParticle(1.0);
                node.position = cc.v2(aabb.center.x, aabb.yMin);
                sceneView.addChild(node);
            };
            return [item.node, position, callback] as gm.BoosterAnimatorEntry;
        });
        return entries;
    }

    public static createLuckyParticle(duration: number): cc.Node {
        return ParticleUtils.createParticle({
            file: 'particles/boosters/BoosterEff_Lucky',
            duration,
        });
    }

    public static createSpeedParticle(duration: number): cc.Node {
        return ParticleUtils.createParticle({
            file: 'particles/boosters/BoosterEff_Speed',
            duration,
        });
    }

    public static createDiamondParticle(duration: number): cc.Node {
        return ParticleUtils.createParticle({
            file: 'particles/boosters/BoosterEff_Diamond',
            duration,
        });
    }

    public static createSpookyParticle(duration: number): cc.Node {
        return ParticleUtils.createParticle({
            file: 'particles/boosters/BoosterEff_Spooky',
            duration,
        });
    }
}