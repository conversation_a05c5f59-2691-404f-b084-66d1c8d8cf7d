import { StoryLevelView } from '../StoryLevelView';
import {<PERSON><PERSON><PERSON>, <PERSON>, LavaGhost, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "../../../engine/gm_engine";
import {LevelManager} from "../../../manager/level/LevelManager";
import * as ee from "../../../libraries/ee/index";
import * as gm from '../../../engine/gm_engine';

export class pickUpBoosterController {

    private levelManager: LevelManager;

    public constructor(
        private readonly levelView: StoryLevelView
    ) {
        this.levelManager = ee.ServiceLocator.resolve(LevelManager);
    }

    public isActived() : boolean {
        return this.levelManager.isPickUpActive;
    }

    public get isPickingUp(): boolean {
        return this.levelManager.isPickingUp;
    }

    public set isPickingUp(value: boolean) {
        this.levelManager.isPickingUp = value;
    }

    public ActivePickUp() {
        if (this.minerIsPulling()) {
            return;
        }
        if (this.levelManager.isPickUpActive) {
            return;
        }
        this.levelManager.isPickUpActive = true;
        const entityManager = this.levelView.entityView.entityManager;
        const items = entityManager.findComponents(gm.Pickable);
        for (let i = items.length - 1; i >= 0; --i) {
            const item = items[i];
            item.blinkGreen();
            const pullable = item.getComponent(gm.Pullable);
            pullable && pullable.onPulled(() => {
                item.stopBlink();
            });
        }
    }

    private minerIsPulling(): boolean {
        const miner = this.levelView.entityView.controllers[0].item
        return miner.claw.isCapturing();
    }

    public deactivePickUp() {
        if (!this.levelManager.isPickUpActive) {
            return;
        }
        this.levelManager.isPickUpActive = false;
        const entityManager = this.levelView.entityView.entityManager;
        const items = entityManager.findComponents(gm.Pickable);
        for (let i = items.length - 1; i >= 0; --i) {
            const item = items[i];
            item.stopBlink();
        }
    }

    public tryToPickAt(position: cc.Vec2): Entity {
        const entityManager = this.levelView.entityView.entityManager;
        const items = entityManager.findComponents(gm.Pickable);
        for (let i = items.length - 1; i >= 0; --i) {
            const item = items[i];
            if (!item.active) {
                continue;
            }

            let rect = item.pickSize.getBoundingBoxToWorld();

            // trường hợp LavaGhost có pickSize là của anim explosive => khi idle thì lấy tạm đổi lấy size là mask
            const lavaGhost = item.getComponent(LavaGhost);
            if (lavaGhost != undefined) {
                if (lavaGhost.canDie()) {
                    rect = item.mask.getBoundingBoxToWorld();
                }
            }

            if (rect.contains(position)) {
                return item.getComponent(Entity);
            }
        }
        return undefined;
    }
}
