import * as gm from '../../engine/gm_engine';

export interface LevelController {
    /** Applies the specified booster. */
    applyBoosters(boosters: gm.BoosterType[]): void;

    /** Resumes the current level. */
    resumeLevel(): void;

    /** Pauses the current level. */
    pauseLevel(): void;

    /** Immediately makes the current level failed. */
    failLevel(): void;

    /** Replays the current level. */
    replayLevel(): Promise<void>;

    /** Processes to the next level. */
    nextLevel(): Promise<void>;

    /** Check if passed target */
    passedTarget(): boolean;

    /** Returns to the map scene. */
    backToMap(): void;

    /** Capture screen */
    captureScreen(): void;

    fadeIn(duration: number): Promise<void>;

    fadeOut(duration: number): Promise<void>;
}

export class NullLevelController implements LevelController {
    public applyBoosters(boosters: gm.BoosterType[]): void { }
    public resumeLevel(): void { }
    public pauseLevel(): void { }
    public failLevel(): void { }
    public async replayLevel(): Promise<void> { }
    public async nextLevel(): Promise<void> { }
    public backToMap(): void { }
    public passedTarget(): boolean { return false; }
    public captureScreen(): void { }
    public async fadeIn(duration: number): Promise<void> { }
    public async fadeOut(duration: number): Promise<void> { }
}