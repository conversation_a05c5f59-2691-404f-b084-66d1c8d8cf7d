
/** Displays the current progression/target for the current story level. */
export abstract class StoryLevelProgressView extends cc.Component {
    /** Gets or sets the current progress. */
    public abstract progress: number;

    /** Gets or sets the target progress. */
    public abstract targets: number[];

    /** Starts blinking, used in tutorial. */
    public abstract startBlinking(): void;

    /** Stops blinking, used in tutorial. */
    public abstract stopBlinking(): void;
}