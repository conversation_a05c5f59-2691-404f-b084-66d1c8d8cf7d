const { ccclass, property, executeInEditMode } = cc._decorator;

@ccclass
@executeInEditMode
export class DolphinAnimation extends cc.Component {
    @property(cc.Animation)
    private readonly dolphin: cc.Animation | null = null;

    protected onLoad(): void {
        const duration = this.dolphin!.defaultClip.duration / this.dolphin!.defaultClip.speed;
        this.dolphin!.node.opacity = 0;
        this.node.runAction(cc.repeatForever(cc.sequence(
            cc.sequence(
                cc.scaleBy(0, Math.random() < 0.5 ? -1 : 1, 1),
                cc.callFunc(() => {
                    this.dolphin!.node.opacity = 255;
                    this.dolphin!.node.x = (Math.random() - 0.5) / 0.5 * this.node.width / 2;
                    this.dolphin!.play();
                }),
                cc.delayTime(duration),
                cc.targetedAction(this.dolphin!.node, cc.fadeOut(0)),
            ),
            cc.delayTime(4),
        )));
    }
}
