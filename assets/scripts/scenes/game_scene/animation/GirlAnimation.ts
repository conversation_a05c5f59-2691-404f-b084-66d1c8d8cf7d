
const { ccclass, property } = cc._decorator;

@ccclass
export default class GirlAni<PERSON> extends cc.Component {
    @property(cc.Node)
    private readonly girl: cc.Node | null = null;

    @property(cc.Animation)
    private readonly animationGirl: cc.Animation | null = null;

    protected onLoad(): void {
        const duration = this.animationGirl!.defaultClip.duration / this.animationGirl!.defaultClip.speed;
        this.animationGirl.node.opacity = 0;
        this.node.opacity = 255;

        cc.tween(this.node)
            .call(() => {
                this.animationGirl.play();
            })
            .delay(2)
            .call(() => {
                this.girl.opacity = 0;
                this.animationGirl.node.opacity = 255;
                this.animationGirl.play();
            })
            .start()
    }
}
