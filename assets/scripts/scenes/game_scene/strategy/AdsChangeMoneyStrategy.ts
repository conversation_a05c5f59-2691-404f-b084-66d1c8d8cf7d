import { ChangeMoneyStrategy } from "./ChangeMoneyStrategy";

export class AdsAnimatedChangeMoneyStrategy implements ChangeMoneyStrategy {
    public async perform(sceneView: cc.Node, src: cc.Vec2, dst: cc.Vec2, value: number): Promise<void> {
        const node = new cc.Node();
        node.color = cc.Color.GREEN;
        node.position = cc.v3(src);
        sceneView.addChild(node);

        const label = node.addComponent(cc.Label);
        label.string = value.toString();
        label.fontSize = 70;
        label.lineHeight = 0;

        const outline = node.addComponent(cc.LabelOutline);
        outline.color = cc.Color.BLACK;
        outline.width = 2;

        const action = cc.sequence(
            cc.spawn(
                cc.moveBy(0.3, cc.v2(-150, 160)),
                cc.scaleTo(0.3, 1)),
            cc.delayTime(0.2),
            cc.repeat(
                cc.sequence(
                    cc.callFunc(() => {
                        node.color = cc.Color.WHITE;
                    }),
                    cc.delayTime(0.15),
                    cc.callFunc(() => {
                        node.color = cc.Color.GREEN;
                    }),
                    cc.delayTime(0.1)),
                2));
        await new Promise<void>(resolve => node.runAction(cc.sequence(action, cc.callFunc(() => resolve()))));
        node.removeFromParent(true);
        node.destroy();
    }
}