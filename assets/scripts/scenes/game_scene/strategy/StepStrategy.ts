export interface StepStrategy {
    reset(): void;

    /** Server steps. */
    tick(delta: number): void;

    /** Local steps. */
    step(delta: number): void;
}

export class VariantStepStrategy implements StepStrategy {
    private accumulatedDelta = 0;

    public constructor(
        private readonly stepDuration: number,
        private readonly callback: (stepDuration: number) => void) {
    }

    public reset(): void {
        this.accumulatedDelta = 0;
    }

    public tick(delta: number): void {
        // No effect.
    }

    public step(delta: number): void {
        this.accumulatedDelta += delta;
        while (this.accumulatedDelta >= this.stepDuration) {
            this.accumulatedDelta -= this.stepDuration;
            this.callback(this.stepDuration);
        }
    }
}

export class FullVariantStepStrategy implements StepStrategy {
    private accumulatedDelta = 0;

    public constructor(
        private readonly minStepDuration: number,
        private readonly maxStepDuration: number,
        private readonly callback: (stepDuration: number) => void) {
    }

    public reset(): void {
        this.accumulatedDelta = 0;
    }

    public tick(delta: number): void { }

    public step(delta: number): void {
        this.accumulatedDelta += delta;
        while (this.accumulatedDelta >= this.maxStepDuration) {
            this.accumulatedDelta -= this.maxStepDuration;
            this.callback(this.maxStepDuration);
        }
        if (this.accumulatedDelta >= this.minStepDuration) {
            this.callback(this.accumulatedDelta);
            this.accumulatedDelta = 0;
        }
    }
}

export class SmoothStepStrategy implements StepStrategy {
    private accumulatedDelta = 0;
    private serverAccumulatedDelta = 0;

    public constructor(
        private readonly strategy: StepStrategy) {
    }

    public reset(): void {
        this.accumulatedDelta = 0;
        this.serverAccumulatedDelta = 0;
        this.strategy.reset();
    }

    public tick(delta: number): void {
        this.serverAccumulatedDelta += delta;
    }

    public step(_delta: number): void {
        const delta = Math.max(0, Math.min(_delta, this.serverAccumulatedDelta - this.accumulatedDelta));
        this.accumulatedDelta += delta;
        this.strategy.step(delta);
    }
}

export interface SimulationManager {
    paused: boolean;
    speed: number;
    pickUpOn: boolean;
    reset(): void;
    tick(delta: number): number;
    step(delta: number): number;
}

export class DefaultSimulationManager implements SimulationManager {
    public paused = false;
    public pickUpOn = false;
    public speed = 1;

    public constructor(private readonly strategy: StepStrategy) {
    }

    public reset(): void {
        this.strategy.reset();
    }

    private calculate(delta: number): number {
        if (this.paused) {
            return 0;
        }
        return delta * this.speed;
    }

    public tick(_delta: number): number {
        if (this.paused) {
            return 0;
        }
        const delta = this.calculate(_delta);
        this.strategy.tick(delta);
        return delta;
    }

    public step(_delta: number): number {
        if (this.paused) {
            return 0;
        }
        const delta = this.calculate(_delta);
        if (!this.pickUpOn) {
            this.strategy.step(delta);
        }
        return delta;
    }
}