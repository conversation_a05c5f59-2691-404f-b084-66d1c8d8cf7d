import assert = require('assert');
import * as gm from '../../../engine/gm_engine';
import * as ee from '../../../libraries/ee/index';
import { ChestManager } from '../../../manager/chest/ChestManager';
import { LevelManager } from '../../../manager/gm_manager';

export interface AddChestStrategy {
    perform(manager: gm.EntityManager): void;
}

export class DefaultAddChestStrategy implements AddChestStrategy {
    public perform(manager: gm.EntityManager): void {
        const view = manager.getView().node;
        const viewSize = view.getContentSize();

        assert(view.anchorX === 0.5 && view.anchorY === 0.5);
        const minX = viewSize.width * -0.4;
        const maxX = viewSize.width * +0.4;
        const minY = viewSize.height * -0.4;
        const maxY = viewSize.height * +0.1;
        const area = cc.rect(minX, minY, maxX - minX, maxY - minY);

        const helper = new gm.SpaceFinder(area);
        manager.findComponents(gm.Collidable).forEach(item => {
            const shape = item.getShape(view);
            const aabb = shape.getAABB();
            helper.addObstacle(aabb);
        });

        cc.loader.loadRes('prefabs/level_items/chest', (err, prefab: cc.Prefab) => {
            const node = cc.instantiate(prefab);
            view.addChild(node, gm.EntityOrder.Chest);

            const chestManager = ee.ServiceLocator.resolve(ChestManager);
            const chest = node.getComponent(gm.Chest);
            chest.setType(chestManager.getRandomSpawnChestType());
            manager.addEntity(chest);

            const chestShape = chest.getComponent(gm.Collidable).getShape(view);
            const chestAabb = chestShape.getAABB();
            const offset = cc.v2(chestAabb.xMin, chestAabb.yMin);

            const position = helper.solve(chestAabb.size);
            node.position = cc.v3(position.sub(offset));
        });
    }
}

export class ConditionAddChestStrategy implements AddChestStrategy {
    public constructor(
        private readonly strategy: AddChestStrategy,
        private readonly levelManager: LevelManager,
        private readonly limitArea: number,
        private readonly limitLevel: number) {
    }

    public perform(manager: gm.EntityManager): void {
        const nowArea = this.levelManager.getCurrentStoryArea();
        const nowLevel = this.levelManager.getCurrentStoryLevel();
        if (nowArea > this.limitArea || nowLevel >= this.limitLevel) {
            this.strategy.perform(manager);
        }
    }
}