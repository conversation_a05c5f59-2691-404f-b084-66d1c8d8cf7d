import * as gm from '../../engine/gm_engine';

const { ccclass, disallowMultiple, property } = cc._decorator;

@ccclass
@disallowMultiple
export class LevelTargetSeparator extends cc.Component {
    @property({ type: cc.Sprite, visible: true })
    private readonly _barSprite: cc.Sprite | null = null;

    @property({ type: cc.SpriteFrame, visible: true })
    private readonly _barSpriteFrame: cc.SpriteFrame | null = null;

    @property({ type: cc.SpriteFrame, visible: true })
    private readonly _disabledBarSpriteFrame: cc.SpriteFrame | null = null;

    @property({ type: cc.Sprite, visible: true })
    private readonly _starSprite: cc.Sprite | null = null;

    @property({ type: cc.SpriteFrame, visible: true })
    private readonly _starSpriteFrame: cc.SpriteFrame | null = null;

    @property({ type: cc.SpriteFrame, visible: true })
    private readonly _disabledStarSpriteFrame: cc.SpriteFrame | null = null;

    private get barSprite(): cc.Sprite {
        return gm.retrieveNull(this._barSprite);
    }

    private get barSpriteFrame(): cc.SpriteFrame {
        return gm.retrieveNull(this._barSpriteFrame);
    }

    private get disabledBarSpriteFrame(): cc.SpriteFrame {
        return gm.retrieveNull(this._disabledBarSpriteFrame);
    }

    private get starSprite(): cc.Sprite {
        return gm.retrieveNull(this._starSprite);
    }

    private get starSpriteFrame(): cc.SpriteFrame {
        return gm.retrieveNull(this._starSpriteFrame);
    }

    private get disabledStarSpriteFrame(): cc.SpriteFrame {
        return gm.retrieveNull(this._disabledStarSpriteFrame);
    }

    protected onLoad(): void {
        this.setReached(false);
    }

    public setReached(reached: boolean): void {
        if (reached) {
            this.barSprite.spriteFrame = this.barSpriteFrame;
            this.starSprite.spriteFrame = this.starSpriteFrame;
        } else {
            this.barSprite.spriteFrame = this.disabledBarSpriteFrame;
            this.starSprite.spriteFrame = this.disabledStarSpriteFrame;
        }
    }
}
