import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import { UserAvatarView } from '../leaderboard/UserAvatarView';
import { PvpLevelProgressView } from './PvpLevelProgressView';

const { ccclass, disallowMultiple, property } = cc._decorator;

/** Used in editor. */
@ccclass
@disallowMultiple
class PvpLevelProgressViewImpl extends PvpLevelProgressView {
    @property({ type: cc.ProgressBar, visible: true })
    private readonly _progressBar: cc.ProgressBar | null = null;

    @property([cc.Label])
    private readonly progressLabels: cc.Label[] = [];

    @ee.nest([UserAvatarView])
    private readonly avatarViews: UserAvatarView[] = [];

    private get progressBar(): cc.ProgressBar {
        return gm.retrieveNull(this._progressBar);
    }

    private _delta = 0;
    private _total = 0;

    protected onLoad(): void {
        assert(this._progressBar !== null);
        this.updateDisplay();
    }

    public get delta(): number {
        return this._delta;
    }

    public set delta(value: number) {
        if (this._delta !== value) {
            this._delta = value;
            this.updateDisplay();
        }
    }

    public get total(): number {
        return this._total;
    }

    public set total(value: number) {
        if (this._total !== value) {
            this._total = value;
            this.updateDisplay();
        }
    }

    public async loadAvatars(urls: string[]): Promise<void> {
        await Promise.all(this.avatarViews.map((item, index) => item.loadItem(urls[index])));
    }

    /** Internally updates the display text. */
    private updateDisplay(): void {
        // Update text.
        this.progressLabels[0].string = `${Math.max(0, this.total / 2 + this.delta)}`;
        this.progressLabels[1].string = `${Math.max(0, this.total / 2 - this.delta)}`;

        // Update bar.
        this.progressBar.progress = 0.5 + this.delta / this.total;
    }
}