import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import * as ee from "../../libraries/ee/index";
import { PrefabUtils } from "../../utils/PrefabUtils";
import { BoosterLayer } from './BoosterLayer';
import { DropDownMenu } from "../../ui/DropDownMenu";
import { TopHud } from '../common/TopHud';
import {BottomHud} from "../common/BottomHud";

const { ccclass, disallowMultiple, property } = cc._decorator;

@ccclass
@disallowMultiple
export class BagLayer extends cc.Component {
    public static create(): Promise<BagLayer> {
        return PrefabUtils.createPrefab(this, 'prefabs/bag/bag_layer');
    }

    @property({ type: cc.Node, visible: true })
    private readonly _content: cc.Node | null = null;

    @ee.nest(BoosterLayer)
    private readonly _boosterLayer: BoosterLayer | null = null;

    private get content(): cc.Node {
       return gm.retrieveNull(this._content);
    }

    private get boosterLayer(): BoosterLayer {
        return gm.retrieveNull(this._boosterLayer);
    }

    private _topHud: TopHud | null = null;
    private _bottomHud : BottomHud | null = null;

    public get topHud(): TopHud {
        return gm.retrieveNull(this._topHud);
    }

    public get bottomHud(): BottomHud {
        return gm.retrieveNull(this._bottomHud);
    }

    public set topHud(value: TopHud) {
        this._topHud = value;
        this.boosterLayer.topHud = this.topHud;
    }

    public set bottomHud(value: BottomHud) {
        this._bottomHud = value;
        this.boosterLayer.bottomHud = this.bottomHud;
    }


    private isInitialized = false;

    protected onLoad(): void {
        ee.AsyncManager.getInstance().add(BagLayer.name, async () => {
            // if (this._topHud !== null) {
            //     this.achievementLayer.topHud = this.topHud;
            // }
            this.updateRendering();
            // this.achievementLayer.updateDisplay();
            // this.achievementLayer.listener = {
            //     updated: (sender, achievements) => {
            //         this.updateRendering();
            //     },
            // };
            this.isInitialized = true;
        });
    }

    protected onEnable(): void {
        this.node.on('scrolling', this.onScrolling, this);
    }

    protected onDisable(): void {
        this.node.off('scrolling', this.onScrolling, this);
    }

    protected update(delta: number): void {
        if (!this.isInitialized) {
            return;
        }
        this.boosterLayer.node.parent.getComponent(ee.NestedPrefab).applySync();
    }

    private onScrolling(): void {
        this.updateRendering();
    }

    private updateRendering(): void {
        // const helper = new gm.TransformHelper(this.node, this.achievementLayer.node);
        // const from = helper.convertTo(cc.v2(0, -this.node.height / 2)).y;
        // const to = helper.convertTo(cc.v2(0, +this.node.height / 2)).y;
        // this.achievementLayer.clip(from, to);
    }
}
