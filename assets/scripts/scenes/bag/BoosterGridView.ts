import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {
    AreaInfo,
    BoosterInfo,
    BoosterStorageManager,
    BoosterStorageUtils, FeatureManager, LevelInfo, LevelManager,
    SceneName,
    StoreItem, StoreManager,
} from '../../manager/gm_manager';
import {BoosterPanelItem} from "../../game/booster_panel/BoosterPanelItem";
import {BuyBoosterDialog} from "../../game/booster_panel/BuyBoosterDialog";
import {BoosterPreview} from "./BoosterPreview";
import {BoosterType} from "../../engine/gm_engine";
import {PiggyBankManager} from "../PiggyBank/PiggyBankManager";

const {ccclass, property} = cc._decorator;

@ccclass
export class BoosterGridView extends cc.Component {
    @property({ type: cc.Node, visible: true })
    private readonly _layout: cc.Node | null = null;

    private get layout(): cc.Node {
        return gm.retrieveNull(this._layout);
    }

    @property({ type: cc.Prefab, visible: true })
    private readonly _itemPrefab: cc.Prefab | null = null;

    private get itemPrefab(): cc.Prefab {
        return gm.retrieveNull(this._itemPrefab);
    }

    @property({ type: cc.ScrollView, visible: true })
    private readonly _scrollView: cc.ScrollView | null = null;

    private get scrollView(): cc.ScrollView {
        return gm.retrieveNull(this._scrollView);
    }

    @property({ type: cc.Prefab, visible: true })
    private readonly _boosterDialogPrefab: cc.Prefab | null = null;

    private get boosterDialogPrefab(): cc.Prefab {
        return gm.retrieveNull(this._boosterDialogPrefab);
    }

    @property(cc.Integer)
    private _columns = 4;

    @property({ type: cc.Integer })
    public get columns(): number {
        return this._columns;
    }

    public set columns(value: number) {
        value = Math.max(value, 1);
        if (this._columns !== value) {
            this._columns = value;
        }
    }

    @property(cc.Boolean)
    private _centerize = false;

    @property({ type: cc.Boolean })
    public get centerize(): boolean {
        return this._centerize;
    }

    public set centerize(value: boolean) {
        if (this._centerize !== value) {
            this._centerize = value;
        }
    }

    @property(cc.Float)
    private _paddingLeft = 0;

    @property({ type: cc.Float })
    public get paddingLeft(): number {
        return this._paddingLeft;
    }

    public set paddingLeft(value: number) {
        if (this._paddingLeft !== value) {
            this._paddingLeft = value;
        }
    }

    @property(cc.Float)
    private _paddingRight = 0;

    @property({ type: cc.Float })
    public get paddingRight(): number {
        return this._paddingRight;
    }

    public set paddingRight(value: number) {
        if (this._paddingRight !== value) {
            this._paddingRight = value;
        }
    }

    @property(cc.Float)
    private _paddingTop = 0;

    @property({ type: cc.Float })
    public get paddingTop(): number {
        return this._paddingTop;
    }

    public set paddingTop(value: number) {
        if (this._paddingTop !== value) {
            this._paddingTop = value;
        }
    }

    @property(cc.Float)
    private _paddingBottom = 0;

    @property({ type: cc.Float })
    public get paddingBottom(): number {
        return this._paddingBottom;
    }

    public set paddingBottom(value: number) {
        if (this._paddingBottom !== value) {
            this._paddingBottom = value;
        }
    }

    @property(cc.Float)
    private _spacingX = 0;

    @property({ type: cc.Float })
    public get spacingX(): number {
        return this._spacingX;
    }

    public set spacingX(value: number) {
        if (this._spacingX !== value) {
            this._spacingX = value;
        }
    }

    @property(cc.Float)
    private _spacingY = 0;

    @property({ type: cc.Float })
    public get spacingY(): number {
        return this._spacingY;
    }

    public set spacingY(value: number) {
        if (this._spacingY !== value) {
            this._spacingY = value;
        }
    }

    private _cellWidth = 1;

    public get cellWidth(): number {
        return this._cellWidth;
    }

    public set cellWidth(value: number) {
        if (this._cellWidth !== value) {
            this._cellWidth = value;
        }
    }

    public get cellHeight(): number {
        return this.cellWidth * 1.5;
    }

    private _boosterPreview: BoosterPreview;
    public set boosterPreview(value: BoosterPreview) {
        this._boosterPreview = value;
        this.updatePreview(this.boosterViews[0]);
    }

    private boosterViews: cc.Node[] = [];
    private sceneName: SceneName = SceneName.Unknown;
    private boosterStorageManager: BoosterStorageManager;
    private piggyBankManager: PiggyBankManager;

    /** Current info. */
    private isEvent = false;
    private area = 0;
    private level = 0;
    private _areaInfo?: AreaInfo;
    private _levelInfo?: LevelInfo;
    private get areaInfo(): AreaInfo {
        return this._areaInfo || (this._areaInfo = (() => {
            const levelManager = ee.ServiceLocator.resolve(LevelManager);
            return this.isEvent
                ? levelManager.getEventAreaInfo(this.area)
                : levelManager.getStoryAreaInfo(this.area);
        })());
    }

    protected onLoad (): void {
        this.boosterStorageManager = ee.ServiceLocator.resolve(BoosterStorageManager);
        this.piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const lastUnlockedArea = levelManager.getLastUnlockedArea();
        const info = levelManager.getStoryAreaInfo(lastUnlockedArea);
        const boosters = this.boosterStorageManager.getBoosters(
            this.area, this.level, lastUnlockedArea, info.unlockedLevels - 1);
        this.loadBoosters(boosters);
        this.sceneName = SceneName.SceneBag
        this.boosterStorageManager.addObserver(this.uuid, {
            onBalanceChanged: (type: gm.BoosterType) => {
                this.updateBalanceBoosters(type);
            },
        });
    }

    protected onDestroy() {
        this.boosterStorageManager.removeObserver(this.uuid);
    }

    public updateLayout(boosters: BoosterInfo[]): void {
        // Hide unused views.
        for (let i = boosters.length, n = this.boosterViews.length; i < n; ++i) {
            const view = this.boosterViews[i];
            view.active = false;
        }

        if (boosters.length == 0) {
            this._boosterPreview.updateUI(undefined);
            return;
        }

        // Show needed views.
        for (let i = 0, n = boosters.length; i < n; ++i) {
            const view = this.boosterViews[i];
            view.active = true;
        }

        for (let i = 0, n = boosters.length; i < n; ++i) {
            const view = this.boosterViews[i];
            this.updateItem(boosters[i], view);
        }

        this.updatePreview(this.boosterViews[0]);
        this.scrollToTop();
    }

    private loadBoosters(boosters: BoosterInfo[]): void {
        // Manually align, fix align not work when node is initially disabled.
        if (CC_EDITOR) {
            // Editor works correctly.
        } else {
            this.node.getComponent(cc.Widget).updateAlignment();
        }

        const layout = this.layout;
        const width = this.node.width;
        const height = this.node.height;
        const ratio = height / width;

        let layoutWidth: number;
        if (ratio > 0.4) {
            this.columns = 4;
        } else {
            this.columns = 5;
        }

        layoutWidth = width - 240;
        const horizontalSpacing = this.paddingLeft + this.paddingRight + this.spacingX * (this.columns - 1);
        this.cellWidth = (layoutWidth - horizontalSpacing) / (this.columns);

        // Number of rows.
        const rows = Math.ceil(boosters.length / this.columns);

        // Calculate layout height.
        const verticalSpacing = this.paddingTop + this.paddingBottom + this.spacingY * (rows - 1);
        const layoutHeight = verticalSpacing + rows * this.cellHeight;

        const widget = layout.getComponent(cc.Widget);
        widget.isAlignTop = false;
        widget.isAlignBottom = false;
        layout.height = layoutHeight;

        // Apply cell size.
        const cellWidth = this.cellWidth;
        const cellHeight = this.cellHeight;
        const cardCount = boosters.length;

        // Update cell position.
        let y = -this.paddingTop - layout.anchorY * layoutHeight + layoutHeight;
        for (let index = 0, row = 0; index < cardCount; ++row) {
            let x = this.paddingLeft - layout.anchorX * layoutWidth;
            const rowCells = cardCount - index;
            if (rowCells < this.columns && this.centerize) {
                x += (this.columns - rowCells) * (cellWidth + this.spacingX) / 2;
            }
            for (let col = 0; col < this.columns && index < cardCount; ++col, ++index) {
                const view = cc.instantiate(this.itemPrefab);
                this.updateItem(boosters[index], view);
                this.boosterViews.push(view);
                // Container to hold the item.
                const wrapper = new cc.Node();
                wrapper.height = cellHeight;
                wrapper.width = cellWidth;
                wrapper.x = x + wrapper.anchorX * cellWidth;
                wrapper.y = y - wrapper.anchorY * cellHeight;
                x += cellWidth + this.spacingX;
                wrapper.addChild(view);
                layout.addChild(wrapper);
            }
            y -= cellHeight + this.spacingY;
        }
    }

    private updateItem(info: BoosterInfo, view: cc.Node)  {
        const item = view.getComponent(BoosterPanelItem);
        const unlocked = info.unlock;
        item.setTopBottom(30);
        item.itemInBag = true;
        item.type = info.type;
        item.isSelected = false;
        item.unlocked = info.unlock;
        item.unlockedLevel = BoosterStorageUtils.getUnlockedLevel(info.type);
        item.storeItem = this.boosterStorageManager.getStoreItemType(info.type);
        item.controller = {
            getBoosterBalance: (booster) => {
                let freeBoosterAfterUnlock = ee.ServiceLocator.resolve(FeatureManager).freeBoosterAfterUnlock;
                return Math.max(0, this.boosterStorageManager.getBalance(booster) - (unlocked ? 0 : freeBoosterAfterUnlock));
            },
            getBoosterCost: (booster) => {
                return this.boosterStorageManager.getCost(booster, this.boosterStorageManager.getStoreItemType(booster));
            },
            buyBooster: async (booster) => {
                return this.showConfirmBuyBooster(
                    booster,
                    this.boosterStorageManager.getStoreItemType(booster),
                    this.boosterStorageManager);
            },
        };
        item.updatePreview = {
            updateBoosterPreview: (booster: BoosterPanelItem) => {
                return this.updatePreview(view);
            },
        };
    }

    private updatePreview(view: cc.Node): boolean {
        if (this._boosterPreview == null) {
            return false;
        }
        this.unSelectAllItem();
        const item = view.getComponent(BoosterPanelItem);
        item.isSelected = true;
        this._boosterPreview.storeItem = this.boosterStorageManager.getStoreItemType(item.type);
        this._boosterPreview.controller = item.controller;
        this._boosterPreview.updateUI(item)
        return true;
    }

    private unSelectAllItem() {
        this.boosterViews.forEach(view => {
            const item = view.getComponent(BoosterPanelItem);
           item.isSelected = false;
        });
    }

    public scrollToTop(): void {
        if (this.boosterViews.length === 0) {
            return;
        }
        const view = this.boosterViews[0];
        const y = view.getBoundingBox().yMax;
        this.scrollView.scrollToOffset(cc.v2(0, -y), 0.5);
    }

    private showConfirmBuyBooster(
        boosterType: gm.BoosterType,
        costType: StoreItem,
        storageManager: BoosterStorageManager,
    ): Promise<boolean> {
        const dialog = cc.instantiate(this.boosterDialogPrefab).getComponent(BuyBoosterDialog);
        dialog.costType = costType;
        dialog.baseCost = storageManager.getCost(boosterType, costType);
        dialog.boosterType = boosterType;
        const promise = new Promise<boolean>(resolve => {
            dialog.controller = {
                buy: (sender, amount, cost) => {
                    storageManager.boosterBuyFrom = "bagScene";
                    storageManager.boosterBuyAmount = amount;
                    storageManager.boosterBuyCost = cost;
                    dialog.onDidHide(() => {
                        storageManager.afterBuyMoreCurrency = (boosterType, amount, cost) => {
                            this.afterBuyMoreCurrency(boosterType, amount, cost);
                        };
                        this.piggyBankManager.autoCashBack = false;
                        const result = storageManager.buyBooster(boosterType, costType, this.sceneName, {
                            amount,
                            cost,
                        });
                        resolve(result);
                    });
                    dialog.hide();
                },
            };
        });
        dialog.show(storageManager.controller.dialogManager, 1.4);
        return promise;
    }

    private updateBalanceBoosters(type: gm.BoosterType) {
        const view = this.boosterViews.find((obj) => obj.getComponent(BoosterPanelItem).type === type);
        if (view) {
            const item = view.getComponent(BoosterPanelItem);
            item.updateBalance();
        }
    }

    private afterBuyMoreCurrency(boosterType: BoosterType, amount: number, cost: number) {
        const boosterManager = ee.ServiceLocator.resolve(BoosterStorageManager);
        const costType = boosterManager.getStoreItemType(boosterType)
        // Kiểm tra trước để tránh buyBooster gọi needMoreCurrency khi không đủ.
        const balanceCostType = ee.ServiceLocator.resolve(StoreManager).getItemBalance(costType);
        if (balanceCostType >= cost) {
            this.piggyBankManager.autoCashBack = false;
            const result = boosterManager.buyBooster(boosterType, costType, SceneName.SceneStoryInGame, {
                amount, cost
            });
            if (result) {
                this._boosterPreview.showFlyingBooster().then(()=>{
                    this.piggyBankManager.cashBackRuby().then();
                });
            }
        } else {
            this.showConfirmBuyBooster(
                boosterType,
                boosterManager.getStoreItemType(boosterType),
                boosterManager).then((result) => {
                if (result) {
                    this._boosterPreview.showFlyingBooster().then(()=>{
                        this.piggyBankManager.cashBackRuby().then();
                    });
                } else {
                    this.piggyBankManager.autoCashBack = true;
                }
            });
        }
    }
}
