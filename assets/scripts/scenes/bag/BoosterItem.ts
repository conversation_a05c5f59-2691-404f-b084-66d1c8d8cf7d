import assert = require('assert');
import * as ee from '../../libraries/ee/index';
import * as gm from '../../engine/gm_engine';
import {BoosterType} from "../../engine/gm_engine";
import {BoosterGridView} from "./BoosterGridView";
import {BoosterPreview} from "./BoosterPreview";
import {CommonDialog} from "../../dialog/CommonDialog";
import {MenuController} from "../MenuScene";

const {ccclass, disallowMultiple, property} = cc._decorator;

@ccclass
@disallowMultiple
export class BoosterItem extends cc.Component {
    @ee.nest(BoosterGridView)
    private readonly _boosterGridView: BoosterGridView | null = null;

    public get boosterGridView(): BoosterGridView {
        return gm.retrieveNull(this._boosterGridView);
    }

    @ee.nest(BoosterPreview)
    private readonly _boosterPreview: BoosterPreview | null = null;

    private get boosterPreview(): BoosterPreview {
        return gm.retrieveNull(this._boosterPreview);
    }

    protected onLoad(): void {
        this.initialize();
    }

    private initialize(): void {
        this.boosterGridView.boosterPreview = this.boosterPreview;
    }
}
