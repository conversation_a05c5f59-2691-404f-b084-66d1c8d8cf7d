import assert = require('assert');
import * as ee from '../../libraries/ee/index';
import * as gm from '../../engine/gm_engine';
import {DropDownMenu} from "../../ui/DropDownMenu";
import {BoosterFiler, BoosterStorageManager} from "../../manager/booster/BoosterStorageManager";
import {AreaInfo, LevelInfo, LevelManager} from "../../manager/level/LevelManager";
import {BoosterLayer} from "./BoosterLayer";
import {BoosterGridView} from "./BoosterGridView";

const {ccclass, property} = cc._decorator;

@ccclass
export class BoosterHeader extends cc.Component {
    @property({ type: DropDownMenu, visible: true })
    private readonly _dropDownMenu: DropDownMenu | null = null;

    private get dropDownMenu(): DropDownMenu {
        if (this._dropDownMenu === null) {
            throw Error('Item not registered.');
        }
        return this._dropDownMenu;
    }

    // protected onLoad(): void {
    //     //this.initDropDownMenu();
    // }

    private isEvent = false;
    private area = 0;
    private level = 0;
    private _areaInfo?: AreaInfo;
    private _levelInfo?: LevelInfo;
    private get areaInfo(): AreaInfo {
        return this._areaInfo || (this._areaInfo = (() => {
            const levelManager = ee.ServiceLocator.resolve(LevelManager);
            return this.isEvent
                ? levelManager.getEventAreaInfo(this.area)
                : levelManager.getStoryAreaInfo(this.area);
        })());
    }
    private filter?: BoosterFiler;

    public  initDropDownMenu(boosterGridView: BoosterGridView): void {
        this.dropDownMenu.setOnSelectedCallBack(index => {
            const dict: { [type: number]: BoosterFiler } = {
                [0]: BoosterFiler.Collected,
                [1]: BoosterFiler.Unlocked,
                [2]: BoosterFiler.All,
            };
            this.filter = dict[index];

            const boosterStorageManager = ee.ServiceLocator.resolve(BoosterStorageManager);
            const levelManager = ee.ServiceLocator.resolve(LevelManager);
            const lastUnlockedArea = levelManager.getLastUnlockedArea();
            const info = levelManager.getStoryAreaInfo(lastUnlockedArea);
            const boosters = boosterStorageManager.getBoosters(
                this.area, this.level, lastUnlockedArea, info.unlockedLevels - 1, this.filter);
            boosterGridView.updateLayout(boosters);
        });
    }
    
}
