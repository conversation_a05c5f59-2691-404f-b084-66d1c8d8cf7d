import * as gm from "../../engine/gm_engine";
import * as ee from "../../libraries/ee/index";
import {BoosterPanelItem, Controller} from "../../game/booster_panel/BoosterPanelItem";
import {LevelManager} from "../../manager/level/LevelManager";
import {StoreItemView} from "../../game/common/StoreItemView";
import {StoreItem} from "../../manager/store/StoreManager";
import {CrashlyticManager, crashlytics} from "../../manager/crashlytic/CrashlyticManager";
import {AudioManager} from "../../manager/audio/AudioManager";
import {SoundType} from "../../manager/audio/SoundType";
import {TrackingManager} from "../../manager/analytics/TrackingManager";
import {SceneName} from "../../manager/analytics/AnalyticsConfig";
import {OfferManager} from "../../manager/offer/OfferManager";
import {DialogManager} from "../../libraries/ee/index";
import {OfferType} from "../../dialog/OfferDialog";
import {PiggyBankManager} from "../PiggyBank/PiggyBankManager";
import {BoosterStorageManager} from "../../manager/booster/BoosterStorageManager";

const {ccclass, property} = cc._decorator;

@ccclass
export class BoosterPreview extends cc.Component {
    @ee.nest(StoreItemView)
    private readonly _storeItemView: StoreItemView | null = null;

    @property({ type: ee.LanguageComponent, visible: true })
    private readonly _boosterName: ee.LanguageComponent | null = null;

    @property({ type: ee.LanguageComponent, visible: true })
    private readonly _boosterDescription: ee.LanguageComponent | null = null;

    @property({ type: ee.LanguageComponent, visible: true })
    private readonly _unlockedLevel:ee.LanguageComponent | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _unlocked: cc.Node | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _costLabel: cc.Label | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _buyButtons: cc.Node | null = null;

    private boosterItem: BoosterPanelItem;
    private piggyBankManager: PiggyBankManager;

    protected onLoad (): void {
        this.piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
    }

    private get storeItemView(): StoreItemView {
        if (this._storeItemView === null) {
            throw Error('Item not registered');
        }
        return this._storeItemView;
    }

    private get boosterName(): ee.LanguageComponent {
        if (this._boosterName === null) {
            throw Error('Item not registered');
        }
        return this._boosterName;
    }

    private get boosterDescription(): ee.LanguageComponent {
        if (this._boosterDescription === null) {
            throw Error('Item not registered');
        }
        return this._boosterDescription;
    }

    private get unlockedLevel(): ee.LanguageComponent {
        if (this._unlockedLevel === null) {
            throw Error('Item not registered');
        }
        return this._unlockedLevel;
    }

    private get unlocked(): cc.Node {
        return gm.retrieveNull(this._unlocked);
    }

    private get costLabel(): cc.Label {
        return gm.retrieveNull(this._costLabel);
    }

    private get buyButtons(): cc.Node {
        return gm.retrieveNull(this._buyButtons);
    }

    private _controller: Controller = {
        getBoosterBalance(): number { return 0; },
        getBoosterCost(): number { return 0; },
        async buyBooster(): Promise<boolean> { return false; },
    };

    public get controller(): Controller {
        return this._controller;
    }

    public set controller(value: Controller) {
        this._controller = value;
    }

    public get storeItem(): StoreItem {
        return this.storeItemView.type;
    }

    public set storeItem(value: StoreItem) {
        this.storeItemView.type = value;
    }

    public updateUI(booster: BoosterPanelItem): void {
        if (booster === undefined) {
            this.boosterName.node.active = false;
            this.boosterDescription.node.active = false;
            this.unlockedLevel.node.active = false;
            return;
        }
        let levelManger = ee.ServiceLocator.resolve(LevelManager)
        let maxCurrentLevel = levelManger.getUnlockedStoryLevelsCount();

        this.boosterItem = booster;
        this.boosterName.node.active = true;
        this.boosterDescription.node.active = true;
        this.unlockedLevel.node.active = true;
        this.boosterName.key = this.getBoosterName(booster.type);
        this.boosterDescription.key = this.getBoosterDescription(booster.type);

        const unlocked = maxCurrentLevel < booster.unlockedLevel;
        if (unlocked) {
            this.unlockedLevel.key = "unlock_at_level";
            this.unlockedLevel.paramValues = [`${booster.unlockedLevel}`];
            this.unlocked.active = true;
            this.buyButtons.active = false;
        } else {
            this.unlocked.active = false;
            this.costLabel.string = this.controller.getBoosterCost(booster.type).toString();
            this.buyButtons.active = true;
        }
    }

    /** Registered in editor. */
    @crashlytics
    private buy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.buy, `${this.uuid}`);
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.SceneBag, 'btn_buy');
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        this.boosterItem.previousBalance = this.controller.getBoosterBalance(this.boosterItem.type);
        this.controller.buyBooster(this.boosterItem.type).then(success => {
            if (success) {
                this.boosterItem.showFlyingBooster().then(()=>{
                    this.piggyBankManager.cashBackRuby().then();
                });
            } else {
                this.piggyBankManager.autoCashBack = true;
            }
        });
    }

    public showFlyingBooster(): Promise<void> {
        return this.boosterItem.showFlyingBooster();
    }

    /** Registered in editor. */
    @crashlytics
    private onBoosterOfferButtonPressed(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onBoosterOfferButtonPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.SceneBag, 'btn_booster_offer');
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        let offerDialogManager = ee.ServiceLocator.resolve(OfferManager);
        offerDialogManager.showOfferDialogByType(ee.ServiceLocator.resolve(DialogManager), OfferType.Booster_Offer).then();
    }

    private getBoosterName(type: gm.BoosterType): string {
        const map: { [key: number]: string } = {
            [gm.BoosterType.AntSpray]: 'booster_ant_spray',
            [gm.BoosterType.Clock]: 'booster_clock',
            [gm.BoosterType.DiamondPolish]: 'booster_diamond_polish',
            [gm.BoosterType.Dynamite]: 'booster_dynamite',
            [gm.BoosterType.Glove]: 'booster_glove',
            [gm.BoosterType.LuckyClover]: 'booster_lucky',
            [gm.BoosterType.OldHam]: 'booster_old_ham',
            [gm.BoosterType.PirateHat]: 'booster_pirate_hat',
            [gm.BoosterType.Speed]: 'booster_oil',
            [gm.BoosterType.SpiritJar]: 'booster_spirit_jar',
            [gm.BoosterType.SpookyDoll]: 'booster_spooky_doll',
            [gm.BoosterType.Strength]: 'booster_strength',
            [gm.BoosterType.TitanRope]: 'booster_titan_rope',
            [gm.BoosterType.WantedPoster]: 'booster_wanted_poster',
            [gm.BoosterType.PickUp]: 'booster_pick_up',
        };
        return map[type];
    }

    private getBoosterDescription(type: gm.BoosterType): string {
        const map: { [key: number]: string } = {
            [gm.BoosterType.AntSpray]: 'booster_desc_ant_spray',
            [gm.BoosterType.Clock]: 'booster_desc_clock',
            [gm.BoosterType.DiamondPolish]: 'booster_desc_diamond_polish',
            [gm.BoosterType.Dynamite]: 'booster_desc_dynamite',
            [gm.BoosterType.Glove]: 'booster_desc_glove',
            [gm.BoosterType.LuckyClover]: 'booster_desc_lucky',
            [gm.BoosterType.OldHam]: 'booster_desc_old_ham',
            [gm.BoosterType.PirateHat]: 'booster_desc_pirate_hat',
            [gm.BoosterType.Speed]: 'booster_desc_oil',
            [gm.BoosterType.SpiritJar]: 'booster_desc_spirit_jar',
            [gm.BoosterType.SpookyDoll]: 'booster_desc_spooky_doll',
            [gm.BoosterType.Strength]: 'booster_desc_strength',
            [gm.BoosterType.TitanRope]: 'booster_desc_titan_rope',
            [gm.BoosterType.WantedPoster]: 'booster_desc_wanted_poster',
            [gm.BoosterType.PickUp]: 'booster_desc_pick_up',
        };
        return map[type];
    }
}
