import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import {ResourcesUtils} from "../../utils/ResourcesUtils";
import * as ee from "../../libraries/ee/index";
import {UserProfileManager} from "../../manager/profile/UserProfileManager";
import {SocialUser} from "../../manager/social/SocialManager";
import {
    DEFAULT_AVATAR_ID,
    DEFAULT_FRAME_ID,
    LIST_FRAMES,
    LIST_PICTURES
} from "../../manager/profile/DefaultUserProfileManager";

const {ccclass, disallowMultiple, property} = cc._decorator;

class AvatarCache {
    private cache: { [key: string]: cc.SpriteFrame | undefined | null } = {};

    /** Currently running promices. */
    private promises: Array<Promise<cc.SpriteFrame>> = [];

    /** Limit parallel promises to avoid heavy UI processing. */
    private readonly maxQueueSize = 2;

    public async loadItem(url: string): Promise<cc.SpriteFrame> {
        const item = this.cache[url];
        if (item === null) {
            throw Error('Null image');
        }
        if (item !== undefined) {
            return item;
        }

        // Don't use IF: there are maybe multiple waiting promises.
        while (this.promises.length >= this.maxQueueSize) {
            await Promise.race(this.promises);
        }
        const promise = new Promise<cc.SpriteFrame>((resolve, reject) => {
            cc.loader.load({url, type: 'jpeg'}, (err: Error, texture: cc.Texture2D) => {
                if (err) {
                    this.cache[url] = null;
                    reject(err);
                } else {
                    const spriteFrame = new cc.SpriteFrame(texture);
                    this.cache[url] = spriteFrame;
                    resolve(spriteFrame);
                }
            });
        });
        this.promises.push(promise);

        try {
            // Wait for result.
            return await promise;
        } finally {
            // Remove from promise queue.
            const index = this.promises.indexOf(promise);
            assert(index !== -1);
            this.promises.splice(index, 1);
        }
    }
}

const _avatarCache = new AvatarCache();

export abstract class UserAvatarView extends cc.Component {
    public static readonly DEFAULT_AVATAR_URL = '__default__';

    /** Asynchronously loads the avatar item. */
    public abstract loadItem(url: string): Promise<void>;

    public abstract  updateAvatarById(avatarId: string): void;

    public abstract updateFrameById(frameId: string): void;

    // /** Asynchronously loads avatar by name of sprite in local. */
    // public abstract loadItemLocal(spriteName: string): Promise<void>;
}

@ccclass
@disallowMultiple
export class DefaultUserAvatarView extends UserAvatarView {
    /**
     * Must use default avatar sprite to optimize draw call
     * since downloaded avatar sprite is always in a separated texture.
     */
    @property({type: cc.Sprite, visible: true})
    private readonly _defaultAvatarSprite: cc.Sprite | null = null;

    @property({type: cc.Sprite, visible: true})
    private readonly _avatarSprite: cc.Sprite | null = null;

    @property({type: cc.Sprite, visible: true})
    private readonly _frameSprite: cc.Sprite | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _waitingIcon: cc.Node | null = null;

    private get defaultAvatarSprite(): cc.Sprite {
        return gm.retrieveNull(this._defaultAvatarSprite);
    }

    private get avatarSprite(): cc.Sprite {
        return gm.retrieveNull(this._avatarSprite);
    }

    private get frameSprite(): cc.Sprite {
        return gm.retrieveNull(this._frameSprite);
    }

    private get waitingIcon(): cc.Node {
        return gm.retrieveNull(this._waitingIcon);
    }

    /** Current avatar url. */
    private _url?: string;

    /** Prevents multiple loading cause incorrect avatar image. */
    private version = 0;

    protected onLoad(): void {
        assert(this._avatarSprite !== null);
        // // Hidden initially.
        // this.defaultAvatarSprite.node.opacity = 0;
        // this.avatarSprite.node.opacity = 0;
        // this.waitingIcon.active = true;
        // this.loadItem(UserAvatarView.DEFAULT_AVATAR_URL);

        this.waitingIcon.active = false;
        this.defaultAvatarSprite.node.opacity = 255;
        this.avatarSprite.node.opacity = 0;
    }

    protected onEnable() {
        this.loadItemLocal();
        ee.ServiceLocator.resolve(UserProfileManager).addObserver(this.uuid, {
            socialUserChange: (sender: UserProfileManager, newProfile: SocialUser) => {
                this.updateAvatar(newProfile);
                this.updateFrame(newProfile);
            }
        })
    }

    protected onDisable() {
        ee.ServiceLocator.resolve(UserProfileManager).removeObserver(this.uuid)
    }


    public async loadItem(url: string): Promise<void> {

    }

    private loadItemLocal(): void {
        const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
        const socialUser = profileManager.socialUser;
        this.updateAvatar(socialUser);
        this.updateFrame(socialUser);
    }

    public updateAvatar(socialUser: SocialUser): void {
        if (socialUser && socialUser.picture?.length > 0) {
            this.updateAvatarById(socialUser.picture)
            return;
        }
    }

    public updateFrame(socialUser: SocialUser): void {
        if (socialUser && socialUser.frame?.length > 0) {
            this.updateFrameById(socialUser.frame)
            return;
        }
    }

    public updateAvatarById(avatarId: string): void {
        if (!LIST_PICTURES.includes(avatarId)) {
            avatarId = DEFAULT_AVATAR_ID;
        }
        ResourcesUtils.loadAvatar(avatarId).then(avatar => {
            this.avatarSprite.node.opacity = 255;
            this.avatarSprite.spriteFrame = avatar;
            this.waitingIcon.active = false;
        })
    }

    public updateFrameById(frameId: string): void {
        if (!LIST_FRAMES.includes(frameId)) {
            frameId = DEFAULT_FRAME_ID;
        }
        ResourcesUtils.loadFrame(frameId).then(frame => {
            this.frameSprite.node.opacity = 255;
            this.frameSprite.spriteFrame = frame;
        })
    }

}
