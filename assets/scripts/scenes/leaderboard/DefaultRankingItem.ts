import assert = require('assert');
import * as ee from '../../libraries/ee/index';
import * as gm from '../../engine/gm_engine';

import { UserAvatarView } from './UserAvatarView';
import { UserRankingInfo, UserRankingItem, UserRankingMode } from './UserRankingItem';

const { ccclass, disallowMultiple, property } = cc._decorator;

/** Trim long name. */
const trimName = (name: string) => {
    return name.length <= 15 ? name : `${name.slice(0, 12)}...`;
};

@ccclass
@disallowMultiple
export class DefaultRankingItem extends UserRankingItem {
    /** Displays and loads the user's avatar. */
    @ee.nest(UserAvatarView)
    private readonly _avatarView: UserAvatarView | null = null;

    /** Displays the user's ranking, one-indexed. */
    @property({ type: cc.Label, visible: true })
    private readonly _rankingLabel: cc.Label | null = null;

    @property({ type: cc.Sprite, visible: true })
    private readonly _upIndicator: cc.Sprite | null = null;

    @property({ type: cc.Sprite, visible: true })
    private readonly _downIndicator: cc.Sprite | null = null;

    /** Displays the user's display name. */
    @property({ type: cc.Label, visible: true })
    private readonly _nameLabel: cc.Label | null = null;

    /** Displays the user's score. */
    @property({ type: cc.Label, visible: true })
    private readonly _scoreLabel: cc.Label | null = null;

    /** Displayed when the ranking mode is story. */
    @property({ type: cc.Sprite, visible: true })
    private readonly _storyIcon: cc.Sprite | null = null;

    /** Displayed when the ranking mode is pvp. */
    @property({ type: cc.Sprite, visible: true })
    private readonly _pvpIcon: cc.Sprite | null = null;

    private get avatarView(): UserAvatarView {
        return gm.retrieveNull(this._avatarView);
    }

    private get rankingLabel(): cc.Label {
        return gm.retrieveNull(this._rankingLabel);
    }

    private get upIndicator(): cc.Sprite {
        return gm.retrieveNull(this._upIndicator);
    }

    private get downIndicator(): cc.Sprite {
        return gm.retrieveNull(this._downIndicator);
    }

    private get nameLabel(): cc.Label {
        return gm.retrieveNull(this._nameLabel);
    }

    private get scoreLabel(): cc.Label {
        return gm.retrieveNull(this._scoreLabel);
    }

    private get storyIcon(): cc.Sprite {
        return gm.retrieveNull(this._storyIcon);
    }

    private get pvpIcon(): cc.Sprite {
        return gm.retrieveNull(this._pvpIcon);
    }

    private _mode: UserRankingMode;

    public get mode(): UserRankingMode {
        return this._mode;
    }

    public set mode(value: UserRankingMode) {
        if (this._mode !== value) {
            this._mode = value;
            this.isModeDirty = true;
        }
    }

    private _info: UserRankingInfo;

    public get info(): UserRankingInfo {
        return this._info;
    }

    public set info(value: UserRankingInfo) {
        this.info.id = value.id;
        this.info.rank = value.rank;
        this.info.name = value.name;
        this.info.value = value.value;
        this.info.avatar = value.avatar;
    }

    private isModeDirty = false;
    private isRankDirty = false;
    private isNameDirty = false;
    private isValueDirty = false;
    private isAvatarDirty = false;

    public constructor() {
        super();
        // Dummy values.
        this._mode = UserRankingMode.Pvp;

        const self = this;
        this._info = new (class implements UserRankingInfo {
            public id = '';

            private _rank = 0;
            private _name = '';
            private _value = 0;
            private _avatar = '';

            public get rank(): number { return this._rank; }
            public get name(): string { return this._name; }
            public get value(): number { return this._value; }
            public get avatar(): string { return this._avatar; }

            public set rank(value: number) {
                if (this._rank !== value) {
                    this._rank = value;
                    self.isRankDirty = true;
                }
            }

            public set name(value: string) {
                if (this._name !== value) {
                    this._name = value;
                    self.isNameDirty = true;
                }
            }

            public set value(value: number) {
                if (this._value !== value) {
                    this._value = value;
                    self.isValueDirty = true;
                }
            }

            public set avatar(value: string) {
                if (this._avatar !== value) {
                    this._avatar = value;
                    self.isAvatarDirty = true;
                }
            }
        })();
    }

    protected onLoad(): void {
        assert(this._avatarView !== null);
        assert(this._rankingLabel !== null);
        assert(this._upIndicator !== null);
        assert(this._downIndicator !== null);
        assert(this._nameLabel !== null);
        assert(this._scoreLabel !== null);
        assert(this._storyIcon !== null);
        assert(this._pvpIcon !== null);

        // FIXME: rank changes.
        this.upIndicator.node.active = false;
        this.downIndicator.node.active = false;

        this.updateMode();
        this.updateRank();
        this.updateName();
        this.updateValue();
        this.updateAvatar();

        
        this.scoreLabel.fontSize = 30;
    }

    protected update(delta: number): void {
        if (this.isModeDirty) {
            this.isModeDirty = false;
            this.updateMode();
        }
        if (this.isRankDirty) {
            this.isRankDirty = false;
            this.updateRank();
        }
        if (this.isNameDirty) {
            this.isNameDirty = false;
            this.updateName();
        }
        if (this.isValueDirty) {
            this.isValueDirty = false;
            this.updateValue();
        }
        if (this.isAvatarDirty) {
            this.isAvatarDirty = false;
            this.updateAvatar();
        }
    }

    private updateMode(): void {
        if (this.mode === UserRankingMode.Pvp) {
            this.pvpIcon.node.active = true;
            this.storyIcon.node.active = false;
        } else {
            this.pvpIcon.node.active = false;
            this.storyIcon.node.active = true;
        }
    }

    private updateRank(): void {
        this.rankingLabel.string = `${this.info.rank + 1}`;
    }

    private updateName(): void {
        this.nameLabel.string = trimName(this.info.name);
    }

    private updateValue(): void {
        this.scoreLabel.string = `${this.info.value}`;
    }

    private updateAvatar(): void {
        this.avatarView.loadItem(this.info.avatar);
    }
}
