import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import {
    <PERSON>lipper,
    GenericOptimizer,
    ScrollViewClipper,
} from '../../utils/Clipper';
import {
    UserRankingInfo,
    UserRankingItem,
    UserRankingMode,
} from './UserRankingItem';

const { ccclass, disallowMultiple, property } = cc._decorator;

export abstract class UserRankingList extends cc.Component {
    public abstract mode: UserRankingMode;

    /** Gets or sets the ranking items. */
    public abstract infos: UserRankingInfo[];
}

@ccclass
@disallowMultiple
class DefaultUserRankingList extends UserRankingList {
    /** Contains all ranking items. */
    @property({ type: cc.ScrollView, visible: true })
    private _scrollView: cc.ScrollView | null = null;

    @property({ type: cc.Prefab, visible: true })
    private _rankingPrefab: cc.Prefab | null = null;

    private get scrollView(): cc.ScrollView {
        return gm.retrieveNull(this._scrollView);
    }

    private get rankingPrefab(): cc.Prefab {
        return gm.retrieveNull(this._rankingPrefab);
    }

    private _optimizer?: GenericOptimizer<UserRankingInfo>;
    private _clipper?: Clipper;

    private get optimizer(): GenericOptimizer<UserRankingInfo> {
        if (this._optimizer === undefined) {
            this._optimizer = new GenericOptimizer<UserRankingInfo>(this.scrollView.content)
                .setCreator(() => {
                    const node = cc.instantiate(this.rankingPrefab);
                    const item = node.getComponent(UserRankingItem);
                    this.items.push(item);
                    item.mode = this.mode;
                    return node;
                })
                .setGetter((index, view) => {
                    const item = view.getComponent(UserRankingItem);
                    return item.info;
                })
                .setUpdater((index, view, model) => {
                    const item = view.getComponent(UserRankingItem);
                    item.info = model;
                });
        }
        return this._optimizer;
    }

    private get clipper(): Clipper {
        if (this._clipper === undefined) {
            this._clipper = new ScrollViewClipper(this.scrollView.content, true, this.optimizer)
                .offset(200);
        }
        return this._clipper;
    }

    /** Current display ranking mode. */
    private _mode = UserRankingMode.Pvp;

    private items: UserRankingItem[];
    private _infos: UserRankingInfo[];

    private isDisplayDirty = false;

    public get mode(): UserRankingMode {
        return this._mode;
    }

    public set mode(value: UserRankingMode) {
        if (this._mode !== value) {
            this._mode = value;
            this.items.forEach(item => item.mode = value);
        }
    }

    public get infos(): UserRankingInfo[] {
        return this.optimizer.getModels();
    }

    public set infos(value: UserRankingInfo[]) {
        if (this._infos !== value) {
            this._infos = value;
            const self = this;
            this.optimizer.setModels(value.map((item, index) => new (class implements UserRankingInfo {
                public get id(): string { return item.id; }
                public get rank(): number { return item.rank; }
                public get name(): string { return item.name; }
                public get value(): number { return item.value; }
                public get avatar(): string { return item.avatar; }

                private apply(applier: (info: UserRankingInfo) => void): void {
                    const info = self.optimizer.getModel(index);
                    info && applier(info);
                }

                public set id(_value: string) {
                    item.id = _value;
                    this.apply(info => info.id = _value);
                }

                public set rank(_value: number) {
                    item.rank = _value;
                    this.apply(info => info.rank = _value);
                }

                public set name(_value: string) {
                    item.name = _value;
                    this.apply(info => info.name = _value);
                }

                public set value(_value: number) {
                    item.value = _value;
                    this.apply(info => info.value = _value);
                }

                public set avatar(_value: string) {
                    item.avatar = _value;
                    this.apply(info => info.avatar = _value);
                }
            })()));
            this.updateDisplay();
        }
    }

    public constructor() {
        super();
        this.items = [];
        this._infos = [];
    }

    protected onLoad(): void {
        assert(this._scrollView !== null);
        assert(this._rankingPrefab !== null);
        this.updateDisplay();
    }

    protected onEnable(): void {
        this.scrollView.node.on('scrolling', this.onScrolling, this);
    }

    protected onDisable(): void {
        this.scrollView.node.off('scrolling', this.onScrolling, this);
    }

    protected update(delta: number): void {
        if (this.isDisplayDirty) {
            this.isDisplayDirty = false;
            this.updateDisplay();
        }
    }

    private onScrolling(): void {
        this.isDisplayDirty = true;
    }

    private updateDisplay(): void {
        this.clipper.clip();
    }
}
