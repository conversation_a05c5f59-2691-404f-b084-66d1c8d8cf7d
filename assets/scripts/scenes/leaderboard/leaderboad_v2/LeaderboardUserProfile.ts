import {UserProfileLayer} from "../../profile/UserProfileLayer";

const {ccclass, property} = cc._decorator;

@ccclass
export default class LeaderboardUserProfile extends UserProfileLayer {
    protected onLoad() {
        
    }
    public async hideAnimation(): Promise<void> {
        return new Promise(resolve => {
            const node = this.node;
            const move = cc.tween(node)
                .to(0.2, {position: cc.v3(0, -node.height/2, 0), opacity: 0}, {easing: 'sineOut'})
                .call(resolve)
                .start();
        });
    }
    public async showAnimation(): Promise<void> {
        return new Promise(resolve => {
            const node = this.node;
            node.opacity = 0;
            node.position = cc.v3(0, -node.height/2, 0);
            const move = cc.tween(node)
                .to(0.2, {position: cc.v3(0, 0, 0), opacity: 255}, {easing: 'sineOut'})
                .call(resolve)
                .start();
        });
    } 
}
