import UserRankingInfos from "./UserRankingInfos";
import { RankingItem } from "./RankingItem";
import * as ee from '../../../libraries/ee/index';
import { RankingView } from "./RankingView";
import { RankingCategory, RankingScope } from "../../leaderboard_v2/LeaderboardLayer";
import { LeaderboardManager } from "../../../manager/leaderboard/LeaderboardManager";
import SpriteFrame = cc.SpriteFrame;
import { MenuScenePage } from "../../MenuScene";


const { ccclass, property } = cc._decorator;


@ccclass
class TopRankingViewImpl extends RankingView {
    changePage(page: MenuScenePage): void {
        throw new Error("Method not implemented.");
    }

    @ee.nest(RankingItem)
    private _top1Ranking: RankingItem

    @ee.nest(RankingItem)
    private _top2Ranking: RankingItem

    @ee.nest(RankingItem)
    private _top3Ranking: RankingItem

    @property({ type: cc.Sprite, visible: true })
    public ribbonSprite: cc.Sprite | null = null;

    @property({ type: cc.Sprite, visible: true })
    public background: cc.Sprite | null = null;

    @property({ type: [SpriteFrame], visible: true })
    public ribbonFrame: SpriteFrame[] = [];

    @property({ type: [SpriteFrame], visible: true })
    public backgroundFrame: SpriteFrame[] = [];
    @property({ type: ee.LanguageComponent, visible: true })
    private readonly _title: ee.LanguageComponent | null = null;
    private category: RankingCategory;
    private scope: RankingScope;
    private _leaderboardManager: LeaderboardManager;

    protected onLoad() {
        this._leaderboardManager = ee.ServiceLocator.resolve(LeaderboardManager);
    }

    public set listUserData(value: UserRankingInfos[]) {
        const rankings = [this._top1Ranking, this._top2Ranking, this._top3Ranking];
        const isWeeklyWorld = this.category == RankingCategory.Weekly && this.scope == RankingScope.World;
        rankings.forEach((ranking, index) => {
            if (value[index]) {
                ranking.setInfo(value[index]);
                ranking.setRewards(isWeeklyWorld ? this._leaderboardManager.Reward.getRewardConfigByRank(value[index].rank) : null);
                ranking.node.active = true
            }
            else
            {
                ranking.node.active = false;
            }
        });
    }

    updateView(category: RankingCategory, scope: RankingScope): void {
        if (this.category != category) {
            switch (category) {
                case RankingCategory.Player:
                    this._title.key = "leaderboard_top_player_title";
                    this.ribbonSprite.spriteFrame = this.ribbonFrame[0]
                    this.background.spriteFrame = this.backgroundFrame[0]
                    break;
                case RankingCategory.Weekly:
                    this._title.key = "leaderboard_top_weekly_title";
                    this.ribbonSprite.spriteFrame = this.ribbonFrame[1]
                    this.background.spriteFrame = this.backgroundFrame[1]
                    break;
                case RankingCategory.Team:
                    this._title.key = "leaderboard_top_team_title";
                    this.ribbonSprite.spriteFrame = this.ribbonFrame[2]
                    this.background.spriteFrame = this.backgroundFrame[2]
            }
        }
        this.category = category;
        this.scope = scope;
    }


}
