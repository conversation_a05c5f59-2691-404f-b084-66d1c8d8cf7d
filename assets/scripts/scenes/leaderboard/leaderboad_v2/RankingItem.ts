import * as ee from '../../../libraries/ee/index';
import {UserAvatarView} from "../UserAvatarView";
import UserRankingInfos from "./UserRankingInfos";
import {LeaderboardReward, LeaderboardRewardConfig} from "../../leaderboard_v2/LeaderboardReward";
import {RewardInfoPopup} from "./RewardInfoPopup";
import {MenuScenePage} from "../../MenuScene";
import LeaderboardNavigatePopup from "./LeaderboardNavigatePopup";
import BadgeView from "../../../team/BadgeView";

const {ccclass, disallowMultiple, property} = cc._decorator;

export abstract class RankingItem extends cc.Component {
    public abstract setInfo(info: UserRankingInfos): this;

    public abstract setRewards(config: LeaderboardRewardConfig): this;

    public abstract onChangePage(page: MenuScenePage): void;

    public onPressViewProfileCallback: (info: UserRankingInfos) => void;
    public onPressItem:(info: UserRankingInfos) => void;

}

@ccclass
@disallowMultiple
class DefaultRankingItemImpl extends RankingItem {

    /** Displays the user's ranking, one-indexed. */
    @property({type: cc.Label, visible: true})
    private readonly _rankingLabel: cc.Label | null = null;

    @property({type: cc.Sprite, visible: true})
    private readonly _rankingFrame: cc.Sprite | null = null;

    @property({type: [cc.SpriteFrame], visible: true})
    private readonly _listTopRankDot: cc.SpriteFrame[] | [] = [];

    /** Displays and loads the user's avatar. */
    @ee.nest(UserAvatarView)
    private readonly _avatarView: UserAvatarView | null = null;

    /** Displays the user's display name. */
    @property({type: cc.Label, visible: true})
    private readonly _nameLabel: cc.Label | null = null;

    @property({type: cc.Label, visible: true})
    private readonly _levelLabel: cc.Label | null = null;

    @property({type: cc.Label, visible: true})
    private readonly _teamLabel: cc.Label | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _rewardPoint: cc.Node | null = null;

    /** Displays the user's star. */
    @property({type: cc.Label, visible: true})
    private readonly _starLabel: cc.Label | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _rewardInfoPopupPrefab: cc.Prefab | null = null;
    
    @property({type: cc.Prefab, visible: true})
    private _navigatePopup: cc.Prefab | null = null;

    /** for avatar of team-chat */
    @property(cc.Node)
    private avatarNode: cc.Node = null;
    @property(cc.Node)
    private nameNode: cc.Node = null;
    @property(cc.Node)
    private badgeNode: cc.Node = null;
    @property(cc.Node)
    private nameTeamNode: cc.Node = null;
    @property(cc.Node)
    private memberNode: cc.Node = null;

    @property(BadgeView)
    private badge: BadgeView = null;
    @property(cc.Label)
    private lblTeamName: cc.Label = null;
    @property(cc.Label)
    private lblMemberCount: cc.Label = null;

    private info: UserRankingInfos;
    private rewards: LeaderboardRewardConfig;
    private _toggle: cc.Toggle = null;
    private rewardPopup: RewardInfoPopup = null;
    private navigatePopup: LeaderboardNavigatePopup = null;

    public setInfo(info: UserRankingInfos) {
        this.info = info;
        this._rankingLabel.string = info.rank.toString();
        this._levelLabel.string = info.level.toString();
        this._nameLabel.string = info.name;
        this._teamLabel.string = info.team;
        this._starLabel.string = info.totalStar.toString();
        this._rankingFrame.node.active = info.rank <= 3;
        this._rankingFrame.spriteFrame = this._listTopRankDot[info.rank - 1];

        this.avatarNode.active = false;
        this.nameNode.active = false;
        this.badgeNode.active = false;
        this.nameTeamNode.active = false;
        this.memberNode.active = false;
        if (["OPEN", "CLOSE"].includes(info.team)) {
            this.badgeNode.active = true;
            this.nameTeamNode.active = true;
            this.memberNode.active = true;
            this.badge.badgeID = info.avatarId;
            this.lblTeamName.string = info.name;
            this.lblMemberCount.string = `${info.level} / 30`;
        } else {
            this.avatarNode.active = true;
            this.nameNode.active = true;
            this._avatarView.updateAvatarById(info.avatarId);
            this._avatarView.updateFrameById(info.frameId);
        }

        if (this._toggle && this._toggle.isChecked) {
            this._toggle.isChecked = false
        }

        return this;
    }

    public onChangePage(page: MenuScenePage) {
        if (this._toggle && this._toggle.isChecked) {
            this._toggle.isChecked = false
        }
        if (this.rewardPopup) {
            this.rewardPopup.node.removeFromParent();
        }
        if (this.navigatePopup) {
            this.navigatePopup.node.removeFromParent();
        }
    }

    public setRewards(config: LeaderboardRewardConfig): this {
        this.rewards = config
        this._rewardPoint.active = this.rewards != null;
        return this;
    }

    public onPress(toggle: cc.Toggle) {
        this._toggle = toggle;
        if (!toggle.isChecked) return;
        this.onPressItem && this.onPressItem(this.info)
        this.navigatePopup = LeaderboardNavigatePopup.showPopup(this._navigatePopup, this.node, this.node.parent.parent.parent.parent.parent.parent.parent.parent.parent.parent)
            .setViewProfileCallback(() => this.onPressViewProfileCallback(this.info));

        // this.onPressCallback(this.info);
    }

    public onPressShowReward() {
        if (this.rewards) {
            this.onPressItem && this.onPressItem(this.info)
            this.rewardPopup = RewardInfoPopup.showPopup(this._rewardInfoPopupPrefab, this._rewardPoint, this.node.parent.parent.parent.parent.parent.parent.parent.parent.parent.parent, this.rewards);
        }
    }


}
