import UserRankingInfos from "./UserRankingInfos";
import {GenericOptimizer} from "../../../utils/Clipper";
import {RankingItem} from "./RankingItem";
import {RankingCategory, RankingScope} from "../../leaderboard_v2/LeaderboardLayer";
import {LeaderboardManager} from "../../../manager/leaderboard/LeaderboardManager";
import * as ee from "../../../libraries/ee/index";
import {UserProfileManager} from "../../../manager/profile/UserProfileManager";
import {MenuScenePage} from "../../MenuScene";
import {LeaderboardDropDown} from "./LeaderboardDropDown";
import Toggle = cc.Toggle;

const {ccclass, property} = cc._decorator;

export abstract class RankingView extends cc.Component {

    // List data for rendering
    abstract listUserData: UserRankingInfos[];

    // Update view when change category: PLAYER,WEEKLY,TEAM
    abstract updateView(category: RankingCategory, scope: RankingScope): void;

    // call when change page: LEADERBOARD, EVENT, HOME ...
    abstract changePage(page: MenuScenePage): void

    // callback when toggle nation
    filterNationCallback: (nation: string) => void

    // callback when click item
    onClickItem: (info: UserRankingInfos) => void

    // on click try again button
    onGetLeaderBoard: () => void

    onChangeNation: (scope: RankingScope) => void
}

@ccclass
class RankingViewImpl extends RankingView {
    @property({type: cc.Prefab, visible: true})
    private _rankingItemPrefab: cc.Prefab = null;

    @property({type: cc.Node, visible: true})
    public _rankingContent: cc.Node = null;

    @property({type: cc.ScrollView, visible: true})
    private _rankingScrollView: cc.ScrollView = null;

    @property({type: LeaderboardDropDown, visible: true})
    private _filterContainer: LeaderboardDropDown = null;

    @property({type: cc.Node, visible: true})
    private _noInternetLayer: cc.Node = null;

    @property({type: cc.Node, visible: true})
    private _scrollToTopButton: cc.Node = null;


    @property({type: cc.Node, visible: true})
    private _scrollToBottomButton: cc.Node = null;

    private _listData: UserRankingInfos[] = [];

    private _optimizer?: GenericOptimizer<UserRankingInfos>;
    private category: RankingCategory;
    private scope: RankingScope;
    private _leaderboardManager: LeaderboardManager;
    private _nationCode: string = "";

    protected onLoad() {
        this._leaderboardManager = ee.ServiceLocator.resolve(LeaderboardManager);
        const userProfileManager = ee.ServiceLocator.resolve(UserProfileManager);

        this._filterContainer.setOnSelectedCallBack((scope) => {
            this.onChangeNation && this.onChangeNation(scope)
        })
        this._nationCode = userProfileManager.socialUser.nationCode;
        if (this._nationCode == ""
            || this._nationCode == null
            || this._nationCode == "unknown"
            || this._nationCode == "null") {
            this._filterContainer.node.active = false;
        } else {
            // this._toggleNation.key = this._nationCode;
        }

    }

    public set listUserData(value: UserRankingInfos[]) {
        if (value.length == 0) {
            this._noInternetLayer.active = true;
            this._rankingContent.active = false;
            this._scrollToTopButton.active = false;
            this._scrollToBottomButton.active = false;
            return
        }
        this._rankingContent.active = true;
        this._noInternetLayer.active = false;

        this._listData = value;
        this.optimizer.setModels(this._listData);
        this.updateRendering()
        this.updateDisplay()
    }

    private updateRendering(): void {
        this._scrollToTopButton.active = this._rankingScrollView.getScrollOffset().y > 10;
        this._scrollToBottomButton.active = this._rankingScrollView.getScrollOffset().y < this._rankingScrollView.getMaxScrollOffset().y - 10;
        this.clip(-this._rankingContent.parent.height / 2, this._rankingContent.parent.height / 2 + 20);
    }

    private onScrolling(): void {
        this.updateRendering();
    }

    private get optimizer(): GenericOptimizer<UserRankingInfos> {
        if (this._optimizer === undefined) {
            this._optimizer = new GenericOptimizer<UserRankingInfos>(this._rankingContent)
                .setCreator(() => {
                    const node = cc.instantiate(this._rankingItemPrefab);
                    // const item = node.getComponent(DefaultRankingItem);
                    return node;
                })
                .setUpdater((index, view, model) => {
                    const item = view.getComponent(RankingItem);
                    item.setInfo(model)
                    const rewardConfig = (this.category == RankingCategory.Weekly && this.scope == RankingScope.World)
                        ? this._leaderboardManager.Reward.getRewardConfigByRank(model.rank)
                        : null;
                    item.setRewards(rewardConfig);
                    item.onPressViewProfileCallback = (info) => {
                        this.onClickItem(info)
                    }
                    item.onPressItem = (info) => {
                        this._scrollToTopButton.active = false
                        this._scrollToBottomButton.active = false
                    }
                });

        }
        return this._optimizer;
    }

    public clip(from: number, to: number): void {
        const content = this._rankingContent;
        const offset = content.y;
        this.optimizer.process(from - offset, to - offset, true);
    }

    public updateDisplay() {
        const layout = this._rankingContent.getComponent(cc.Layout);
        (layout as any)._doLayout();
        this.node.height = this._rankingContent.height - this._rankingContent.y;
        this.onPressScrollTop()
    }

    onToggleClicked(toggle: Toggle, eventData: string) {
        if (toggle.isChecked) {
            this.filterNationCallback(eventData)
        }
    }

    changePage(page: MenuScenePage): void {
        this._filterContainer.dropDownBox.active = false
        this.updateDisplay()
        const activeList = this.optimizer.getMainActiveView()
        for (let i in activeList) {
            activeList[i].getComponent(RankingItem).onChangePage(page)
        }
    }

    public updateView(category: RankingCategory, scope: RankingScope): void {
        if (this.category != category) {
            this._filterContainer.setSelection(scope);
            this._filterContainer.dropDownBox.active = false
        }
        this._filterContainer.node.active = true

        if (this._nationCode == ""
            || this._nationCode == null
            || this._nationCode == "unknown"
            || this._nationCode == "null") {

            this._filterContainer.node.active = false;
        } else if (category == RankingCategory.Weekly || category == RankingCategory.Team) {
            this._filterContainer.node.active = false
        }
        this.category = category;
        this.scope = scope;
    }

    public onClickTryAgain() {
        this.onGetLeaderBoard()
    }

    public onPressScrollTop() {
        this._rankingScrollView.scrollToOffset(cc.v2(0, 5), 1);
        this._rankingScrollView.scrollToOffset(cc.v2(0, -5), 1);
    }

    public onPressScrollBottom() {
        const maxYOffset = this._rankingScrollView.getMaxScrollOffset().y
        this._rankingScrollView.scrollToOffset(cc.v2(0, maxYOffset - 5), 1);
        this._rankingScrollView.scrollToOffset(cc.v2(0, maxYOffset + 5), 1);
    }


}
