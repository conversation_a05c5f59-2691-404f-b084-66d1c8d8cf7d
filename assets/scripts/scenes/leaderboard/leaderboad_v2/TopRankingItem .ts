import SpriteFrame = cc.SpriteFrame;
import { nest } from "../../../libraries/ee/index";
import { UserAvatarView } from "../UserAvatarView";
import UserRankingInfos from "./UserRankingInfos";
import { RankingItem } from "./RankingItem";
import { LeaderboardRewardConfig } from "../../leaderboard_v2/LeaderboardReward";
import { TopRewardUI } from "./TopRewardUI";
import {MenuScenePage} from "../../MenuScene";
import BadgeView from "../../../team/BadgeView";

const { ccclass, property } = cc._decorator;

@ccclass
class TopRankingItem_v2 extends RankingItem {

    @property({ type: [SpriteFrame], visible: true })
    public spriteFrame: SpriteFrame[] = [];

    @property({ type: [SpriteFrame], visible: true })
    public subSpriteFrame: SpriteFrame[] = [];

    @property({ type: [SpriteFrame], visible: true })
    public spriteDot: SpriteFrame[] = [];

    @property({ type: cc.Label, visible: true })
    public rankingLabel: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    public nameLabel: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    public levelLabel: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    public teamLabel: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    public starLabel: cc.Label | null = null;

    @property({ type: cc.Sprite, visible: true })
    public rankingFrame: cc.Sprite | null = null;

    @property({ type: cc.Sprite, visible: true })
    public dotSprite: cc.Sprite | null = null;

    @nest(UserAvatarView)
    public avatarView: UserAvatarView | null = null;

    @property({ type: TopRewardUI, visible: true })
    private readonly _topRewardUI: TopRewardUI | null = null;

    @property({ type: [cc.Sprite], visible: true })
    public subFrameEffected: cc.Sprite[] | null = [];

    /** for avatar of team-chat */
    @property(cc.Node)
    private avatarNode: cc.Node = null;
    @property(BadgeView)
    private badge: BadgeView = null;
    @property(cc.Node)
    private badgeNode: cc.Node = null;

    private _info: UserRankingInfos | null = null;

    public setInfo(info: UserRankingInfos) {
        this._info = info;
        this.rankingFrame.spriteFrame = this.spriteFrame[info.rank - 1];
        this.dotSprite.spriteFrame = this.spriteDot[info.rank - 1];
        this.subFrameEffected.forEach(item => item.spriteFrame = this.subSpriteFrame[info.rank - 1])
        this.rankingLabel.string = info.rank.toString();
        this.nameLabel.string = info.name;
        this.levelLabel.string = info.level.toString();
        this.teamLabel.string = info.team;
        this.starLabel.string = info.totalStar.toString();

        this.avatarNode.active = false;
        this.badgeNode.active = false;
        if (["OPEN", "CLOSE"].includes(info.team)) {
            this.badgeNode.active = true;
            this.badge.badgeID = info.avatarId;
        } else {
            this.avatarNode.active = true;
            this.avatarView.updateAvatarById(info.avatarId);
            this.avatarView.updateFrameById(info.frameId);
        }
        return this;
    }

    public setRewards(config: LeaderboardRewardConfig): this {
        const isActive = config != null;
        this._topRewardUI.node.active = isActive;
        if (isActive) {
            this._topRewardUI.setRuby(config.ruby);
            this._topRewardUI.setFrameByRank(this._info.rank);
        }
        return this;
    }

    onChangePage(page: MenuScenePage): void {
    }


}
