import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {
    LeaderboardManager,
} from '../../manager/gm_manager';
import { TimeUtils } from '../../utils/TimeUtis';
import { WaitingIcon } from '../common/WaitingIcon';
import { UserAvatarView } from './UserAvatarView';
import { UserRankingMode } from './UserRankingItem';
import { UserRankingList } from './UserRankingList';

const { ccclass, disallowMultiple, property } = cc._decorator;

export abstract class SocialRankingView extends cc.Component {
    /** Gets or sets the ranking mode. */
    public abstract mode: UserRankingMode;

    /**
     * Asynchroously loads the ranking for
     * @param userIds Friend user IDs.
     */
    public abstract loadRanking(userIds: string[]): Promise<void>;
}

@ccclass
@disallowMultiple
class DefaultSocialRankingView extends SocialRankingView {
    @property({ type: cc.Node, visible: true })
    private readonly _rankingLayer: cc.Node | null = null;

    @ee.nest(UserRankingList)
    private readonly _userRankingList: UserRankingList | null = null;

    @ee.nest(WaitingIcon)
    private readonly _waitingIcon: WaitingIcon | null = null;

    private get rankingLayer(): cc.Node {
        return gm.retrieveNull(this._rankingLayer);
    }

    private get userRankingList(): UserRankingList {
        return gm.retrieveNull(this._userRankingList);
    }

    private get waitingIcon(): WaitingIcon {
        return gm.retrieveNull(this._waitingIcon);
    }

    /** Whether the leaderboard is loading. */
    private loading = false;

    private _mode = UserRankingMode.Story;
    private initializationPromise?: Promise<void>;
    private initializationResolve?: () => void;
    private isInitialized = false;

    public get mode(): UserRankingMode {
        return this._mode;
    }

    public set mode(value: UserRankingMode) {
        this._mode = value;
        this.isInitialized && (this.userRankingList.mode = value);
    }

    protected onLoad(): void {
        this.initializationPromise = new Promise<void>(resolve => this.initializationResolve = resolve);
        ee.AsyncManager.getInstance().add(DefaultSocialRankingView.name, async () => {
            this.rankingLayer.active = false;
            this.waitingIcon.node.active = false;
            this.userRankingList.mode = this.mode;
            this.isInitialized = true;
            this.initializationResolve && this.initializationResolve();
        });
    }

    public async loadRanking(userIds: string[]): Promise<void> {
        if (this.loading) {
            return;
        }
        this.loading = true;
        this.initializationPromise && await this.initializationPromise;
        this.rankingLayer.active = false;
        this.waitingIcon.node.active = true;
        await this.updateRanking(userIds);
        if (this.isValid) {
            this.loading = false;
            this.rankingLayer.active = true;
            this.waitingIcon.node.active = false;
        }
    }

    private async updateRanking(userIds: string[]): Promise<void> {
        // Add a delay to avoid heavy UI processing.
    //     const delay = TimeUtils.sleep(1000);
    //
    //     const leaderboardManager = ee.ServiceLocator.resolve(LeaderboardManager);
    //     const entries = await leaderboardManager.fetchUserSummaryLeaderboard(userIds);
    //     await delay;
    //     if (!this.isValid) {
    //         return;
    //     }
    //     this.userRankingList.infos = entries.map((item, index) => ({
    //         id: item.uid,
    //         rank: index,
    //         name: item.name,
    //         value: item.totalStar,
    //         avatar: '', // Will be updated later.
    //     }));
    }
}
