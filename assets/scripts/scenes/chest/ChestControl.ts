import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {ChestInfo, ChestManager, ChestSlot} from "../../manager/chest/ChestManager";
import {
    AudioManager,
    CrashlyticManager,
    crashlytics,
    SceneName,
    SoundType, trackClick,
    TrackingManager,
} from '../../manager/gm_manager';
import {ChestView} from '../common/ChestView';
import {NotificationView} from "../common/NotificationView";
import {ChestDialog} from "../../dialog/ChestDialog";
import {ChestType} from "../../engine/gm_engine";

const {ccclass, disallowMultiple, property} = cc._decorator;

interface Listener {
    pressedRuby(info: ChestInfo): void;

    pressedGold(info: ChestInfo): void;
}

@ccclass
@disallowMultiple
export class ChestControl extends cc.Component {
    @ee.nest(ChestView)
    private _chestView: ChestView | null = null;

    public get chestView(): ChestView {
        return gm.retrieveNull(this._chestView);
    }

    @property({type: ee.LanguageComponent, visible: true})
    private _chestName: ee.LanguageComponent | null = null;

    public get chestName(): ee.LanguageComponent {
        return gm.retrieveNull(this._chestName);
    }

    @property({type: cc.Label, visible: true})
    private _chestPrice: cc.Label[] = [];

    public get chestPrice(): cc.Label[] {
        return gm.retrieveNull(this._chestPrice);
    }

    @property({type: cc.Label, visible: true})
    private _chestPriceGold: cc.Label | null = null;

    public get chestPriceGold(): cc.Label {
        return gm.retrieveNull(this._chestPriceGold);
    }

    @property({type: cc.Node, visible: true})
    private _priceRubyLayer: cc.Node | null = null;

    public get priceRubyLayer(): cc.Node {
        return gm.retrieveNull(this._priceRubyLayer);
    }

    @property({type: cc.Node, visible: true})
    private _priceGoldRubyLayer: cc.Node | null = null;

    public get priceGoldRubyLayer(): cc.Node {
        return gm.retrieveNull(this._priceGoldRubyLayer);
    }

    @property(cc.Node)
    private buttonNode: cc.Node | null = null;

    @property(NotificationView)
    private chestNotification: NotificationView | null = null;

    private info?: ChestInfo;
    private listener: Listener;

    private currentClaimCount: number;
    private chestSlotClear: ChestSlot[] = [];

    public constructor() {
        super();
        this.listener = {
            pressedRuby: () => {
            },
            pressedGold: () => {
            },
        };
    }

    protected onLoad(): void {
        assert(this._chestName !== null);
        assert(this._chestView !== null);
        assert(this._chestPrice !== null);
        assert(this._chestPriceGold !== null);
        assert(this._priceGoldRubyLayer !== null);
    }

    public setListener(listener: Listener): this {
        this.listener = listener;
        return this;
    }

    public setInfo(chestInfo: ChestInfo, skin: string): this {
        this.info = chestInfo;
        //this.chestName.key = chestInfo.name;
        // this.chestPrice.forEach(chestPrice => {
        //     chestPrice.string = chestInfo.price.toString();
        // });

        //this.chestView.type = chestInfo.type;

        let chestManager = ee.ServiceLocator.resolve(ChestManager);
        this.currentClaimCount = 0;
        this.chestSlotClear = [];
        for (let i = 0; i < 6; ++i) {
            let slot = chestManager.getSlot(i);
            if(slot.getType() === chestInfo.type) {
                this.currentClaimCount++;
                this.chestSlotClear.push(slot);
            }
        }

        this.updateDisplay(chestInfo);

        return this;
    }

    private updateDisplay(info: ChestInfo): void {
        this.chestName.key = info.name;
        this.chestView.type = info.type;

        this.chestPrice.forEach(chestPrice => {
            chestPrice.string = info.price.toString();
        });

        this.updateButtons();
    }

    private updateButtons() {
        if(this.currentClaimCount > 0) {
            this.buttonNode.active = true;
            this.chestNotification.setValue(this.currentClaimCount);
            this.priceRubyLayer.active = false;
            this.priceGoldRubyLayer.active = false;
        }
        else {
            this.buttonNode.active = false;
            this.chestPriceGold.string = this.info.priceGold.toString();
            this.priceRubyLayer.active = this.info.priceGold === 0;
            this.priceGoldRubyLayer.active = this.info.priceGold !== 0;
        }
    }

    public getChestView(): ChestView {
        return this.chestView;
    }

    /** Register in editor */
    @crashlytics
    @trackClick(SceneName.SceneMenuShop)
    private pressedRubyButton(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.pressedRubyButton, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        this.listener.pressedRuby && this.listener.pressedRuby(this.info!);
    }

    /** Register in editor */
    @crashlytics
    @trackClick(SceneName.SceneMenuShop)
    private pressedGoldButton(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.pressedGoldButton, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        this.listener.pressedGold && this.listener.pressedGold(this.info!);
    }

    @crashlytics
    private pressedOpenChest(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.pressedOpenChest, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        this.receiveChest();
    }

    private receiveChest(): void {
        ChestDialog.create().then(dialog => {
            const rootDialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
            dialog.chestType = this.info.type;
            dialog.isRewardGold = false;
            dialog.show(rootDialogManager);
            dialog.onDidHide(() => {
                this.currentClaimCount--;
                this.chestSlotClear.pop().clear();
                this.updateButtons();
            });
        });
    }
}
// export enum ChestType {
//     Free,
//     Copper,
//     Silver,
//     Gold,
//     Diamond,
//     Star,
// }
function getChestNameByType(type: ChestType) {
    let dic : Record<ChestType, string> = {
        [gm.ChestType.Free]: 'FreeChest',
        [gm.ChestType.Copper]: 'CopperChest',
        [gm.ChestType.Silver]: 'SilverChest',
        [gm.ChestType.Gold]: 'GoldChest',
        [gm.ChestType.Diamond]: 'DiamondChest',
        [gm.ChestType.Star]: 'StarChest',
    }
    return dic[type];
}
