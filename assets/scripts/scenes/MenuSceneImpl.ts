import assert = require('assert');
import {ExitDialog} from '../dialog/ExitDialog';
import * as gm from '../engine/gm_engine';
import * as ee from "../libraries/ee/index";
import {DialogManager, ServiceLocator} from "../libraries/ee/index";
import {
    AchievementManager,
    AreaInfo,
    AudioManager,
    CardManager,
    CharacterUpgradeTutorial,
    ConfigManager,
    ConversionScene,
    CrashlyticManager,
    crashlytics,
    DailyQuestManager,
    DailyRewardManager,
    LeaderboardManager,
    LevelManager,
    NotificationManager,
    RatingManager,
    RewardManager,
    RewardType,
    RewardUtils,
    SceneManager,
    SceneName,
    ShopManager,
    SoundType,
    StoreItem,
    StoreManager,
    TrackingManager,
    Tutorial,
    TutorialManager,
    UserProfileManager,
    VipManager,
} from "../manager/gm_manager";
import {NativeCall} from '../utils/NativeCall';
import {CharacterLayer} from "./character/CharacterLayer";
import {BottomHud, NotificationType,} from './common/BottomHud';
import {TopHud, TopHudTemplate,} from "./common/TopHud";
import {HomeLayer, HomeLayerPage,} from "./HomeLayer";
import {IapShopCategory} from './IapShopCategory';
import {IapShopLayer} from "./IapShopLayer";
import {MapLayer} from './map/MapLayer';
import {MapUtils} from './map/MapUtils';
import {MenuController, MenuScene, MenuSceneAction, MenuScenePage,} from './MenuScene';
import {SettingDialog} from "./setting/SettingDialog";
import {VipLayer} from "./vip/VipLayer";
import {TopHudImpl} from "./common/TopHudImpl";
import {BottomHudImpl} from "./common/BottomHudImpl";
import {CardRewardPerLevelManager} from "../manager/card/CardRewardPerLevelManager";
import {OfferManager} from "../manager/offer/OfferManager";
import {DefaultOfferManager} from "../manager/offer/DefaultOfferManager";
import {LeaderboardLayer} from "./leaderboard_v2/LeaderboardLayer";
import {HiddenTempleManager} from "./hidden_temple/HiddenTempleManager";
import HiddenTempleLayer from "./hidden_temple/HiddenTempleLayer";
import {GameServerManager} from "../manager/game_server/GameServerManager";
import CancelDeleteAccountDialog from "../dialog/CancelDeleteAccountDialog";
import {PiggyBankManager} from "./PiggyBank/PiggyBankManager";
import {LeaderboardRewardDialog} from './game_scene/dialogs/LeaderboardRewardDialog';
import {EffectHelper} from "../utils/EffectHelper";
import UIBounceAnimator from "../ui/UIBounceAnimator";
import {TimeUtils} from "../utils/TimeUtis";
import {prefabPool} from "../manager/scene/prefabPool";
import PiggyBankButton from "./PiggyBank/PiggyBankButton";
import HiddenTempleButton from "./hidden_temple/HiddenTempleButton";
import {LoadingProcess} from "./common/LoadingProcess";
import {TeamLayer} from "./team/TeamLayer";
import {EventLayer, EventLayerMode} from "./event/EventLayer";
import {MyTeamManager} from "../manager/team/MyTeamManager";
import UnlockTeamDialog from "../dialog/UnlockTeamDialog";

const { ccclass, disallowMultiple, property } = cc._decorator;

/** Gets the culling group index for the specified page. */
const getPageGroupIndex = (page: MenuScenePage) => {
    return (page as number) + 1;
};

/** Gets the culling mask for the specified page. */
const getPageCullingMask = (page: MenuScenePage) => {
    return 1 << getPageGroupIndex(page);
};

/** Used in editor. */
@ccclass
@disallowMultiple
class MenuSceneImpl extends MenuScene {
    /** Contains tab pages. */
    @property({ type: cc.PageView, visible: true })
    private readonly _pageView: cc.PageView | null = null;

    @property({ type: TopHudImpl, visible: true })
    private readonly _topHud: TopHud | null = null;

    @property({ type: BottomHudImpl, visible: true })
    private readonly _bottomHud: BottomHud | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _selectLevelDialogPrefab: cc.Prefab | null = null;

    @property({ type: cc.BlockInputEvents, visible: true })
    private readonly _blocker: cc.BlockInputEvents | null = null;

    private get pageView(): cc.PageView {
        return gm.retrieveNull(this._pageView);
    }

    private get selectLevelDialogPrefab(): cc.Prefab {
        return gm.retrieveNull(this._selectLevelDialogPrefab);
    }

    private get blocker(): cc.BlockInputEvents {
        return gm.retrieveNull(this._blocker);
    }

    /** Gets the current top hud. */
    private get topHud(): TopHud {
        return gm.retrieveNull(this._topHud);
    }

    /** Gets the current bottom hud. */
    private get bottomHud(): BottomHud {
        return gm.retrieveNull(this._bottomHud);
    }

    @property({ type: cc.Prefab, visible: true })
    private readonly _vipDialogPrefab: cc.Prefab | null = null;

    @property({ type: cc.Prefab, visible: true })
    private readonly _settingDialogPrefab: cc.Prefab | null = null;

    private get vipDialogPrefab(): cc.Prefab {
        return gm.retrieveNull(this._vipDialogPrefab);
    }

    private get settingDialogPrefab(): cc.Prefab {
        return gm.retrieveNull(this._settingDialogPrefab);
    }

    private _shopLayer: IapShopLayer | null = null;
    private _characterLayer: CharacterLayer | null = null;
    private _homeLayer: HomeLayer | null = null;
    private _teamLayer: TeamLayer | null = null;
    private _leaderboardLayer: LeaderboardLayer | null = null;
    private _menuShowRating: number = 0;
    private pagePrefabNames  = [
        "iap_shop_layer",
        "character_layer",
        "home_layer",
        "team_layer",
        "leaderboard_layer"
    ];

    public get shopLayer(): IapShopLayer {
        return gm.retrieveNull(this._shopLayer);
    }

    public get characterLayer(): CharacterLayer {
        return gm.retrieveNull(this._characterLayer);
    }

    public get homeLayer(): HomeLayer {
        return gm.retrieveNull(this._homeLayer);
    }

    public get teamLayer(): TeamLayer {
        return gm.retrieveNull(this._teamLayer);
    }

    private _dialogManager?: ee.DialogManager;

    /** Gets the current dialog manager associated with this scene. */
    private get dialogManager(): ee.DialogManager {
        return gm.retrieveUndefined(this._dialogManager);
    }

    /** Current page or target page. */
    private currentPage?: MenuScenePage;

    private actionMask = 0;

    private isInitialized = false;
    private needUpdatePageSize = false;

    /**
     * Whether or not to animate the level marker.
     * Only considered if showSelectLevel is true.
     * Considers tutorial is enabled or not.
     */
    private animateMarker = false;

    /** Whether or not page view scrolling is enabled. */
    private readonly isScrollingEnabled = true;

    private _characterTutorial?: Tutorial;

    /** Gets the character upgrade tutorial. */
    private get characterTutorial(): Tutorial {
        return gm.retrieveUndefined(this._characterTutorial);
    }

    /** Not used. */
    // private isPlayLevelTutorial: boolean = false;

    private isDialogExist: boolean = false;

    private _controller?: MenuController;
    private didMenuLoaded = false;

    /** Whether the home layer is pushing a sub-layer. */
    private isHomeLayerPushing = false;

    /** Whether the home layer is popping a sub-layer. */
    private isHomeLayerPopping = false;

    /** Dùng phân biệt về home là lần đầu từ load hay từ các layer khác */
    private hasJustLoaded = false;

    /** Lưu trữ trạng thái đã iniitialized của các layers */
    //[iap_shop_layer, character_layer, home_layer, team_layer, leaderboard_layer]
    private initializedLayers: boolean[] = [false, false, true, false, false];

    public get controller(): MenuController {
        return gm.retrieveUndefined(this._controller);
    }

    private audioManger: AudioManager;

    @crashlytics
    protected onLoad(): void {
        this.hasJustLoaded = true;
        this.audioManger = ee.ServiceLocator.resolve(AudioManager);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        assert(this._pageView !== null);
        this._dialogManager = new ee.DefaultDialogManager(this.node);

        // Tạm trừ lại rewards trong phần thưởng level để animate tăng
        this.minusRewardsForDisplayAnimation(ee.ServiceLocator.resolve(LevelManager));

        ee.ServiceLocator.register(this.dialogManager);
        ee.ServiceLocator.resolve(AudioManager).playMusic(SoundType.BgMenu);
        ee.ServiceLocator.resolve(TrackingManager).trackEventOpen(SceneName.SceneMenuHome);
        ee.AsyncManager.getInstance().add(MenuSceneImpl.name, async () => {
            await this.initialize();
            this.isInitialized = true;
            if (this.needUpdatePageSize) {
                this.needUpdatePageSize = false;
                this.resizePages();
            }
        });
        ee.ServiceLocator.resolve(TrackingManager).trackConversionScene(ConversionScene.SceneMenu);

        if (CC_PREVIEW && !CC_JSB) {
            window["menuScene"] = this;
        }
    }

    private async initialize(): Promise<void> {
        assert(this._topHud !== null);
        assert(this._bottomHud !== null);

        try {
            const t1 = ee.ServiceLocator.resolve(StoreManager).refreshMarketItems();
            const t2 = ee.sleep(3000);
            await Promise.race([t1, t2]);
        } catch (ex) {
            cc.log(`${ex}`);
        }

        this.initializeController();
        this.initializeTopHud();
        this.initializeBottomHud();
        this.initializeCamera();
        await this.initializeLayers();
        this.initializeHomeLayer();
        //this.initializeEventLayer();

        // this.initializeShopLayer();
        // this.initializeCharacterLayer();
        // this.initializeLeaderboardLayer();

        // Instantly change the current page to home.
        this.pageView.scrollToPage(MenuScenePage.Home, 0);
        await this.changePage(MenuScenePage.Home, 0);
        //this.initializeCharacterUpgradeTutorial();
        this.initializeBackButton();
        this.onSceneDidEnter();
    }

    @crashlytics
    protected onEnable(): void {
        // Các task có khả năng dẫn đến lock thì push vào đây (Lock tức là cần tắt Dialog mới xong await)
        const awaitables: Function[] = [];
        let eventLayer: EventLayer = null;

        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
        ee.ServiceLocator.resolve(SceneManager).addObserver(this.uuid, {
            willEnter: async () => {
                const levelManager = ee.ServiceLocator.resolve(LevelManager);
                const openMap = (this.actionMask & MenuSceneAction.OpenMap) !== 0;
                const openEvent = (this.actionMask & MenuSceneAction.OpenEvent) !== 0;
                const openCharacterEvent = (this.actionMask & MenuSceneAction.OpenCharacterEvent) !== 0;
                const goToNextLevel = (this.actionMask & MenuSceneAction.NextLevel) !== 0;

                if (openMap || openEvent || openCharacterEvent) {
                    const [area, level] = this.parseAreaAndLevel(this.actionMask);
                    let layer: MapLayer;
                    if (openMap) {
                        layer = await this.homeLayer.pushMapLayer(false);
                    } else if (openEvent) { // openEvent
                        eventLayer = await this.homeLayer.pushEventLayer(false);
                        eventLayer.showMode(EventLayerMode.Main)
                        layer = await eventLayer.getMainEventLayer().pushMapLayer(false);
                        await layer.scrollToLevel({ area });
                    } else if (openCharacterEvent) {
                        eventLayer = await this.homeLayer.pushEventLayer(false);
                        eventLayer.showMode(EventLayerMode.Jaki)
                        layer = await eventLayer.getCharacterEventLayer().pushMapLayer(false);
                        await layer.scrollToLevel();
                    }
                    this.homeLayer.blockMenu(true);

                    let info: AreaInfo;
                    if (openMap) info = levelManager.getStoryAreaInfo(area);
                    else if (openEvent) info = levelManager.getEventAreaInfo(area);
                    else if (openCharacterEvent) info = levelManager.getCharacterAreaInfo();

                    // Check if there is any area/game clear events in the current area.
                    const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
                    if (info.shouldReward /** Should display area clear dialog first. */
                        || profileManager.canProcessExp() /** Should display level up dialog first. */) {
                        // Dialogs should be handled later in didEnter event.
                        await layer.scrollToLevel({
                            area,
                            level
                        });
                    } else {
                        const [nextArea, nextLevel] = goToNextLevel
                            ? openMap
                                ? MapUtils.findNextStoryLevel(levelManager, area, level)
                                : [area, MapUtils.findNextEventLevel(levelManager, area, level)]
                            : [area, level];
                        // Immediately switch to the current area and level.
                        await layer.scrollToLevel({
                            area: nextArea,
                            level: nextLevel
                        });
                    }
                }
                // Don't put this in didEnter because UI will still show old values.
                this.setNotification();
            },
            didEnter: async () => {
                const piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
                const templeManager = ee.ServiceLocator.resolve(HiddenTempleManager);
                const levelManager = ee.ServiceLocator.resolve(LevelManager);
                const vipManager = ee.ServiceLocator.resolve(VipManager);
                const cardRewardManager = ee.ServiceLocator.resolve(CardRewardPerLevelManager);
                levelManager.isLoadingGameScene = false;

                const openStory = (this.actionMask & MenuSceneAction.OpenMap) !== 0;
                const openEvent = (this.actionMask & MenuSceneAction.OpenEvent) !== 0;
                const openCharacterEvent = (this.actionMask & MenuSceneAction.OpenCharacterEvent) !== 0;
                const openMap = openStory || openEvent || openCharacterEvent;
                const goToNextLevel = (this.actionMask & MenuSceneAction.NextLevel) !== 0;
                const selectLevel = (this.actionMask & MenuSceneAction.ShowSelectLevel) !== 0;

                let curArea: number;
                let curLevel: number;
                let curAreaInfo: AreaInfo;

                let nextArea: number;
                let nextLevel: number;
                let mapLayer: MapLayer;

                if (openStory) {
                    mapLayer = this.homeLayer.getTopLayer().getComponent(MapLayer);
                } else if (openEvent) {
                    mapLayer = await eventLayer.getMainEventLayer().pushMapLayer(false);
                } else if (openCharacterEvent) {
                    mapLayer = await eventLayer.getCharacterEventLayer().pushMapLayer(false);
                }

                // Chọn id của level tiếp theo
                if (openMap) {
                    [curArea, curLevel] = this.parseAreaAndLevel(this.actionMask);
                    if (openStory) {
                        curAreaInfo = levelManager.getStoryAreaInfo(curArea);
                    } else if (openEvent) {
                        curAreaInfo = levelManager.getEventAreaInfo(curArea);
                    } else if (openCharacterEvent) {
                        curAreaInfo = levelManager.getCharacterAreaInfo();
                    }

                    if (goToNextLevel) {
                        if (openStory) {
                            [nextArea, nextLevel] = MapUtils.findNextStoryLevel(levelManager, curArea, curLevel);
                        } else if (openEvent) {
                            nextArea = curArea; // Event mode chỉ có 1 Area
                            nextLevel = MapUtils.findNextEventLevel(levelManager, curArea, curLevel);
                        } else if (openCharacterEvent) {
                            nextArea = curArea; // Character mode chỉ có 1 Area
                            nextLevel = MapUtils.findNextCharacterLevel(levelManager, curLevel);
                        }
                    } else {
                        nextArea = curArea;
                        nextLevel = curLevel;
                    }
                    mapLayer.blockMenu(true);
                }

                awaitables.push(async() => {
                    await this.ShowLevelRewards(mapLayer, levelManager);
                })

                awaitables.push(async () => {
                    await this.processExp();
                })

                if (openMap) {
                    assert(mapLayer != null);
                    awaitables.push(async () => {
                        await mapLayer.handleDialogs({
                            area: curArea,
                            areaInfo: curAreaInfo,
                            level: curLevel,
                        });
                    })
                    awaitables.push(this.checkUnlockEventMode.bind(this));
                }

                awaitables.push(this.showDialogAtHomeLayerWithRewards.bind(this));

                awaitables.push(async () => {
                    await this.showVipRewardDialog(vipManager);
                });

                if (selectLevel) {
                    if (mapLayer == null || openStory) {
                        let lastUnlockedStoryArea = levelManager.getLastUnlockedArea();
                        let lastUnlockedStoryLevel = levelManager.getStoryLastUnlockedLevel(lastUnlockedStoryArea);
                        awaitables.push(async () => {
                            await this.showSelectLevelDialog(lastUnlockedStoryArea, lastUnlockedStoryLevel, !openMap);
                        });
                    } else {
                        awaitables.push(async () => {
                            await mapLayer.showSelectLevelDialog(nextArea, nextLevel);
                        });
                    }

                }

                awaitables.push(this.showDialogAtHomeLayerNotReward.bind(this));

                // piggy bank unlock hoặc hết hạn với ruby bank nhỏ hơn tối thiểu ?
                const piggyEnd = piggyBankManager.isEndEvent() && !piggyBankManager.checkCanBuy();
                if (!piggyEnd) {
                    const unlockPiggyBank = piggyBankManager.checkUnlock() && !piggyBankManager.isHadShowUnlock();
                    const fullPiggyBank = !piggyBankManager.isHadShowFull() && piggyBankManager.checkFull();
                    const lastChance = piggyBankManager.isEndEvent() && piggyBankManager.checkCanBuy()
                    if (unlockPiggyBank || fullPiggyBank || lastChance) {
                        awaitables.push(async () => {
                            await piggyBankManager.showUnlockDialog(this.topHud, this.dialogManager)
                        });
                    }
                }

                // hidden temple unlock
                const openUnlockTemple = !templeManager.isHadShowUnlock() && templeManager.checkUnlock(nextArea, nextLevel, false);
                if (openUnlockTemple) {
                    awaitables.push(async () => {
                        await templeManager.showUnlockDialog(this.dialogManager)
                    });
                }

                if (mapLayer != null) {
                    if (goToNextLevel) {
                        awaitables.push(async () => {
                            await mapLayer.scrollToLevel({
                                area: nextArea,
                                level: nextLevel
                            });
                        });
                    }
                }

                // const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
                // if (profileManager.canProcessExp()) {
                //     awaitables.push(async () => {
                //         await profileManager.processExp(this.dialogManager);
                //     });
                // }

                this.schedule(this.setNotification, 2);

                (async () => {
                    for (const awaitable of awaitables) {
                        let shouldProcessNext = await awaitable();
                        if (typeof shouldProcessNext === 'boolean' && shouldProcessNext === false) {
                            break;
                        }
                        if (levelManager.isLoadingGameScene) {
                            break;
                        }
                    }
                })().then(async () => {
                    if (levelManager.isLoadingGameScene) {
                        return;
                    }
                    this.homeLayer.checkShowPiggyBankButton(selectLevel);
                    this.homeLayer.checkShowPlayPassButton(selectLevel);
                    this.homeLayer.checkShowHiddenTempleButton();
                    this.homeLayer.blockMenu(false);
                    mapLayer && mapLayer.blockMenu(false);
                    this.pageView.enabled = true;
                    this.blocker.enabled = false
                });
            },
        });

        this.pageView.node.on(cc.Node.EventType.SIZE_CHANGED, this.resizePages, this);
        cc.game.on(cc.game.EVENT_SHOW, this.onGameShown, this);
        cc.game.on(cc.game.EVENT_HIDE, this.onGameHidden, this);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
        ee.ServiceLocator.resolve(SceneManager).removeObserver(this.uuid);
        this.unschedule(this.setNotification);
        this.pageView.node.off(cc.Node.EventType.SIZE_CHANGED, this.resizePages, this);
        cc.game.off(cc.game.EVENT_SHOW, this.onGameShown, this);
        cc.game.off(cc.game.EVENT_HIDE, this.onGameHidden, this);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }


    private async ShowLevelRewards(mapLayer: MapLayer, levelManager: LevelManager): Promise<void> {
        if (mapLayer == null) {
            await this.animateLevelRewards(mapLayer, levelManager, null);
            return;
        }

        this.pageView.enabled = false;
        this.topHud.setEventButtons(mapLayer.hiddenTempleButton, mapLayer.piggyBankButton, mapLayer.playPassButton);
        const animator = new UIBounceAnimator(mapLayer.eventButtonContainer,
            mapLayer.topButtonContainer,
            mapLayer.bottomButtonContainer,
            0.4, 10
        );
        await this.animateLevelRewards(mapLayer, levelManager, animator);
    }

    private async animateLevelRewards(mapLayer: MapLayer, levelManager: LevelManager, animator: UIBounceAnimator): Promise<void> {
        const totalRewards = levelManager.totalRewards;
        if (totalRewards == null) {
            return;
        }

        const rewardManager = ee.ServiceLocator.resolve(RewardManager);

        const eventSubType = ["axe", "gold_pass", "free_pass", "piggy_bank"];
        const [eventList, storeList] = totalRewards.reduce(
            ([events, stores], item) => {
                if (eventSubType.includes(item.raw?.subType)) {
                    events.push(item);
                } else {
                    stores.push(item);
                }
                return [events, stores];
            },
            [[], []]
        );

        // flying store first
        if (storeList.length > 0) {
            await EffectHelper.showFlyingRewardListInMenuScene(storeList, cc.Vec2.ZERO);
            this.addRewardsForDisplayAnimation(levelManager);
        }

        // flying event rewards
        const piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
        const hiddenTempleManager = ee.ServiceLocator.resolve(HiddenTempleManager);

        const [hiddenButton, PiggyButton, playPassButton] = this.topHud.getEventButtons();
        const hiddenTempleButton = hiddenButton.getComponent(HiddenTempleButton);
        const piggyBankButton = PiggyButton.getComponent(PiggyBankButton);

        // Thêm RubyBank
        const piggyReward = levelManager.piggyBank;
        if (piggyReward > 0) {
            let piggy = rewardManager.createReward({
                type: 'store',
                subType: 'piggy_bank',
                value: piggyReward,
            })
            eventList.push(piggy);
        }

        if (eventList.length > 0) {

            // Ẩn các nút nếu không có rewards.
            if (mapLayer != null) {
                const axe = eventList.find(item => item.raw?.subType === "axe") || null;
                mapLayer.hiddenTempleButton.active = axe != null;

                const piggy = eventList.find(item => item.raw?.subType === "piggy_bank") || null;
                mapLayer.piggyBankButton.active = piggy != null;

                const pass = eventList.find(item => item.raw?.subType === "free_pass" || item.raw?.subType === "gold_pass") || null;
                mapLayer.playPassButton.active = pass != null;
            }

            if (animator != null) {
                animator.show();
                await animator.waitForShow();
            }
            await EffectHelper.showFlyingRewardListInMenuScene(eventList, cc.Vec2.ZERO);
            piggyBankButton?.animateValue(piggyBankManager.rubyBank);
            hiddenTempleButton?.setAxeAmount(hiddenTempleManager.getData().axeAmount);
        }

        // Chờ Flying Animation
        await TimeUtils.sleep(1000);
        if (animator != null) {
            animator.hide();
            await animator.waitForHide();
        }

        // open chest
        if (totalRewards.length > 0) {
            const pulledChest = totalRewards.find(item => item.type === RewardType.Chest) || null;
            if (pulledChest != null) {
                let chestReward = rewardManager.createReward({
                    type: 'chest',
                    subType: pulledChest.raw.subType,
                    value: 1
                })

                let dialog = await RewardUtils.openChestDialog([chestReward]);
                dialog[dialog.length - 1].onWillHide(() => {
                    //this._dialogLayer.active = false;
                })
                await dialog[dialog.length - 1].waitForHide();

                // Chờ Flying Animation
                await TimeUtils.sleep(1000);
            }
        }
        levelManager.totalRewards = null;
        levelManager.piggyBank = 0;
        levelManager.axeRewards = 0;
    }

    private minusRewardsForDisplayAnimation(levelManager: LevelManager) {
        const totalRewards = levelManager.totalRewards;
        if (totalRewards == null) {
            return;
        }

        totalRewards.forEach((reward) => {
            if (reward.type === RewardType.Store) {
                if (reward.raw.subType === "xp") {
                    const profileManager = ee.ServiceLocator.resolve(UserProfileManager)
                    profileManager.addExp(-reward.raw.value, null);
                }
                if (reward.raw.subType === "gold") {
                    const storeManager = ee.ServiceLocator.resolve(StoreManager)
                    const balance = storeManager.getItemBalance(StoreItem.Gold);
                    const gold = balance - reward.raw.value;
                    storeManager.setItemBalance(StoreItem.Gold, gold);
                }
            }
        })
    }

    private addRewardsForDisplayAnimation(levelManager: LevelManager) {
        const totalRewards = levelManager.totalRewards;
        if (totalRewards == null) {
            return;
        }

        totalRewards.forEach((reward) => {
            if (reward.type === RewardType.Store) {
                if (reward.raw.subType === "xp") {
                    const profileManager = ee.ServiceLocator.resolve(UserProfileManager)
                    profileManager.addExp(reward.raw.value, null);
                }
                if (reward.raw.subType === "gold") {
                    const storeManager = ee.ServiceLocator.resolve(StoreManager)
                    const balance = storeManager.getItemBalance(StoreItem.Gold);
                    const gold = balance + reward.raw.value;
                    storeManager.setItemBalance(StoreItem.Gold, gold);
                }
            }
        })
    }

    private async showVipRewardDialog(vipManager: VipManager): Promise<boolean> {
        const showDialog = vipManager.checkShowRewardDialog();
        if (showDialog) {
            await vipManager.waitForEndEvent();
            return true;
        }
        return false
    }

    private async showSelectLevelDialog(area: number, level: number, fromHome: boolean): Promise<void> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        let areaInfo = levelManager.getStoryAreaInfo(area);
        let levelInfo = areaInfo.levels[level];
        await MapUtils.showSelectLevelDialog(
            this.selectLevelDialogPrefab,
            this.dialogManager,
            this.controller,
            areaInfo, levelInfo,
            fromHome);
    }

    private onGameShown(): void {
        ee.ServiceLocator.resolve(NotificationManager).unschedule();
        if (!this.didMenuLoaded) {
            return;
        }
        cc.log('onGameShown');
        NativeCall.queryAllPendingPurchases();
    }

    private onGameHidden(): void {
        ee.ServiceLocator.resolve(NotificationManager).schedule();
        ee.ServiceLocator.resolve(TrackingManager).saveTimeLastLogin();
    }

    private parseAreaAndLevel(mask: number): [number, number] {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const openMap = (mask & MenuSceneAction.OpenMap) !== 0;
        const openEvent = (mask & MenuSceneAction.OpenEvent) !== 0;
        const openCharacterEvent = (mask & MenuSceneAction.OpenCharacterEvent) !== 0;
        let area = -1;
        let level = -1;
        if (openMap) {
            area = levelManager.getCurrentStoryArea();
            level = levelManager.getCurrentStoryLevel();
        } else if (openEvent) {
            area = levelManager.getCurrentEventArea();
            level = levelManager.getCurrentEventLevel();
        } else if (openCharacterEvent) {
            area = 0;
            level = levelManager.getCurrentCharacterLevel();
        }
        if (MenuSceneAction.hasSelectLevel(this.actionMask)) {
            [area, level] = MenuSceneAction.getSelectLevel(this.actionMask);
        }
        return [area, level];
    }

    @crashlytics
    private setNotification(): void {
        // Disabled: call multiple times.
        // ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.setNotification, `${this.uuid}`);

        // Set notification for new deal on iap shop
        const shopManager = ee.ServiceLocator.resolve(ShopManager);
        const levelManager = ee.ServiceLocator.resolve(LevelManager);

        const offerPacks = shopManager.getOfferPacks();
        const cardPacks = shopManager.getCardPacks();
        let newOffer = 0;
        for (const offerPack of offerPacks) {
            if (offerPack.isNew) {
                newOffer++;
            }
        }
        this.bottomHud.setNotificationByType(newOffer + (cardPacks.isNew ? 1 : 0), NotificationType.Shop);
        const cardManager = ee.ServiceLocator.resolve(CardManager);
        const cards = cardManager.findCards();
        let newCard = 0;
        let canUpgradeCard = 0;
        for (const card of cards) {
            if (card.new && card.collected) {
                newCard++;
            }
            if (card.canUpgrade()) {
                canUpgradeCard++;
            }
        }
        this.bottomHud.setNotificationByType(canUpgradeCard, NotificationType.CharacterGreen);
        this.bottomHud.setNotificationByType(newCard, NotificationType.CharacterRed);

        // // Event
        // this.bottomHud.setNotificationByType(levelManager.isEventModeUnlocked ? 1 : 0, NotificationType.Event);

        // Set notification for vip daily reward readied to claim
        const vipManager = ee.ServiceLocator.resolve(VipManager);
        this.topHud.setNotificationVip(vipManager.canClaimedReward() ? 1 : 0);

        // Set notification for daily quest and achievement complete
        const currentQuest = ee.ServiceLocator.resolve(DailyQuestManager).getCurrentQuest();
        const achievements = ee.ServiceLocator.resolve(AchievementManager).getAchievements();
        const questClaimedValue = (currentQuest.isCompleted() && !currentQuest.isClaimed()) ? 1 : 0;
        let subQuestClaimedValue =
            currentQuest.objectives
                .map(o => o.isCompleted() && !o.isClaimed ? 1 : 0)
                .reduce((a, b) => a + b, 0);
        let achievementValue = 0;
        for (const achievement of achievements) {
            const objective = achievement.getCurrentObjective();
            if (objective !== undefined) {
                if (objective.isCompleted()) {
                    achievementValue++;
                }
            }
        }

        if (this._characterLayer) {
            this._characterLayer.setNotificationCard();
        }
        if (this._homeLayer) {
            this._homeLayer.setNotificationByPage(questClaimedValue + subQuestClaimedValue + achievementValue, HomeLayerPage.QuestAchievement);
            // Set notification for daily reward that isn't claim
            const dailyManager = ee.ServiceLocator.resolve(DailyRewardManager);
            this._homeLayer.setNotificationByPage(dailyManager.isRewardAvailable() ? 1 : 0, HomeLayerPage.Daily);

            // Set notification for event
            const evenModeUnlock = levelManager.isEventModeUnlocked ? 1 : 0
            this._homeLayer.setNotificationByPage(0, HomeLayerPage.Event)

        }
    }

    /** Initializes the top hud. */
    private initializeTopHud(): void {
        this.topHud.listener = {
            onPressedSetting: this.showSettingDialog.bind(this),
            onPressedBack: async () => {
                await this.homeLayer.popLayer();
                await this.showDialogAtHomeLayer();
            },
            onPlusRuby: () => {
                this.showShopPage(IapShopCategory.Ruby);
            },
            onPlusGold: () => {
                this.showShopPage(IapShopCategory.Gold);
            },
            onPlusEnergy: () => {
                this.showShopPage(IapShopCategory.Energy);
            },
            // onPlusTicket: () => {
            // this.showShopAtCategory(IapShopCategory.Ticket);
            // },
            onPressedLevel: () => {
                if (this.isHomeLayerPushing || this.isHomeLayerPushing) {
                    // Wait for animation.
                    return;
                }
                this.showLevelProfile();
            },
            onPlusPvpTicket: () => {
                this.showShopPage(IapShopCategory.PvpTicket);
            },
            onPressedVip: this.showVipDialog.bind(this),
        };
    }

    /** Initializes the bottom hud. */
    private initializeBottomHud(): void {
        this.bottomHud.listener = {
            selected: (sender, page) => {
                this.homeLayer.popAllLayers(0.2).then(() => {
                    this.changePage(page, 0.6);
                })
            },
            setFilterToAll: () => {
                this._characterLayer && this._characterLayer.setFilterToAll();
            }
        };
    }

    /** Initializes the menu controller. */
    private initializeController(): void {
        this._controller = {
            showShopCategory: (category: IapShopCategory) => {
                this.showShopPage(category);
            },
            setAllButtonEnabled: (enabled: boolean) => {
                this.setBottomTopHudEnabled(enabled);
            },
            changePage: (page: MenuScenePage) => {
                this.changePage(page, 0.3);
            }
        };
    }

    private initializeHomeLayer(): void {
        this.homeLayer.topHud = this.topHud;
        this.homeLayer.bottomHud = this.bottomHud;
        this.homeLayer.controller = this.controller;
        let offerManager = ee.ServiceLocator.resolve(OfferManager);
        this.homeLayer.listener = {
            layerWillPush: sender => {
                this.isHomeLayerPushing = true;
                this.renderPages(MenuScenePage.Home);
                this.updateTopHudTemplate();
            },

            layerDidPush: sender => {
                this.isHomeLayerPushing = false;
                this.renderPages(MenuScenePage.Home);
                this.updateButtonHome();
            },
            layerWillPop: sender => {
                this.isHomeLayerPopping = true;
                this.renderPages(MenuScenePage.Home);
            },
            layerDidPop: sender => {
                this.isHomeLayerPopping = false;
                this.renderPages(MenuScenePage.Home);
                this.updateTopHudTemplate();
                this.updateButtonHome();
            },
            goToOffer: sender => {
                //this.showShopPage(IapShopCategory.Offer);
                //this.showOfferDialog().then();
                offerManager.showOfferDialogScript(this.dialogManager).then();
            },
        };
        this.homeLayer.preloadLayers();
    }

    // private initializeEventLayer(): void {
    //     let info: EventLayerInfo = {
    //         listener: {
    //         },
    //         menuController: this.controller,
    //         topHud: this.topHud,
    //         bottomHud: this.bottomHud
    //     }
    //     this._eventLayer.setInfo(info);
    // }

    private initializeTeamLayer(): void {
        this.teamLayer.controller = this.controller;
        this.teamLayer.dialogManager = this.dialogManager;
        this.teamLayer.myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        this.teamLayer.gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        this.teamLayer.topHud = this.topHud;
        this.teamLayer.bottomHud = this.bottomHud;
        this.teamLayer.init();
    }

    private async initializeCharacterLayer(): Promise<void> {
        this.characterLayer.setMenuController(this.controller);
        this.characterLayer.setTopHud(this.topHud);
    }

    private async initializeShopLayer(): Promise<void> {
        this.shopLayer.dialogManager = this.dialogManager;
        this.shopLayer.setTopHud(this.topHud);
        this.shopLayer.setBottomHud(this.bottomHud);
    }

    private initializeLeaderboardLayer(): void {
        this._leaderboardLayer.setBottomHud(this.bottomHud);
        this._leaderboardLayer.setTopHub(this.topHud);
    }

    private async initializeLayers(): Promise<void> {
        const asyncManager = new ee.AsyncManager();
        // const layers = this.pagePrefabNames.map((prefabName, index) => {
        //     const node = prefabPool.instance.get(prefabName);
        //
        //     // size is not consistent and may be changed in resizePages().
        //     // Steps: Facebook Instant Games:
        //     // menu scene => game scene => level complete => show interstitial ad => menu scene.
        //     const size = this.pageView.node.getContentSize();
        //     node.setContentSize(size);
        //     node.groupIndex = getPageGroupIndex(index as MenuScenePage);
        //     this.pageView.addPage(node);
        //     return node;
        // });
        // // Gán các layer sau khi tất cả hoàn tất
        // this._shopLayer = layers[0].getComponent(IapShopLayer);
        // this._characterLayer = layers[1].getComponent(CharacterLayer);
        // this._homeLayer = layers[2].getComponent(HomeLayer);
        // this._eventLayer = layers[3].getComponent(EventLayer);
        // this._leaderboardLayer = layers[4].getComponent(LeaderboardLayer);

        const size = this.pageView.node.getContentSize();
        for (let i = 0; i < 2; i++) {
            const node = new cc.Node();
            node.setContentSize(size);
            node.groupIndex = getPageGroupIndex(i as MenuScenePage);
            this.pageView.addPage(node);
        }

        // i = 2
        const homeNode = prefabPool.instance.get("home_layer")
        homeNode.setContentSize(size);
        homeNode.groupIndex = getPageGroupIndex(MenuScenePage.Home);
        this.pageView.addPage(homeNode);

        // const eventNode = prefabPool.instance.get("event_layer")
        // eventNode.setContentSize(size);
        // eventNode.groupIndex = getPageGroupIndex(MenuScenePage.Event);
        // this.pageView.addPage(eventNode);

        for (let i = 3; i < 5; i++) {
            const node = new cc.Node();
            node.setContentSize(size);
            node.groupIndex = getPageGroupIndex(i as MenuScenePage);
            this.pageView.addPage(node);
        }

        await asyncManager.flushAll({ size: 1, delay: 0 });
        asyncManager.destroy();

        this._homeLayer = homeNode.getComponent(HomeLayer);
        //this._eventLayer = eventNode.getComponent(EventLayer);
    }

    private async initializeLayer(page: MenuScenePage): Promise<void> {
        if (page === MenuScenePage.Home) { //} || page === MenuScenePage.Event) {
            return;
        }
        const node = prefabPool.instance.get(this.pagePrefabNames[page])
        switch (page) {
            case MenuScenePage.Shop:
                this._shopLayer = node.getComponent(IapShopLayer);
                await this._shopLayer.init();
                await this.initializeShopLayer();
                this._shopLayer.getMoreOffers(false);
                break;
            case MenuScenePage.Character:
                this._characterLayer = node.getComponent(CharacterLayer);
                await this.initializeCharacterLayer();
                await this.initializeCharacterUpgradeTutorial();
                break;
            case MenuScenePage.Team:
                this._teamLayer = node.getComponent(TeamLayer);
                this.initializeTeamLayer();
                break;
            case MenuScenePage.LeaderBoard:
                this._leaderboardLayer = node.getComponent(LeaderboardLayer);
                this.initializeLeaderboardLayer();
                break;
        }

        const pageNode = this.pageView.getPages()[page];
        pageNode.removeAllChildren();
        const size = this.pageView.node.getContentSize();
        node.setContentSize(size);
        node.setPosition(0, 0);
        pageNode.addChild(node);
        this.initializedLayers[page] = true;
    }

    private initializeBackButton(): void {
        this.getComponent(ee.BackButtonListener).setCallback(async () => {
            if (this.currentPage === MenuScenePage.Home) {
                if (this.homeLayer.canGoBack()) {
                    await this.homeLayer.popLayer();
                    await this.showDialogAtHomeLayer();
                } else {
                    if (this.isDialogExist) {
                        return;
                    }
                    this.isDialogExist = true;
                    const dialog = await ExitDialog.create();
                    dialog.onDidHide(() => {
                        this.isDialogExist = false;
                    });
                    dialog.show(this.dialogManager);
                }
            }
        });
    }

    /** Sets whether switchToStoryMap should enabled or not. */
    public addAction(action: number): this {
        this.actionMask |= action;
        return this;
    }

    private stopSoundFromHiddenTempleLayer(page: MenuScenePage): void {
        // Tắt sound nếu đổi page từ HiddenTemple
        if (this.currentPage === MenuScenePage.Home) {
            const topLayer = this.homeLayer.getTopLayer();
            if (topLayer) {
                const templeLayer = topLayer.getComponent(HiddenTempleLayer);
                if (templeLayer) {
                    this.audioManger.stopAllSounds();
                }
            }
        }
    }

    private checkToShowPiggyBankButton(page: MenuScenePage): void {
        if (page === MenuScenePage.Home) {
            this.homeLayer.checkShowPiggyBankButton(false);
        }
    }

    private checkToShowPlayPassButton(page: MenuScenePage): void {
        if (page === MenuScenePage.Home) {
            this.homeLayer.checkShowPlayPassButton(false);
        }
    }

    /** Changes the currently displaying page. */
    private async changePage(page: MenuScenePage, duration: number): Promise<void> {
        const notChange = this.currentPage === page;

        // Nếu đổi page mà page đó chưa initialize
        if (!notChange) {
            if (!this.initializedLayers[page]) {
                if (page === MenuScenePage.LeaderBoard) {
                    await this.initializeLayer(page);
                } else {
                    const loadingProcess = await LoadingProcess.create();
                    loadingProcess.show(this.dialogManager);
                    await this.initializeLayer(page);
                    loadingProcess.hide();
                }
            }
        }

        this.currentPage = page;
        this.stopSoundFromHiddenTempleLayer(this.currentPage);
        // Các hàm chỉ thưc hiện khi change Page từ các page khác
        if (!this.hasJustLoaded) {
            this.processExp().then(() => {
                this.checkToShowPiggyBankButton(this.currentPage);
                this.checkToShowPlayPassButton(this.currentPage)
            })
        } else {
            this.hasJustLoaded = false;
        }

        const sceneMap: { [key: number]: SceneName } = {
            [MenuScenePage.Shop]: SceneName.SceneMenuShop,
            [MenuScenePage.Character]: SceneName.SceneMenuLoadout,
            [MenuScenePage.Home]: SceneName.SceneMenuHome,
            [MenuScenePage.Team]: SceneName.SceneTeam,
            [MenuScenePage.LeaderBoard]: SceneName.SceneMenuLeaderboard,
        };

        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
        trackingManager.trackEventOpen(sceneMap[page]); ``

        // const notChange = this.currentPage === page;
        // this.currentPage = page;

        // Remove all dialogs.
        const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        dialogManager.popToLevel(0);

        // Update top hud.
        this.topHud.changePage(page);
        this.updateTopHudTemplate();

        // Update bottom hud.
        this.bottomHud.changePage(page);
        this.updateButtonHome();

        // FIXME: too specific.
        if (this._characterLayer) {
            this.characterLayer.onMenuPageChanged(page);
        }

        if (page === MenuScenePage.Shop) {
            // Mark isNew to false.
            const shopManager = ee.ServiceLocator.resolve(ShopManager);
            shopManager.getCardPacks().isNew = false;
            shopManager.getOfferPacks().forEach(item => item.isNew = false);
            trackingManager.trackConversionScene(ConversionScene.SceneShop);
        }

        if (page === MenuScenePage.Team) {
            this.teamLayer.updateBackButton(page);
        }
        else {
            if(this._teamLayer && this._teamLayer.originalBackCallback){
                this.topHud.listener.onPressedBack = this._teamLayer.originalBackCallback;
            }
        }

        // track conversion scene MenuMap
        if (page === MenuScenePage.Home && this.homeLayer._isLayerStoryOpening) {
            trackingManager.trackConversionScene(ConversionScene.SceneMap);
        }

        this.scrollToPage(page, duration);

        if (duration == 0) { //Menu Load first page
            return;
        }
        if (notChange) { //page not changed
            return;
        }
        if (this.currentPage === MenuScenePage.Character) {
            this.showCollectedEvent().then();
        }
    }

    private updateButtonHome() {
        const buttonHome = this.bottomHud.getNodeByPage(MenuScenePage.Home).getComponent(cc.Button);
        const layer = this.homeLayer.getTopLayer();
        buttonHome.interactable = this.currentPage != MenuScenePage.Home || layer != null;
    }

    private updateTopHudTemplate(): void {
        const page = this.currentPage;
        if (page === MenuScenePage.Home) {
            this.topHud.applyTemplate(this.homeLayer.template);
            this.topHud.setBackButtonVisible(
                this.homeLayer.canGoBack() || this.isHomeLayerPushing || this.isHomeLayerPopping);
        } else {
            let template = TopHudTemplate.Default;
            switch (page) {
                case MenuScenePage.Shop:
                    template = TopHudTemplate.Shop;
                    break;
                case MenuScenePage.Team:
                    template = TopHudTemplate.Default;
                    break;
                default:
                    break;
            }
            this.topHud.applyTemplate(template);
            this.topHud.setBackButtonVisible(false);
        }
    }

    /** Shows the setting dialog. */
    @crashlytics
    private showSettingDialog(): void {
        if (this.isDialogExist) {
            return;
        }
        this.isDialogExist = true;

        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.showSettingDialog, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        const dialog = cc.instantiate(this.settingDialogPrefab).getComponent(SettingDialog);
        dialog.show(this.dialogManager);
        dialog.onDidHide(() => {
            this.isDialogExist = false;
        });
    }

    /** Shows the VIP dialog. */
    @crashlytics
    private showVipDialog(): void {
        if (this.isDialogExist) {
            return;
        }
        this.isDialogExist = true;

        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.showVipDialog, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);

        const dialog = cc.instantiate(this.vipDialogPrefab).getComponent(VipLayer);
        dialog.show(this.dialogManager);
        dialog.setOnPressShopCallBack(() => {
            this.showShopPage(IapShopCategory.Offer);
        });
        dialog.onDidHide(() => {
            this.isDialogExist = false;
        });
    }

    private showShopPage(category: number): void {
        this.changePage(MenuScenePage.Shop, 0.3).then(()=>{
            this.shopLayer.showCategory(category);
        });
    }

    // private async showEventPage(mode: number): Promise<void> {
    //     await this.changePage(MenuScenePage.Event, 0.3);
    //     this._eventLayer.showMode(mode);
    // }

    private showLevelProfile(): void {
        this.changePage(MenuScenePage.Home, 0.3).then(()=>{
            if (this.isHomeLayerPushing || this.isHomeLayerPushing) {
                return;
            }
            this.homeLayer.pushProfileLayer();
            ee.ServiceLocator.resolve(TrackingManager).trackConversionScene(ConversionScene.SceneProfile);
        });
    }

    /** Initializes the character upgrade tutorial. */
    private async initializeCharacterUpgradeTutorial(): Promise<void> {
        const tutorialManager = ee.ServiceLocator.resolve(TutorialManager);
        this._characterTutorial =
            new CharacterUpgradeTutorial(tutorialManager, this.bottomHud, this.characterLayer.node);
    }

    /** FIXME: public */

    public checkCharacterUpgradeTutorial(): void {
        //// Tạm thời bỏ, design lại sau
        // if (this.characterTutorial.checkStart()) {
        //     if (this.currentPage === MenuScenePage.Character) {
        //         this.characterTutorial.finish();
        //     }
        //     this.characterTutorial.start();
        // }
    }

    /** Intializes the camera for culling. */
    private initializeCamera(): void {
        // Render default nodes.
        cc.Camera.main.cullingMask = 1;

        // Render adjacent pages.
        this.pageView.node.on('scrolling', () => {
            const currentPage = this.pageView.getCurrentPageIndex() as MenuScenePage;
            const page = this.pageView.getPages()[currentPage];
            const contentX = this.pageView.content.position.x * (-1 /** Negate */);
            if (Math.abs(contentX - page.position.x) < 1e-3) {
                // Ignore.
            } else if (contentX < page.position.x) {
                this.renderPages(currentPage - 1, currentPage);
            } else {
                this.renderPages(currentPage, currentPage + 1);
            }
        });

        // Reset culling when after scrolling has ended.
        this.pageView.node.on('scroll-ended', () => {
            const currentPage = this.pageView.getCurrentPageIndex() as MenuScenePage;
            if (currentPage === this.currentPage) {
                // Page changed by select bottom tab.
                // Manual render.
                this.renderPages(currentPage);
            } else {
                this.stopSoundFromHiddenTempleLayer(this.currentPage);
                // Page changed by scrolling.
                //this.currentPage = currentPage;
                this.changePage(currentPage, 0);
            }
        });
    }

    private async showOfferDialog(): Promise<boolean> {
        const offerManager = ee.ServiceLocator.resolve(DefaultOfferManager);

        if (offerManager.getConfig().currentLevel <= 4 || offerManager.getConfig().session == 2) return true;
        if (!offerManager.alreadyShowOnMenu) {
            await offerManager.showOfferDialogScript(this.dialogManager);
            offerManager.alreadyShowOnMenu = true;
        }
        return true;
    }

    /** Attempts to show rating dialog. */  // chuyen vao rating manager
    private async showRatingDialog(): Promise<boolean> {
        const ratingManager = ee.ServiceLocator.resolve(RatingManager);
        if (!ratingManager.canShowRateDialog()) {
            return true;
        }
        this._menuShowRating++;
        if (this._menuShowRating == 1) {
            await ratingManager.showRatingDialog(this.dialogManager);
        }
        return true;
    }

    /** Resizes all page views. */
    private resizePages(): void {
        if (!this.isInitialized) {
            this.needUpdatePageSize = true;
            return;
        }

        const size = this.pageView.node.getContentSize();
        this.pageView.getPages().forEach(item => {
            item.setContentSize(size);
        });

        // Update layout.
        this.pageView.content.getComponent(cc.Layout).updateLayout();

        const oldX = this.pageView.content.x;
        this.pageView.content.x = -size.width / 2;
        this.pageView.content.x = oldX;

        // Update mask.
        const mask = this.pageView.content.parent;
        mask.setContentSize(size);

        this.currentPage && this.selectPage(this.currentPage);
    }

    /** Only renders the specified pages. */
    private renderPages(from: number, _to?: number): void {
        const to = _to === undefined ? from : _to;
        let mask = 1;
        this.pageView.getPages().forEach((item, index) => {
            if (from <= index && index <= to) {
                const page = index as MenuScenePage;
                const pageMask = getPageCullingMask(page);
                if (page === MenuScenePage.Home) {
                    // Special case:
                    // Must be the same group index with stackedLayer in home layer.
                    const subLayerIndex = 10;
                    const subLayerMask = 1 << subLayerIndex;
                    if (this.homeLayer.canGoBack()) {
                        // Has on-top layers.
                        mask |= subLayerMask;
                    } else {
                        mask |= pageMask;
                    }
                    if (this.isHomeLayerPushing || this.isHomeLayerPopping) {
                        // Render both home layer and its sub-layers.
                        mask |= subLayerMask | pageMask;
                    }
                } else {
                    mask |= pageMask;
                }
                // FIXME: Lagging.
                item.opacity = 255;
            } else {
                // FIXME: Lagging.
                item.opacity = 0;
            }
        });
        cc.Camera.main.cullingMask = mask;
    }

    /** Immediately selects the specified page. */
    private selectPage(page: MenuScenePage): void {
        this.renderPages(page);
        this.pageView.scrollToPage(page, 0);
    }

    /** Scrolls to the specified page. */
    private scrollToPage(page: MenuScenePage, duration: number): void {
        // Update culling mask.
        let currentIndex = this.pageView.getCurrentPageIndex();
        let nextIndex = page as number;
        if (currentIndex > nextIndex) {
            // Ensure currentIndex < nextIndex.
            [currentIndex, nextIndex] = [nextIndex, currentIndex];
        }
        this.renderPages(currentIndex, nextIndex);
        this.pageView.scrollToPage(page, duration);
    }

    /** set all button enabled */
    private setBottomTopHudEnabled(enabled: boolean): void {
        this.bottomHud.buttonEnabled = enabled;
        this.topHud.getComponentsInChildren(cc.Button).forEach(btn => btn.interactable = enabled);
    }

    private onSceneDidEnter(): void {
        cc.log('onSceneDidEnter');
        this.didMenuLoaded = true;
        this.onGameShown();
    }

    /** Attempts to show pre-register rewards dialog. */
    private async showPreRegisterRewardsDialog(): Promise<boolean> {
        return true;

        /*
        const specialRewardManager = ee.ServiceLocator.resolve(SpecialRewardManager);
        if (!specialRewardManager.canGetPreRegisterRewards()) {
            return true;
        }
        specialRewardManager.claimRewards();
        const rewards = specialRewardManager.getPreRegisterRewards();
        const dialog = await SpecialRewardDialog.create();
        dialog.setRewards(rewards);
        dialog.show(this.dialogManager);
        return await new Promise<boolean>(resolve => {
            dialog.onDidHide(async () => {
                await EffectHelper.showFlyingRewardListInMenuScene(rewards, cc.Vec2.ZERO);
                resolve(true);
            });
        });
        */
    }

    private async checkUnlockEventMode(): Promise<boolean> {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const rewardManager = ee.ServiceLocator.resolve(RewardManager);
        const configManager = ee.ServiceLocator.resolve(ConfigManager);

        if (levelManager.isEventModeUnlocked) {
            // OK.
            return true;
        }
        //  Unlock if condition is met.
        let lastStoryLevel = levelManager.getUnlockedStoryLevelsCount();
        if (lastStoryLevel > 10 /** Clear the first area. */) {
            await new Promise<void>(async resolve =>  {
                levelManager.isEventModeUnlocked = true;
                ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.Finish);
                let rewardDialog = await RewardUtils.showRewardDialog([
                    rewardManager.createReward({
                        type: 'store',
                        subType: 'energy',
                        value: 10
                    })
                ], this.dialogManager, SceneName.DialogEventUnlock, () => {
                    resolve();
                });

                rewardDialog.setTitleKey(`text_game_congratulation`);
                rewardDialog.setSubTitleKey(`event_unlock`);
                // await new Promise<void>(resolve => rewardDialog.onDidHide(() => {
                //     resolve();
                // }));
            });
        }
        return true;
    }

    private async showCollectedEvent(): Promise<boolean> {
        const cardManager = ee.ServiceLocator.resolve(CardManager);
        await cardManager.showCollectedSetDialog(this.dialogManager);
        return true;
    }

    private async processExp(): Promise<boolean> {
        const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
        if(this.currentPage === MenuScenePage.Home) {
            await profileManager.receivePendingRewards();
            await this.checkToShowUnlockTeamDialog()
        }
        return true;
    }

    private async checkToShowUnlockTeamDialog(): Promise<void> {
        const profileManager = ee.ServiceLocator.resolve(UserProfileManager);
        const currentLevel = profileManager.getCurrentLevel();
        const myTeamManager = ServiceLocator.resolve(MyTeamManager);
        this._teamLayer && this.teamLayer.updateJoinTeamUI(currentLevel);
        let isHadShowUnlock = myTeamManager.isHadShowUnlock();
        if (currentLevel >= 2 && !isHadShowUnlock && !myTeamManager.isHadShowUnlockDialog()) {
            // mark unlock dialog showed
            myTeamManager.setHadShowUnlockDialog();

            const dialog = await UnlockTeamDialog.create();
            dialog.initialize(
                () => {
                    myTeamManager.setHadShowUnlock();
                    this.changePage(MenuScenePage.Team, 0.3).then();
                    this._teamLayer && this.teamLayer.OnActiveSearchTeamPressed();
                },
                () => {
                }
            )
            dialog.show(this.dialogManager);
            return await new Promise<void>(resolve => {
                dialog.onDidHide(async () => {
                    resolve();
                });
            });
        }
    }

    private async showDialogAtHomeLayer(): Promise<void> {
        if (this.homeLayer.canGoBack()) {
            return;
        }

        for (const promise of [
            () => this.showCancelDeletionDialog(),
            () => this.showPreRegisterRewardsDialog(),
            //() => this.processExp(),
            () => this.checkUnlockEventMode(),
            () => this.showRatingDialog(),
            () => this.showOfferDialog(),
            () => this.showWeeklyRewardDialog(),
        ]) {
            const shouldProcessNext = await promise();
            if (!shouldProcessNext) {
                break;
            }
        }
    }

    private async showDialogAtHomeLayerWithRewards(): Promise<void> {
        if (this.homeLayer.canGoBack()) {
            return;
        }
        for (const promise of [
            () => this.checkUnlockEventMode(),
            () => this.showWeeklyRewardDialog(),
        ]) {
            const shouldProcessNext = await promise();
            if (!shouldProcessNext) {
                break;
            }
        }
    }

    private async showDialogAtHomeLayerNotReward(): Promise<void> {
        if (this.homeLayer.canGoBack()) {
            return;
        }
        for (const promise of [
            () => this.showCancelDeletionDialog(),
            () => this.showPreRegisterRewardsDialog(),
            () => this.showRatingDialog(),
            () => this.showOfferDialog(),
        ]) {
            const shouldProcessNext = await promise();
            if (!shouldProcessNext) {
                break;
            }
        }
    }

    private async showCancelDeletionDialog(): Promise<boolean> {
        if (await ee.ServiceLocator.resolve(GameServerManager).deleteAccountController.isDeleting()) {
            const dialog = await CancelDeleteAccountDialog.create()
            dialog.show(ee.ServiceLocator.resolve(DialogManager))
            return await new Promise(resolve => dialog.onDidHide(() => {
                resolve(true);
            }));
        }
        return true

    }
    private async showWeeklyRewardDialog(): Promise<boolean> {
        const leaderboardManager = ee.ServiceLocator.resolve(LeaderboardManager)
        const rewards = await leaderboardManager.Reward.checkAndGetWeeklyReward();
        if (rewards != null) {
            const dialog = await LeaderboardRewardDialog.create();
            dialog.setReward(rewards);
            dialog.show(this.dialogManager);
            return await new Promise(resolve => dialog.onDidHide(() => {
                resolve(true);
            }));
        }
        return true
    }
}
