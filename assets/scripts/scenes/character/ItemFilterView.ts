import assert = require('assert');
import * as ee from '../../libraries/ee/index';
import { AudioManager } from '../../manager/audio/AudioManager';
import { SoundType } from '../../manager/audio/SoundType';
import { ItemCardFilterType } from '../../manager/card/CardManager';
import { CrashlyticManager, crashlytics } from '../../manager/gm_manager';
import { ItemFilterSelectionView } from './ItemFilterSelectionView';

const { ccclass, disallowMultiple, executeInEditMode, property } = cc._decorator;

export abstract class ItemFilterView extends cc.Component {
    public abstract expanded: boolean;
    public abstract filter: ItemCardFilterType;
    public abstract listener: Listener;

    public abstract setFilterToAll(): void;
}

interface Listener {
    filterChanged?(sender: ItemFilterView, filter: ItemCardFilterType): void;
}

@ccclass
@disallowMultiple
@executeInEditMode
class ItemFilterViewImpl extends ItemFilterView {
    @property(cc.Boolean)
    private _expanded = false;

    @property({ type: cc.Boolean })
    public get expanded(): boolean {
        return this._expanded;
    }

    public set expanded(value: boolean) {
        if (this._expanded !== value) {
            this._expanded = value;
            this.updateDisplay();
        }
    }

    @property({ type: cc.Enum(ItemCardFilterType) })
    private _type = ItemCardFilterType.AllState;

    @property({ type: cc.Enum(ItemCardFilterType) })
    public get type(): ItemCardFilterType {
        return this._type;
    }

    public set type(value: ItemCardFilterType) {
        if (this._type !== value) {
            this._type = value;
            this.listener.filterChanged && this.listener.filterChanged(this, this.rarity | value);
            this.updateDisplay();
        }
    }

    @property({ type: cc.Enum(ItemCardFilterType) })
    private _rarity = ItemCardFilterType.AllRarity;

    @property({ type: cc.Enum(ItemCardFilterType) })
    public get rarity(): ItemCardFilterType {
        return this._rarity;
    }

    public set rarity(value: ItemCardFilterType) {
        if (this._rarity !== value) {
            this._rarity = value;
            this.listener.filterChanged && this.listener.filterChanged(this, this.type | value);
            this.updateDisplay();
        }
    }

    public get filter(): ItemCardFilterType {
        return this.type | this.rarity;
    }

    public set filter(value: ItemCardFilterType) {
        this.type = value & ItemCardFilterType.AllState;
        this.rarity = value & ItemCardFilterType.AllRarity;
    }

    @ee.nest(ItemFilterSelectionView)
    private currentType: ItemFilterSelectionView | null = null;

    @ee.nest(ItemFilterSelectionView)
    private currentRarity: ItemFilterSelectionView | null = null;

    @property(cc.Node)
    private expandedLayer: cc.Node | null = null;

    @property({ visible: true, type: ee.ConditionTouchListener })
    private readonly _outsideListener: ee.ConditionTouchListener | null = null;

    public get outsideListener(): ee.ConditionTouchListener {
        if (this._outsideListener === null) {
            throw Error('Item not registered');
        }
        return this._outsideListener;
    }

    @ee.nest([ItemFilterSelectionView])
    private filterTypeViews: ItemFilterSelectionView[] = [];

    @ee.nest([ItemFilterSelectionView])
    private filterRarityViews: ItemFilterSelectionView[] = [];

    public listener: Listener = {};

    public onLoad(): void {
        assert(this.currentType !== null);
        assert(this.currentRarity !== null);
        assert(this.expandedLayer !== null);
        assert(this._outsideListener !== null);
        assert(this.filterTypeViews.length === 3);
        assert(this.filterRarityViews.length === 5);

        this.currentType!.selected = false;
        this.currentRarity!.selected = false;

        const types = [
            ItemCardFilterType.Collected,
            ItemCardFilterType.Uncollected,
            ItemCardFilterType.AllState,
        ];
        this.filterTypeViews.forEach((item, index) => {
            item.type = types[index];
            item.listener = {
                pressed: (sender) => {
                    this.type = sender.type;
                    this.updateDisplay();
                },
            };
        });

        this.outsideListener.setConditionCallback((listener, touch) => {
            if (!this.outsideListener.node._hitTest(touch.getLocation(), listener) && this.expanded) {
                this.collapse();
                return true;
            }
            return false;
        });

        const rarities = [
            ItemCardFilterType.Common,
            ItemCardFilterType.Rare,
            ItemCardFilterType.Epic,
            ItemCardFilterType.Legendary,
            ItemCardFilterType.AllRarity,
        ];
        this.filterRarityViews.forEach((item, index) => {
            item.type = rarities[index];
            item.listener = {
                pressed: (sender) => {
                    this.rarity = sender.type;
                    this.updateDisplay();
                },
            };
        });

        // remove touch on header
        this.currentType!.node.removeComponent(cc.Button);
        this.currentRarity!.node.removeComponent(cc.Button);

        this.updateDisplay();
    }

    /** Expands the filter list. */
    public expand(): void {
        this.expanded = true;
    }

    /** Collapses the filter list. */
    public collapse(): void {
        this.expanded = false;
    }

    /** Registered in editor. */
    @crashlytics
    private onExpandButtonPressed(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onExpandButtonPressed, `${this.uuid}`);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        if (this.expanded) {
            this.collapse();
        } else {
            this.expand();
        }
    }

    private updateDisplay(): void {
        this.expandedLayer!.active = this.expanded;
        this.filterTypeViews.forEach(item => item.selected = (this.type === item.type));
        this.filterRarityViews.forEach(item => item.selected = (this.rarity === item.type));
        this.currentType!.type = this.type;
        this.currentRarity!.type = this.rarity;

        const isSelectedAll = this.rarity === ItemCardFilterType.AllRarity && this.type === ItemCardFilterType.AllState;
        this.currentRarity!.node.parent.active = !isSelectedAll;
        const layouts = this.getComponentsInChildren(cc.Layout);
        layouts.forEach(layout => layout.updateLayout());
    }

    public setFilterToAll(): void{
        this.type = ItemCardFilterType.AllState;
        this.rarity = ItemCardFilterType.AllRarity;
        this.collapse();
    }
}