import { ItemCard } from '../../manager/card/CardManager';
import { TopHud } from '../common/TopHud';
import { MenuController } from '../MenuScene';
import { ItemCardView } from './ItemCardView';
import { ItemCategory } from './ItemCategoryView';

export interface ItemSelectionViewListener {
    selectionChanged?(sender: ItemSelectionView, item: ItemCard): void;
    categoryChanged?(sender: ItemSelectionView, category: ItemCategory): void;
    deckChanged?(sender: ItemSelectionView, deck: number): void;
}

export abstract class ItemSelectionView extends cc.Component {
    public abstract category: ItemCategory;
    public abstract deck: number;
    public abstract listener: ItemSelectionViewListener;
    public abstract getCardViewById(id: string): ItemCardView;
    public abstract setMenuController(constroller: MenuController): void;
    public abstract updateRendering(): void;
    public abstract selectCard(card: ItemCard): void;
    public abstract setTopHud(topHud: TopHud): void;
    public abstract updateCategory(category: ItemCategory): void;
    public abstract equipBestCard(): void;
    public abstract equipPotentialCard(): void;
    public abstract setNotificationCard(): void;
    public abstract setFilterToAll(): void;
}