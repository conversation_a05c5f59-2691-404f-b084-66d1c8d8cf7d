export enum ItemCategory {
    Loadout = 0,
    Character = 1,
    Car = 2,
    C<PERSON> = 3,
    Rope = 4,
    Pet = 5,
}

export enum ItemCategoryBackground {
    /** For loadout and deck 3. */
    ExpandTopLeft,

    /** For loadout and deck 1 or 2. */
    ExpandLeftCorner,

    /** For other categories. */
    ExpandLeft,
}

export abstract class ItemCategoryView extends cc.Component {
    /** Item's category. */
    public abstract category: ItemCategory;

    /** Whether the item is selected. */
    public abstract selected: boolean;

    public abstract backgroundType: ItemCategoryBackground;
    public abstract listener: ItemCategoryViewListener;

    public abstract setNotificationCard(): void;
}

export interface ItemCategoryViewListener {
    pressed?(sender: ItemCategoryView): void;
}