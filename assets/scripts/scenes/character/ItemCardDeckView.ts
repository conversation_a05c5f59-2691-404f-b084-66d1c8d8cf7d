import assert = require('assert');
import * as ee from '../../libraries/ee/index';
import { CrashlyticManager, crashlytics } from '../../manager/gm_manager';

const { ccclass, disallowMultiple, executeInEditMode, property } = cc._decorator;

export abstract class ItemCardDeckView extends cc.Component {
    /** Gets or sets whether the item is equipped. */
    public abstract equipped: boolean;

    /** Gets or sets whether the item deck. */
    public abstract deck: number;

    public abstract listener: Listener;
}

interface Listener {
    pressed?(sender: ItemCardDeckView): void;
}

@ccclass
@disallowMultiple
@executeInEditMode
class ItemCardDeckViewImpl extends ItemCardDeckView {
    @property(cc.Node)
    private unequippedLayer: cc.Node | null = null;

    @property(ee.LanguageComponent)
    private unequippedLabel: ee.LanguageComponent | null = null;

    @property(ee.LanguageComponent)
    private equippedLabel: ee.LanguageComponent | null = null;

    @property(cc.Boolean)
    private _equipped = false;

    @property({ type: cc.Boolean })
    public get equipped(): boolean {
        return this._equipped;
    }

    public set equipped(value: boolean) {
        if (this._equipped !== value) {
            this._equipped = value;
            this.displayDirty = true;
        }
    }

    @property(cc.Integer)
    private _deck = 0;

    @property({ type: cc.Integer })
    public get deck(): number {
        return this._deck;
    }

    public set deck(value: number) {
        if (this._deck !== value) {
            this._deck = value;
            this.displayDirty = true;
        }
    }

    private displayDirty = false;

    public listener: Listener = {};

    protected onLoad(): void {
        assert(this.unequippedLayer !== null);
        assert(this.unequippedLabel !== null);
        assert(this.equippedLabel !== null);
        this.updateDisplay();
    }

    protected update(delta: number): void {
        if (this.displayDirty) {
            this.displayDirty = false;
            this.updateDisplay();
        }
    }

    /** Registered in editor. */
    @crashlytics
    private onPressed(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onPressed, `${this.uuid}`);
        this.listener.pressed && this.listener.pressed(this);
    }

    private updateDisplay(): void {
        if (this.equipped) {
            this.equippedLabel!.node.active = true;
            this.equippedLabel!.key = `text_in_deck`;
            this.equippedLabel!.paramValues = [`${this.deck + 1}`];
            this.unequippedLayer!.active = false;
        } else {
            this.equippedLabel!.node.active = false;
            this.unequippedLayer!.active = true;
            this.unequippedLabel!.key = `text_use_in_deck`;
            this.unequippedLabel!.paramValues = [`${this.deck + 1}`];
        }
    }
}
