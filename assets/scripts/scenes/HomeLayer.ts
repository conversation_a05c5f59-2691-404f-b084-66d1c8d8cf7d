import assert = require('assert');
import {CantOpenEventModeDialog} from '../dialog/CantOpenEventModeDialog';
import * as gm from '../engine/gm_engine';
import * as ee from "../libraries/ee/index";
import {
    AudioManager,
    BoosterStorageManager,
    ConversionScene,
    CrashlyticManager,
    crashlytics,
    DataManager,
    FeatureManager,
    LevelManager,
    SceneName,
    ShopManager,
    SocialManager,
    SocialUser,
    SoundType,
    StoreItem,
    StoreManager,
    TrackingManager,
    UserProfileManager,
    VipManager,
} from '../manager/gm_manager';
import {QuestLayer} from '../ui/daily_quest/QuestLayer';
import {ParticleUtils} from "../utils/ParticleUtils";
import {BottomHud} from './common/BottomHud';
import {NotificationView} from './common/NotificationView';
import {TopHud, TopHudTemplate} from "./common/TopHud";
import {UserAvatarView} from './leaderboard/UserAvatarView';
import {MapLayer} from "./map/MapLayer";
import {MenuController} from "./MenuScene";
import {UserProfileLayer} from './profile/UserProfileLayer';
import {StackedLayerView} from './StackedLayerView';
import {OfferManager} from "../manager/offer/OfferManager";
import {OfferType} from "../dialog/OfferDialog";
import {HelpInfoDialog} from "../dialog/HelpInfoDialog";
import {EffectHelper} from "../utils/EffectHelper";
import {MapUtils} from "./map/MapUtils";
import {PlayButton} from "../ui/PlayButton";
import HiddenTempleLayer from "./hidden_temple/HiddenTempleLayer";
import HiddenTempleButton from "./hidden_temple/HiddenTempleButton";
import {HiddenTempleManager} from "./hidden_temple/HiddenTempleManager";
import {NotEnoughCurrencyDialog} from "../game/more_currency_dialog/NotEnoughCurrencyDialog";
import {MoreCurrencyUtils} from "../game/more_currency_dialog/MoreCurrencyUtils";
import {TimeUtils} from "../utils/TimeUtis";
import {PiggyBankManager} from "./PiggyBank/PiggyBankManager";
import PiggyBankButton from "./PiggyBank/PiggyBankButton";
import PlayPassLayer from "./play_pass/PlayPassLayer";
import PlayPassButton from "./play_pass/PlayPassButton";
import PlayPassInfoDialog from "./play_pass/PlayPassInfoDialog";
import {EventLayer, EventLayerInfo} from "./event/EventLayer";
import GuidePlayTutorial from "../manager/tutorial/GuidePlayTutorial";

const {ccclass, disallowMultiple, property} = cc._decorator;

interface Listener {
    /** Occurs when a sub-layer is about to be pushed. */
    layerWillPush?(sender: HomeLayer): void;

    /** Occurs when a sub-layer is pushed. */
    layerDidPush?(sender: HomeLayer): void;

    /** Occurs when the top sub-layer is about to be popped. */
    layerWillPop?(sender: HomeLayer): void;

    /** Occurs when the top sub-layer is popped. */
    layerDidPop?(sender: HomeLayer): void;

    /** Occurs when the offer button pressed. */
    goToOffer?(sender: HomeLayer): void;
}

export enum HomeLayerPage {
    Daily = 0,
    QuestAchievement = 1,
    HelpInfo = 2,
    Offer = 3,
    Event = 4,
}

export abstract class HomeLayer extends cc.Component {
    /** Gets the current top hub template. */
    public abstract template: TopHudTemplate;

    /** Gets or sets the top hud. */
    public abstract topHud: TopHud;

    /**
     * Gets or sets the bottom hud.
     * FIXME: remove (only used by DailyRewardCell).
     */
    public abstract bottomHud: BottomHud;

    /** Gets or sets the menu controller. */
    public abstract controller: MenuController;

    public abstract listener: Listener;

    /** Pushes the specified layer. */
    public abstract pushLayer(layer: cc.Node, options?: {
        slide?: number,
        fade?: number,
    }): Promise<void>;

    /** Nạp trước các layers mục đích khắc phục độ trễ mỗi lần load layer */
    public abstract preloadLayers(): void;

    /** Goes back to the last layer. */
    public abstract popLayer(duration?: number): Promise<boolean>;

    public abstract popAllLayers(duration?: number): Promise<void>;

    public abstract pushMapLayer(animated: boolean): Promise<MapLayer>;

    public abstract pushEventLayer(animated: boolean): Promise<EventLayer>;

    public abstract pushProfileLayer(): Promise<UserProfileLayer>;

    public abstract canGoBack(): boolean;

    /** Get top layer of stacks */
    public abstract getTopLayer(): cc.Node | null;

    /** Sets the notification for the specified sub page. */
    public abstract setNotificationByPage(value: number, page: HomeLayerPage): void;

    public abstract _isLayerStoryOpening: boolean;

    /** check to show play pass button  */
    public abstract checkShowPlayPassButton(selectLevel: boolean): void;

    /** check to show piggy Bank button  */
    public abstract checkShowPiggyBankButton(selectLevel: boolean): void;

    /** check to show hidden temple button */
    public abstract checkShowHiddenTempleButton(): void;

    /** block bottom menu */
    public abstract blockMenu(value: boolean): void;

    public abstract startTutorial(): void;
    public abstract stopTutorial(): void;
}

@ccclass
@disallowMultiple
class HomeLayerImpl extends HomeLayer {
    /**
     * Contains sub-layers.
     * Don't set type: StackedLayerView because it is an abstract class.
     */
    @property({type: cc.Node, visible: true})
    private readonly _stackedLayer: cc.Node | null = null;

    @property({type: cc.BlockInputEvents, visible: true})
    private readonly _blocker: cc.BlockInputEvents | null = null;

    @ee.nest(UserAvatarView)
    private readonly _avatarView: UserAvatarView | null = null;

    /** Displays the username. */
    @property({type: cc.Label, visible: true})
    private readonly _usernameLabel: cc.Label | null = null;

    /** Displays the user's medal. */
    @property({type: cc.Label, visible: true})
    private readonly _medalLabel: cc.Label | null = null;


    @property({type: cc.Node, visible: true})
    private readonly _shortcutLayer: cc.Node | null = null;

    @ee.nest(NotificationView)
    private readonly _questAchievementNotification: NotificationView | null = null;

    @ee.nest(NotificationView)
    private readonly _eventNotification: NotificationView | null = null;

    @property(cc.Node)
    private beginnerOffer: cc.Node = null;

    @property(cc.Node)
    private energyOffer: cc.Node = null;

    @property(cc.Node)
    private cardOffer: cc.Node = null;

    @property(cc.Node)
    private goldbarOffer: cc.Node = null;

    @property(cc.Node)
    private rubyOffer: cc.Node = null;

    @property(cc.Node)
    private boosterOffer: cc.Node = null;

    @property(cc.Node)
    private noAdsOffer: cc.Node = null;

    @property(GuidePlayTutorial)
    private tutorial: GuidePlayTutorial = null;

    @property(cc.Node)
    private tutorialPanel: cc.Node = null;

    public get questAchievementNotification(): NotificationView {
        return gm.retrieveNull(this._questAchievementNotification);
    }

    public get eventNotification(): NotificationView {
        return gm.retrieveNull(this._eventNotification);
    }

    @ee.nest(NotificationView)
    private readonly _dailyRewardNotification: NotificationView | null = null;

    public get dailyRewardNotification(): NotificationView {
        return gm.retrieveNull(this._dailyRewardNotification);
    }

    @ee.nest(NotificationView)
    private readonly _helpInfoNotification: NotificationView | null = null;

    public get helpInfoNotification(): NotificationView {
        return gm.retrieveNull(this._helpInfoNotification);
    }

    @property({type: cc.Prefab, visible: true})
    private readonly _mapLayerPrefab: cc.Prefab | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _eventLayerPrefab: cc.Prefab | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _bagLayerPrefab: cc.Prefab | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _dailyRewardLayerPrefab: cc.Prefab | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _questLayerPrefab: cc.Prefab | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _profileLayerPrefab: cc.Prefab | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _cantOpenEventModePrefab: cc.Prefab | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _selectLevelDialogPrefab: cc.Prefab | null = null;

    @property(PlayButton) private playButton: PlayButton = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _hiddenTemplePrefab: cc.Prefab | null = null;

    @property({type: HiddenTempleButton, visible: true})
    private readonly _hiddenTempleButton: HiddenTempleButton | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly _playPassLayerPrefab: cc.Prefab | null = null;

    @property({type: PlayPassButton, visible: true})
    private readonly _playPassButton: PlayPassButton | null = null;

    @property({type: PiggyBankButton, visible: true})
    private readonly _piggyBankButton: PiggyBankButton | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _waiting: cc.Node | null = null;

    private get waiting(): cc.Node {
        return gm.retrieveNull(this._waiting);
    }

    private get playPassLayerPrefab(): cc.Prefab {
        return gm.retrieveNull(this._playPassLayerPrefab);
    }

    private get hiddenTemplePrefab(): cc.Prefab {
        return gm.retrieveNull(this._hiddenTemplePrefab);
    }

    private get hiddenTempleButton(): HiddenTempleButton | null {
        return gm.retrieveNull(this._hiddenTempleButton);
    }

    private get piggyBankButton(): PiggyBankButton | null {
        return gm.retrieveNull(this._piggyBankButton);
    }

    private get playPassButton(): PlayPassButton | null {
        return gm.retrieveNull(this._playPassButton);
    }

    private get mapLayerPrefab(): cc.Prefab {
        return gm.retrieveNull(this._mapLayerPrefab);
    }

    private get eventLayerPrefab(): cc.Prefab {
        return gm.retrieveNull(this._eventLayerPrefab);
    }

    private get bagLayerPrefab(): cc.Prefab {
        return gm.retrieveNull(this._bagLayerPrefab);
    }

    private get dailyRewardLayerPrefab(): cc.Prefab {
        return gm.retrieveNull(this._dailyRewardLayerPrefab);
    }

    private get selectLevelDialogPrefab(): cc.Prefab {
        return gm.retrieveNull(this._selectLevelDialogPrefab);
    }

    private get questLayerPrefab(): cc.Prefab {
        return gm.retrieveNull(this._questLayerPrefab);
    }

    private get profileLayerPrefab(): cc.Prefab {
        return gm.retrieveNull(this._profileLayerPrefab);
    }

    private get cantOpenEventModePrefab(): cc.Prefab {
        return gm.retrieveNull(this._cantOpenEventModePrefab);
    }

    private get stackedLayer(): StackedLayerView {
        return gm.retrieveNull(this._stackedLayer).getComponent(StackedLayerView);
    }

    private get blocker(): cc.BlockInputEvents {
        return gm.retrieveNull(this._blocker);
    }

    private get avatarView(): UserAvatarView {
        return gm.retrieveNull(this._avatarView);
    }

    private get usernameLabel(): cc.Label {
        return gm.retrieveNull(this._usernameLabel);
    }

    private get medalLabel(): cc.Label {
        return gm.retrieveNull(this._medalLabel);
    }


    public get template(): TopHudTemplate {
        if (this.stackedLayer.getSize() === 0 && !this.isStackedLayerLocked) {
            return TopHudTemplate.Default;
        }
        return this._template;
    }

    public set template(template: TopHudTemplate) {
        this._template = template;
    }

    private get shortcutLayer(): cc.Node {
        return gm.retrieveNull(this._shortcutLayer);
    }

    public listener: Listener = {};

    private _topHud: TopHud | null = null;
    private _bottomHud: BottomHud | null = null;
    private _controller?: MenuController;
    private _template = TopHudTemplate.Default;
    public _isLayerStoryOpening = false;
    private _isHelpInfoDialogOpening = false;

    /** Whether the stacked layer is locked, i.e. playing transition animation. */
    private isStackedLayerLocked = false;

    /** Prevent pushing multiple sub-layers by multiple taps. */
    private isLayerLocked = false;

    public get topHud(): TopHud {
        return gm.retrieveNull(this._topHud);
    }

    public set topHud(value: TopHud) {
        this._topHud = value;
        this._topHud.setEventButtons(this.hiddenTempleButton.node, this.piggyBankButton.node, this.playPassButton.node);

        this.waiting.active = false;
        this.waiting.parent = this._topHud.node.parent;

    }

    public get bottomHud(): BottomHud {
        return gm.retrieveNull(this._bottomHud);
    }

    public set bottomHud(value: BottomHud) {
        this._bottomHud = value;
    }

    public get controller(): MenuController {
        return gm.retrieveUndefined(this._controller);
    }

    public set controller(value: MenuController) {
        this._controller = value;
    }

    private crashlyticManager: CrashlyticManager;
    private audioManager: AudioManager;
    private trackingManager: TrackingManager;
    private socialManager: SocialManager;
    private levelManager: LevelManager;
    private userProfileManager: UserProfileManager;
    private dialogManager: ee.DialogManager;
    private storeManager: StoreManager;
    private offerManager: OfferManager;
    private shopManager: ShopManager;

    private hiddenTempleManager: HiddenTempleManager;
    private piggyBankManager: PiggyBankManager;

    private mapLayer: MapLayer = null;
    private eventLayer: EventLayer = null;
    private bagLayer: cc.Node = null;
    private questLayer: QuestLayer = null;
    private dailyRewardLayer: cc.Node = null;
    private playPassLayer: PlayPassLayer = null;

    public onLoad(): void {
        assert(this._stackedLayer !== null);
        assert(this._avatarView !== null);
        assert(this._usernameLabel !== null);
        assert(this._medalLabel !== null);

        this.crashlyticManager = ee.ServiceLocator.resolve(CrashlyticManager);
        this.audioManager = ee.ServiceLocator.resolve(AudioManager);
        this.trackingManager = ee.ServiceLocator.resolve(TrackingManager)
        this.socialManager = ee.ServiceLocator.resolve(SocialManager);
        this.levelManager = ee.ServiceLocator.resolve(LevelManager);
        this.userProfileManager = ee.ServiceLocator.resolve(UserProfileManager)
        this.dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        this.storeManager = ee.ServiceLocator.resolve(StoreManager);
        this.offerManager = ee.ServiceLocator.resolve(OfferManager);
        this.shopManager = ee.ServiceLocator.resolve(ShopManager);

        this.hiddenTempleManager = ee.ServiceLocator.resolve(HiddenTempleManager);
        this.piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);


        ee.AsyncManager.getInstance().add(HomeLayerImpl.name, async () => {
            await this.initialize();
        });

        this.playButton.setPressedCallback(() => {
            this.onPlayButtonPressed();
        })

    }

    private async initialize(): Promise<void> {
        // Must not collide with group index in menu scene.
        this.stackedLayer.node.groupIndex = 10;

        // block menu cho đến khi menuScene đã show hết các dialogs
        this.blocker.enabled = true;

        this.updateLoginState(this.socialManager);

        this.medalLabel.string = this.userProfileManager.getMedalCount().toString();

        const dataManager = ee.ServiceLocator.resolve(DataManager);
        const featureManager = ee.ServiceLocator.resolve(FeatureManager);
        const isHelpRewarded = dataManager.getValue('is_help_rewarded', false);
        this.setNotificationByPage(isHelpRewarded ? 0 : 1, HomeLayerPage.HelpInfo);

        const boosterStorageManager = ee.ServiceLocator.resolve(BoosterStorageManager);
        boosterStorageManager.controller = ({
            needMoreCurrency: (type, storeItem, amount) => {
                if (storeItem === StoreItem.Gold) {
                    MoreCurrencyUtils.show({
                        dialogManager: this.dialogManager,
                        sceneName: SceneName.DialogStoryBooster,
                        storeItem: StoreItem.Gold,
                        needed: amount
                    }).then()

                } else {
                    if (featureManager.moreCurrencyIsPack) { // Done
                        MoreCurrencyUtils.show({
                            dialogManager: this.dialogManager,
                            sceneName: SceneName.DialogStoryBooster,
                            storeItem: StoreItem.Ruby,
                            needed: amount,
                            callback: (pressed) => {
                                if (pressed) {
                                    boosterStorageManager.afterBuyMoreCurrency &&
                                    boosterStorageManager.afterBuyMoreCurrency(type,
                                        boosterStorageManager.boosterBuyAmount, boosterStorageManager.boosterBuyCost);
                                    boosterStorageManager.afterBuyMoreCurrency = null;
                                }
                            }
                        }).then()
                    } else {
                        NotEnoughCurrencyDialog.create().then(dialog => {
                            dialog
                                .setInfo(StoreItem.Ruby, (isBuy) => {
                                    if (isBuy) {
                                        boosterStorageManager.afterBuyMoreCurrency &&
                                        boosterStorageManager.afterBuyMoreCurrency(type,
                                            boosterStorageManager.boosterBuyAmount, boosterStorageManager.boosterBuyCost);
                                        boosterStorageManager.afterBuyMoreCurrency = null;
                                    }
                                });
                            dialog.show(this.dialogManager);
                        })
                    }
                }
            },

            get dialogManager(): ee.DialogManager {
                return ee.ServiceLocator.resolve(ee.DialogManager);
            },
        });
    }

    public preloadLayers() {
        this.mapLayer = cc.instantiate(this.mapLayerPrefab).getComponent(MapLayer);
        this.mapLayer.controller = this.controller;
        this.mapLayer.dialogManager = this.dialogManager;
        this.mapLayer.topHud = this.topHud;
        this.mapLayer.bottomHud = this.bottomHud;
        this.pushLayer(this.mapLayer.node).then(()=>{
            this.popLayer(0).then();
        })

        this.bagLayer = cc.instantiate(this.bagLayerPrefab);
        this.questLayer = cc.instantiate(this.questLayerPrefab).getComponent(QuestLayer);
        this.dailyRewardLayer = cc.instantiate(this.dailyRewardLayerPrefab);

        this.eventLayer = cc.instantiate(this.eventLayerPrefab).getComponent(EventLayer);
        this.eventLayer.initialize();
        this.eventLayer.updateDisplay();
        let info: EventLayerInfo = {
            listener: {},
            menuController: this.controller,
            topHud: this.topHud,
            bottomHud: this.bottomHud
        }
        this.eventLayer.setInfo(info);

        this.playPassLayer = cc.instantiate(this.playPassLayerPrefab).getComponent(PlayPassLayer);
        this.playPassLayer.loadData();
        this.playPassLayer.initialize(
            () => {
                if (this.canGoBack()) {
                    this.popLayer(0).then();
                }
            },
            () => {
                this.playPassButton.showButton(null);
            });
        this.pushLayer(this.playPassLayer.node).then(()=>{
            this.popLayer(0).then();
        })
    }

    protected onEnable(): void {
        this.socialManager.addObserver(this.uuid, {
            loggedIn: sender => this.updateLoginState(sender),
            loggedOut: sender => this.updateLoginState(sender),
        });
        this.levelManager.addObserver(this.uuid + `isEventModeUnlockedChanged`, {
            isEventModeUnlockedChanged: (sender, isUnlocked) => {
            },
        });
        this.playButton.setNextLevel_v2(this.levelManager.getUnlockedStoryLevelsCount());
        this.levelManager.addObserver(this.uuid + `unlockedStoryLevel`, {
            unlockedStoryLevel: sender => {
                this.playButton.setNextLevel_v2(this.levelManager.getUnlockedStoryLevelsCount());
            }
        });

        this.hiddenTempleManager.setBeginEventTempleCallback(() => {
            this.beginEventTemple();
        })

        this.userProfileManager.addObserver(this.uuid, {
            socialUserChange: (sender: UserProfileManager, newProfile: SocialUser) => {
                this.usernameLabel.string = this.userProfileManager.socialUser.name
            }
        })

        this.schedule(this.setTimeOffer, 1, cc.macro.REPEAT_FOREVER);

    }

    private setTimeOffer() {
        const offerTypes = [
            {type: OfferType.Beginner_Offer, node: this.beginnerOffer},
            {type: OfferType.Energy_Offer, node: this.energyOffer},
            // {type: OfferType.Card_Offer, node: this.cardOffer},
            {type: OfferType.Ruby_Offer, node: this.rubyOffer},
            {type: OfferType.Booster_Offer, node: this.boosterOffer},
            // {type: OfferType.GoldBar_Offer, node: this.goldbarOffer},
            {type: OfferType.VIP_Offer, node: this.noAdsOffer}
        ];

        offerTypes.forEach(({type, node}) => {
            const remainingTime = this.shopManager.getOfferPacks()[type].getRemainingTime();
            const label = node.getChildByName("time").getComponentInChildren(cc.Label);
            label.string = TimeUtils.getTimeString(remainingTime);
        });

        this.checkBeginnerOfferAvailable();

    }

    protected onDisable(): void {
        this.socialManager.removeObserver(this.uuid);
        this.levelManager.removeObserver(this.uuid + `isEventModeUnlockedChanged`);
        this.levelManager.removeObserver(this.uuid + `unlockedStoryLevel`);
        this.hiddenTempleManager.setBeginEventTempleCallback(null);
        this.userProfileManager.removeObserver(this.uuid)
        this.unschedule(this.setTimeOffer)
    }

    private updateLoginState(manager: SocialManager): void {
        // if (manager.isLoggedIn) {
        //     const user = manager.user;
        //     let name = user.name;
        //     if (name.length > 15) {
        //         name = `${name.substr(0, 12)}...`;
        //     }
        //     this.usernameLabel.string = name;
        //     this.avatarView.loadItem(user.picture);
        // } else {
        //     //this.usernameLabel.string = 'You';
        //     //this.avatarView.loadItem(UserAvatarView.DEFAULT_AVATAR_URL);
        //     this.usernameLabel.string = this.userProfileManager).socialUser.name;
        // }

        // Hiển thị tên lấy từ profile (sẽ xem xét lại khi triển khai chức năng leaderboard)
        this.usernameLabel.string = this.userProfileManager.socialUser.name;
    }

    public canGoBack(): boolean {
        return this.stackedLayer.getSize() > 0;
    }

    public async pushLayer(layer: cc.Node, options?: {
        slide?: number,
        fade?: number,
    }): Promise<void> {
        this.blocker.enabled = true;

        if (this.getTopLayer() !== null) {
            // Only one active sub-layer at a time.
            this.stackedLayer.popLayer({
                slide: 0,
                fade: 0.5,
            });
        }
        this.isStackedLayerLocked = true;
        this.listener.layerWillPush && this.listener.layerWillPush(this);
        await this.stackedLayer.pushLayer(layer, options);

        this.isStackedLayerLocked = false;
        this.listener.layerDidPush && this.listener.layerDidPush(this);

        this.blocker.enabled = false;
    }

    public getTopLayer(): cc.Node | null {
        return this.stackedLayer.getTopLayer();
    }

    public async popLayer(duration: number): Promise<boolean> {
        const layer = this.getTopLayer();
        if (layer === null) {
            return false;
        }

        // Fix lỗi ui: particle xuất hiện trên màn hình phụ khi đóng do
        // particle chest trong HiddenTempleLayer không hủy ngay khi layer destroy =>
        // hủy trực tiếp trước khi đóng layer
        const hiddenTempleLayer = layer.getComponent(HiddenTempleLayer)
        if (hiddenTempleLayer) {
            hiddenTempleLayer.removeParticleChest();
        }

        this.blocker.enabled = true;

        this.isStackedLayerLocked = true;
        this.listener.layerWillPop && this.listener.layerWillPop(this);
        await this.stackedLayer.popLayer({fade: duration});
        this.isStackedLayerLocked = false;
        this.listener.layerDidPop && this.listener.layerDidPop(this);

        this.blocker.enabled = false;
        return true;
    }

    public async popAllLayers(duration?: number): Promise<void> {
        let emptied = this.getTopLayer() === null;
        while (!emptied) {
            const popped = await this.popLayer(duration);
            emptied = popped === false;
        }
    }

    private async pushWithLock<T>(pusher: () => Promise<T>): Promise<T> {
        if (this.isLayerLocked) {
            return await Promise.reject(`Layer is locked.`);
        }
        this.isLayerLocked = true;
        try {
            const item = await pusher();
            return item;
        } finally {
            this.isLayerLocked = false;
        }
    }

    public async pushMapLayer(animated: boolean): Promise<MapLayer> {
        this.trackingManager.trackEventOpen(SceneName.SceneStoryMap);
        return this.pushWithLock(async () => {
            const asyncManager = new ee.AsyncManager();
            this.mapLayer.init();
            this.template = TopHudTemplate.Story;
            this.pushLayer(this.mapLayer.node, animated ? {fade: 0.5} : undefined).then();
            await asyncManager.flushAll({size: 5, delay: 20});
            asyncManager.destroy();
            return this.mapLayer;
        });
    }

    public async pushEventLayer(animated: boolean): Promise<EventLayer> {
        this.trackingManager.trackEventOpen(SceneName.SceneMenuEvent);
        return this.pushWithLock(async () => {
            const asyncManager = new ee.AsyncManager();
            this.template = TopHudTemplate.Story;
            this.pushLayer(this.eventLayer.node, animated ? {slide: 0.25} : undefined).then();
            await asyncManager.flushAll({size: 5, delay: 20});
            asyncManager.destroy();
            return this.eventLayer;
        });
    }

    public async pushProfileLayer(): Promise<UserProfileLayer> {
        return this.pushWithLock(async () => {
            const name = '___user_profile_layer';
            const layer = this.getTopLayer();
            if (layer && layer.name === name) {
                throw `user profile layer is present.`;
            }

            const item = cc.instantiate(this.profileLayerPrefab).getComponent(UserProfileLayer);
            item.node.name = name;
            this.template = TopHudTemplate.Default;
            await this.pushLayer(item.node, {slide: 0.25});

            return item;
        });
    }

    public async pushHiddenTemple(animated: boolean): Promise<HiddenTempleLayer> {
        return this.pushWithLock(async () => {
            const asyncManager = new ee.AsyncManager();
            const hiddenTemple = cc.instantiate(this.hiddenTemplePrefab).getComponent(HiddenTempleLayer);
            hiddenTemple.initialize(() => {
                if (this.canGoBack()) {
                    this.popLayer(0).then();
                }
            });
            hiddenTemple.initCurrent();
            this.pushLayer(hiddenTemple.node, {slide: 0.25}).then(() => {
                // Chưa hiểu nguyên nhân trên mobile chest trong map mở đầu tiên bị mất particle
                // thêm particle cho chest trong map mở đầu tiên sau khi pushLayer
                hiddenTemple.addParticleChest();
            });
            await asyncManager.flushAll({size: 5, delay: 20});
            asyncManager.destroy();
            return hiddenTemple;
        });
    }

    public checkShowPlayPassButton(selectLevel: boolean = false): void {
        if (this.piggyBankManager.isShowDialog || selectLevel) {
            this.playPassButton.showButton(null);
            return;
        }
        const callback = {
            onShowInfo: () => {
                this.audioManager.playSound(SoundType.ButtonPress);
                PlayPassInfoDialog.create().then((dialog) => {
                    dialog.show(this.dialogManager);
                });
            },
            onShowPlayPass: () => {
                this.audioManager.playSound(SoundType.ButtonPress);
                this.OnPlayPassButtonPressed();
            },
        }

        this.playPassButton.showButton(callback);
    }

    public checkShowPiggyBankButton(selectLevel: boolean = false) {
        if (selectLevel) {
            this.piggyBankButton.showButton(null);
            return;
        }
        this.piggyBankButton.showButton(this._topHud);
    }

    public checkShowHiddenTempleButton(): void {
        this.hiddenTempleButton.showButton();
    }

    public blockMenu(value: boolean) {
        this.blocker.enabled = value;
    }

    /** Registered in editor. */
    @crashlytics
    private OnPlayPassButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.showWaiting().then(() => {
            this.audioManager.playSound(SoundType.ButtonPress);
            this.crashlyticManager.logFunction(this, this.OnPlayPassButtonPressed, `${this.uuid}`);
            this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_play_pass');
            this.trackingManager.trackConversionScene(ConversionScene.ScenePlayPass);
            this.trackingManager.trackConversionClickPlayPass();
            this.playPassLayer.scrollToLevelPass();
            this.pushWithLock(async () => {
                const asyncManager = new ee.AsyncManager();
                this.pushLayer(this.playPassLayer.node, {slide: 0.25}).then(() => {
                    this.playPassLayer.scrollToLevelPass();
                });
                await asyncManager.flushAll({size: 5, delay: 20});
                asyncManager.destroy();
            }).then(() => {
                this.hideWaiting();
            });
        });
    }

    /** Registered in editor. */
    @crashlytics
    private OnPiggyBankButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.audioManager.playSound(SoundType.ButtonPress);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_piggy_bank');
        this.piggyBankManager.showUnlockDialog(this._topHud, this.dialogManager).then(() => {
            this.checkShowPiggyBankButton();
            this.checkShowPlayPassButton();
        });
    }

    /** Registered in editor. */
    @crashlytics
    private OnHiddenTempleButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.showWaiting().then(() => {
            // disable templeButton để tắt schedule check redDot và endEvent
            const templeButtonActive = this.hiddenTempleButton.node.active;
            this.hiddenTempleButton.node.active = false;

            this.crashlyticManager.log("onHiddenTempleButtonPressed");
            this.audioManager.playSound(SoundType.ButtonPress);
            this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_hidden_temple');
            this.trackingManager.trackConversionScene(ConversionScene.SceneHiddenTemple);
            this.trackingManager.trackConversionClickHiddenTemple();
            this.pushHiddenTemple(true).then(async item => {
                item.setOnDestroyCallback(() => {
                    // trả lại trạng thái ban đầu của temple button khi đóng layer
                    this.hiddenTempleButton.node.active = templeButtonActive;
                })
                this.hideWaiting();
            });
        });
    }

    /** Registered in editor. */
    @crashlytics
    private OnBagButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.showWaiting().then(() => {
            this.crashlyticManager.logFunction(this, this.OnBagButtonPressed, `${this.uuid}`);
            this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_bag');
            this.audioManager.playSound(SoundType.ButtonPress);
            this.pushWithLock(async () => {
                const asyncManager = new ee.AsyncManager();
                this.pushLayer(this.bagLayer, {slide: 0.25}).then();
                await asyncManager.flushAll({size: 5, delay: 20});
                asyncManager.destroy();
            }).then(() => {
                this.hideWaiting();
            });
        });
    }

    /** Registered in editor. */
    private onStoryButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }

        this.showWaiting().then(() => {
            this.crashlyticManager.log("onStoryButtonPressed");
            this.audioManager.playSound(SoundType.ButtonPress);
            this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_story');
            this.trackingManager.trackConversionScene(ConversionScene.SceneMap);
            this.trackingManager.trackConversionClickStory();
            this._isLayerStoryOpening = true;
            this.pushMapLayer(true).then(async item => {
                // Switch to the last unlocked area.
                const lastUnlockedArea = this.levelManager.getLastUnlockedArea();
                await item.scrollToLevel({
                    area: lastUnlockedArea,
                });
                await item.handleDialogs({
                    area: lastUnlockedArea,
                });
                this.hideWaiting(0.6);
            });
        })
    }

    /** Registered in editor. */
    private onEventButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.showWaiting().then((waitingTimeout) => {
            this.crashlyticManager.log("onEventButtonPressed");
            this.audioManager.playSound(SoundType.ButtonPress);
            this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_event');
            this.trackingManager.trackConversionScene(ConversionScene.SceneEvent);
            this.trackingManager.trackConversionClickTeam();
            this.pushEventLayer(true).then(async layer => {
                this.hideWaiting();
            });
        })
    }

    private async showWaiting() {
        this.waiting.active = true;
        await TimeUtils.sleep(100);
    }

    private hideWaiting(duration: number = 0.35) {
        this.scheduleOnce(()=>{
            this.waiting.active = false;
        }, duration);
    }

    @crashlytics
    private onEventGrayButtonPressed(): void {

        if (!this.levelManager.isEventModeUnlocked) {
            //show dialog reach to lv 4
            const cantOpenEventDialog = cc.instantiate(this.cantOpenEventModePrefab).getComponent(CantOpenEventModeDialog);
            cantOpenEventDialog.show(this.dialogManager);
        }
    }

    /** Registered in editor. */
    @crashlytics
    private onDailyRewardButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.crashlyticManager.logFunction(this, this.onDailyRewardButtonPressed, `${this.uuid}`);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_daily_reward');
        this.audioManager.playSound(SoundType.ButtonPress);

        this.pushWithLock(async () => {
            //const layer = cc.instantiate(this.dailyRewardLayerPrefab);

            const asyncManager = new ee.AsyncManager();
            this.pushLayer(this.dailyRewardLayer, {slide: 0.25}).then();
            await asyncManager.flushAll({size: 5, delay: 20});
            asyncManager.destroy();
        });
    }

    private onPlayButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.audioManager.playSound(SoundType.ButtonPress);
        const level = this.levelManager.getUnlockedStoryLevelsCount();
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, `btn_play_level_${level}`)

        //stop tutorial
        this.stopTutorial();

        let lastUnlockedStoryArea = this.levelManager.getLastUnlockedArea();
        let lastUnlockedStoryLevel = this.levelManager.getStoryLastUnlockedLevel(lastUnlockedStoryArea);

        this.showSelectLevelDialog(lastUnlockedStoryArea, lastUnlockedStoryLevel).then();
    }

    public async showSelectLevelDialog(area: number, level: number): Promise<void> {
        let areaInfo = this.levelManager.getStoryAreaInfo(area);
        let levelInfo = areaInfo.levels[level];
        await MapUtils.showSelectLevelDialog(
            this.selectLevelDialogPrefab,
            this.dialogManager,
            this.controller,
            areaInfo, levelInfo,
            true);
    }

    /** Registered in editor. */
    @crashlytics
    private onBeginnerOfferButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.crashlyticManager.logFunction(this, this.onBeginnerOfferButtonPressed, `${this.uuid}`);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_beginner_offer');
        this.audioManager.playSound(SoundType.ButtonPress);

        this.offerManager.showOfferDialogByType(this.dialogManager, OfferType.Beginner_Offer).then(() => {
            this.checkShowPiggyBankButton();
            this.checkShowPlayPassButton();

        });
    }

    /** Registered in editor. */
    @crashlytics
    private onCardOfferButtonPressed(): void {
        // if (this.isStackedLayerLocked) {
        //     return;
        // }
        // this.crashlyticManager.logFunction(this, this.onCardOfferButtonPressed, `${this.uuid}`);
        // this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_card_offer');
        // this.audioManager.playSound(SoundType.ButtonPress);
        //
        // this.offerManager.showOfferDialogByType(this.dialogManager, OfferType.Card_Offer).then(() => {
        //     this.checkShowPiggyBankButton();
        //     this.checkShowPlayPassButton();
        //
        // });
    }

    /** Registered in editor. */
    @crashlytics
    private onRubyOfferButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.crashlyticManager.logFunction(this, this.onRubyOfferButtonPressed, `${this.uuid}`);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_ruby_offer');
        this.audioManager.playSound(SoundType.ButtonPress);

        this.offerManager.showOfferDialogByType(this.dialogManager, OfferType.Ruby_Offer).then(() => {
            this.checkShowPiggyBankButton();
            this.checkShowPlayPassButton();

        });
    }

    /** Registered in editor. */
    @crashlytics
    private onBoosterOfferButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.crashlyticManager.logFunction(this, this.onBoosterOfferButtonPressed, `${this.uuid}`);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_booster_offer');
        this.audioManager.playSound(SoundType.ButtonPress);

        this.offerManager.showOfferDialogByType(this.dialogManager, OfferType.Booster_Offer).then(() => {
            this.checkShowPiggyBankButton();
            this.checkShowPlayPassButton();

        });
    }

    /** Registered in editor. */
    @crashlytics
    private onNoAdsOfferButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.crashlyticManager.logFunction(this, this.onNoAdsOfferButtonPressed, `${this.uuid}`);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_no_ads_offer');
        this.audioManager.playSound(SoundType.ButtonPress);

        this.offerManager.showOfferDialogByType(this.dialogManager, OfferType.VIP_Offer).then(() => {
            this.checkShowPiggyBankButton();
            this.checkShowPlayPassButton();

        });
    }

    /** Registered in editor. */
    @crashlytics
    private onGoldbarOfferButtonPressed(): void {
        // if (this.isStackedLayerLocked) {
        //     return;
        // }
        // this.crashlyticManager.logFunction(this, this.onGoldbarOfferButtonPressed, `${this.uuid}`);
        // this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_goldbar_offer');
        // this.audioManager.playSound(SoundType.ButtonPress);
        //
        // this.offerManager.showOfferDialogByType(this.dialogManager, OfferType.GoldBar_Offer).then(() => {
        //     this.checkShowPiggyBankButton();
        //     this.checkShowPlayPassButton();
        //
        // });
    }

    @crashlytics
    private onEnergyOfferButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.crashlyticManager.logFunction(this, this.onEnergyOfferButtonPressed, `${this.uuid}`);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_energy_offer');
        this.audioManager.playSound(SoundType.ButtonPress);

        this.offerManager.showOfferDialogByType(this.dialogManager, OfferType.Energy_Offer).then(() => {
            this.checkShowPiggyBankButton();
            this.checkShowPlayPassButton();
        });
    }

    @crashlytics
    public checkBeginnerOfferAvailable() {
        const shopManager = ee.ServiceLocator.resolve(ShopManager);
        const vipManager = ee.ServiceLocator.resolve(VipManager);
        const offerPacks = shopManager.getOfferPacks();
        const beginnerOfferBought = offerPacks[OfferType.Beginner_Offer].isBought;
        const vipLevel = vipManager.getCurrentVipLevel() + 1;

        if (!beginnerOfferBought) {
            this.beginnerOffer.active = true;
            this.noAdsOffer.active = false;
            this.cardOffer.active = false;
        } else {
            // Beginner offer was bought
            this.beginnerOffer.active = false;

            if (vipLevel < 4) {
                // VIP level <= 4: show VIP offer, hide Card
                this.noAdsOffer.active = true;
                this.cardOffer.active = false;
            } else {
                // VIP level > 4: show Card offer, hide VIP
                this.noAdsOffer.active = false;
            }
        }

    }

    @crashlytics
    private onHelpInfoButtonPressed(): void {
        this.audioManager.playSound(SoundType.ButtonPress);
        if (this._isHelpInfoDialogOpening) return;
        this._isHelpInfoDialogOpening = true;
        const goldReward: number = 10000;
        let helpInfoDialog = HelpInfoDialog.create().then(dialog => {
            dialog.setReward(goldReward)
                .setClaimedCallBack(async () => {
                    await EffectHelper.showFlyingGoldAnimation(this.node, this._topHud.getNodeByItem(StoreItem.Gold), goldReward);
                    this.setNotificationByPage(0, HomeLayerPage.HelpInfo);
                    this.storeManager.addItemBalance(StoreItem.Gold, goldReward);
                })
                .onDidHide(() => this._isHelpInfoDialogOpening = false)
                .show(this.dialogManager);
        })
        this.crashlyticManager.logFunction(this, this.onHelpInfoButtonPressed, `${this.uuid}`);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_help_info');
    }


    /** Achievements and daily quests. */
    @crashlytics
    private onQuestButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.showWaiting().then(() => {
            this.crashlyticManager.logFunction(this, this.onQuestButtonPressed, `${this.uuid}`);
            this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_daily_quest');
            this.audioManager.playSound(SoundType.ButtonPress);
            this.pushWithLock(async () => {
                const asyncManager = new ee.AsyncManager();
                this.pushLayer(this.questLayer.node, {slide: 0.25}).then();
                await asyncManager.flushAll({size: 5, delay: 20});
                asyncManager.destroy();

                this.questLayer.topHud = this.topHud;
                this.questLayer.bottomHud = this.bottomHud;
            }).then(() => {
                this.hideWaiting();
            });
        });
    }

    /** Not used. */
    @crashlytics
    private onMoreGamesButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.crashlyticManager.logFunction(this, this.onMoreGamesButtonPressed, `${this.uuid}`);
        this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_more_games');
        cc.sys.openURL("https://play.google.com/store/apps/dev?id=7830868662152106484");
    }

    /** Registered in editor. */
    @crashlytics
    private onProfileButtonPressed(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.showWaiting().then(() => {
            this.crashlyticManager.logFunction(this, this.onProfileButtonPressed, `${this.uuid}`);
            this.trackingManager.trackEventClick(SceneName.SceneMenuHome, 'btn_user_profile');
            this.audioManager.playSound(SoundType.ButtonPress);
            this.pushProfileLayer().then(() => {
                this.hideWaiting();
            });
        });
    }

    @crashlytics
    public setNotificationByPage(value: number, page: HomeLayerPage): void {
        // Disabled: Call multiple times..
        // this.crashlyticManager.logFunction(this, this.setNotificationByPage, `${this.uuid}`);

        const dict: { [key: string]: NotificationView } = {
            [HomeLayerPage.Daily            /**/]: this.dailyRewardNotification,
            [HomeLayerPage.QuestAchievement /**/]: this.questAchievementNotification,
            [HomeLayerPage.HelpInfo]: this.helpInfoNotification,
            //[HomeLayerPage.Offer            /**/]: this.offerNotification,
            [HomeLayerPage.Event]: this.eventNotification,
        };
        dict[page].node.parent.active = value > 0;
        dict[page].setValue(value);
    }

    public createGoldGlareParticle(): cc.Node {
        return ParticleUtils.createParticle({
            file: 'particles/menu/sparkling_gold',
        });
    }

    private beginEventTemple(): void {
        this.hiddenTempleButton.node.active = true;
        this.hiddenTempleButton.showButton();
    }

    public startTutorial() {
        this.tutorialPanel.parent = this.node.parent.parent.parent.parent;
        this.tutorial.node.active = true;
    }

    public stopTutorial() {
        this.tutorial.node.active = false;
    }
}