import assert = require('assert');
import * as ee from "./../libraries/ee/index";
import { AnimatingMap } from './map_scene/AnimatingMap';

const { ccclass, property, executeInEditMode } = cc._decorator;

@ccclass
@executeInEditMode
export class EventMap extends cc.Component {
    @ee.nest([AnimatingMap])
    private areas: AnimatingMap[] = [];

    @property(cc.Node)
    private buttonHolder: cc.Node | null = null;

    private btnList: cc.Node[] = [];
    private unlockedArea: number = 3;

    public setUnlockedArea(area: number): this {
        this.unlockedArea = area;
        return this;
    }

    public onLoad(): void {
        this.btnList.push(... this.buttonHolder!.children);
        for (let i = 0; i < 4; i++) {
            this.areas[i].setArea(i + 2).stopAnimation();
            this.areas[i].node.active = false;
        }
        this.showArea(this.unlockedArea);
    }

    private showArea(areaIndex: number): void {
        for (let i = 0; i <= areaIndex; i++) {
            this.areas[i].node.active = true;
        }
        for (let i = 0; i < this.btnList.length; i++) {
            const item = this.btnList[i];
            item.active = i < 2 * areaIndex + 2;
        }
        this.areas[areaIndex].playAnimation().then(() => {
            this.btnList[(areaIndex + 1) * 2].active = true;
            this.btnList[(areaIndex + 1) * 2 + 1].active = true;
        });
    }
}
