import assert = require('assert');
import { ResourcesUtils } from "../../utils/ResourcesUtils";
import { League } from './League';

const { ccclass, disallowMultiple, property } = cc._decorator;

enum LeagueDirection {
    Vertical = 0,
    Horizontal = 1,
}

export abstract class LeagueText extends cc.Component {
    /** Gets or sets the displaying league. */
    public abstract league: League;
}

@ccclass
@disallowMultiple
class LeagueTextImpl extends LeagueText {
    @property({ type: cc.Sprite, visible: true })
    private _leagueText: cc.Sprite | null = null;

    private get leagueText(): cc.Sprite {
        if (this._leagueText === null) {
            throw Error('Item not registered.');
        }
        return this._leagueText;
    }

    @property({ type: cc.Enum(LeagueDirection) })
    public leagueDirection = LeagueDirection.Vertical;

    private _league = League.Bronze;

    public get league(): League {
        return this._league;
    }

    public set league(value: League) {
        if (this._league !== value) {
            this._league = value;
            this.updateDisplay();
        }
    }

    protected onLoad(): void {
        assert(this._leagueText);
        this.updateDisplay();
    }

    public setLeague(league: League): void {
        this._league = league;
        this.updateDisplay();
    }

    private updateDisplay(): void {
        // TODO: using frame from auto atlas
        // const path = 'packed/sheet-pvp';
        // const leagueDict: { [key: number]: string } = {
        //     [League.Bronze]: /**  */ "bronze",
        //     [League.Silver]: /**  */ "silver",
        //     [League.Gold]: /**    */ "gold",
        //     [League.Diamond]: /** */ "diamond",
        //     [League.Master]: /**  */ "master",
        // };
        // const league = leagueDict[this._league];
        // const leagueDirectionDict: { [key: number]: string } = {
        //     [LeagueDirection.Vertical]: "",
        //     [LeagueDirection.Horizontal]: "_line",
        // };
        // const leagueDirection = leagueDirectionDict[this.leagueDirection];
        // ResourcesUtils.loadResources(path, cc.SpriteAtlas).then(res => {
        //     if (!this.isValid) {
        //         return;
        //     }
        //     const spriteFrame = res.getSpriteFrame(`${league}_league${leagueDirection}`);
        //     this.leagueText.spriteFrame = spriteFrame;
        // });
    }
}
