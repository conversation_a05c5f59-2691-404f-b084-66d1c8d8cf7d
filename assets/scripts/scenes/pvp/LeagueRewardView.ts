import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {
    AudioManager,
    SoundType,
} from '../../manager/gm_manager';

const { ccclass, disallowMultiple, property } = cc._decorator;

interface Listener {
    claimReward?(): void;
}

export abstract class LeagueRewardView extends cc.Component {
    public abstract listener: Listener;

    /** Gets or sets the amount of rewarded cards. */
    public abstract amount: number;
}

/** Used in editor. */
@ccclass
@disallowMultiple
class LeagueRewardViewImpl extends LeagueRewardView {
    @property({ type: cc.Label, visible: true })
    private _amountLabel: cc.Label | null = null;

    private get amountLabel(): cc.Label {
        return gm.retrieveNull(this._amountLabel);
    }

    public listener: Listener = {};

    private _amount = 0;

    public get amount(): number {
        return this._amount;
    }

    public set amount(value: number) {
        if (this._amount !== value) {
            this._amount = value;
            this.updateDisplay();
        }
    }

    protected onLoad(): void {
        assert(this._amountLabel !== null);
        this.updateDisplay();
    }

    private updateDisplay(): void {
        this.amountLabel.string = `${this._amount}`;
    }

    /** Used in editor. */
    private claimReward(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        this.listener.claimReward && this.listener.claimReward();
    }
}
