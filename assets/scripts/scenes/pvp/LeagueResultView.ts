import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import * as ee from "../../libraries/ee/index";
import { League, LeagueResult } from './League';
import { LeagueIcon } from './LeagueIcon';
import { LeagueRewardView } from './LeagueRewardView';
import { LeagueText } from './LeagueText';

const { ccclass, disallowMultiple, property } = cc._decorator;

interface Listener {
    claimReward?(): void;
}

export abstract class LeagueResultView extends cc.Component {
    public abstract listener: Listener;

    /** Gets or sets the current league. */
    public abstract league: League;

    /** Gets or sets th league state. */
    public abstract leagueResult: LeagueResult;

    /** Gets or sets the reward amount. */
    public abstract amount: number;
}

@ccclass
@disallowMultiple
class LeagueResultViewImpl extends LeagueResultView {
    @property({ type: ee.LanguageComponent, visible: true })
    private _titleLabel: ee.LanguageComponent | null = null;

    private get titleLabel(): ee.LanguageComponent {
        return gm.retrieveNull(this._titleLabel);
    }

    @ee.nest(LeagueIcon)
    private _leagueIcon: LeagueIcon | null = null;

    private get leagueIcon(): LeagueIcon {
        return gm.retrieveNull(this._leagueIcon);
    }

    @ee.nest(LeagueText)
    private _currentLeagueText: LeagueText | null = null;

    private get currentLeagueText(): LeagueText {
        return gm.retrieveNull(this._currentLeagueText);
    }

    @ee.nest(LeagueText)
    private _nextLeagueText: LeagueText | null = null;

    private get nextLeagueText(): LeagueText {
        return gm.retrieveNull(this._nextLeagueText);
    }

    /** Can be null. */
    @ee.nest(LeagueRewardView)
    private rewardView: LeagueRewardView | null = null;

    @property({ type: ee.LanguageComponent, visible: true })
    private _descriptionLabel: ee.LanguageComponent | null = null;

    private get descriptionLabel(): ee.LanguageComponent {
        return gm.retrieveNull(this._descriptionLabel);
    }

    private _league = League.Bronze;
    private _leagueResult = LeagueResult.Stay;

    private dirty: boolean = false;

    public get listener(): Listener {
        return this.rewardView && this.rewardView.listener || {};
    }

    public set listener(value: Listener) {
        this.rewardView && (this.rewardView.listener = value);
    }

    public get league(): League {
        return this._league;
    }

    public set league(value: League) {
        if (this._league !== value) {
            this._league = value;
            this.dirty = true;
        }
    }

    public get leagueResult(): LeagueResult {
        return this._leagueResult;
    }

    public set leagueResult(value: LeagueResult) {
        if (this._leagueResult !== value) {
            this._leagueResult = value;
            this.dirty = true;
        }
    }

    public get amount(): number {
        return this.rewardView && this.rewardView.amount || 0;
    }

    public set amount(value: number) {
        this.rewardView && (this.rewardView.amount = value);
    }

    protected onLoad(): void {
        assert(this._titleLabel !== null);
        assert(this._leagueIcon !== null);
        assert(this._currentLeagueText !== null);
        assert(this._nextLeagueText !== null);
        assert(this._descriptionLabel !== null);
        this.updateDisplay();
    }

    public update(): void {
        if (this.dirty) {
            this.updateDisplay();
            this.dirty = false;
        }
    }

    private updateDisplay(): void {
        this.leagueIcon.league = this.league;
        this.currentLeagueText.league = this.league;

        if (this.league < League.Master) {
            this.descriptionLabel.key = `rank_hint`;
            this.nextLeagueText.league = this.league + 1;
        } else {
            this.descriptionLabel.key = `rank_hint_max`;
            this.nextLeagueText.league = this.league;
        }
        if (this.leagueResult === LeagueResult.Stay) {
            this.titleLabel.key = `current_rank_at`;
        } else {
            this.titleLabel.key = `current_rank_enter`;
        }
    }
}
