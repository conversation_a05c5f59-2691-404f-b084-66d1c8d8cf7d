import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import {PrefabUtils} from '../../utils/PrefabUtils';
import {CommonDialog} from "../../dialog/CommonDialog";
import Sprite = cc.Sprite;
import SpriteFrame = cc.SpriteFrame;
import tween = cc.tween;
import Tween = cc.Tween;

const {ccclass, disallowMultiple, property} = cc._decorator;

@ccclass
@disallowMultiple
export class LoadingAds extends CommonDialog {
    @property({type: Sprite, visible: true})
    public loadingProgress: Sprite | null = null;

    private progressTween: Tween<Sprite> | null = null;

    public static create(): Promise<LoadingAds> {
        return PrefabUtils.createPrefab(this, 'prefabs/loading_layer/loading_process_ads');
    }

    protected onLoad() {
        super.onLoad();
        this.loadingProgress.fillStart = 0;
        this.loadingProgress.fillRange = 0;

    }

    public async startFill() {
        return new Promise((resolve) => {
            this.progressTween = tween(this.loadingProgress)
                .to(1.2, { fillRange: 1 })
                .call(resolve) 
                .start();
        });
    }


    public cancelAndFill() {
        if (this.progressTween) {
            this.progressTween.stop();
            this.progressTween = null;
        }
        this.loadingProgress.fillRange = 1;
    }
}