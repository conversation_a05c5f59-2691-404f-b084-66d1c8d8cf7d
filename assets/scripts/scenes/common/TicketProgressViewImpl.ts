import assert = require('assert');
import * as gm from '../../engine/gm_engine';
import {TimeUtils} from "../../utils/TimeUtis";
import {TicketProgressView} from './TicketProgressView';
import {TimeManager} from "../../manager/time/TimeManager";
import * as ee from "../../libraries/ee/index";

const {ccclass, disallowMultiple, property} = cc._decorator;

/** Used in editor. */
@ccclass
@disallowMultiple
class TicketProgressViewImpl extends TicketProgressView {
    @property({type: cc.ProgressBar, visible: true})
    private readonly _progressBar: cc.ProgressBar | null = null;

    @property({type: cc.Label, visible: true})
    private readonly _timeLabel: cc.Label | null = null;

    private _timeManager: TimeManager;

    private get progressBar(): cc.ProgressBar {
        return gm.retrieveNull(this._progressBar);
    }

    private get timeLabel(): cc.Label {
        return gm.retrieveNull(this._timeLabel);
    }

    private _timePoint = 0;
    private _duration = 0;

    public get timePoint(): number {
        return this._timePoint;
    }

    public set timePoint(value: number) {
        if (this._timePoint !== value) {
            this._timePoint = value;
            this.doLayout();
        }
    }

    public get duration(): number {
        return this._duration;
    }

    public set duration(value: number) {
        if (this._duration !== value) {
            this._duration = value;
            this.doLayout();
        }
    }

    protected onLoad(): void {
        assert(this._progressBar !== null);
        assert(this._timeLabel !== null);
        this._timeManager = ee.ServiceLocator.resolve(TimeManager);
        this.doLayout();
        this.schedule(this.doLayout, 1);
    }

    protected onDestroy(): void {
        this.unschedule(this.doLayout);
    }

    private doLayout(): void {
        const remainingTime = this._timeManager.getRemainingTime(this.timePoint, this.duration);
        this.timeLabel.string = TimeUtils.getTimeString(remainingTime);

        const now = this._timeManager.secondsNow();
        this.progressBar.progress = (remainingTime) / this.duration;
    }
}
