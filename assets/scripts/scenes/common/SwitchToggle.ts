import {VibrateManager} from "../../manager/vibrate/VibrateManager";

const {ccclass, property} = cc._decorator;
import * as ee from '../../libraries/ee/index';

@ccclass
export default class SwitchToggle extends cc.Component {

    @property(cc.Vec2) onPosition: cc.Vec2 = null;

    @property(cc.Vec2) offPosition: cc.Vec2 = null;

    @property(cc.Node) switchControl: cc.Node = null;

    private toggle: cc.Toggle = null;
    private _onChangedState: (isCheck: boolean) => void;

    protected onLoad() {
        this.toggle = this.getComponent(cc.Toggle);
        this.node.on('click', this.onCheckStateChanged, this);
        const enable = ee.ServiceLocator.resolve(VibrateManager).isVibrateEnabled;
        this.toggle.isChecked = enable;
        this.switchControl.setPosition(cc.v3(enable ? this.onPosition : this.offPosition))
    }

    public setOnChangedCallback(f: (isCheck: boolean) => void): void {
        this._onChangedState = f;
    }

    private onCheckStateChanged() {
        let onState = !this.toggle.isChecked;
        const targetPos = onState ? this.onPosition : this.offPosition;
        this._onChangedState(onState)
        cc.tween(this.switchControl)
            .to(0.15, {position: cc.v3(targetPos)})
            .start();
    }

    public toggleState() {
        this.onCheckStateChanged();
        this.toggle.isChecked = !this.toggle.isChecked;
    }
}
