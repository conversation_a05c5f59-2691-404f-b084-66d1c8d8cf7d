import * as gm from '../../engine/gm_engine';
import {
    RewardInfo,
    RewardType,
} from '../../manager/gm_manager';

const { ccclass, disallowMultiple, property } = cc._decorator;

@ccclass
@disallowMultiple
export class EffectItemView extends cc.Component {
    @property({ type: cc.Sprite, visible: true })
    private _cardSprite: cc.Sprite | null = null;

    @property({ type: cc.Node, visible: true })
    private _commonLayer: cc.Node | null = null;

    public get cardSprite(): cc.Sprite {
        return gm.retrieveNull(this._cardSprite);
    }

    public get commonLayer(): cc.Node {
        return gm.retrieveNull(this._commonLayer);
    }

    private updateIndex = 0;

    public setReward(reward: RewardInfo): void {
        ++this.updateIndex;
        if (reward.type === RewardType.Card) {
            this.cardSprite.node.active = true;
            this.commonLayer.active = false;
        } else {
            this.cardSprite.node.active = false;
            this.commonLayer.active = true;
            const currentIndex = this.updateIndex;
            reward.effect.load().then(view => {
                if (this.updateIndex === currentIndex) {
                    this.commonLayer.removeAllChildren();
                    this.commonLayer.addChild(view);
                }
            });
        }
    }
}
