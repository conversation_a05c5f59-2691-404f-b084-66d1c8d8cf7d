import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import { AdsManager, CrashlyticManager, crashlytics } from '../../manager/gm_manager';
import Button = cc.Button;
import {TimeUtils} from "../../utils/TimeUtis";

const { ccclass, disallowMultiple, property } = cc._decorator;

type OnItemPressedCallback = () => void;

@ccclass
@disallowMultiple
export class ShopItemCell extends cc.Component {
    @property(cc.Label)
    private priceLabel: cc.Label | null = null;

    @property(cc.Label)
    private quantityLabel: cc.Label | null = null;

    @property(ee.LanguageComponent)
    private unlimitedQuantityLabel: ee.LanguageComponent | null = null;

    @property(cc.Label)
    private saleOffLabel: cc.Label | null = null;

    @property(cc.Node)
    private priceNode: cc.Node | null = null;

    @property(cc.Node)
    private videoNode: cc.Node | null = null;

    @property(ee.NestedPrefab)
    private waitingIcon: ee.NestedPrefab | null = null;

    @property({ type: Button, visible: true })
    private buyButton: Button | null = null;

    @property(cc.Node)
    private saleOffNode: cc.Node | null = null;

    @property(ee.NestedPrefab)
    private itemPrefab: ee.NestedPrefab | null = null;

    @property({ type: cc.Node, visible: true })
    private _iconPriceIconNode: cc.Node | null = null;

    private get iconPriceIconNode(): cc.Node {
        return gm.retrieveNull(this._iconPriceIconNode);
    }
    private callback: OnItemPressedCallback = () => { };

    public onLoad(): void {
        this.saleOffNode!.active = false;
        this.iconPriceIconNode.active = false;
    }

    public setOnItemPressedCallback(callback: OnItemPressedCallback): this {
        this.callback = callback;
        return this;
    }

    public setPrice(price: string, showIcon: boolean): this {
        this.priceLabel!.string = price;
        this.iconPriceIconNode.active = showIcon;
        if (!showIcon) {
            const containterWidth = this.priceLabel!.node.parent.width;
            this.priceLabel!.node.width = containterWidth * 0.9;
        }
        return this;
    }

    public setItemPrefab(prefab: cc.Prefab): this {
        this.itemPrefab!.prefab = prefab;
        this.waitingIcon.node.active = false;
        return this;
    }

    public showVideoIcon(video: boolean): this {
        this.buyButton!.interactable = true;

        if (video) {
            const adsManager = ee.ServiceLocator.resolve(AdsManager)
            const adsReady = adsManager.isReadyShowRewardedVideo()
            this.readyShowVideo(adsReady)
            adsManager.onRewardedAdsReadyShow(this.uuid, this.readyShowVideo.bind(this))

        }
        this.videoNode!.active = video;

        this.priceNode!.active = !video;
        return this;
    }
    onDestroy(): void {
        const adsManager = ee.ServiceLocator.resolve(AdsManager)
        if (!this.priceNode!.active) {
            adsManager.removeRewardedAdsReadyShow(this.uuid)
        }
    }
    public readyShowVideo(ready: boolean): this {
        this.buyButton!.interactable = ready;
        this.waitingIcon!.node.active = !ready;
        return this;
    }

    public setQuantity(num: number): this {
        if (num === 0) {
            return this;
        }

        this.unlimitedQuantityLabel!.node.active = false;
        this.quantityLabel!.node.active = true;

        this.quantityLabel!.string = num.toString();
        return this;
    }

    public setUnlimitedTime(num: number): this {
        if (num === 0) {
            return this;
        }

        this.quantityLabel!.node.active = false;
        this.unlimitedQuantityLabel!.node.active = true;
        let timeFormated = TimeUtils.getRemainString(num*60*60);
        this.unlimitedQuantityLabel!.paramValues = [timeFormated];
        return this;
    }

    public setSaleOff(show: boolean, num: number): this {
        this.saleOffNode!.active = show;
        this.saleOffLabel!.string = `Extra ${num} %`;
        return this;
    }

    public setSaleOff_v2(show: boolean, num: number): this {
        this.saleOffNode!.active = show;
        this.saleOffLabel!.string = `Off ${num} %`;
        return this;
    }

    @crashlytics
    public onItemPressed(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onItemPressed, `${this.uuid}`);
        this.callback();
    }
}
