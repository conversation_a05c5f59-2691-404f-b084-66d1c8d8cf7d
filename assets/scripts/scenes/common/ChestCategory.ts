import * as gm from '../../engine/gm_engine';
import * as ee from "../../libraries/ee/index";
import {FreeChestControl} from "../chest/FreeChestControl";
import {VipManager} from "../../manager/vip/VipManager";
import {ChestDialog} from "../../dialog/ChestDialog";
import {StoreItem, StoreManager} from "../../manager/store/StoreManager";
import {ChestManager} from "../../manager/chest/ChestManager";
import {TrackingManager} from "../../manager/analytics/TrackingManager";
import {
    SceneName,
    TrackResultIap,
    TrackResultItem,
    TrackSinkType,
    TrackSourceType
} from "../../manager/analytics/AnalyticsConfig";
import {ChestUtils} from "../../manager/chest/ChestUtils";
import {ChestControl} from "../chest/ChestControl";
import {ConfirmChestDialog} from "../../dialog/ConfirmChestDialog";
import {RewardUtils} from "../../manager/reward/RewardUtils";
import {NotEnoughCurrencyDialog} from "../../game/more_currency_dialog/NotEnoughCurrencyDialog";
import {MoreCurrencyUtils} from "../../game/more_currency_dialog/MoreCurrencyUtils";
import {FeatureManager} from "../../manager/config/FeatureManager";
import {PiggyBankManager} from "../PiggyBank/PiggyBankManager";

const {ccclass, property} = cc._decorator;

@ccclass
export class ChestCategory extends cc.Component {
    @property(cc.Layout)
    private layout: cc.Layout | null = null;

    @property({ type: cc.Prefab, visible: true })
    private readonly _freeChestPrefab: cc.Prefab | null = null;

    @property({ type: cc.Prefab, visible: true })
    private readonly _chestPrefab: cc.Prefab | null = null;

    private get freeChestPrefab(): cc.Prefab {
        return gm.retrieveNull(this._freeChestPrefab);
    }

    private get chestPrefab(): cc.Prefab {
        return gm.retrieveNull(this._chestPrefab);
    }

    private piggyBankManager: PiggyBankManager = null;
    private freeChestControl: FreeChestControl | null = null;
    private chestViewPressed: boolean = false;
    private isInitialized = false;

    public onLoad(): void {
        this.piggyBankManager = ee.ServiceLocator.resolve(PiggyBankManager);
        ee.AsyncManager.getInstance().add(ChestCategory.name, async () => {
            await this.initialize();
            this.isInitialized = true;
            this.loadInfo();
        });
    }

    private async initialize(): Promise<void> {
        const vipManager = ee.ServiceLocator.resolve(VipManager);
        vipManager.addObserver("chest_scroll_view", () => {
            this.updateFreeChestInfo();
        });
    }

    public onDestroy(): void {
        const vipManager = ee.ServiceLocator.resolve(VipManager);
        vipManager.removeObserver("chest_scroll_view");
    }

    private loadInfo(): void {
        this.createFreeChest(this.freeChestPrefab);
        this.createBuyChests(this.chestPrefab);
        this.layout.updateLayout();
    }

    private async showChestDialog(type: gm.ChestType, isRewardGold: boolean): Promise<void> {
        const dialog = await ChestDialog.create();
        const rootDialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        dialog.chestQuantity = 1;
        dialog.chestType = type;
        dialog.isRewardGold = isRewardGold;
        dialog.show(rootDialogManager);
        await new Promise<void>(resolve => dialog.onDidHide(() => {
            this.chestViewPressed = false;
            resolve();
        }));
    }

    private createFreeChest(prefab: cc.Prefab): void {
        const wrapper = new cc.Node();
        this.layout.node.addChild(wrapper);

        const item = cc.instantiate(prefab);
        wrapper.addChild(item);

        const control = item!.getComponent(FreeChestControl);
        control.onGetFreeChest(() => this.receiveFreeChest());

        this.freeChestControl = control;
        const chestView = control.getChestView();
        chestView.callback = () => {
            if (this.chestViewPressed) return;

            if (control.canReceiveFreeChest()) {
                this.receiveFreeChest();
            } else {
                this.showChestInfoDialog(gm.ChestType.Free, 0, StoreItem.Ruby);
            }
            this.chestViewPressed = true;
        };
        this.updateFreeChestInfo();
    }

    private receiveFreeChest(): void {
        cc.log('claim count: receiveFreeChest');
        const chestManager = ee.ServiceLocator.resolve(ChestManager);
        chestManager.claimFreeChest();
        this.showChestDialog(gm.ChestType.Free, true).then();
        this.updateFreeChestInfo();

        const trackingMgr = ee.ServiceLocator.resolve(TrackingManager);
        trackingMgr.trackEventSourceItem(SceneName.SceneMenuChest, TrackResultItem.Achieve,
            TrackSourceType.Chest, 'chest_free', 1);
    }

    private updateFreeChestInfo(): void {
        const chestManager = ee.ServiceLocator.resolve(ChestManager);
        const vipManager = ee.ServiceLocator.resolve(VipManager);

        const level = vipManager.getCurrentVipLevel();
        const vipData = vipManager.getVipData(level);

        const chestReduceTime = vipData.benefit.chestTimeReduction;
        const timeRatio = Math.max(0, (100 - chestReduceTime) / 100);

        const info = chestManager.getChestInfo(gm.ChestType.Free);
        const duration = info.duration * timeRatio;
        const lastTime = chestManager.freeChestTimePoint;

        const claimCount = chestManager.freeChestCount;
        this.freeChestControl!.setInfo(lastTime, duration, claimCount);
    }

    private createBuyChests(prefab: cc.Prefab): void {
        const list = [
            gm.ChestType.Copper,
            gm.ChestType.Silver,
            gm.ChestType.Gold,
            gm.ChestType.Diamond,
            gm.ChestType.Star
        ];
        const chestManager = ee.ServiceLocator.resolve(ChestManager);

        for (const type of list) {
            const info = chestManager.getChestInfo(type);
            const skin = ChestUtils.getSkin(type);
            const wrapper = new cc.Node();
            this.layout.node.addChild(wrapper);

            const item = cc.instantiate(prefab);
            wrapper.addChild(item);

            const control = item!.getComponent(ChestControl);
            control.setInfo(info!, skin)
                .setListener({
                    pressedRuby: (chestInfo) => {
                        this.showChestInfoDialog(type, chestInfo.price, StoreItem.Ruby);
                    },
                    pressedGold: (chestInfo) => {
                        this.showChestInfoDialog(type, chestInfo.priceGold, StoreItem.Gold);
                    },
                });
            const chestView = control.getChestView();
            chestView.callback = () => {
                if (this.chestViewPressed) {
                    return;
                }
                this.showChestInfoDialog(type, 0, StoreItem.Ruby);
                this.chestViewPressed = true;
            };
        }
    }

    private showChestInfoDialog(type: gm.ChestType, price: number, storeItem: StoreItem): void {
        const storeManager = ee.ServiceLocator.resolve(StoreManager);
        const ruby = storeManager.getItemBalance(StoreItem.Ruby);
        const showButton = price > 0;

        ConfirmChestDialog.create().then(chestDialog => {
            const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
            chestDialog.chestType = type;
            chestDialog.price = price;
            chestDialog.currencyType = storeItem;
            chestDialog.isButtonEnabled = showButton;
            chestDialog.isVideoButtonEnabled = false;
            chestDialog.controller = {
                pressedOpenByCurrency: () => {
                    const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
                    const trackConversionBuyChestBy = (storeItem: StoreItem)=>{
                        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
                        if (storeItem === StoreItem.Ruby) {
                            trackingManager.trackConversionBuyChestByRuby();
                        } else {
                            trackingManager.trackConversionBuyChestByGold();
                        }
                    }

                    const itemName = `chest_${RewardUtils.getChestName(type)}`;
                    const currency = storeManager.getItemBalance(storeItem);
                    if (currency >= price) {
                        this.piggyBankManager.autoCashBack = false;
                        storeManager.addItemBalance(storeItem, -price);
                        this.showChestDialog(type, storeItem !== StoreItem.Gold).then(()=>{
                            this.piggyBankManager.cashBackRuby(1.2).then();
                        })
                        trackingManager.trackEventSinkIap(SceneName.SceneMenuChest,
                            TrackResultIap.Bought, storeItem === StoreItem.Ruby ? TrackSinkType.Ruby : TrackSinkType.GoldBar,
                            itemName, price);
                        trackConversionBuyChestBy(storeItem);
                        trackingManager.trackEventSourceItem(SceneName.SceneMenuChest,
                            TrackResultItem.BuyDone, TrackSourceType.Chest,
                            itemName, 1);

                    } else {

                        if(storeItem === StoreItem.Gold){ // Done
                            MoreCurrencyUtils.show({
                                dialogManager: dialogManager,
                                sceneName: SceneName.DialogStoryBooster,
                                storeItem: storeItem,
                                needed: price
                            }).then()
                        }else{
                            // show offer
                            NotEnoughCurrencyDialog.create().then(dialog => {
                                dialog
                                    .setInfo(storeItem, null);
                                dialog.show(dialogManager);
                            })
                        }
                        trackingManager.trackEventSinkIap(SceneName.SceneMenuChest,
                            TrackResultIap.Error, storeItem === StoreItem.Ruby ? TrackSinkType.Ruby : TrackSinkType.GoldBar,
                            itemName, price);
                        trackingManager.trackEventSourceItem(SceneName.SceneMenuChest,
                            TrackResultItem.Error, TrackSourceType.Chest,
                            itemName, 1);
                    }
                },
            };
            chestDialog.onDidHide(() => {
                this.chestViewPressed = false;
            });
            chestDialog.show(dialogManager);
        });
    }
}
