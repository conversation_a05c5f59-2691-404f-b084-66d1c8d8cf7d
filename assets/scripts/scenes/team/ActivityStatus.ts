const {ccclass, property} = cc._decorator;
import * as ee from '../../libraries/ee/index';

export enum Status {
    HIGH,
    MEDIUM,
    LOW,
}

@ccclass
export class ActivityStatus extends cc.Component {
    @property(cc.Node)
    private dot: cc.Node = null;

    @property(cc.Node)
    private text: cc.Node = null;

    @property(cc.Color)
    private highColor: cc.Color = cc.Color.WHITE;

    @property(cc.SpriteFrame)
    private highSprite: cc.SpriteFrame = null;

    @property(cc.Color)
    private mediumColor: cc.Color = cc.Color.WHITE;

    @property(cc.SpriteFrame)
    private mediumSprite: cc.SpriteFrame = null;

    @property(cc.Color)
    private lowColor: cc.Color = cc.Color.WHITE;

    @property(cc.SpriteFrame)
    private lowSprite: cc.SpriteFrame = null;

    public set status(value: Status) {
        this.dot.color = this.getColor(value);
        this.text.color = this.getColor(value);
        this.dot.getComponent(cc.Sprite).spriteFrame = this.getSprite(value);
        this.text.getComponent(ee.LanguageComponent).key = this.getKey(value);
    }

    private getColor(value: Status): cc.Color {
        switch (value) {
            case Status.HIGH:
                return this.highColor;
            case Status.MEDIUM:
                return this.mediumColor;
            case Status.LOW:
                return this.lowColor;
            default:
                return cc.Color.WHITE;
        }
    }

    private getSprite(value: Status): cc.SpriteFrame {
        switch (value) {
            case Status.HIGH:
                return this.highSprite;
            case Status.MEDIUM:
                return this.mediumSprite;
            case Status.LOW:
                return this.lowSprite;
            default:
                return this.lowSprite;
        }
    }

    private getKey(value: Status): string {
        switch (value) {
            case Status.HIGH:
                return "text_high";
            case Status.MEDIUM:
                return "text_medium";
            case Status.LOW:
                return "text_low";
            default:
                return "text_low";
        }
    }
}
