import {AudioManager, CrashlyticManager, crashlytics, SoundType, UserProfileManager,} from "../../manager/gm_manager";
import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {MemberInfo, MemberItem} from "./MemberItem";
import VerticalScrollView from "./VerticalScrollView";
import {ITeamLayerListener, TeamInfo} from "./TeamLayer";
import {ActivityStatus, Status} from "./ActivityStatus";
import BadgeView from "../../team/BadgeView";
import {MyTeamManager, ROLE} from "../../manager/team/MyTeamManager";
import TeamConfirmDialog from "../../dialog/TeamConfirmDialog";
import {DialogManager, ServiceLocator} from "../../libraries/ee/index";
import {UpdateTeamProfileDialog} from "../../dialog/UpdateTeamProfileDialog";
import BubbleAction from "./BubbleAction";
import {GameServerManager} from "../../manager/game_server/GameServerManager";
import {FloatingText} from "./FloatingText";
import {ChatNetworkManager} from "../../team/chat/Manager/ChatNetworkManager";

const {ccclass, property} = cc._decorator;

@ccclass
export class InforTeamLayer extends cc.Component {
    @property(cc.Label)
    private lblName: cc.Label = null;

    @property(cc.Label)
    private lblMemberCount: cc.Label = null;

    @property(BadgeView)
    private badge: BadgeView = null;

    @property(cc.Label)
    private lblScore: cc.Label = null;

    @property(cc.Label)
    lblTeamType: cc.Label = null;

    @property(cc.Label)
    private lblRequireLevel: cc.Label = null;

    @property(cc.Label)
    private lblDescription: cc.Label = null;

    @property(ActivityStatus)
    private activityStatus: ActivityStatus = null;

    @property(cc.Node)
    private btnJoin: cc.Node = null;

    @property(cc.Node)
    private btnLeave: cc.Node = null;

    @property(cc.Node)
    private btnEdit: cc.Node = null;

    @property(cc.Node)
    private btnPending: cc.Node = null;

    @property(VerticalScrollView)
    private scrollView: VerticalScrollView<MemberInfo> | null = null;

    @property(cc.Prefab)
    private memberItemPrefab: cc.Prefab | null = null;

    @property(ee.ConditionTouchListener)
    private conditionTouchListener: ee.ConditionTouchListener = null;

    @property(cc.Node)
    private popupParent: cc.Node = null;

    @property(cc.Node)
    private navigatePopup: cc.Node = null;

    @property(FloatingText)
    private floatingText: FloatingText = null;

    private _listener?: ITeamLayerListener;
    private _teamProfile: TeamInfo = null;
    private _teamMember: MemberInfo[] = [];
    private _myRole: string;

    private _showPopupIndex = -1;

    public set teamProfile(value: TeamInfo) {
        this._teamProfile = value;
        this.setTeamProfileUI(value);
    }

    public set teamMember(value: MemberInfo[]) {
        this._teamMember = value;
        this.refreshRole();
        this.setTeamMemberUI(value);
    }

    @crashlytics
    protected onLoad(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        this.scrollView
            .setCreator(() => {
                return cc.instantiate(this.memberItemPrefab);
            })
            .setUpdater((index, view, model) => {
                model.ranking = index + 1;
                view.getComponent(MemberItem).setInfo(model);
                view.off(`click`);
                view.on('click', () => {
                    if (this.navigatePopup.active && this._showPopupIndex === index) {
                        this.navigatePopup.active = false;
                        this._showPopupIndex = -1;
                        return;
                    }
                    this.navigatePopup.active = true;
                    this._showPopupIndex = index;

                    const memberItem = view.getComponent(MemberItem)
                    const avatarNode = memberItem.getAvatarView();

                    const helper = new gm.TransformHelper(avatarNode, this.popupParent);
                    const position = helper.convertTo(cc.Vec2.ZERO);
                    this.navigatePopup.getComponent(BubbleAction)
                        .fromRole(this._myRole)
                        .toRole(model.role)
                        .show(position);
                    this.navigatePopup.getComponent(BubbleAction).listener = {
                        onDemoteCoLeader: () => {
                            this.demoteMember(model);
                        },
                        onPromoteCoLeader: () => {
                            this.promoteMember(model);
                        },
                        onKickOut: () => {
                            this.kickMember(model);
                        },
                        onViewProfile: () => {
                            this._listener?.onViewUserProfilePressed(model.userId);
                        }
                    }
                });
            })
            .setScrolling(this.onScrolling.bind(this));

        this.conditionTouchListener.setConditionCallback((listener, touch) => {
            // @ts-ignore
            if (!this.conditionTouchListener.node._hitTest(touch.getLocation(), listener) && this.navigatePopup.active) {
                this.navigatePopup.active = false;
                this._showPopupIndex = -1
                return true;
            }
            return false;
        });
    }

    private  onScrolling() {
        this.navigatePopup.active = false;
        this._showPopupIndex = -1
    }

    @crashlytics
    protected onEnable(): void {
        this.navigatePopup.active = false;
        this._showPopupIndex = -1;
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
        ee.ServiceLocator.resolve(ChatNetworkManager).addObserver(this.uuid, {
            notify: (type: string) => {
                const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
                if (['USER_JOIN', 'USER_LEAVE', 'KICK_USER', 'PROMOTION_TO_CO_LEADER', 'PROMOTION_TO_LEADER', 'DEMOTION_TO_MEMBER'].includes(type)) {
                    gameServerManager.getTeamMembers(this._teamProfile.teamId).then(teamMember => {
                        this.teamMember = teamMember;
                        this.updateDisplay();
                    });
                }
            }
        });
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
        ee.ServiceLocator.resolve(ChatNetworkManager).removeObserver(this.uuid);
    }

    public setInfo(teamProfile: TeamInfo, teamMember: MemberInfo[]): void {
        this.teamProfile = teamProfile;
        this.teamMember = teamMember;
        //this.updateDisplay();
        this.showButtons();
    }

    private setTeamProfileUI(teamProfile: TeamInfo): void {
        this.lblName.string = teamProfile.teamName;
        this.lblMemberCount.string = `${teamProfile.teamSize} / 30`;
        this.badge.badgeID = teamProfile.avatar;
        this.lblScore.string = teamProfile.teamScore.toString();
        this.lblTeamType.string = teamProfile.teamType;
        this.lblRequireLevel.string = teamProfile.minimumLevelRequired.toString();
        this.lblDescription.string = teamProfile.description;
        this.activityStatus.status = Status[teamProfile.activity];
    }

    private setTeamMemberUI(teamMember: MemberInfo[]): void {
        this.scrollView.infoList = teamMember;
        this.scrollView.updateDisplay();
        this.scrollView.scrollToTop(0.5);
    }

    private async showButtons() {
        // Hide all buttons initially
        this.btnJoin.active = false;
        this.btnLeave.active = false;
        this.btnEdit.active = false;
        this.btnPending.active = false;

        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        const myTeamInfo = myTeamManager.getTeamInfo();

        // Check if user is in a team
        const isInTeam = myTeamInfo.teamId !== "";

        if (isInTeam) {
            // User is in a team - show edit and leave buttons
            // Check if user is viewing their own team
            if (myTeamInfo.teamId === this._teamProfile.teamId) {
                // User is viewing their own team
                try {
                    const myRole = await myTeamManager.getMyRole();

                    if (myRole === ROLE.LEADER) {
                        // Leader can edit and leave
                        this.btnEdit.active = true;
                        this.btnLeave.active = true;
                    } else {
                        // Co-leader and members can only leave
                        this.btnLeave.active = true;
                    }
                } catch (error) {
                    // If error getting role, just show leave button
                    this.btnLeave.active = true;
                }
            }
            // If viewing another team while in a team, show no buttons
        } else {
            // User is not in a team - show join or pending button
            try {
                const pendingRequests = await myTeamManager.getPendingJoinRequests();
                cc.log(`pendingRequests: ${pendingRequests}`);
                const hasPendingRequest = pendingRequests.includes(this._teamProfile.teamId);

                if (hasPendingRequest) {
                    // Show pending button if user has sent a request to this team
                    this.btnPending.active = true;
                } else {
                    // Show join button if no pending request
                    this.btnJoin.active = true;
                }
            } catch (error) {
                // If error getting pending requests, default to showing join button
                this.btnJoin.active = true;
            }
        }
    }

    private async updateDisplay() {
        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        const userProfileManager = ee.ServiceLocator.resolve(UserProfileManager);
        this.btnJoin.active = false;
        this.btnLeave.active = false;
        this.btnEdit.active = false;
        if (myTeamManager.getTeamInfo().teamId == "") {
            this.btnJoin.active = true;
            this.btnLeave.active = false;
            this.btnEdit.active = false;
            this._myRole = '';
        } else {
            const userID = await myTeamManager.getUserID();
            const role = this._teamMember.find(member => member.userId === userID)?.role;
            if (role == ROLE.LEADER) {
                this.btnJoin.active = false;
                this.btnLeave.active = true;
                this.btnEdit.active = true;
            } else {
                this.btnJoin.active = false;
                this.btnLeave.active = true;
                this.btnEdit.active = false;
            }
            this._myRole = role;
        }
        if (userProfileManager.getCurrentLevel() + 1 < this._teamProfile.minimumLevelRequired) {
            this.btnJoin.active = false;
        }
    }

    private refreshRole() {
        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        if (myTeamManager.getTeamInfo().teamId == "") {
            this._myRole = '';
        } else {
            myTeamManager
                .getUserID()
                .then((userID) => {
                    this._myRole = this._teamMember.find(member => member.userId === userID)?.role;
                })
        }
    }

    setListener(listener: ITeamLayerListener) {
        this._listener = listener;
    }

    @crashlytics
    private onJoinPressed(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onJoinPressed, `${this.uuid}`);
        this._listener?.onJoinPressed(this._teamProfile);
    }

    //Button Edit
    @crashlytics
    private onUpdateTeamProfilePressed(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onUpdateTeamProfilePressed, `${this.uuid}`);

        UpdateTeamProfileDialog.create().then(dialog => {
            dialog.setUpdateCallback((updatedTeamInfo) => {
                // Update local team profile with the new data
                this._teamProfile = {...this._teamProfile, ...updatedTeamInfo};
                this.setTeamProfileUI(this._teamProfile);
            });
            dialog.show(ee.ServiceLocator.resolve(DialogManager));
        })
    }

    //Leave
    @crashlytics
    private onLeavePressed(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLeavePressed, `${this.uuid}`);
        //this._listener?.onLeavePressed(this._teamProfile.teamId);
        // Show team confirm dialog, if yes, call onLeavePressed, if no, close dialog
        TeamConfirmDialog.create().then(dialog => {
            dialog.initialize(
                () => {
                    this._listener?.onLeavePressed(this._teamProfile.teamId);
                }, () => {
                });
            dialog.setKeyContent("team_ask_leave");
            dialog.setKeyTitle("text_leave_team");
            dialog.show(ee.ServiceLocator.resolve(DialogManager));
        })
    }

    //Pending
    @crashlytics
    private onPendingPressed(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onPendingPressed, `${this.uuid}`);
        // Show info that request is pending - could show a dialog or just a floating text
        this.floatingText.content.key = "team_request_pending";
        this.floatingText.show();
    }

    private promoteMember(i: MemberInfo): void {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.promoteMemberWithId(i.userId, this._teamProfile.teamId).then(res => {
            if (res) {
                this.floatingText.content.key = "promote_member_successfully";
            } else {
                this.floatingText.content.key = "promote_member_failed";
            }
            this.floatingText.show();
            gameServerManager.getTeamMembers(this._teamProfile.teamId).then(teamMember => {
                this.teamMember = teamMember;
                this.updateDisplay();
            });
        });
    }

    private demoteMember(i: MemberInfo): void {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.demoteMemberWithId(i.userId, this._teamProfile.teamId).then(res => {
            if (res) {
                this.floatingText.content.key = "demote_member_successfully";
                this.floatingText.content.paramValues = [i.name];
            } else {
                this.floatingText.content.key = "demote_member_failed";
            }
            this.floatingText.show();
            gameServerManager.getTeamMembers(this._teamProfile.teamId).then(teamMember => {
                this.teamMember = teamMember;
                this.updateDisplay();
            });
        });
    }

    private kickMember(i: MemberInfo): void {
        // Show confirm dialog first
        TeamConfirmDialog.create().then(dialog => {
            dialog.initialize(
                () => {
                    // Only call API if user confirms
                    ee.ServiceLocator.resolve(GameServerManager).kickMemberWithId(i.userId, this._teamProfile.teamId).then(res => {
                        if (res) {
                            this.floatingText.content.key = "kick_member_successfully";
                            this.floatingText.content.paramValues = [i.name];
                        } else {
                            this.floatingText.content.key = "kick_member_failed";
                        }
                        this.floatingText.show();
                        ee.ServiceLocator.resolve(GameServerManager).getTeamMembers(this._teamProfile.teamId).then(teamMember => {
                            this.teamMember = teamMember;
                            this.updateDisplay();
                        });
                    });
                },
                () => {
                    // Do nothing if user cancels
                }
            );
            dialog.setKeyContent("team_ask_kick");
            dialog.setKeyTitle("text_kick_out");
            dialog.setParamContent([i.name]);
            dialog.show(ee.ServiceLocator.resolve(DialogManager));
        });
    }


}
