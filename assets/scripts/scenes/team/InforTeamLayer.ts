import {AudioManager, CrashlyticManager, crashlytics, SoundType, UserProfileManager,} from "../../manager/gm_manager";
import * as gm from '../../engine/gm_engine';
import * as ee from '../../libraries/ee/index';
import {DialogManager} from '../../libraries/ee/index';
import {MemberInfo, MemberItem} from "./MemberItem";
import VerticalScrollView from "./VerticalScrollView";
import {ITeamLayerListener, TeamInfo} from "./TeamLayer";
import {ActivityStatus, Status} from "./ActivityStatus";
import BadgeView from "../../team/BadgeView";
import {MyTeamManager, ROLE} from "../../manager/team/MyTeamManager";
import TeamConfirmDialog from "../../dialog/TeamConfirmDialog";
import {UpdateTeamProfileDialog} from "../../dialog/UpdateTeamProfileDialog";
import BubbleAction from "./BubbleAction";
import {GameServerManager, JoinRequestStatus} from "../../manager/game_server/GameServerManager";
import {FloatingText} from "./FloatingText";
import {NotificationMessageEntity} from "../../team/chat/Manager/ChatManager";

const {ccclass, property} = cc._decorator;

@ccclass
export class InforTeamLayer extends cc.Component {
    @property(ee.LanguageComponent)
    private inforTeamTitle: ee.LanguageComponent = null;

    @property(cc.Label)
    private lblName: cc.Label = null;

    @property(cc.Label)
    private lblMemberCount: cc.Label = null;

    @property(BadgeView)
    private badge: BadgeView = null;

    @property(cc.Label)
    private lblScore: cc.Label = null;

    @property(cc.Label)
    lblTeamType: cc.Label = null;

    @property(cc.Label)
    private lblRequireLevel: cc.Label = null;

    @property(cc.Label)
    private lblDescription: cc.Label = null;

    @property(ActivityStatus)
    private activityStatus: ActivityStatus = null;

    @property(cc.Node)
    private btnJoin: cc.Node = null;

    @property(cc.Node)
    private btnLeave: cc.Node = null;

    @property(cc.Node)
    private btnEdit: cc.Node = null;

    @property(cc.Node)
    private btnPending: cc.Node = null;

    @property(VerticalScrollView)
    private scrollView: VerticalScrollView<MemberInfo> | null = null;

    @property(cc.Prefab)
    private memberItemPrefab: cc.Prefab | null = null;

    @property(ee.ConditionTouchListener)
    private conditionTouchListener: ee.ConditionTouchListener = null;

    @property(cc.Node)
    private popupParent: cc.Node = null;

    @property(cc.Node)
    private navigatePopup: cc.Node = null;

    @property(FloatingText)
    private floatingText: FloatingText = null;

    private _listener?: ITeamLayerListener;
    private _teamProfile: TeamInfo = null;
    private _teamMember: MemberInfo[] = [];
    // private _myRole: string;

    private _showPopupIndex = -1;
    private _fromLeaderboard: boolean = false;

    setLayerTitle(key: string) {
        this.inforTeamTitle.key = key;
    }

    public get teamProfile() {
        return this._teamProfile;
    }

    public set teamProfile(value: TeamInfo) {
        this._teamProfile = value;
        this.setTeamProfileUI(value);
    }

    public set teamMember(value: MemberInfo[]) {
        this._teamMember = value;
        this.setTeamMemberUI(value);
    }


    @crashlytics
    protected onLoad(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        this.scrollView
            .setCreator(() => {
                return cc.instantiate(this.memberItemPrefab);
            })
            .setUpdater((index, view, model) => {
                model.ranking = index + 1;
                view.getComponent(MemberItem).setInfo(model);
                view.off(`click`);
                view.on('click', () => {
                    if (this.navigatePopup.active && this._showPopupIndex === index) {
                        this.navigatePopup.active = false;
                        this._showPopupIndex = -1;
                        return;
                    }
                    this.navigatePopup.active = true;
                    this._showPopupIndex = index;

                    const memberItem = view.getComponent(MemberItem)
                    const avatarNode = memberItem.getAvatarView();

                    const helper = new gm.TransformHelper(avatarNode, this.popupParent);
                    const position = helper.convertTo(cc.Vec2.ZERO);
                    const myRole = this._fromLeaderboard ? ROLE.MEMBER : ee.ServiceLocator.resolve(MyTeamManager).getMyRole();
                    this.navigatePopup.getComponent(BubbleAction)
                        .fromRole(myRole)
                        .toRole(model.role)
                        .show(position);
                    this.navigatePopup.getComponent(BubbleAction).listener = {
                        onDemoteCoLeader: () => {
                            this.demoteMember(model);
                        },
                        onPromoteCoLeader: () => {
                            this.promoteMember(model);
                        },
                        onKickOut: () => {
                            this.kickMember(model);
                        },
                        onViewProfile: () => {
                            this._listener?.onViewUserProfilePressed(model.userId);
                        }
                    }
                });
            })
            .setScrolling(this.onScrolling.bind(this));

        this.conditionTouchListener.setConditionCallback((listener, touch) => {
            // @ts-ignore
            if (!this.conditionTouchListener.node._hitTest(touch.getLocation(), listener) && this.navigatePopup.active) {
                this.navigatePopup.active = false;
                this._showPopupIndex = -1
                return true;
            }
            return false;
        });
    }

    private onScrolling() {
        this.navigatePopup.active = false;
        this._showPopupIndex = -1
    }

    @crashlytics
    protected onEnable(): void {
        this.navigatePopup.active = false;
        this._showPopupIndex = -1;
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    public async setInfo(teamProfile: TeamInfo, teamMember: MemberInfo[], fromLeaderboard: boolean = false){
        this._fromLeaderboard = fromLeaderboard;
        this.teamProfile = teamProfile;
        this.teamMember = teamMember;
        await this.showButtons()
    }

    public async onNotify(notify: NotificationMessageEntity, teamInfo: TeamInfo = null) {
        if (['USER_JOIN', 'USER_LEAVE', 'KICK_USER', 'PROMOTION_TO_CO_LEADER', 'PROMOTION_TO_LEADER', 'DEMOTION_TO_MEMBER'].includes(notify.type)) {
            const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);

            //Lấy info và membes của team đang join.
            const teamProfile = await myTeamManager.loadTeamProfile();
            const teamMembers = await myTeamManager.loadTeamMembers();

            if (teamInfo) {
                // cập nhật ui info và members của team đang view (trong team leaderboard)
                this.teamProfile = await myTeamManager.getTeamProfileByID(teamInfo.teamId)
                this.teamMember = await myTeamManager.getTeamMemberByID(teamInfo.teamId);
                await this.showButtons();
            } else {
                // cập nhật ui info và members của team đang join
                this.teamProfile = myTeamManager.getTeamInfo();
                this.teamMember = myTeamManager.getTeamMembers();
            }
        }
    }

    private setTeamProfileUI(teamProfile: TeamInfo): void {
        this.lblName.string = teamProfile.teamName;
        this.lblMemberCount.string = `${teamProfile.teamSize} / 30`;
        this.badge.badgeID = teamProfile.avatar;
        this.lblScore.string = teamProfile.teamScore.toString();
        this.lblTeamType.string = teamProfile.teamType;
        this.lblRequireLevel.string = teamProfile.minimumLevelRequired.toString();
        this.lblDescription.string = teamProfile.description;
        this.activityStatus.status = Status[teamProfile.activity];
    }

    private setTeamMemberUI(teamMember: MemberInfo[]): void {
        this.scrollView.infoList = teamMember;
        this.scrollView.updateDisplay();
        this.scrollView.scrollToTop(0.5);
    }

    public updateButtons(status: JoinRequestStatus): void {
        // update buttons này chỉ dành team leaderboard.
        if (!this._fromLeaderboard) {
            return;
        }
        this.btnLeave.active = false;
        this.btnEdit.active = false;
        this.btnJoin.active = status === JoinRequestStatus.BANNED;
        this.btnPending.active = status === JoinRequestStatus.PENDING;
    }

    async showButtons() {
        this.btnJoin.active = false;
        this.btnLeave.active = false;
        this.btnEdit.active = false;
        this.btnPending.active = false;

        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        const myTeamInfo = myTeamManager.getTeamInfo();
        let level = ee.ServiceLocator.resolve(UserProfileManager).getCurrentLevel();
        const canJoin = ++level >= 3;

        const isInTeam = myTeamInfo.teamId !== "";

        if (isInTeam) {
            if (myTeamInfo.teamId === this._teamProfile.teamId) {
                if (!this._fromLeaderboard) {
                    try {
                        await myTeamManager.loadTeamMembers();
                        const myRole = myTeamManager.getMyRole();
                        if (myRole === ROLE.LEADER) {
                            this.btnEdit.active = true;
                            this.btnLeave.active = true;
                        } else {
                            this.btnLeave.active = true;
                        }
                    } catch (error) {
                        this.btnLeave.active = true;
                    }
                }
            } else {
                this.btnJoin.active = canJoin;
            }
        } else {
            const pendingRequests = myTeamManager.getPendingJoinRequests();
            const hasPendingRequest = pendingRequests.find(q => q.teamId === this._teamProfile.teamId);
            if (hasPendingRequest) {
                this.btnPending.active = true;
            } else {
                this.btnJoin.active = canJoin;
            }
        }
    }

    // private async updateDisplay() {
    //     const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
    //     const userProfileManager = ee.ServiceLocator.resolve(UserProfileManager);
    //     this.btnJoin.active = false;
    //     this.btnLeave.active = false;
    //     this.btnEdit.active = false;
    //     if (myTeamManager.getTeamInfo().teamId == "") {
    //         this.btnJoin.active = true;
    //         this.btnLeave.active = false;
    //         this.btnEdit.active = false;
    //         this._myRole = '';
    //     } else {
    //         const userID = await myTeamManager.getUserID();
    //         const role = this._teamMember.find(member => member.userId === userID)?.role;
    //         if (role == ROLE.LEADER) {
    //             this.btnJoin.active = false;
    //             this.btnLeave.active = true;
    //             this.btnEdit.active = true;
    //         } else {
    //             this.btnJoin.active = false;
    //             this.btnLeave.active = true;
    //             this.btnEdit.active = false;
    //         }
    //         this._myRole = role;
    //     }
    //     if (userProfileManager.getCurrentLevel() + 1 < this._teamProfile.minimumLevelRequired) {
    //         this.btnJoin.active = false;
    //     }
    // }

    setListener(listener: ITeamLayerListener) {
        this._listener = listener;
    }

    private async confirmJoin() {
        TeamConfirmDialog.create().then(dialog => {
            dialog.initialize(
                () => {
                    this.doJoin()
                }, () => {
                });
            dialog.setKeyContent("team_ask_join_another");
            dialog.setKeyTitle("text_confirmation");
            dialog.show(ee.ServiceLocator.resolve(DialogManager));
        })
    }

    @crashlytics
    private async onJoinPressed() {
        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        const myTeamInfo = myTeamManager.getTeamInfo();
        const isInTeam = myTeamInfo.teamId !== "";

        if (isInTeam) {
            this.confirmJoin().then();
        } else {
            this.doJoin().then();
        }
    }

    private async doJoin(): Promise<void> {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onJoinPressed, `${this.uuid}`);
        this._listener?.onJoinPressed(this._teamProfile).then(r => {



            if (r[0] === JoinRequestStatus.PENDING) {
                this.btnPending.active = true;
                this.btnJoin.active = false;
                this.btnLeave.active = false;
                this.btnEdit.active = false;
                this.floatingText.content.key = "pending_join_request";
                this.floatingText.show();
            }
            if (r[0] === JoinRequestStatus.BANNED) {
                this.floatingText.content.key = "request_banned";
                this.floatingText.show();
            }
            if (r[0] === JoinRequestStatus.REJECTED) {
                if (r[1] === 1) {
                    this.floatingText.content.key = "no_internet_notify";
                    this.floatingText.show();
                }
                if (r[1] === 3) {
                    this.floatingText.content.key = "team_not_found";
                    this.floatingText.show();
                }
                if (r[1] === 8) {
                    this.floatingText.content.key = "pending_join_request";
                    this.floatingText.show();
                }
                if (r[1] === 13) {
                    if (this._teamProfile.teamType === "OPEN") {
                        this.floatingText.content.key = "team_is_full";
                    }
                    if (this._teamProfile.teamType === "CLOSE") {
                        this.floatingText.content.key = "team_not_enough_spots";
                    }
                    this.floatingText.show();
                }
            }
        })
    }

    //Button Edit
    @crashlytics
    private onUpdateTeamProfilePressed(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onUpdateTeamProfilePressed, `${this.uuid}`);

        UpdateTeamProfileDialog.create().then(dialog => {
            dialog.setUpdateCallback((updatedTeamInfo) => {
                // Update local team profile with the new data
                this._teamProfile = {...this._teamProfile, ...updatedTeamInfo};
                this.setTeamProfileUI(this._teamProfile);
            });
            dialog.show(ee.ServiceLocator.resolve(DialogManager));
        })
    }

    //Leave
    @crashlytics
    private onLeavePressed(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLeavePressed, `${this.uuid}`);
        //this._listener?.onLeavePressed(this._teamProfile.teamId);
        // Show team confirm dialog, if yes, call onLeavePressed, if no, close dialog
        TeamConfirmDialog.create().then(dialog => {
            dialog.initialize(
                () => {
                    this._listener?.onLeavePressed(this._teamProfile.teamId);
                }, () => {
                });
            dialog.setKeyContent("team_ask_leave");
            dialog.setKeyTitle("text_leave_team");
            dialog.show(ee.ServiceLocator.resolve(DialogManager));
        })
    }

    public rejectJoinRequest(reason: string): void {
        this.floatingText.content.key = reason;
        this.floatingText.show();
        this.btnJoin.active = true;
        this.btnPending.active = false;
    }

    public pendingJoinRequest(): void {
        this.btnJoin.active = false;
        this.btnPending.active = true;
    }

    public showFloating(key: string, params: string[] = null): void {
        this.floatingText.content.key = key;
        if (params && params.length > 0) {
            this.floatingText.content.paramValues = params;
        }
        this.floatingText.show();
    }

    //Pending
    @crashlytics
    private async onCancelJoinRequest(){
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onCancelJoinRequest, `${this.uuid}`);

        // looking for pending requests have any team id match with current team id
        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        const pendingRequests = myTeamManager.getPendingJoinRequests();
        const request = pendingRequests.find(q => q.teamId === this._teamProfile.teamId);
        if (!request) {
            return;
        }
        const requestID = request.requestId;
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        gameServerManager.cancelJoinRequest(requestID).then(() => {
            this.btnJoin.active = true;
            this.btnPending.active = false;
        });
    }

    private promoteMember(i: MemberInfo): void {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        gameServerManager.promoteMemberWithId(i.userId, this._teamProfile.teamId).then(res => {
            if (res) {
                this.floatingText.content.key = "promote_member_successfully";
            } else {
                this.floatingText.content.key = "promote_member_failed";
            }
            this.floatingText.show();
            // myTeamManager.getMyTeamMember().then((member) => {
            //     this.teamMember = member;
            //     this.showButtons().then();
            // })
        });
    }

    private demoteMember(i: MemberInfo): void {
        const gameServerManager = ee.ServiceLocator.resolve(GameServerManager);
        const myTeamManager = ee.ServiceLocator.resolve(MyTeamManager);
        gameServerManager.demoteMemberWithId(i.userId, this._teamProfile.teamId).then(res => {
            if (res) {
                this.floatingText.content.key = "demote_member_successfully";
                this.floatingText.content.paramValues = [i.name];
            } else {
                this.floatingText.content.key = "demote_member_failed";
            }
            this.floatingText.show();
            // gameServerManager.getTeamMembers(this._teamProfile.teamId).then(teamMember => {
            //     this.teamMember = teamMember;
            //     // Update MyTeamManager if this is user's own team
            //     if (myTeamManager.getTeamInfo().teamId === this._teamProfile.teamId) {
            //         myTeamManager.updateTeamMembers(teamMember);
            //     }
            //     this.showButtons(); // Use showButtons instead of updateDisplay
            // });
        });
    }

    private kickMember(i: MemberInfo): void {
        // Show confirm dialog first
        TeamConfirmDialog.create().then(dialog => {
            dialog.initialize(
                () => {
                    ee.ServiceLocator.resolve(GameServerManager)
                        .kickMemberWithId(i.userId, this._teamProfile.teamId).then(res => {
                        if (res) {
                            this.floatingText.content.key = "kick_member_successfully";
                            this.floatingText.content.paramValues = [i.name];
                        } else {
                            this.floatingText.content.key = "kick_member_failed";
                        }
                        this.floatingText.show();
                    });
                },
                () => {
                    // Do nothing if user cancels
                }
            );
            dialog.setKeyContent("team_ask_kick");
            dialog.setKeyTitle("text_kick_out");
            dialog.setParamContent([i.name]);
            dialog.show(ee.ServiceLocator.resolve(DialogManager));
        });
    }

    public async hideAnimation(): Promise<void> {
        return new Promise(resolve => {
            const node = this.node;
            const move = cc.tween(node)
                .to(0.2, {position: cc.v3(0, -node.height / 2, 0), opacity: 0}, {easing: 'sineOut'})
                .call(resolve)
                .start();
        });
    }

    public async showAnimation(): Promise<void> {
        return new Promise(resolve => {
            const node = this.node;
            node.opacity = 0;
            node.position = cc.v3(0, -node.height / 2, 0);
            const move = cc.tween(node)
                .to(0.2, {position: cc.v3(0, 0, 0), opacity: 255}, {easing: 'sineOut'})
                .call(resolve)
                .start();
        });
    }
}
