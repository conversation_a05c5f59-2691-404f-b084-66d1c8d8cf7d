import {MenuController, MenuScenePage} from "../MenuScene";
import * as ee from "../../libraries/ee/index";
import {ServiceLocator} from "../../libraries/ee/index";
import * as gm from '../../engine/gm_engine';
import {IErrResponse} from "@senspark/ee-x";
import {SearchTeamView} from "./SearchTeamView";
import TeamScrollView from "./TeamScrollView";
import {
    Commands,
    GameServerManager,
    JoinRequestStatus,
    TeamInfoResponse
} from "../../manager/game_server/GameServerManager";
import {UserProfileManager} from "../../manager/profile/UserProfileManager";
import {CreateTeamDialog} from "../../dialog/CreateTeamDialog";
import {AudioManager, crashlytics, SoundType,} from "../../manager/gm_manager";
import {StackedLayerView} from "../StackedLayerView";
import {TopHud} from "../common/TopHud";
import {TimeUtils} from "../../utils/TimeUtis";
import {InforTeamLayer} from "./InforTeamLayer";
import {BottomHud} from "../common/BottomHud";
import ChatLayer from "../../team/chat/ChatLayer";
import {MemberInfo} from "./MemberItem";
import {MyTeamManager} from "../../manager/team/MyTeamManager";
import {ChatNetworkManager} from "../../team/chat/Manager/ChatNetworkManager";
import {AlertCloseType, AlertDialog} from "../../dialog/AlertDialog";
import {UserProfileUtils} from "../../utils/UserProfileUtils";
import {UserProfile} from "../profile/UserProfileView";
import {UserProfileLayer} from "../profile/UserProfileLayer";
import {checkInternetConnection} from "../../server/InternetChecking";
import {MessageEntity, NotifyData, RequestJoin} from "../../team/chat/Manager/ChatManager";

const {ccclass, property, disallowMultiple} = cc._decorator;

export interface TeamInfo {
    activity: string;
    avatar: string;
    description: string;
    minimumLevelRequired: number;
    teamId: string;
    teamName: string;
    teamScore: number;
    teamSize: number;
    teamType: string
}

export interface ITeamLayerListener {
    findTeamByName?(teamName: string): void;

    createNewTeam?(): void;

    onJoinPressed?(teamInfo: TeamInfo): Promise<[JoinRequestStatus, number]>;

    onLeavePressed?(teamId: string): void;

    onViewTeamProfilePressed?(teamId: string): void;

    onKickOff?(): void;

    showChatLayer?(): void;

    onViewUserProfilePressed?(userId: string): void;


}

@ccclass
@disallowMultiple
export class TeamLayer extends cc.Component {
    @property({type: cc.Node, visible: true})
    private readonly _stackedLayer: cc.Node | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _waiting: cc.Node | null = null;


    @property({type: cc.Node, visible: true})
    private readonly _nodeContent: cc.Node | null = null;

    private get stackedLayer(): StackedLayerView {
        return gm.retrieveNull(this._stackedLayer).getComponent(StackedLayerView);
    }

    private get waiting(): cc.Node {
        return gm.retrieveNull(this._waiting);
    }

    private get nodeContent(): cc.Node {
        return gm.retrieveNull(this._nodeContent);
    }

    @property(cc.Node)
    private mainLayer: cc.Node = null;

    @property(cc.Node)
    private introLayer: cc.Node = null;

    @property(cc.Node)
    private buttonOpenSearchTeam: cc.Node = null;

    @property(cc.Node)
    private textTeamRequired: cc.Node = null;

    @ee.nest(SearchTeamView)
    private searchTeamView: SearchTeamView = null;

    @ee.nest(TeamScrollView)
    private teamScrollView: TeamScrollView = null;

    @property(cc.Prefab)
    private inforTeamLayerPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    private chatLayerPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    private profileLayerPrefab: cc.Prefab = null;

    @property(cc.Node)
    private noInternetLayer: cc.Node = null;

    private _controller?: MenuController;
    private _dialogManager?: ee.DialogManager;
    private _myTeamManager?: MyTeamManager;
    private _gameServerManager?: GameServerManager;
    private _inforTeamLayer: InforTeamLayer = null;
    private _chatLayer: ChatLayer = null;
    private _topHud: TopHud | null = null;
    private _bottomHud: BottomHud | null = null;

    /** Whether the stacked layer is locked, i.e. playing transition animation. */
    private isStackedLayerLocked = false;

    /** Prevent pushing multiple sub-layers by multiple taps. */
    private isLayerLocked = false;

    public get dialogManager(): ee.DialogManager {
        return gm.retrieveUndefined(this._dialogManager);
    }

    public set dialogManager(value: ee.DialogManager) {
        this._dialogManager = value;
    }

    public get myTeamManager(): MyTeamManager {
        return gm.retrieveUndefined(this._myTeamManager);
    }

    public set myTeamManager(value: MyTeamManager) {
        this._myTeamManager = value;
    }

    public get gameServerManager(): GameServerManager {
        return gm.retrieveUndefined(this._gameServerManager);
    }

    public set gameServerManager(value: GameServerManager) {
        this._gameServerManager = value;
    }

    public get controller(): MenuController {
        return gm.retrieveUndefined(this._controller);
    }

    public set controller(value: MenuController) {
        this._controller = value;
    }

    public get inforTeamLayer(): InforTeamLayer {
        if (this._inforTeamLayer === null) {
            this._inforTeamLayer = cc.instantiate(this.inforTeamLayerPrefab).getComponent(InforTeamLayer);
        }
        return this._inforTeamLayer;
    }

    public get chatLayer(): ChatLayer {
        if (this._chatLayer === null) {
            this._chatLayer = cc.instantiate(this.chatLayerPrefab).getComponent(ChatLayer);
        }
        return this._chatLayer;
    }

    public get topHud(): TopHud {
        return gm.retrieveNull(this._topHud);
    }

    public set topHud(value: TopHud) {
        this._topHud = value;
    }

    public get bottomHud(): BottomHud {
        return gm.retrieveNull(this._bottomHud);
    }

    public set bottomHud(value: BottomHud) {
        this._bottomHud = value;
    }

    private _originalBackCallback: () => void;
    public get originalBackCallback(): () => void {
        return this._originalBackCallback;
    }

    // ngăn nhấn nút back nhiều lần liên tiếp
    private isPressBack: boolean = false;

    // tránh cập nhật backButton khi page khác đang active
    private pageView: cc.PageView = null;
    private isActive(): boolean {
        if (this.pageView == null) {
            cc.log(`Not found pageView`);
            return false;
        }
        const currentPage = this.pageView.getCurrentPageIndex() as MenuScenePage;
        return currentPage === MenuScenePage.Team;
    }

    protected onLoad(): void {
        const profileManager = ee.ServiceLocator.resolve(UserProfileManager);

        this.introLayer.active = !this.myTeamManager.isHadShowUnlock();
        this.mainLayer.active = this.myTeamManager.isHadShowUnlock();

        if (!this.myTeamManager.isHadShowUnlock()) {
            this.updateJoinTeamUI(profileManager.getCurrentLevel());
        }
        this.teamScrollView.setListener({
            onViewPressed: (teamId: string) => {
                this.OnViewTeamButtonPressed(teamId);
                this.inforTeamLayer.setLayerTitle('text_join_team');
            }
        })

        let parent = this.node.parent;
        while (parent != null && parent.parent != null) {
            this.pageView = parent.getComponent(cc.PageView);
            if (this.pageView != null) {
                break;
            }
            parent = parent.parent;
        }
    }

    private async _debugLoggingPendingRequest(){
        const pendingRequests = this.myTeamManager.getPendingJoinRequests();
        for(let p of pendingRequests) {
            cc.log(JSON.stringify(p))
        }

        cc.log("=====================================")
        let serverPendingRequest = await this._gameServerManager.getMyTeamRequests();

        for(let x of serverPendingRequest) {
            cc.log(JSON.stringify(x))
        }
    }

    public init(): void {
        ee.AsyncManager.getInstance().add(TeamLayer.name, async () => {
            await this.initialize();
            this.searchTeamView.setListener(this);
            this.joinTeam().then();
        });
        this.gameServerManager.registerBroadcastMessageListener(Commands.BROADCAST_NEW_MESSAGE, this.uuid, this.handleNewMessage.bind(this));
        this.gameServerManager.registerBroadcastMessageListener(Commands.BROADCAST_UPDATE_JOIN_REQUEST, this.uuid, this.handleUpdateJoinRequest.bind(this));
        this.gameServerManager.registerBroadcastMessageListener(Commands.BROADCAST_UPDATE_TEAM_INFO, this.uuid, this.handleUpdateTeamInfo.bind(this));
    }

    private async handleNewMessage(message: string): Promise<void> {
        const msg = JSON.parse(message).msg as MessageEntity;
        if (msg.type !== "NOTIFY") {
            return;
        }
        const notify = msg.notificationMessage;
        if (notify.type === `USER_JOIN`) {
            const notifyData = JSON.parse(notify.data) as NotifyData
            const myUserId = await this.myTeamManager.getUserID();
            if (notifyData.userId === myUserId) {
                await this.myTeamManager.loadTeamProfile();
                await this.joinTeam();
            }
        }

        if (this._inforTeamLayer != null) {
            await this.inforTeamLayer.onNotify(notify);
        }
    }

    private async handleUpdateJoinRequest(message: string): Promise<void> {
        const joinRequest = JSON.parse(message) as RequestJoin;
        const myUserId = await this.gameServerManager.getUId();
        const statusKey: { [key: string]: string } = {
            REJECTED: "rejected_join_request",
            CANCELLED: "cancelled_join_request",
            EXPIRED: "expired_join_request",
        };
        if ([`REJECTED`, `CANCELLED`, `EXPIRED`].includes(joinRequest.status)) {
            if (joinRequest.userId === myUserId) {
                this.myTeamManager.deletePendingJoinRequests(joinRequest);
                if (this._inforTeamLayer != null) {
                    this.inforTeamLayer.rejectJoinRequest(statusKey[joinRequest.status]);
                }
            }
        }

        if (joinRequest.status === `APPROVED`) {
            if (joinRequest.userId === myUserId) {
                this.myTeamManager.deletePendingJoinRequests(joinRequest);
            }
        }

    }

    private handleUpdateTeamInfo(message: string): void {
        const teamInfo = JSON.parse(message) as TeamInfoResponse;
        this.myTeamManager.updateTeamInfoFromResponse(teamInfo)
        if (this._inforTeamLayer != null) {
            if (this.inforTeamLayer.teamProfile.teamId === teamInfo.teamId) {
                this.inforTeamLayer.teamProfile = this.myTeamManager.getTeamInfo();
            }
        }
    }

    protected onDestroy() {
        // destroy observer
        ee.ServiceLocator.resolve(UserProfileManager).removeObserver(this.uuid);
        this.gameServerManager.unregisterBroadcastMessageListener(Commands.BROADCAST_NEW_MESSAGE, this.uuid);
        this.gameServerManager.unregisterBroadcastMessageListener(Commands.BROADCAST_UPDATE_JOIN_REQUEST, this.uuid);
        this.gameServerManager.unregisterBroadcastMessageListener(Commands.BROADCAST_UPDATE_TEAM_INFO, this.uuid);
    }

    public updateJoinTeamUI(level: number): void {
        const canJoin = ++level >= 3;
        this.textTeamRequired.active = !canJoin;
        this.buttonOpenSearchTeam.active = canJoin;
    }


    private async joinTeam():Promise<void> {
        const shouldShowChatLayer = await this.shouldShowChatLayer();
        if (shouldShowChatLayer) {
            //pop all layer
            await this.popAllLayer();
            this.showChatLayer();
        }
    }

    public updateBackButton(page: MenuScenePage = null) {
        let isActive = page ? page === MenuScenePage.Team : this.isActive()
        // Không cập nhật nút back khi không phải là page hiện hành.
        if (!isActive) {
            return;
        }
        const topLayer = this.getTopLayer();
        const canGoBack = topLayer != null && topLayer !== this.chatLayer.node;

        this.topHud.setBackButtonVisible(canGoBack);
        if (canGoBack) {
            this._originalBackCallback = this.topHud.listener.onPressedBack;
            this.topHud.listener.onPressedBack = () => {
                if (this.isPressBack) {
                    return;
                }
                this.isPressBack = true;
                this.popLayer().then(() => {
                    this.topHud.listener.onPressedBack = this._originalBackCallback;
                    this.scheduleOnce(() => {
                        this.isPressBack = false;
                    }, 1)
                });
            }
        }
        this.mainLayer.active = (topLayer == null);
        this.mainLayer.opacity = this.introLayer.active ? 0 : 255;

    }

    /** Asynchronously initializes. */
    private async initialize(): Promise<void> {
        await this.findTeamByName(null);
    }

    public async findTeamByName(teamName: string): Promise<void> {
        let data = await this.gameServerManager.searchTeamByName(teamName);
        this.teamScrollView.infoList = data;
    }

    public createNewTeam(): void {
        CreateTeamDialog.create().then(dialog => {
            dialog.setListener(this);
            dialog.show(this.dialogManager);
        })
    }

    public async onJoinPressed(teamInfo: TeamInfo): Promise<[JoinRequestStatus, number]> {
        const [ok, error] = await this.gameServerManager.joinTeam(teamInfo.teamId);
        if (!error) {
            if (ok === JoinRequestStatus.PENDING) {
                // this.myTeamManager.loadData();
                this.myTeamManager.loadPendingJoinRequests().then();
            }
            if (ok === JoinRequestStatus.APPROVED) {
                this.myTeamManager.setTeamInfo(teamInfo);
                this.joinRoom();
            }
            return [ok, 0];
        } else {
            const chatNetworkManager = ServiceLocator.resolve(ChatNetworkManager);
            const roomPreview = await chatNetworkManager.getRoomPreview();
            if (roomPreview != null) {
                this.myTeamManager.setTeamInfo(teamInfo);
                this.joinRoom();
                return [JoinRequestStatus.APPROVED, 0]
            }
            return [JoinRequestStatus.REJECTED, error];
        }
    }

    protected onEnable() {
        this.schedule(this.checkInternet, 3);
    }

    protected onDisable() {
        this.unschedule(this.checkInternet);
    }

    private checkInternet() {
        checkInternetConnection((hasInternet) => {
            this.noInternetLayer.active = !hasInternet;
            this.nodeContent.opacity = hasInternet ? 255 : 0;
        });
    }

    private joinRoom() {
        this.waiting.active = true;
        // this.myTeamManager.loadData();
        this.popAllLayer(false).then(() => {
            this.showChatLayer();
        });
    }

    //onLeavePressed
    public async onLeavePressed(teamID: string): Promise<void> {
        const res = await this.gameServerManager.leaveTeam(teamID);
        if (res) {
            this.leaveRoom();
        }
    }

    public onViewTeamProfilePressed(teamID: string): void {
        this.inforTeamLayer.setLayerTitle('text_my_team');
        this.OnViewTeamButtonPressed(teamID);
    }

    public async onViewUserProfilePressed(userId: string): Promise<void> {
        if (this.isStackedLayerLocked) {
            return;
        }

        let userProfile: UserProfile = null;

        this.showWaiting().then(() => {
            this.pushWithLock(async () => {
                const asyncManager = new ee.AsyncManager();
                // this.isStackedLayerLocked = true;
                const profileLayer = cc.instantiate(this.profileLayerPrefab).getComponent(UserProfileLayer);
                let data = await this.gameServerManager.getUserInfo(userId);
                if (data == null || (data as IErrResponse).errCode) {
                    userProfile = UserProfileUtils.createLocalUserProfile();
                } else {
                    userProfile = data as UserProfile;
                }
                this.scheduleOnce(() => {
                    profileLayer.setInfo(userProfile);
                })
                profileLayer.setActiveEditProfile(false);
                await this.pushLayer(profileLayer.node, {slide: 0.25});
                // this.isStackedLayerLocked = false;
                await asyncManager.flushAll({size: 5, delay: 20});
                asyncManager.destroy();
            }).then(() => {
                this.waiting.active = false;
            });
        })
    }

    public onKickOff() {
        this.leaveRoom();
    }

    private leaveRoom() {
        if (this.myTeamManager.getTeamInfo().teamId == "") {
            return false;
        }
        this.waiting.active = true;
        this.myTeamManager.clearData();
        ee.ServiceLocator.resolve(ChatNetworkManager).clearRoomPreview();
        this.popAllLayer().then(() => {
            this.waiting.active = false;
        });
    }

    public showChatLayer(): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.showWaiting().then(() => {
            this.pushWithLock(async () => {
                const asyncManager = new ee.AsyncManager();
                // this.isStackedLayerLocked = true;
                this.chatLayer.setTopHub(this._topHud);
                this.chatLayer.setBottomHud(this._bottomHud);
                this.chatLayer.setListener(this);
                await this.pushLayer(this.chatLayer.node, {slide: 0.25})
                // this.isStackedLayerLocked = false;
                await asyncManager.flushAll({size: 5, delay: 20});
                asyncManager.destroy();
            }).then(() => {
                this.waiting.active = false;
            });
        });
    }

    private async getTeamProfileByID(id: string): Promise<TeamInfo> {
        const teamProfile = await this.gameServerManager.getTeamProfile(id);
        this.myTeamManager.updateTeamInfoFromProfile(teamProfile)
        return teamProfile;
    }

    private async getTeamMemberByID(id: string): Promise<MemberInfo[]> {
        const teamMember = await this.gameServerManager.getTeamMembers(id);
        return teamMember;
    }

    private async showWaiting(duration: number = 100): Promise<void> {
        this.waiting.active = true;
        await TimeUtils.sleep(duration);
    }

    private async pushWithLock<T>(pusher: () => Promise<T>): Promise<T> {
        if (this.isLayerLocked) {
            throw `Layer is locked.`;
        }
        this.isLayerLocked = true;
        try {
            const item = await pusher();
            return item;
        } finally {
            this.isLayerLocked = false;
        }
    }

    public async pushLayer(layer: cc.Node, options?: {
        slide?: number,
        fade?: number,
    }): Promise<void> {
        this.isStackedLayerLocked = true;
        layer.active = true;
        await this.stackedLayer.pushLayer(layer, options);
        this.updateBackButton();
        this.isStackedLayerLocked = false;
    }

    public getTopLayer(): cc.Node | null {
        return this.stackedLayer.getTopLayer();
    }

    public async popLayer(): Promise<void> {
        this.isStackedLayerLocked = true;
        await this.stackedLayer.popLayer();
        this.updateBackButton();
        this.isStackedLayerLocked = false;
    }

    public async popAllLayer(final: boolean = true): Promise<void> {
        this.isStackedLayerLocked = true;
        while (this.stackedLayer.getSize() > 0) {
            await this.stackedLayer.popLayer({hiddenTop: true});

        }
        if (final) {
            this.updateBackButton();
        }
        this.isStackedLayerLocked = false;
    }

    // should show chat layer if my-team-profile have team id
    private async shouldShowChatLayer() {
        if (this.myTeamManager.getTeamInfo().teamId == "") {
            return false;
        }
        // có team rồi, vẫn phải kiểm tra có RoomPreview chưa mới vào chatLayer
        const chatNetworkManager = ServiceLocator.resolve(ChatNetworkManager);
        const roomPreview = await chatNetworkManager.getRoomPreview();
        if (roomPreview == null) {
            this.leaveRoom();
            return false;
        }
        return true;
    }

    @crashlytics
    private OnViewTeamButtonPressed(teamID: string): void {
        if (this.isStackedLayerLocked) {
            return;
        }
        this.showWaiting().then(() => {
            this.pushWithLock(async () => {
                const asyncManager = new ee.AsyncManager();
                // this.isStackedLayerLocked = true;
                const [teamProfile, teamMember] = await Promise.all([
                    this.getTeamProfileByID(teamID),
                    this.getTeamMemberByID(teamID),
                ])
                if (!teamProfile) {
                    this._showErrorDialog(`no_internet_notify`, () => {
                        this.findTeamByName(null);
                    });
                    this.isStackedLayerLocked = false;
                    this.waiting.active = false;
                    asyncManager.destroy();
                    return;
                }
                await this.pushLayer(this.inforTeamLayer.node, {slide: 0.25});
                this.inforTeamLayer.setInfo(teamProfile, teamMember);
                this.inforTeamLayer.setListener(this);
                // this.isStackedLayerLocked = false;
                await asyncManager.flushAll({size: 5, delay: 20});
                asyncManager.destroy();
            }).then(() => {
                this.waiting.active = false;
            });
        });
    }

    @crashlytics
    public OnActiveSearchTeamPressed(): void {
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        ee.ServiceLocator.resolve(UserProfileManager).removeObserver(this.uuid);
        this.myTeamManager.setHadShowUnlock();
        this.introLayer.active = false;
        this.mainLayer.active = true;
        this.mainLayer.opacity = this.introLayer.active ? 0 : 255;
        //this.findTeamByName(null);
    }

    private _showErrorDialog(text: string, onClose?: () => void) {
        AlertDialog.create().then(alertDialog => {
            alertDialog.show(this.dialogManager);
            alertDialog.setKey(text);
            alertDialog.setCloseType(AlertCloseType.OKButton);
            if (onClose) {
                alertDialog.onDidHide(onClose);
            }
        });
    }

    private _demoteMember(userId: string): boolean {
        this.gameServerManager.demoteMemberWithId(userId, this.myTeamManager.getTeamInfo().teamId).then(res => {
            if (res) {
                this.myTeamManager.loadData();
            } else {
                //this._showErrorDialog();
            }
        });
        return true;
    }
}
