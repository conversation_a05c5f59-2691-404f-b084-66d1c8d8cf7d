import { CommonDialog } from "../../dialog/CommonDialog";
import * as ee from '../../libraries/ee/index';
import { CrashlyticManager, crashlytics } from "../../manager/gm_manager";
import { PrefabUtils } from "../../utils/PrefabUtils";

const { ccclass, disallowMultiple, property } = cc._decorator;

@ccclass
@disallowMultiple
export class ResetDialog extends CommonDialog {
    public static create(): Promise<ResetDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/menu_scene/setting/reset_dialog');
    }

    @property(cc.Button)
    private resetBtn: cc.Button | null = null;

    private touchTimePoint = 0;

    @crashlytics
    protected onLoad(): void {
        super.onLoad();
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);

        this.resetBtn!.node.on(cc.Node.EventType.TOUCH_START, () => {
            this.touchTimePoint = Date.now();
        }, this);
        this.resetBtn!.node.on(cc.Node.EventType.TOUCH_END, () => {
            if (Date.now() - this.touchTimePoint > 3000) {
                this.resetGame();
            }
        }, this);

    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    private resetGame(): void {

    }
}