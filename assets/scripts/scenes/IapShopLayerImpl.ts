import assert = require('assert');
import { AlertDialog } from "../dialog/AlertDialog";
import { ConfirmRerollDialog } from '../dialog/ConfirmRerollDialog';
import { NoAdsDialog } from "../dialog/NoAdsDialog";
import { WaitingDialog } from "../dialog/WaitingDialog";
import * as gm from '../engine/gm_engine';
import { MoreCurrencyUtils } from '../game/more_currency_dialog/MoreCurrencyUtils';
import * as ee from "../libraries/ee/index";
import {
    CardManager,
    EnergyManager,
    FeatureManager,
    IapResult,
    PvpManager,
    RewardUtils,
    SceneName,
    ShopManager,
    StoreItem,
    StoreManager,
    TrackAdsRewardWatch,
    TrackingManager,
    TrackResultIap,
    TrackResultItem,
    TrackResultSoftCurrency,
    TrackSinkType,
    TrackSourceType, VipManager,
    WatchAdsHelper,
} from "../manager/gm_manager";
import { NodeClipper } from '../utils/Clipper';
import { EffectHelper } from "../utils/EffectHelper";
import { CardCategory } from './common/CardCategory';
import { EnergyCategory } from "./common/EnergyCategory";
import { GoldCategory } from "./common/GoldCategory";
import { OfferCategory } from "./common/OfferCategory";
import { PvpTicketCategory } from './common/PvpTicketCategory';
import { RubyCategory } from "./common/RubyCategory";
import { TopHud } from "./common/TopHud";
import { IapShopCategory } from './IapShopCategory';
import { IapShopLayer } from './IapShopLayer';
import { ChestCategory } from "./common/ChestCategory";
import { NotEnoughCurrencyDialog } from "../game/more_currency_dialog/NotEnoughCurrencyDialog";
import { IAPUtils } from "../utils/IAPUtils";
import { VibrateManager } from "../manager/vibrate/VibrateManager";
import MiniShopCategory from "./shop/MiniShopCategory";
import { BottomHud } from "./common/BottomHud";
import { MenuScenePage } from "./MenuScene";
import {TimeUtils} from "../utils/TimeUtis";


const { ccclass, disallowMultiple, property } = cc._decorator;

export interface IapShopLayerController {
    onShopPackPressed(pack: number): void;
}

export type OnShopPackPressed = (pack: number) => void;

/** Used in editor. */
@ccclass
@disallowMultiple
class IapShopLayerImpl extends IapShopLayer {
    @property({ type: cc.ScrollView, visible: true })
    private readonly _scrollView: cc.ScrollView | null = null;

    @property([cc.Prefab])
    private readonly categoryPrefabs: cc.Prefab[] = [];

    @property(cc.Prefab)
    private readonly miniShopPrefab: cc.Prefab | null = null;

    private get scrollView(): cc.ScrollView {
        return gm.retrieveNull(this._scrollView);
    }

    private _topHud: TopHud | null = null;

    public get topHud(): TopHud {
        return gm.retrieveNull(this._topHud);
    }

    public set topHud(value: TopHud) {
        this._topHud = value;
    }

    private _bottomHud: BottomHud | null = null;


    private _cardCategory: CardCategory | null = null;
    private _chestCategory: ChestCategory | null = null;
    private _offerCategory: OfferCategory | null = null;
    private _rubyCategory: RubyCategory | null = null;
    private _goldCategory: GoldCategory | null = null;
    private _energyCategory: EnergyCategory | null = null;
    private _pvpTicketCategory: PvpTicketCategory | null = null;

    private _miniShopCategory: MiniShopCategory | null = null;

    private _dialogManager?: ee.DialogManager;

    private get cardCategory(): CardCategory {
        return gm.retrieveNull(this._cardCategory);
    }

    private get chestCategory(): ChestCategory {
        return gm.retrieveNull(this._chestCategory);
    }

    private get offerCategory(): OfferCategory {
        return gm.retrieveNull(this._offerCategory);
    }

    private get rubyCategory(): RubyCategory {
        return gm.retrieveNull(this._rubyCategory);
    }

    private get goldCategory(): GoldCategory {
        return gm.retrieveNull(this._goldCategory);
    }

    private get energyCategory(): EnergyCategory {
        return gm.retrieveNull(this._energyCategory);
    }

    private get pvpTicketCategory(): PvpTicketCategory {
        return gm.retrieveNull(this._pvpTicketCategory);
    }

    public get dialogManager(): ee.DialogManager {
        if (this._dialogManager === undefined) {
            throw Error('Item not set.');
        }
        return this._dialogManager;
    }

    public set dialogManager(value: ee.DialogManager) {
        this._dialogManager = value;
    }

    private get shopManager(): ShopManager {
        return ee.ServiceLocator.resolve(ShopManager);
    }

    private get storeManager(): StoreManager {
        return ee.ServiceLocator.resolve(StoreManager);
    }

    private get cardManager(): CardManager {
        return ee.ServiceLocator.resolve(CardManager);
    }

    /** Used to lock buy process. */
    private buyPromise?: Promise<void>;

    protected onLoad(): void {
        assert(this._scrollView !== null);
        // ee.AsyncManager.getInstance().add(IapShopLayerImpl.name, async () => {
        //     await this.initialize();
        //     this.updateRendering();
        //     this.scrollView.scrollToPercentVertical(10, 1);
        // });
    }

    public async init() {
        await this.initialize()
        this.updateRendering();
        this.scrollView.scrollToPercentVertical(10, 1);
    }

    protected onEnable(): void {
        this.scrollView.node.on('scrolling', this.onScrolling, this);
    }

    protected onDisable(): void {
        this.scrollView.node.off('scrolling', this.onScrolling, this);
    }

    public setTopHud(top: TopHud): this {
        this.topHud = top;
        return this;
    }

    public setBottomHud(bottomHud: BottomHud): this {
        this._bottomHud = bottomHud;
        const changePageCallback = this._bottomHud.listener.changed;
        this._bottomHud.listener.changed = ((sender: BottomHud, page: MenuScenePage) => {
            changePageCallback && changePageCallback(sender, page);
            if (page != MenuScenePage.Shop) {
                this.getMoreOffers(false);
            }

        })
        return this;
    }

    private async initialize(): Promise<void> {
        await this.initializeLayers();
        try {
            const t1 = ee.ServiceLocator.resolve(StoreManager).refreshMarketItems();
            const t2 = ee.sleep(3000);
            await Promise.race([t1, t2]);
        } catch (ex) {
            cc.log(`${ex}`);
        }
        this.initializeCardLayer();
        this.initializeChestLayer();
        this.initializeRubyLayer();
        this.initializeGoldCategory();
        this.initializeEnergyLayer();
        this.initializePvpTicketLayer();
        this.initializeOfferLayer();
        this.initializeMiniShop();
    }


    public getMoreOffers(getMore: boolean): void {

        // Thiết lập trạng thái hiển thị cho tất cả các danh mục
        const categories = [
            this._offerCategory,
            this._chestCategory,
            this._rubyCategory,
            this._energyCategory,
            this._cardCategory,
            this._goldCategory,
            // this._pvpTicketCategory
        ];

        // Cập nhật trạng thái hiển thị cho tất cả các danh mục
        categories.forEach(category => {
            category.node.active = getMore;
        });

        // Thiết lập trạng thái hiển thị cho mini shop
        this._miniShopCategory.node.active = !getMore;

        this.scheduleOnce(()=>{
            //this.scrollView.scrollToTop();

            // Áp dụng hiệu ứng cuộn nhẹ để kích hoạt cập nhật giao diện
            this.scrollView.scrollToOffset(cc.v2(0, -5), 1);
            this.scrollView.scrollToOffset(cc.v2(0, 5), 1);

            // Cập nhật layout
            this.scrollView.content.getComponent(cc.Layout).updateLayout();
        })
    }

    private async initializeLayers(): Promise<void> {
        const pvpManager = ee.ServiceLocator.resolve(PvpManager);

        // initialize minishop category first
        const node = cc.instantiate(this.miniShopPrefab);
        this.scrollView.content.addChild(node);
        const widgets = node.getComponentsInChildren(cc.Widget);
        widgets.forEach(item => item.updateAlignment());
        this._miniShopCategory = node.getComponent(MiniShopCategory);

        // initialize other layers
        const layers = this.categoryPrefabs.map((prefab, index) => {
            if (!pvpManager.isEnabled && index === 6) {
                return {} as cc.Node; // Silent warnings.
            }
            const node = cc.instantiate(prefab);
            this.scrollView.content.addChild(node);
            const widgets = node.getComponentsInChildren(cc.Widget);
            widgets.forEach(item => item.updateAlignment());
            return node;
        });

        this._offerCategory     /**/ = layers[0].getComponent(OfferCategory);
        this._offerCategory.onRemoveVipOffer = () => {
            this.scheduleOnce(() => {
                this.scrollView.scrollToOffset(cc.v2(0, this.scrollView.getScrollOffset().y + 1), 0.01); // Kéo nhẹ
                this.scheduleOnce(() => {
                    this.scrollView.scrollToOffset(cc.v2(0, this.scrollView.getScrollOffset().y - 1), 0.01); // Kéo về
                }, 0.01);
            }, 0);
        }

        this._chestCategory     /**/ = layers[1].getComponent(ChestCategory);
        this._rubyCategory      /**/ = layers[2].getComponent(RubyCategory);
        this._energyCategory    /**/ = layers[3].getComponent(EnergyCategory);
        this._cardCategory      /**/ = layers[4].getComponent(CardCategory);

        this._goldCategory      /**/ = layers[5].getComponent(GoldCategory);

        pvpManager.isEnabled && (this._pvpTicketCategory = layers[6].getComponent(PvpTicketCategory));
    }

    private initializeCardLayer(): void {
        this.cardCategory.setController({
            buy: packIndex => this.buyCardPack(packIndex),
            reroll: () => this.rerollCard(),
        });
        this.cardCategory.setInfo(this.shopManager.getCardPacks());
    }

    private initializeChestLayer(): void {

    }

    private initializeRubyLayer(): void {
        this.rubyCategory.setCallback(packIndex => this.buyPack(() => this.buyRubyPack(packIndex)));
        this.rubyCategory.setInfo(this.shopManager.getRubyPacks());
    }

    private initializeGoldCategory(): void {
        this.goldCategory.setCallback(packIndex => this.buyPack(() => this.buyGoldPack(packIndex)));
        this.goldCategory.setInfo(this.shopManager.getGoldBarPacks());
    }

    private initializeEnergyLayer(): void {
        this.energyCategory.setCallback(packIndex => this.buyPack(() => this.buyEnergyPack(packIndex)));
        this.energyCategory.setInfo(this.shopManager.getEnergyPacks());
    }

    private initializePvpTicketLayer(): void {
        const pvpManager = ee.ServiceLocator.resolve(PvpManager);
        if (!pvpManager.isEnabled) {
            return;
        }
        this.pvpTicketCategory.setCallback(packIndex => this.buyPack(() => this.buyPvpTicketPack(packIndex)));
        this.pvpTicketCategory.setInfo(this.shopManager.getCommonPacks(StoreItem.PvpTicket));
    }

    private initializeOfferLayer(): void {
        this.offerCategory.setController({
            onShopPackPressed: packIndex => this.buyPack(() => IAPUtils.buyOfferPack(this.shopManager.getOfferPacks()[packIndex], SceneName.SceneMenuShop)),
        });

        this.offerCategory.setInfo(this.shopManager.getOfferPacks());
    }

    public getNodeByCategory(category: IapShopCategory): cc.Node {
        const dict: { [key: string]: () => cc.Node } = {
            [IapShopCategory.Card   /*      */]: () => this.cardCategory.node,
            [IapShopCategory.Offer  /*      */]: () => this.offerCategory.node,
            [IapShopCategory.Chest  /*      */]: () => this.chestCategory.node,
            [IapShopCategory.Ruby   /*      */]: () => this.rubyCategory.node,
            [IapShopCategory.Gold   /*      */]: () => this.goldCategory.node,
            [IapShopCategory.Energy /*      */]: () => this.energyCategory.node,
            [IapShopCategory.PvpTicket /*   */]: () => this.pvpTicketCategory.node,
        };
        return dict[category]();
    }

    public showCategory(category: IapShopCategory): void {

        this.scrollView.stopAutoScroll();
        const item = this.getNodeByCategory(category);
        if (!item.active) {
            this.getMoreOffers(true)
        }
        this.scrollView.scrollToOffset(cc.v2(0, Math.abs(item.getBoundingBox().yMax)), 0.5);
        this.updateRendering();
    }

    private async buyPack(callback: () => Promise<void>): Promise<void> {
        if (this.buyPromise) {
            return this.buyPromise;
        }
        this.buyPromise = callback();
        await this.buyPromise;
        this.buyPromise = undefined;
    }

    /** Purchases the specified card pack. */
    private async buyCardPack(index: number): Promise<void> {
        const pack = this.shopManager.getCardPacks();
        const day = pack.days[pack.currentDay];
        const item = day.items[index];

        const cardId = item.reward.raw.subType;
        const card = this.cardManager.findCardById(cardId);
        const cost = this.cardManager.getCardRarityPrice(card.rarity) * item.amount;

        // Change reward (once).
        item.reward.quantity = item.amount;
        const rewards = [item.reward]; // Single reward.

        const purchaseCallback = async () => {
            const dialog = await RewardUtils.showRewardDialog(
                rewards, this.dialogManager, SceneName.SceneMenuShop);
            dialog.setTitleKey('text_card');
            await new Promise<void>(resolve => dialog.onDidHide(() => {
                // Mark user bought the offer pack to reset offer time
                pack.buy(index);
                resolve();
            }));
        };
        // Can only purchase with gold.
        /* if (true costType === StoreItem.Gold ) { */
        const balance = this.storeManager.getItemBalance(StoreItem.Gold);
        if (cost <= balance) {
            this.storeManager.addItemBalance(StoreItem.Gold, -cost);
            await purchaseCallback();
            ee.ServiceLocator.resolve(TrackingManager).trackEventSinkSoftCurrency(
                SceneName.SceneMenuShop, TrackResultSoftCurrency.Bought, TrackSinkType.GoldBar, cost);
            ee.ServiceLocator.resolve(TrackingManager).trackConversionBuyCard();
        } else {
            let promise: Promise<void>;
            promise = MoreCurrencyUtils.show({
                dialogManager: this.dialogManager,
                sceneName: SceneName.DialogStoryBooster,
                storeItem: StoreItem.Gold,
                needed: cost
            });

            await promise;
        }
        /* } */

        /*else if (costType === StoreItem.Ruby) {
            const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
            await MoreCurrencyUtils.showConfirmCard({
                dialogManager: this.dialogManager,
                storeManager: this.storeManager,
                trackingManager,
                sceneName: SceneName.SceneMenuShop,
                packIndex: index,
                itemCard: card,
                cost,
                amount: item.amount,
                onPurchased: purchaseCallback,
                onNeedMoreRuby: () => this.showCategory(IapShopCategory.Ruby),
            });
        } else {
            assert(false);
        }
        */
    }

    /** Purchases a ruby pack. */
    private async buyRubyPack(index: number, srcNode?: cc.Node): Promise<void> {
        const sceneName = SceneName.SceneMenuShop;
        const storeItem = StoreItem.Ruby;
        const packs = this.shopManager.getRubyPacks();
        const pack = packs[index];
        if (pack.iap === "") {
            const rewarded = await this.showVideoAds(TrackAdsRewardWatch.ShopRuby);
            if (rewarded) {
                this.showFlyingCurrencyAnimation(storeItem, index, srcNode).then(() => {
                    this.storeManager.addItemBalance(storeItem, pack.ruby);
                });
                ee.ServiceLocator.resolve(TrackingManager).trackEventSourceSoftCurrency(
                    sceneName, TrackResultSoftCurrency.Bought, TrackSourceType.Ruby, pack.ruby);
                ee.ServiceLocator.resolve(TrackingManager).trackConversionWatchAdSoftCurrencyAtShop();
            }
        } else {
            const buyCallback = async (result: IapResult) => {

                if (result === IapResult.Succeeded) {
                    ee.ServiceLocator.resolve(VibrateManager).makeVibrate();
                    await this.showFlyingCurrencyAnimation(StoreItem.Ruby, index, srcNode);
                    await this.showVipRewardDialog();
                    ee.ServiceLocator.resolve(TrackingManager).trackConversionBuyRubyPackage();
                    ee.ServiceLocator.resolve(TrackingManager).trackEventBuyIAPPackage(
                        sceneName, result, pack.name, pack.ruby, "ruby");
                } else {
                    ee.ServiceLocator.resolve(TrackingManager).trackEventBuyIAPPackage(
                        sceneName, result, pack.name, pack.ruby, "ruby");
                    AlertDialog.create().then(dialog => {
                        dialog.setKey("iap_purchase_failed");
                        dialog.show(this.dialogManager);
                    })
                }
            };

            const dialog = await WaitingDialog.create();
            dialog.onDidShow(async () => {
                const result = await this.storeManager.buyItem(pack.name);
                dialog.onDidHide(async () => {
                    await buyCallback(result);
                });
                dialog.hide();
            });
            const promise = new Promise<void>(resolve => dialog.onDidHide(() => resolve()));
            dialog.show(this.dialogManager);
            await promise;
        }
    }

    /** Purchases a gold pack. */
    private async buyGoldPack(index: number, srcNode?: cc.Node): Promise<void> {
        const sceneName = SceneName.SceneMenuShop;
        const storeItem = StoreItem.Gold;
        const packs = this.shopManager.getGoldBarPacks();
        const pack = packs[index];
        if (pack.iap === "") {
            const rewarded = await this.showVideoAds(TrackAdsRewardWatch.ShopGold);
            if (rewarded) {
                this.showFlyingCurrencyAnimation(storeItem, index, srcNode).then(() => {
                    this.storeManager.addItemBalance(storeItem, pack.quantity);
                });
                ee.ServiceLocator.resolve(TrackingManager).trackEventSourceSoftCurrency(
                    sceneName, TrackResultSoftCurrency.Bought, TrackSourceType.GoldBar, pack.quantity);
                ee.ServiceLocator.resolve(TrackingManager).trackConversionWatchAdSoftCurrencyAtShop();
            }
        } else {
            const buyCallback = async (result: IapResult) => {
                if (result === IapResult.Succeeded) {
                    ee.ServiceLocator.resolve(VibrateManager).makeVibrate();
                    await this.showFlyingCurrencyAnimation(StoreItem.Gold, index, srcNode);
                    await this.showVipRewardDialog();
                    ee.ServiceLocator.resolve(TrackingManager).trackConversionBuyGoldPackage();
                    ee.ServiceLocator.resolve(TrackingManager).trackEventBuyIAPPackage(
                        sceneName, result, pack.name, pack.quantity, "gold_bar");
                } else {
                    ee.ServiceLocator.resolve(TrackingManager).trackEventBuyIAPPackage(
                        sceneName, result, pack.name, pack.quantity, "gold_bar");
                    AlertDialog.create().then(dialog => {
                        dialog.setKey("iap_purchase_failed");
                        dialog.show(this.dialogManager);
                    })
                }
            };

            const dialog = await WaitingDialog.create();
            dialog.onDidShow(async () => {
                const result = await this.storeManager.buyItem(pack.name);
                dialog.onDidHide(async () => {
                    await buyCallback(result);
                });
                dialog.hide();
            });
            const promise = new Promise<void>(resolve => dialog.onDidHide(() => resolve()));
            dialog.show(this.dialogManager);
            await promise;
        }
    }

    /** Purchases an energy pack. */
    private async buyEnergyPack(index: number, srcNode?: cc.Node): Promise<void> {
        const sceneName = SceneName.SceneMenuShop;
        const storeItem = StoreItem.Energy;
        const packs = this.shopManager.getEnergyPacks();
        const pack = packs[index];
        if (pack.iap === "") {
            const rewarded = await this.showVideoAds(TrackAdsRewardWatch.ShopEnergy);
            if (rewarded) {
                this.showFlyingCurrencyAnimation(storeItem, index, srcNode).then(() => {
                    this.storeManager.addItemBalance(storeItem, pack.quantity);
                });
                ee.ServiceLocator.resolve(TrackingManager).trackEventSourceSoftCurrency(
                    sceneName, TrackResultSoftCurrency.Bought, TrackSourceType.Energy, pack.quantity);
                ee.ServiceLocator.resolve(TrackingManager).trackConversionReceiveEnergyByWatchAds();
            }
        } else {
            const buyCallback = async (result: IapResult) => {
                if (result === IapResult.Succeeded) {
                    ee.ServiceLocator.resolve(VibrateManager).makeVibrate();
                    const energyManager = ee.ServiceLocator.resolve(EnergyManager);
                    await this.showFlyingCurrencyAnimation(StoreItem.Energy, index, srcNode);
                    await this.showVipRewardDialog();
                    ee.ServiceLocator.resolve(TrackingManager).trackConversionBuyEnergy();
                    ee.ServiceLocator.resolve(TrackingManager).trackEventBuyIAPPackage(
                        sceneName, result, pack.name, pack.quantity, "energy");
                    if (pack.unlimited > 0) {
                        energyManager.increaseUnlimitedDuration(pack.unlimited * 3600);
                    }
                } else {
                    ee.ServiceLocator.resolve(TrackingManager).trackEventBuyIAPPackage(
                        sceneName, result, pack.name, pack.quantity, "energy");
                    AlertDialog.create().then(dialog => {
                        dialog.setKey("iap_purchase_failed");
                        dialog.show(this.dialogManager);
                    })
                }
            };
            const dialog = await WaitingDialog.create();
            dialog.onDidShow(async () => {
                const result = await this.storeManager.buyItem(pack.name);
                dialog.onDidHide(async () => {
                    await buyCallback(result);
                });
                dialog.hide();
            });
            const promise = new Promise<void>(resolve => dialog.onDidHide(() => resolve()));
            dialog.show(this.dialogManager);
            await promise;
        }
    }

    /** Purchases a PvP ticket pack. */
    private async buyPvpTicketPack(index: number): Promise<void> {
        const sceneName = SceneName.SceneMenuShop;
        const storeItem = StoreItem.PvpTicket;
        const packs = this.shopManager.getCommonPacks(storeItem);
        const pack = packs[index];
        if (pack.price === 0) {
            const rewarded = await this.showVideoAds(TrackAdsRewardWatch.ShopVipTicket);
            if (rewarded) {
                this.showFlyingCurrencyAnimation(storeItem, index).then(() => {
                    this.storeManager.addItemBalance(storeItem, pack.quantity);
                });
                ee.ServiceLocator.resolve(TrackingManager).trackEventSourceSoftCurrency(
                    sceneName, TrackResultSoftCurrency.Bought, TrackSourceType.PvpTicket, pack.quantity);
            }
        } else {
            const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
            await MoreCurrencyUtils.showConfirmGold({
                dialogManager: this.dialogManager,
                storeManager: this.storeManager,
                trackingManager,
                sceneName,
                pack,
                onPurchased: async () => {
                    this.showFlyingCurrencyAnimation(storeItem, index).then(() => {
                        this.storeManager.addItemBalance(storeItem, pack.quantity);
                        this.showVipRewardDialog();
                    });
                },
                onNeedMoreRuby: () => this.showCategory(IapShopCategory.Ruby),
            });
        }
    }

    private async showVipRewardDialog(): Promise<boolean> {
        let sceneName = cc.director.getScene().name;
        if (sceneName !== "game_scene") {
            const vipManager = ee.ServiceLocator.resolve(VipManager);
            const showDialog = vipManager.checkShowRewardDialog();
            if (showDialog) {
                await vipManager.waitForEndEvent();
                return true;
            }
            return false
        }
        return false;
    }

    /**
     * Function not used
     * @private
     */
    private async rerollCard(): Promise<void> {
        const sceneName = SceneName.SceneMenuShop;
        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
        const currency = this.storeManager.getItemBalance(StoreItem.Ruby);
        if (currency >= 200) {
            const buyCallback = async () => {
                trackingManager.trackEventSinkIap(
                    sceneName, TrackResultIap.Bought, TrackSinkType.Ruby, `reroll_card`, 200);
                trackingManager.trackEventSourceItem(
                    sceneName, TrackResultItem.BuyDone, TrackSourceType.Card, `reroll_card`, 1);

                this.storeManager.addItemBalance(StoreItem.Ruby, -200);
                this.shopManager.getCardPacks().reroll();
            };

            const dialog = await ConfirmRerollDialog.create();

            let pressed = false;
            dialog.setPressedCallback(() => {
                pressed = true;
                dialog.hide();
            });
            const promise = new Promise<void>(resolve =>
                dialog.onDidHide(async () => {
                    pressed && await buyCallback();
                    resolve();
                }));
            dialog.show(this.dialogManager);
        } else {
            trackingManager.trackEventSourceItem(
                sceneName, TrackResultItem.Error, TrackSourceType.Card, `reroll_card`, 0);

            let promise: Promise<void>;
            if (ee.ServiceLocator.resolve(FeatureManager).moreCurrencyIsPack) { // Done
                promise = MoreCurrencyUtils.show({
                    dialogManager: this.dialogManager,
                    sceneName: SceneName.DialogStoryBooster,
                    storeItem: StoreItem.Ruby,
                    needed: 200
                });
            } else {
                const dialog = await NotEnoughCurrencyDialog.create();
                promise = new Promise<void>(resolve => dialog.onDidHide(() => resolve()));
                dialog
                    .setInfo(StoreItem.Ruby, null);
                dialog.show(this.dialogManager);
            }

            await promise;
        }
    }

    private showFlyingCurrencyAnimation(type: StoreItem, pack: number, srcNode?: cc.Node): Promise<void> {
        const dstNode = this.topHud.getNodeByItem(type);
        if (!srcNode) {
            srcNode = this.getPackNode(type, pack);
        }
        const subTypeStr = RewardUtils.parserStoreItemString(type);
        return EffectHelper.showFlyingRewardFromRawReward({ type: 'store', subType: subTypeStr, value: 10 },
            srcNode, dstNode);
    }

    private getPackNode(type: StoreItem, pack: number): cc.Node {
        const cateType = this.getCategoryType(type);
        const cateNode = this.getNodeByCategory(cateType);

        switch (type) {
            case StoreItem.Ruby: {
                const cateRuby = cateNode.getComponent(RubyCategory);
                return cateRuby.getPackNode(pack);
            }
            case StoreItem.Gold: {
                const goldCate = cateNode.getComponent(GoldCategory);
                return goldCate.getPackNode(pack);
            }
            case StoreItem.Energy: {
                const energyCate = cateNode.getComponent(EnergyCategory);
                return energyCate.getPackNode(pack);
            }
            // case StoreItem.Ticket: {
            //     const ticketCate = cateNode.getComponent(TicketCategory);
            //     return ticketCate.getPackNode(pack);
            // }
            case StoreItem.PvpTicket: {
                const pvpTicketCate = cateNode.getComponent(PvpTicketCategory);
                return pvpTicketCate.getPackNode(pack);
            }
            default: {
                return new cc.Node();
            }
        }
    }

    private getCategoryType(type: StoreItem): IapShopCategory {
        let ret = IapShopCategory.Ruby;
        switch (type) {
            case StoreItem.Ruby: {
                ret = IapShopCategory.Ruby;
                break;
            }
            case StoreItem.Gold: {
                ret = IapShopCategory.Gold;
                break;
            }
            case StoreItem.Energy: {
                ret = IapShopCategory.Energy;
                break;
            }
            // case StoreItem.: {
            //     ret = IapShopCategory.Ticket;
            //     break;
            // }
            case StoreItem.PvpTicket: {
                ret = IapShopCategory.PvpTicket;
                break;
            }
        }
        return ret;
    }

    /** Watches a rewarded video, return whether the user was rewarded or not. */
    private async showVideoAds(adsReward: TrackAdsRewardWatch): Promise<boolean> {
        return await new Promise(resolve => {
            (new WatchAdsHelper(SceneName.SceneMenuShop, adsReward)).onSuccess(() => {
                resolve(true);
            }).onNoAds(async () => {
                const dialog = await NoAdsDialog.create();
                dialog.onDidHide(() => resolve(false));
                dialog.show(this.dialogManager);
            }).start();
        });
    }

    private onScrolling(): void {
        this.updateRendering();
    }

    private updateRendering(): void {
        // Clip.
        const content = this.scrollView.content;
        const clipper = new NodeClipper(content.parent).depth(3).multi(true);
        clipper.clip();
    }

    private initializeMiniShop() {
        this._miniShopCategory.Controller = {
            EnergyController: {
                infos: this.shopManager.getEnergyPacks(),
                OnPressed: (pack: number, srcNode?: cc.Node) => {
                    this.buyPack(() => this.buyEnergyPack(pack, srcNode)).then();
                }

            },
            RubyController: {
                infos: this.shopManager.getRubyPacks(),
                OnPressed: (pack: number, srcNode?: cc.Node) => {
                    this.buyPack(() => this.buyRubyPack(pack, srcNode)).then();
                }
            },
            OfferController: {
                infos: this.shopManager.getOfferPacks(),
                OnPressed: (packIndex: number, srcNode?: cc.Node) => {
                    this.buyPack(() => IAPUtils.buyOfferPack(this.shopManager.getOfferPacks()[packIndex], SceneName.SceneMenuShop))
                }
            },
            GoldController: {
                infos: this.shopManager.getGoldBarPacks(),
                OnPressed: (pack: number, srcNode?: cc.Node) => {
                    this.buyPack(() => this.buyGoldPack(pack, srcNode)).then();
                }
            },
            onGetMoreOfferPressed: () => {
                this.getMoreOffers(true);
            }
        }
        this._miniShopCategory.initialize().then();
    }
}
