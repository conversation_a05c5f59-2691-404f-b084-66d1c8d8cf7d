import { PrivacyDialog } from '../dialog/PrivacyDialog';
import * as gm from '../engine/gm_engine';
import * as ee from '../libraries/ee/index';
import * as ee_x from '@senspark/ee-x';
import {
    AudioManager,
    AutoMineManager,
    ConfigDefaults,
    ConfigKey,
    ConfigManager,
    CrashlyticManager,
    crashlytics,
    CreateNullSocialManager,
    DataManager,
    DefaultAchievementManager,
    DefaultAdsManager,
    DefaultAppLifeCycleManager,
    DefaultAppManager,
    DefaultAudioManager,
    DefaultBoosterStorageManager,
    DefaultCardManager,
    DefaultChestManager,
    DefaultCrashlyticManager,
    DefaultDailyRewardManager,
    DefaultDataManager,
    DefaultEnergyManager,
    DefaultFeatureManager,
    DefaultLevelManager,
    DefaultLogManager,
    DefaultNotificationManager,
    DefaultPrizeWheelManager,
    DefaultPvpManager,
    DefaultPvpTicketManager,
    DefaultRatingManager,
    DefaultRewardManager,
    DefaultSceneManager,
    DefaultShopManager,
    DefaultSpecialRewardManager,
    DefaultStoreManager,
    DefaultTimeManager,
    DefaultTutorialManager,
    DefaultUserProfileManager,
    DefaultVipManager,
    FirebaseAnalyticsManager,
    FirebaseConfigManager, LeaderboardManager,
    LevelManager,
    LocalDataStorage,
    MultiAnalyticsManager,
    NotificationHandler,
    NullAnalyticsManager,
    NullCrashlyticManager,
    NullNotificationManager,
    NullSocialManager,
    SceneManager,
    SceneName,
    SessionConfigManager,
    SimulatedAdsManager,
    SimulatedAppManager,
    SimulatedStoreManager,
    SoundType,
    StoreManager,
    TimeManager,
    TrackingManager,
    TrackSourceType,
    TrackSourceTypeReason,
} from "../manager/gm_manager";
import { JsbUtils } from '../utils/JsbUtils';
import { NativeCall } from '../utils/NativeCall';
import { PlatformUtils } from '../utils/PlatformUtils';
import { MenuScene } from './MenuScene';
import { CostCenterManager } from "../manager/analytics/CostCenterManager";
import { DefaultCheatManager } from "../manager/cheat/DefaultCheatManager";
import { SceneUtils } from "../manager/scene/SceneUtils";
import { CardRewardPerLevelManagerImpl } from "../manager/card/CardRewardPerLevelManager";
import { DefaultOfferManager } from "../manager/offer/DefaultOfferManager";
import { GameServerManager } from "../manager/game_server/GameServerManager";
import { LevelLoaderManager } from "../manager/level/LevelLoaderManager";
import { LevelCloudSyncer } from "../manager/level/LevelCloudSyncer";
import { defaultServerConfig } from "../manager/level/DefaultServerConfig";
import { IGameServerManager } from "../manager/game_server/IGameServerManager";
import ILevelCloudSyncer from "../manager/level/ILevelCloudSyncer";
import { NewDailyQuestManager } from "../manager/daily_quest/NewDailyQuestManager";
import { DefaultHiddenTempleManager } from "./hidden_temple/HiddenTempleManager";
import { FirebaseAuthenManager } from "../manager/authentication/FirebaseAuthenManager";
import { DefaultVibrateManager } from "../manager/vibrate/DefaultVibrateManager";
import { NullVibrateManager } from "../manager/vibrate/NullVibrateManager";
import { DefaultPiggyBankManager } from "./PiggyBank/PiggyBankManager";
import { DefaultLeaderboardManager } from "../manager/leaderboard/DefaultLeaderboardManager";
import {PlayPassManager, PlayPassManagerImpl} from "./play_pass/PlayPassManager";
import DefaultChatManager from "../team/chat/Manager/DefaultChatManager";
import {ChatUserCacheManager} from "../team/chat/Manager/ChatUserCacheManager";
import WebSocketChatNetworkManager from "../team/chat/Manager/WebSocketChatNetworkManager";
import {DefaultMyTeamManager} from "../manager/team/DefaultMyTeamManager";

const { ccclass, disallowMultiple, property } = cc._decorator;

@ccclass
@disallowMultiple
export class SplashScene extends cc.Component {
    @property({ type: cc.Label, visible: true })
    private readonly _versionName: cc.Label | null = null;

    @property({ type: cc.Label, visible: true })
    private readonly _versionCode: cc.Label | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _versionInfo: cc.Node | null = null;

    @property({ type: ConfigDefaults, visible: true })
    private readonly _configDefaults: ConfigDefaults | null = null;

    private _dialogManager?: ee.DialogManager;

    private concurrentDownloads = 0;

    /** Gets the current dialog manager associated with this scene. */
    private get dialogManager(): ee.DialogManager {
        return gm.retrieveUndefined(this._dialogManager);
    }

    private get versionName(): cc.Label {
        return gm.retrieveNull(this._versionName);
    }

    private get versionCode(): cc.Label {
        return gm.retrieveNull(this._versionCode);
    }

    private get versionInfo(): cc.Node {
        return gm.retrieveNull(this._versionInfo);
    }

    private switchScene = false;

    protected onLoad(): void {
        ee_x.PluginManager.initializePlugins();
        cc.debug.setDisplayStats(false);
        // fixBmFont();
        // this.printInfo();
        this._dialogManager = new ee.DefaultDialogManager(this.node);
        this.initialize().then();
    }

    private showGameVersion(): void {
        const winSize = cc.winSize;
        this.versionName.string = "";// JsbUtils.getVersionName();
        this.versionCode.string = "";// JsbUtils.getVersionCode();
        this.versionInfo.setPosition(-winSize.width / 2.1, -winSize.height / 2.4);
    }

    private printInfo(): void {
        cc.log(`isNative = ${cc.sys.isNative}`);
        cc.log(`isBrowser = ${cc.sys.isBrowser}`);
        cc.log(`isMobile = ${cc.sys.isMobile}`);
        cc.log(`platform = ${cc.sys.platform}`);
        cc.log(`languageCode = ${cc.sys.languageCode}`);
        cc.log(`language = ${cc.sys.language}`);
        cc.log(`os = ${cc.sys.os}`);
        cc.log(`osVersion = ${cc.sys.osVersion}`);
        cc.log(`osMainVersion = ${cc.sys.osMainVersion}`);
        cc.log(`browserType = ${cc.sys.browserType}`);
        cc.log(`browserVersion = ${cc.sys.browserVersion}`);
        const canvasSize = cc.view.getCanvasSize();
        cc.log(`canvasSize = ${canvasSize.width} ${canvasSize.height}`);
        const designResolutionSize = cc.view.getDesignResolutionSize();
        cc.log(`designResolutionSize = ${designResolutionSize.width} ${designResolutionSize.height}`);
        const frameSize = cc.view.getFrameSize();
        cc.log(`frameSize = ${frameSize.width} ${frameSize.height}`);
        cc.log(`devicePixelRatio = ${cc.view.getDevicePixelRatio()}`);
        const visibleSize = cc.view.getVisibleSize();
        cc.log(`visibleSize = ${visibleSize.width} ${visibleSize.height}`);
        const visibleSizeInPixels = cc.view.getVisibleSizeInPixel();
        cc.log(`visibleSizeInPixels = ${visibleSizeInPixels.width} ${visibleSizeInPixels.height}`);
    }

    private async initialize(): Promise<void> {
        this.optimizeDynamicAtlas();
        this.optimizeDownloaders();
        this.initializeBaseManagers();
        await this.initializeConfigManager();
        await this.initializePostManagers();
        ee.ServiceLocator.resolve(TrackingManager).trackConversionTimePlayed();
        ee.ServiceLocator.resolve(TrackingManager).trackConversionAppOpen();
    }

    private optimizeDynamicAtlas(): void {
        const manager = cc.dynamicAtlasManager;
        if (CC_DEV) {
            manager.enabled = true;
            manager.maxAtlasCount = 20;
            // manager.maxFrameSize = 2048;
            // (manager as any).minFrameSize = 1;
        } else {
            // Disable dynamic atlas: bmfont frames are not referenced to the dynamic atlases.
            manager && (manager.enabled = false);
        }
    }

    private optimizeDownloaders(): void {
        cc.log(`optimizeDownloaders`);

        this.concurrentDownloads = cc.macro.DOWNLOAD_MAX_CONCURRENT;

        // Adjust concurrent downloads for smooth loading.
        cc.macro.DOWNLOAD_MAX_CONCURRENT = 3;
        if (PlatformUtils.isInstantGame()) {
            cc.macro.DOWNLOAD_MAX_CONCURRENT = 6;
        }
    }

    private initializeBaseManagers(): void {
        const dataManager = new DefaultDataManager(new LocalDataStorage());
        const timeManager = new DefaultTimeManager();

        ee.ServiceLocator.register(dataManager);
        ee.ServiceLocator.register(timeManager);
    }

    private async createConfigManager(): Promise<ConfigManager> {
        let isMobile = PlatformUtils.isMobileGame();
        const cfg = this._configDefaults;
        if (isMobile) {

            const defaultValues = cfg.remote_config_defaults.json;

            const dataManager = ee.ServiceLocator.resolve(DataManager);
            const timeManager = ee.ServiceLocator.resolve(TimeManager);
            const configManager = new FirebaseConfigManager(
                dataManager,
                timeManager,
                defaultValues);
            await configManager.initialize();
            return configManager;
        } else {
            const configManager = new SessionConfigManager();
            configManager.setDefaultValue("card_info", JSON.parse(cfg.remote_config_defaults.json["card_info"]));
            configManager.setDefaultValue("prize_wheel", JSON.parse(cfg.remote_config_defaults.json["prize_wheel"]));
            configManager.setDefaultValue("daily_reward_v2", JSON.parse(cfg.remote_config_defaults.json["daily_reward_v2"]));
            configManager.setDefaultValue("shop_info_v4", JSON.parse(cfg.remote_config_defaults.json["shop_info_v4"]));
            configManager.setDefaultValue("shop_config", JSON.parse(cfg.remote_config_defaults.json["shop_config"]));
            configManager.setDefaultValue("chest_info", JSON.parse(cfg.remote_config_defaults.json["chest_info"]));
            configManager.setDefaultValue("achievement_info_v2", JSON.parse(cfg.remote_config_defaults.json["achievement_info_v2"]));
            configManager.setDefaultValue("booster_price_v2", JSON.parse(cfg.remote_config_defaults.json["booster_price_v2"]));
            configManager.setDefaultValue("experience", JSON.parse(cfg.remote_config_defaults.json["experience"]));
            configManager.setDefaultValue("level_boosters", JSON.parse(cfg.remote_config_defaults.json["level_boosters"]));
            configManager.setDefaultValue("map_info", JSON.parse(cfg.remote_config_defaults.json["map_info"]));
            configManager.setDefaultValue("event_map_info", JSON.parse(cfg.remote_config_defaults.json["event_map_info"]));
            configManager.setDefaultValue("pvp_info", JSON.parse(cfg.remote_config_defaults.json["pvp_info"]));
            configManager.setDefaultValue("card_upgrade", JSON.parse(cfg.remote_config_defaults.json["card_upgrade"]));
            configManager.setDefaultValue("vip_info", JSON.parse(cfg.remote_config_defaults.json["vip_info"]));
            configManager.setDefaultValue("session_ads_test", cfg.remote_config_defaults.json["session_ads_test"]);
            configManager.setDefaultValue("ads_config_v3", JSON.parse(cfg.remote_config_defaults.json["ads_config_v3"]));
            configManager.setDefaultValue('ads_config_v3_test', JSON.parse(cfg.remote_config_defaults.json["ads_config_v3_test"]));
            configManager.setDefaultValue("pvp_ticket_info", JSON.parse(cfg.remote_config_defaults.json["pvp_ticket_info"]));
            configManager.setDefaultValue("energy_info", JSON.parse(cfg.remote_config_defaults.json["energy_info"]));
            configManager.setDefaultValue("test_device_ids", JSON.parse(cfg.remote_config_defaults.json["test_device_ids"]));
            configManager.setDefaultValue("special_reward", JSON.parse(cfg.remote_config_defaults.json["special_reward"]));
            configManager.setDefaultValue("rating_config", JSON.parse(cfg.remote_config_defaults.json["rating_config"]));
            configManager.setDefaultValue('auto_mine_config', JSON.parse(cfg.remote_config_defaults.json["auto_mine_config"]));
            configManager.setDefaultValue('player_info', JSON.parse(cfg.remote_config_defaults.json["player_info"]));
            configManager.setDefaultValue('ads_interstitial_settings', cfg.remote_config_defaults.json["ads_interstitial_settings"]);
            configManager.setDefaultValue('card_per_lv_complete', cfg.remote_config_defaults.json["card_per_lv_complete"]);
            configManager.setDefaultValue('chest_per_area', cfg.remote_config_defaults.json["chest_per_area"]);
            configManager.setDefaultValue('feature_config', cfg.remote_config_defaults.json["feature_config"]);
            configManager.setDefaultValue('vip_offer_info_v2', JSON.parse(cfg.remote_config_defaults.json["vip_offer_info_v2"]));
            configManager.setDefaultValue('new_daily_quest', JSON.parse(cfg.remote_config_defaults.json["new_daily_quest"]));
            configManager.setDefaultValue('character_map_info', JSON.parse(cfg.remote_config_defaults.json["character_map_info"]));
            configManager.setDefaultValue('hidden_temple_game_config', JSON.parse(cfg.remote_config_defaults.json["hidden_temple_game_config"]));
            configManager.setDefaultValue('booster_area_level_unlock', JSON.parse(cfg.remote_config_defaults.json["booster_area_level_unlock"]));
            configManager.setDefaultValue('piggy_bank_config', JSON.parse(cfg.remote_config_defaults.json["piggy_bank_config"]));
            configManager.setDefaultValue('play_pass_config', JSON.parse(cfg.remote_config_defaults.json["play_pass_config"]));
            configManager.setDefaultValue('chat_config', JSON.parse(cfg.remote_config_defaults.json["chat_config"]));
            configManager.setDefaultValue('discord_link', cfg.remote_config_defaults.json["discord_link"]);
            configManager.setDefaultValue('ads_config_max', cfg.remote_config_defaults.json["ads_config_max"]);

            configManager.setDefaultValue(ConfigKey.LEVELS_OVERRIDE, cfg.levels_override.json);
            return configManager;
        }
    }

    private async initializeConfigManager(): Promise<void> {
        cc.log(`initializeConfigManager`);
        const configManager = await this.createConfigManager();
        ee.ServiceLocator.register(configManager);
    }

    private async initializePostManagers(): Promise<void> {
        console.log("start initializePostManagers")
        const configManager = ee.ServiceLocator.resolve(ConfigManager);
        const dataManager = ee.ServiceLocator.resolve(DataManager);
        const timeManager = ee.ServiceLocator.resolve(TimeManager);

        const logManager = new DefaultLogManager();
        const cardManager = new DefaultCardManager(configManager, dataManager, logManager);
        const vipManager = new DefaultVipManager(configManager, dataManager, timeManager);

        const analyticsManager = new MultiAnalyticsManager();
        let storeManager: StoreManager;
        let crashlyticsManager: CrashlyticManager;

        if (PlatformUtils.isMobileGame()) {
            storeManager = new DefaultStoreManager(configManager, dataManager, analyticsManager);
        } else {
            storeManager = new SimulatedStoreManager(dataManager);
        }

        const boosterStorageManager =
            new DefaultBoosterStorageManager(configManager, dataManager, storeManager, logManager);
        const rewardManager = new DefaultRewardManager(boosterStorageManager, cardManager, storeManager);
        const levelManager = new DefaultLevelManager(configManager, dataManager, rewardManager, logManager);

        if (PlatformUtils.isMobileGame()) {
            const appIdentity = ee_x.Platform.getApplicationId();
            analyticsManager.addManager(new FirebaseAnalyticsManager(configManager, appIdentity));
            //analyticsManager.addManager(new AppsFlyerManager(appIdentity));

            const nowArea = levelManager.getCurrentStoryArea();
            const nowLevel = levelManager.getCurrentStoryLevel();
            const areaInfo = levelManager.getStoryAreaInfo(nowArea);
            const levelInfo = areaInfo.levels[nowLevel];
            const worldLevel = levelInfo.worldIndex;
            analyticsManager.addManager(new CostCenterManager(`${worldLevel + 1}`, 'Story'));

            crashlyticsManager = new DefaultCrashlyticManager();
        } else {
            analyticsManager.addManager(new NullAnalyticsManager());
            crashlyticsManager = new NullCrashlyticManager();
        }

        const playPassManager = new PlayPassManagerImpl(configManager, dataManager, timeManager, rewardManager);
        const autoMineManager = new AutoMineManager(configManager, dataManager, timeManager);
        const profileManager = new DefaultUserProfileManager(configManager, dataManager, levelManager, playPassManager);

        const shopManager = new DefaultShopManager(
            cardManager, configManager, dataManager, profileManager, rewardManager, timeManager);
        const dailyRewardManager = new DefaultDailyRewardManager(
            configManager, dataManager, rewardManager, timeManager);
        const pvpManager = new DefaultPvpManager(configManager, dataManager, rewardManager);
        const chestManager = new DefaultChestManager(configManager, cardManager, storeManager, timeManager, dataManager, rewardManager, profileManager);
        const audioManager = new DefaultAudioManager(dataManager);
        const vibrateManager = PlatformUtils.isMobileGame()
            ? new DefaultVibrateManager(dataManager)
            : new NullVibrateManager(dataManager);
        const trackingManager = new TrackingManager(levelManager, analyticsManager, dataManager, cardManager);
        const adsManager = PlatformUtils.isMobileGame()
            ? new DefaultAdsManager(dataManager, configManager, vipManager, trackingManager, profileManager)
            : new SimulatedAdsManager()

        const dailyQuestManager = new NewDailyQuestManager(configManager, dataManager, rewardManager, timeManager, logManager, levelManager);
        const prizeWheelManager = new DefaultPrizeWheelManager(configManager, dataManager, rewardManager);
        const tutorialManager = new DefaultTutorialManager(dataManager);

        const socialManager = CreateNullSocialManager();

        const achievementManager =
            new DefaultAchievementManager(configManager, dataManager, rewardManager, logManager, cardManager);

        const sceneManager = new DefaultSceneManager();
        const energyManager = new DefaultEnergyManager(configManager, dataManager, vipManager, storeManager, timeManager, profileManager);
        const pvpTicketManager = new DefaultPvpTicketManager(configManager, dataManager, storeManager, timeManager);

        const lifeCycleManager = new DefaultAppLifeCycleManager();
        const specialRewardManager = new DefaultSpecialRewardManager(
            configManager, rewardManager, dataManager, timeManager);
        const appManager = PlatformUtils.isMobileGame()
            ? new DefaultAppManager(dataManager)
            : new SimulatedAppManager();

        const ratingManager = new DefaultRatingManager(appManager, configManager, dataManager, timeManager);

        const dialogManager = new ee.DefaultDialogManager(this.node);
        const notificationHandler = new NotificationHandler(chestManager, dailyRewardManager);
        const notificationManager = PlatformUtils.isMobileGame()
            ? new DefaultNotificationManager(notificationHandler)
            : new NullNotificationManager();
        const cheatManager = new DefaultCheatManager(dataManager, timeManager);
        const cardRewards = new CardRewardPerLevelManagerImpl(dataManager);
        const featureManager = new DefaultFeatureManager(configManager);
        const offerManager = new DefaultOfferManager(profileManager, levelManager, ratingManager, shopManager);

        const authen = new FirebaseAuthenManager()
        await authen.initialize()
        const gameServerManager = new GameServerManager(defaultServerConfig, dataManager, authen);
        const levelCloudSyncer = new LevelCloudSyncer(gameServerManager);
        const levelLoaderManager = new LevelLoaderManager(configManager, levelCloudSyncer, cheatManager);

        const leaderboardManager = new DefaultLeaderboardManager(gameServerManager);
        const hiddenTempleManager = new DefaultHiddenTempleManager(configManager, dataManager, timeManager);
        const piggyBankManager = new DefaultPiggyBankManager(configManager, dataManager, timeManager);

        // team - chat
        const chatUserCacheManager = new ChatUserCacheManager(dataManager);
        const chatManager = new DefaultChatManager(configManager, dataManager, timeManager);
        const chatNetworkManager = new WebSocketChatNetworkManager(this.uuid, gameServerManager, chatManager, chatUserCacheManager);
        const myTeamManager = new DefaultMyTeamManager(gameServerManager, dataManager);

        ee.ServiceLocator.register(appManager);
        ee.ServiceLocator.register(dialogManager);
        ee.ServiceLocator.register(levelManager);
        ee.ServiceLocator.register(storeManager);
        ee.ServiceLocator.register(dailyRewardManager);
        ee.ServiceLocator.register(shopManager);
        ee.ServiceLocator.register(chestManager);
        ee.ServiceLocator.register(dailyQuestManager);
        ee.ServiceLocator.register(achievementManager);
        ee.ServiceLocator.register(audioManager);
        ee.ServiceLocator.register(vibrateManager);
        ee.ServiceLocator.register(profileManager);
        ee.ServiceLocator.register(adsManager);
        ee.ServiceLocator.register(autoMineManager);
        ee.ServiceLocator.register(boosterStorageManager);
        ee.ServiceLocator.register(cardManager);
        ee.ServiceLocator.register(rewardManager);
        ee.ServiceLocator.register(prizeWheelManager);
        ee.ServiceLocator.register(vipManager);
        ee.ServiceLocator.register(tutorialManager);
        ee.ServiceLocator.register(socialManager);
        ee.ServiceLocator.register(leaderboardManager);
        ee.ServiceLocator.register(sceneManager);
        ee.ServiceLocator.register(logManager);
        ee.ServiceLocator.register(pvpTicketManager);
        ee.ServiceLocator.register(energyManager);
        ee.ServiceLocator.register(analyticsManager);
        ee.ServiceLocator.register(trackingManager);
        ee.ServiceLocator.register(crashlyticsManager);
        ee.ServiceLocator.register(lifeCycleManager);
        ee.ServiceLocator.register(specialRewardManager);
        ee.ServiceLocator.register(timeManager);
        ee.ServiceLocator.register(ratingManager);
        ee.ServiceLocator.register(notificationManager);
        ee.ServiceLocator.register(pvpManager);
        ee.ServiceLocator.register(cheatManager);
        ee.ServiceLocator.register(cardRewards);
        ee.ServiceLocator.register(featureManager);
        ee.ServiceLocator.register(offerManager);
        ee.ServiceLocator.register(levelCloudSyncer);
        ee.ServiceLocator.register(levelLoaderManager);
        ee.ServiceLocator.register(gameServerManager);
        ee.ServiceLocator.register(hiddenTempleManager);
        ee.ServiceLocator.register(piggyBankManager);
        ee.ServiceLocator.register(playPassManager);
        ee.ServiceLocator.register(authen);
        ee.ServiceLocator.register(chatUserCacheManager);
        ee.ServiceLocator.register(chatManager);
        ee.ServiceLocator.register(chatNetworkManager);
        ee.ServiceLocator.register(myTeamManager);

        const currentLanguage = dataManager.getValue('language', 'en');
        ee.LanguageManager.getInstance().setCurrentLanguage(currentLanguage);
        ee.LanguageManager.getInstance().setConfigDir('language');

        cheatManager.initialize();

        // FTUE
        const profile = profileManager.getProfileInfo();
        // if (profile.timesPlayed === 0) {
        //     boosterStorageManager.addBalance(gm.BoosterType.Dynamite, 3);
        // }
        ++profile.timesPlayed;

        await this.preloadAudio(10);

        // No need to preload images.
        // await this.preloadImages(59);
        await JsbUtils.calculateSafeInset();

        NativeCall.setTransparentEnabled(false);

        // Restore old value.
        cc.macro.DOWNLOAD_MAX_CONCURRENT = this.concurrentDownloads;

        await this.initServer(gameServerManager, levelCloudSyncer);

        myTeamManager.loadData();
    }

    @crashlytics
    private async preloadAudio(percentage: number): Promise<void> {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.preloadAudio, `begin`);
        try {
            const audioManager = ee.ServiceLocator.resolve(AudioManager);
            await audioManager.preload();
        } catch (ex) {
            cc.log(`Cannot preload audio ${ex}`);
        }
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.preloadAudio, `end`);
    }

    /** Registered in editor. */
    @crashlytics
    private onPlayButtonPressed(): void {
        if (this.switchScene) {
            return;
        }
        this.switchScene = true;
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onPlayButtonPressed);
        this.goToMenuScene();
    }

    @crashlytics
    private goToMenuScene(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.goToMenuScene);
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
        if (levelManager.isStoryLevelCompleted(0, 0)) {
            playPassManager.updateSession(true)
            const sceneManager = ee.ServiceLocator.resolve(SceneManager);
            sceneManager.loadScene('menu_scene', MenuScene).then();
        } else {
            playPassManager.updateSession(false);
            SceneUtils.loadGameSceneStory(0, 0).then(_ => {
                const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
                trackingManager.trackSourceFTUE(SceneName.SceneGame, TrackSourceTypeReason.EnergyFTUE, TrackSourceType.Energy, 20);
                trackingManager.trackSourceSinkFTUE(SceneName.SceneGame, TrackSourceTypeReason.EnergyFTUE);
                trackingManager.trackSourceFTUE(SceneName.SceneGame, TrackSourceTypeReason.ChestFTUE, TrackSourceType.Chest, 1);
                trackingManager.trackSourceSinkFTUE(SceneName.SceneGame, TrackSourceTypeReason.ChestFTUE);
                ee.ServiceLocator.resolve(AudioManager).playMusic(SoundType.Australia);
            });
        }
    }

    private async checkShowPrivacyDialog(): Promise<void> {
        const didAccept = PrivacyDialog.didAcceptPrivacy(ee.ServiceLocator.resolve(DataManager));
        if (didAccept) {
            return;
        }
        const dialog = await PrivacyDialog.create();
        dialog.show(this.dialogManager);
        return await new Promise<void>(resolve => dialog.onDidHide(() => resolve()));
    }

    private async initServer(server: IGameServerManager, cloudSyncer: ILevelCloudSyncer) {
        const withTimeout = <T>(promise: Promise<T>, timeout: number): Promise<T> => {
            return Promise.race([
                promise,
                new Promise<T>((_, reject) =>
                    setTimeout(() => reject(new Error("Operation timed out")), timeout)
                )
            ]);
        };
        try {
            await withTimeout(server.initialize(), 3000);
            await withTimeout(cloudSyncer.initialize(), 4000);
            const replaceSuccess = await server.getAndReplaceData()
            if (!replaceSuccess) {
                this.goToMenuScene();
            }

        } catch (e) {
            console.log(`Failed to initialize server: ${e}`);
            this.goToMenuScene();

        }
    }
}
