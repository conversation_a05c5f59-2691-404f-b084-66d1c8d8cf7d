import * as gm from '../../engine/gm_engine';
import {PrefabUtils} from "../../utils/PrefabUtils";
import {RewardInfo} from "../../manager/reward/RewardManager";
import {CommonDialog} from "../../dialog/CommonDialog";
import {GenericOptimizer} from "../../utils/Clipper";
import PlayPassRewardItem from "./PlayPassRewardItem";
import {CrashlyticManager, crashlytics} from "../../manager/crashlytic/CrashlyticManager";
import * as ee from "../../libraries/ee/index";
import {EnergyManager} from "../../manager/energy/EnergyManager";
import {VibrateManager} from "../../manager/vibrate/VibrateManager";
import {TrackingManager} from "../../manager/analytics/TrackingManager";
import {RewardUtils} from "../../manager/reward/RewardUtils";
import {SceneName} from "../../manager/analytics/AnalyticsConfig";

const {ccclass, property} = cc._decorator;

@ccclass
export default class BonusBankRewardsDialog extends CommonDialog {
    public static create(): Promise<BonusBankRewardsDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/play_pass/bonus_bank_rewards_dialog');
    }

    @property({type: cc.ScrollView, visible: true})
    private readonly scrollView: cc.ScrollView | null = null;

    @property({ type: cc.Layout, visible: true })
    private readonly layout: cc.Layout | null = null;

    @property({type: cc.Prefab, visible: true})
    private readonly rewardPrefab: cc.Prefab | null = null;

    private _optimizer?: GenericOptimizer<RewardInfo>;
    private rewards: RewardInfo[];
    private sceneName: SceneName = SceneName.scenePlayPass;

    @crashlytics
    protected onLoad(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onLoad, `${this.uuid}`);
        const energyManager = ee.ServiceLocator.resolve(EnergyManager);

        super.onLoad();
        ee.ServiceLocator.resolve(VibrateManager).makeVibrate();
        this.addComponent(ee.BackButtonListener).setCallback(() => {
            this.isActive() && this.hide();
        });

        this.onDidHide(() => {
            const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
            this.rewards.forEach(reward => {
                if (reward.raw.subType === "unlimited_energy") {
                    energyManager.increaseUnlimitedDuration(reward.quantity)
                } else {
                    RewardUtils.trackingRewardItem(reward, trackingManager, this.sceneName);
                    reward.claim();
                }
            });
        });
    }

    private get optimizer(): GenericOptimizer<RewardInfo> {
        if (this._optimizer === undefined) {
            this._optimizer = new GenericOptimizer<RewardInfo>(this.layout.node)
                .setCreator(() => {
                    return cc.instantiate(this.rewardPrefab);
                })
                .setUpdater((index, view, model) => {
                    const item = view.getComponent(PlayPassRewardItem);
                    item.setRewardInfo(model).then();
                });
        }
        return this._optimizer;
    }

    public setRewards(rewards: RewardInfo[]): void {
        this.rewards = rewards;
        this.optimizer.setModels(this.rewards);
        this.layout.updateLayout();
        this.updateRendering();
        this.scrollView.scrollToOffset(cc.v2(0, 0));
    }

    public scrollToLeft() {
        this.scrollView.scrollToLeft(1);
    }

    protected onEnable(): void {
        this.scrollView.node.on('scrolling', this.onScrolling, this);
    }

    protected onDisable(): void {
        this.scrollView.node.off('scrolling', this.onScrolling, this);
    }

    private onScrolling(): void {
        this.updateRendering();
    }

    private updateRendering(): void {
        const mask = this.scrollView.content.parent;
        const helper = new gm.TransformHelper(mask, this.layout.node);
        const anchorX = mask.anchorX;
        const from = helper.convertTo(cc.v2(mask.width * -anchorX, 0)).x;
        const to = helper.convertTo(cc.v2(mask.width * (1 - anchorX), 0)).x;
        this.optimizer.process(from, to, false);
    }
}
