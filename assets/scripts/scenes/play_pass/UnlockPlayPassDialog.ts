
import {CommonDialog} from "../../dialog/CommonDialog";
import {PrefabUtils} from "../../utils/PrefabUtils";
import * as ee from "../../libraries/ee";

const {ccclass, property} = cc._decorator;

export interface playPassCallback {
    onShowInfo: () => void;
    onShowPlayPass: () => void;
}

@ccclass
export default class UnlockPlayPassDialog extends CommonDialog {
    public static create(): Promise<UnlockPlayPassDialog> {
        return PrefabUtils.createPrefab(this, 'prefabs/play_pass/unlock_play_pass_dialog');
    }

    private callback: playPassCallback;

    protected onLoad(): void {
        this.setTouchOutsideEnabled(false);
    }

    public setCallback(callback: playPassCallback): void {
        this.callback = callback;
    }

    /** Used in editor */
    private onCloseButtonPressed() {
        this.hide();
    }

    /** Used in editor */
    private onInfoButtonPressed() {
        this.callback.onShowInfo && this.callback.onShowInfo();
    }

    /** Used in editor */
    private onWatchNowButtonPressed() {
        this.onDidHide(()=>{
            this.callback.onShowPlayPass && this.callback.onShowPlayPass();
        })
        this.hide();
    }
}
