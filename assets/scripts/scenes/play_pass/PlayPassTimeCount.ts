import * as gm from '../../engine/gm_engine';
import * as ee from "../../libraries/ee/index";
import {PlayPassManager} from "./PlayPassManager";

const {ccclass, property} = cc._decorator;

@ccclass
export default class PlayPassTimeCount extends cc.Component {
    @property({ type: cc.Label, visible: true })
    private readonly _remainText: cc.Label | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _finishedText: cc.Node | null = null;

    private get remainText(): cc.Label {
        return gm.retrieveNull(this._remainText);
    }

    private get finishedText(): cc.Node {
        return gm.retrieveNull(this._finishedText);
    }

    private playPassManager: PlayPassManager;
    private onEndEvent: (completed: boolean) => void;

    protected onLoad(): void {
        this.playPassManager = ee.ServiceLocator.resolve(PlayPassManager);
    }

    protected onEnable() {
        this.updateRemainText(0);
        this.schedule(this.updateRemainText, 1);
    }

    protected onDisable() {
        this.unschedule(this.updateRemainText);
    }

    public setOnEndEvent(callback: (completed: boolean)=>void): void {
        this.onEndEvent = callback;
    }

    private updateRemainText(dt: number) {
        if (this.playPassManager.isCompleted()) {
            this.onEndEvent && this.onEndEvent(true);
            this.unschedule(this.updateRemainText);
            return;
        }

        if (this.playPassManager.isEndEvent()) {
            this.remainText.node.active = false;
            this.finishedText.active = true;
            this.onEndEvent && this.onEndEvent(false);
        } else {
            this.remainText.string = this.playPassManager.getRemainString();
            this.remainText.node.active = true;
            this.finishedText.active = false;
        }
    }
}
