import * as ee from "../../libraries/ee/index";
import {DialogManager, ServiceLocator} from "../../libraries/ee/index";
import {IRefreshable} from "../../manager/IRefreshable";
import {ConfigManager} from "../../manager/config/ConfigManager";
import {DataManager} from "../../manager/data/DataManager";
import {TimeManager} from "../../manager/time/TimeManager";
import {RewardInfo, RewardManager} from "../../manager/reward/RewardManager";
import {TimeUtils} from "../../utils/TimeUtis";
import UnlockPlayPassDialog, {playPassCallback} from "./UnlockPlayPassDialog";
import {LevelManager} from "../../manager/level/LevelManager";

const SECONDS_PER_DAY = 24 * 3600;

const createDefaultData: () => PlayPassData = () => ({
    isUnlock: false,
    isCompleted: false,
    startTime: 0,
    isGold: false,
    pass: 0,
    levelPass: -1,
    watchAdsTimes: 0,
    freeClaimed: [],
    goldClaimed: [],

    winRequestLevel: false, // điều kiện thứ 1 - đã win level 8
    sinkRuby: false, // điều kiện thứ 2 - có sink ruby
    buyIap: false, // điều kiện thứ 3 - có mua iap
    session: 0, // điều kiện thứ 4 - mở app ở session thứ 3
    hadShowUnlock: false,

    currentForceLevel: 0,
    lastForceDate: "",

    // part 2
    completedTime: 0,
    hadPreviousCompleted: false,

    // part 3
    endEventTime: 0,

    // mua vĩnh viễn frame_play_pass
    buyFramePlayPass: false
})

enum Key {
    Data = 'play_pass_data',
}

interface PlayPassReward {
    type: string;
    subType: string;
    value: number;
}

interface PlayPassItem {
    level: number;
    collect: number;
    "free": PlayPassReward;
    "gold": PlayPassReward;
}

interface FastEndStep {
    percent: number;
    ruby: number;
    ads: number;
}

interface Condition {
    eventDuration: number;
    gracePeriod: number;
    requestLevel: number;
    requestSession: number;
    unlockDelayDays: number;
    winReward: number;
    forceLevelTimes: number;
}


interface PlayPassConfig {
    condition: Condition;
    fast_end_step: FastEndStep[];
    gold_iap_rewards: PlayPassReward[];
    gold_pass_rewards: PlayPassReward[];
    rewards: PlayPassItem[];
}

interface PlayPassData {
    isUnlock: boolean;
    isCompleted: boolean;
    startTime: number;

    isGold: boolean;
    pass: number;
    levelPass: number;
    watchAdsTimes: number;
    freeClaimed: number[];
    goldClaimed: number[];

    winRequestLevel: boolean; // điều kiện thứ 1 - đã win level 8
    sinkRuby: boolean; // điều kiện thứ 2 - có sink ruby
    buyIap: boolean; // điều kiện thứ 3 - có mua iap
    session: number; // điều kiện thứ 4 - mở app ở session thứ 3
    hadShowUnlock: boolean;

    //complete immediately
    currentForceLevel: number;
    lastForceDate: string;

    // đã có kết thức sự kiện ít nhất 1 lần => sẽ áp dụng dk unlock theo part 2.
    // part 2
    completedTime: number,
    hadPreviousCompleted: boolean;

    // ghi nhận thời gian bắt đầu gia han trong khi còn claimable.
    // part 3
    endEventTime: number;

    // Đã mua frame_play_pass thì dùng vĩnh viễn
    buyFramePlayPass: boolean;
}

@ee.service("PlayPassManager")
export abstract class PlayPassManager implements ee.Service, IRefreshable {
    public abstract destroy(): void;

    public abstract isShowPlayPassLayer: boolean
    public abstract isShowDialog: boolean;
    public abstract winLevel: boolean;

    /** callback when pass, watchAds changed */
    public abstract onUpdateUi: () => void;

    /** gold play pass đã được mua chưa */
    public abstract isGold: boolean;

    /** frame_play_pass đã được mua chưa */
    public abstract isBuyFramePlayPass: boolean;

    /** Tổng số levels trong playPass (kể cả BonusBank */
    public abstract totalLevels: number;

    /** đã mở level cuối cùng */
    public abstract levelIsMax: boolean;
    /** level lớn nhất đã mở */
    public abstract levelPass: number;
    /** level sẽ mở tiếp theo */
    public abstract nextLevelPass: number;
    /** thẻ pass hiện có cho level cần mở tiếp theo */
    public abstract nextPass: number;
    /** thẻ pass cần có cho level cần mở tiếp theo */
    public abstract nextPassRequired: number;
    /** ruby cần để mở nahnh level tiếp theo */
    public abstract nextRubyRequired: number;
    /** Số lần đã xem ads */
    public abstract watchAdsTimes: number;
    /** Số lần cần xem để mở level tiếp theo */
    public abstract watchAdsRequired: number;

    /** claim level (index 0)*/
    public abstract claimFreeLevel(level: number): void;
    public abstract claimGoldLevel(level: number): void;

    /** pass sang level kết*/
    public abstract passToNextLevel(): void;
    /** Thêm thẻ pass */
    public abstract addPass(pass: number): void;
    /** Tăng số lần xem quảng cáo */
    public abstract addWatchTimes(value: number): void;
    /** level đã được mở chưa (index 0) */
    public abstract isPass(level: number): boolean;
    /** bonusBank pass khi đã đạt cột mốc 30 */
    public abstract isBonusBankPass(): boolean;

    /** level đã được claim chưa (index 0 */
    public abstract isFreeClaimed(level: number): boolean;
    public abstract isGoldClaimed(level: number): boolean;

    /** Lấy danh sách phần thưởng của Free và Gold Pass */
    public abstract getFreeGoldRewards() : [RewardInfo, RewardInfo][];
    /** Lấy danh sách phần thưởng sau khi mua gold pass */
    public abstract getGoldIapRewards(): RewardInfo[];
    /** Lấy danh sách phần thưởng gold bank item */
    public abstract getGoldPassRewards(): RewardInfo[];

    public abstract startTime: number;
    public abstract checkUnlock(): boolean;
    public abstract checkCanClaim(): boolean;
    public abstract getClaimableAmount(): number;
    public abstract setCompleted(): boolean;
    public abstract isCompleted(): boolean;
    public abstract getRemainString(): string;
    public abstract isEndEvent(): boolean;
    public abstract sinkRuby(): void;
    public abstract buyIap(): void;
    public abstract completeLevel(): number;

    // for show unlock Dialog
    public abstract showUnlockDialog(dialogManager: DialogManager, callback: playPassCallback): Promise<void>;
    public abstract setHadShowUnlock(): void;
    public abstract isHadShowUnlock(): boolean;

    // for complete immediately
    public abstract remainForceLevel: number;
    public abstract canForceLevel: boolean;

    // for session
    public abstract updateSession(pass: boolean): void

    // for refreshData
    public abstract refreshData(): void;

    // for cheat
    public abstract setLevelPass(levelPass: number): void
    public abstract endEventPlayPass(second: number): void
    public abstract resetEventPlayPass(): void
}

export class PlayPassManagerImpl extends PlayPassManager {

    private _isShowPlayPassLayer: boolean;
    public set isShowPlayPassLayer(value: boolean) {
        this._isShowPlayPassLayer = value;
    }
    public get isShowPlayPassLayer(): boolean {
        return this._isShowPlayPassLayer;
    }

    private _isShowDialog: boolean = false;
    public set isShowDialog(value: boolean) {
        this._isShowDialog = value;
    }
    public get isShowDialog(): boolean {
        return this._isShowDialog;
    }

    private _win: boolean = false;
    public set winLevel(value: boolean) {
        this._win = value;
    }
    public get winLevel(): boolean {
        return this._win;
    }

    /** Config */
    private config: PlayPassConfig;

    /** Data */
    private data: PlayPassData;

    /** Updater. */
    private dataDirty = false;
    private readonly node: cc.Node;

    /** Refresh Data */
    private onRefresh: () => void;

    /** Update Ui */
    private _onUpdateUi: () => void;
    public set onUpdateUi(callback: () => void) {
        this._onUpdateUi = callback;
    }

    public constructor(
        configManager: ConfigManager,
        private readonly dataManager: DataManager,
        private readonly timeManager: TimeManager,
        private readonly rewardManager: RewardManager,
        ) {
        super();

        // Config.
        this.config = configManager.getValue("play_pass_config");

        // Data.
        this.data = dataManager.getValue(Key.Data, createDefaultData());

        // Sync handler.
        dataManager.addHandler('play_pass', {
            load: data => {
                this.data = data[Key.Data] || createDefaultData();
                this.dataDirty = true
            },
            save: data => {
                data[Key.Data] = this.data;
            },
        });

        // Data updater.
        this.node = new cc.Node();
        cc.director.getScheduler().schedule(() => {
            if (this.dataDirty) {
                this.dataDirty = false;
                this.saveData();
            }
        }, this.node, 0);

    }

    destroy(): void {
        this.dataManager.removeHandler('play_pass');
        this.node.destroy();
    }

    private saveData(): void {
        this.dataManager.setValue(Key.Data, this.data);
    }

    public override refreshData(): void {
        this.data = this.dataManager.getValue(Key.Data, createDefaultData());
        this.onRefresh && this.onRefresh();
    }

    public get totalLevels(): number {
        return this.config.rewards.length - 1;
    }

    public get isGold(): boolean {
        return this.data.isGold;
    }

    public set isGold(value: boolean) {
        if (this.data.isGold === value) {
            return;
        }
        this.data.isGold = value;

        // nếu mua gold thì lưu mua framePlayPass vĩnh viễn - không theo isGold
        if (value) {
            this.data.buyFramePlayPass = true;
        }

        this._onUpdateUi && this._onUpdateUi();
        this.dataDirty = true;
    }

    public get isBuyFramePlayPass(): boolean {
        return this.data.buyFramePlayPass;
    }

    public get levelIsMax(): boolean {
        return this.data.levelPass >= this.totalLevels - 1;
    }

    public get levelPass(): number {
        return this.data.levelPass;
    }

    public get nextLevelPass(): number {
        if (this.data.levelPass >= this.totalLevels - 1) {
            return this.data.levelPass;
        }
        return this.data.levelPass + 1;
    }

    public get nextPass(): number {
        if (this.levelIsMax) {
            // đã đạt mốc cuối cùng thì hiện thị full pass
            return this.nextPassRequired;
        }
        return this.data.pass;
    }

    public get nextPassRequired(): number {
        if (this.data.levelPass >= this.totalLevels - 1) {
            return this.config.rewards[this.data.levelPass].collect;
        }
        const nextLevel = this.data.levelPass + 1;
        return this.config.rewards[nextLevel].collect;
    }

    public get nextRubyRequired(): number {
        if (this.data.levelPass >= this.totalLevels - 1) {
            return 0;
        }
        const required = this.nextPassRequired;
        const remain = required - this.data.pass;
        const remainPercent = (remain / required) * 100;
        for (let i = 0; i < this.config.fast_end_step.length; i++) {
            const step = this.config.fast_end_step[i];
            if (remainPercent >= step.percent) {
                return step.ruby;
            }
        }
    }

    public get watchAdsTimes(): number {
        return this.data.watchAdsTimes;
    }

    public get watchAdsRequired(): number {
        if (this.data.levelPass >= this.totalLevels - 1) {
            return 0;
        }
        const required = this.nextPassRequired;
        const remain = required - this.data.pass;
        const remainPercent = (remain / required) * 100;
        for (let i = 0; i < this.config.fast_end_step.length; i++) {
            const step = this.config.fast_end_step[i];
            if (remainPercent >= step.percent) {
                return step.ads;
            }
        }
    }

    public claimFreeLevel(level: number): void {
        // Cho claim tất cả rewards => lấy rewards.length thay vì
        // totalLevels đã loại gold bank
        if (level < 0 || level >= this.config.rewards.length) {
            return;
        }
        this.data.freeClaimed.push(level);
        this.dataDirty = true;
    }

    public claimGoldLevel(level: number): void {
        // Cho claim tất cả rewards => lấy rewards.length thay vì
        // totalLevels đã loại gold bank
        if (level < 0 || level >= this.config.rewards.length) {
            return;
        }
        this.data.goldClaimed.push(level);
        this.dataDirty = true;
    }

    public passToNextLevel(): void {
        this.data.currentForceLevel += 1
        const remain = this.nextPassRequired - this.nextPass;
        this.addPass(remain);

    }

    public addPass(pass: number): void {
        if (this.levelIsMax) {
            return;
        }
        this.data.pass += pass;
        let passRequire = this.nextPassRequired;
        while (!this.levelIsMax && this.data.pass >= passRequire) {
            this.data.levelPass += 1;
            this.data.pass = this.data.pass - passRequire;
            passRequire = this.nextPassRequired;
        }
        this._onUpdateUi && this._onUpdateUi();
        this.dataDirty = true;
    }

    public addWatchTimes(value: number): void {
        if (this.levelIsMax) {
            return;
        }
        this.data.watchAdsTimes += value;
        let adsRequire = this.watchAdsRequired;
        while (!this.levelIsMax && this.watchAdsTimes >= adsRequire) {
            this.data.currentForceLevel += 1
            this.data.levelPass += 1;
            this.data.watchAdsTimes = this.data.watchAdsTimes - adsRequire;
            this.data.pass = 0;
            adsRequire = this.watchAdsRequired;
        }
        this._onUpdateUi && this._onUpdateUi();
        this.dataDirty = true;
    }

    public isPass(level: number): boolean {
        if (level < 0 || level >= this.totalLevels) {
            return false;
        }
        return this.data.levelPass >= level;
    }

    public isBonusBankPass(): boolean {
        return this.data.levelPass >= this.totalLevels - 1;
    }

    public isFreeClaimed(level: number): boolean {
        if (level < 0 || level >= this.totalLevels) {
            return false;
        }
        return this.data.freeClaimed.includes(level);
    }

    public isGoldClaimed(level: number): boolean {
        if (level < 0 || level >= this.config.rewards.length) {
            return false;
        }
        return this.data.goldClaimed.includes(level);
    }

    public getFreeGoldRewards() : [RewardInfo, RewardInfo][] {
        let rewards: [RewardInfo, RewardInfo][] = [];
        this.config.rewards.forEach((reward, index) => {
            const freeReward = this.getFreeRewards(index);
            const goldReward = this.getGoldRewards(index);
            rewards.push([freeReward, goldReward]);
        })
        return rewards;
    }

    private getFreeRewards(index: number): RewardInfo {
        const free = this.config.rewards[index].free;
        if (free.type === "") {
            return null;
        }
        return this.rewardManager.createReward({
            type: free.type,
            subType: free.subType === "random_card" ? "card_back" : free.subType,
            value: free.value
        });
    }

    private getGoldRewards(index: number): RewardInfo {
        const gold = this.config.rewards[index].gold;
        if (gold.type === "") {
            return null;
        }
        return this.rewardManager.createReward({
            type: gold.type,
            subType: gold.subType === "random_card" ? "card_back" : gold.subType,
            value: gold.value
        });
    }

    public getGoldIapRewards(): RewardInfo[] {
        const rewards:RewardInfo [] = [];
        this.config.gold_iap_rewards.forEach((reward) => {
            const info = this.rewardManager.createReward({
                type: reward.type,
                subType: reward.subType === "random_card" ? "card_back" : reward.subType,
                value: reward.value
            })
            rewards.push(info);
        })
        return rewards;
    }

    public getGoldPassRewards(): RewardInfo[] {
        const rewards:RewardInfo [] = [];
        this.config.gold_pass_rewards.forEach((reward) => {
            const info = this.rewardManager.createReward({
                type: reward.type,
                subType: reward.subType === "random_card" ? "card_back" : reward.subType,
                value: reward.value
            })
            rewards.push(info);
        })
        return rewards;
    }

    public checkUnlock(): boolean {
        if (this.data.isUnlock) {
            return true;
        }

        let isUnlock = false;
        if (this.data.hadPreviousCompleted) {
            isUnlock =
                this.getRemainDays() <= 0;

        } else {
            isUnlock =
                this.data.winRequestLevel ||
                this.isSessionPass ||
                this.data.sinkRuby ||
                this.data.buyIap;
        }

        if (isUnlock) {
            this.data.isUnlock = true;
            this.data.startTime = this.timeManager.secondsNow();
            this.data.isCompleted = false;
            this.data.completedTime = 0;
            this.dataDirty = true;
        }
        return this.data.isUnlock;
    }

    public checkCanClaim(): boolean {
        const remains = this.getRemainGracePeriod();
        return (remains > 0 && this.getClaimableAmount() > 0);
    }

    public sinkRuby(): void {
        if (!this.data.sinkRuby) {
            this.data.sinkRuby = true;
            this.checkUnlock();
        }
    }

    public buyIap(): void {
        if (this.data.buyIap) {
            return;
        }
        this.data.buyIap = true;
        this.checkUnlock();
    }

    public completeLevel(): number {
        if (!this.data.isUnlock) {
            this.checkWinRequestLevel();
            this.checkUnlock();
        }

        if (!this.data.isUnlock) {
            this.winLevel = false;
            return 0;
        }
        if (this.isEndEvent()) {
            // play pass đã hết thời hạn
            this.winLevel = false;
            return 0;
        }
        if (this.levelIsMax) {
            // đã đạt mốc cuối cùng
            this.winLevel = false;
            return 0;
        }

        let reward = 0;
        if (this.winLevel) {
            reward = this.config.condition.winReward;
        }
        this.winLevel = false;
        return reward;
    }

    private checkWinRequestLevel(){
        if (this.data.winRequestLevel) {
            return;
        }
        const levelManager = ServiceLocator.resolve(LevelManager);
        const area = levelManager.getCurrentStoryArea();
        const level = levelManager.getCurrentStoryLevel();
        const levelWorld = ((area) * 20) + level;
        this.data.winRequestLevel = levelWorld >= this.config.condition.requestLevel;
        this.dataDirty = this.data.winRequestLevel;
    }

    private getRemainGracePeriod(): number {
        const duration = this.config.condition.gracePeriod * SECONDS_PER_DAY;
        if (this.data.endEventTime === 0) return duration;
        return this.timeManager.getRemainingTime(this.data.endEventTime, duration)
    }

    private getRemainDays(): number {
        const duration = this.config.condition.unlockDelayDays * SECONDS_PER_DAY;
        return this.timeManager.getRemainingTime(this.data.completedTime, duration);
    }

    private getRemainSeconds(): number {
        const duration = this.config.condition.eventDuration * SECONDS_PER_DAY
        return this.timeManager.getRemainingTime(this.data.startTime, duration)
    }

    public getRemainString(): string {
        const secondsRemain = this.getRemainSeconds();
        return TimeUtils.getRemainString(secondsRemain);
    }

    public isEndEvent(): boolean {
        return this.data.startTime > 0 && this.getRemainSeconds() <= 0;
    }

    // for show unlock dialog
    public async showUnlockDialog(dialogManager: DialogManager, callback: playPassCallback): Promise<void> {
        if (this.isShowDialog) {
            return;
        }
        this.isShowDialog = true;
        const dialog = await UnlockPlayPassDialog.create();
        dialog.setCallback(callback);
        dialog.show(dialogManager);
        await new Promise<void>(resolve => dialog.onDidHide(() => {
            this.isShowDialog = false;
            resolve();
        }));
        this.setHadShowUnlock();
    }

    public setHadShowUnlock() {
        if (this.data.hadShowUnlock) {
            return;
        }
        this.data.hadShowUnlock = true;
        this.dataDirty = true;
    }

    public isHadShowUnlock(): boolean {
        return this.data.hadShowUnlock;
    }

    public setCompleted(): boolean {
        if (this.data.isCompleted) {
            return true;
        }

        if (this._isShowPlayPassLayer) {
            return false;
        }

        if (this.checkCanClaim()) {
            if (this.data.endEventTime === 0) {
                this.data.endEventTime = this.timeManager.secondsNow();
                this.dataDirty= true;
            }
            return false;
        }

        //reset và chuẩn bị cho lần unlock tiếp theo
        const completedTime = this.timeManager.secondsNow();

        // luu lại buyFrameData sau khi recreate Default
        const buyFramePlayPass = this.data.buyFramePlayPass;
        this.data = createDefaultData();

        this.data.buyFramePlayPass = buyFramePlayPass;
        this.data.isCompleted = true;
        this.data.completedTime = completedTime;
        this.data.hadPreviousCompleted = true;
        this.dataDirty = true;
        return true;
    }

    public isCompleted(): boolean {
        return this.data.isCompleted;
    }

    public get startTime(): number {
        return this.data.startTime;
    }

    public getClaimableAmount(): number {
        let claimableAmount = 0;
        for (let i = 0; i <= this.data.levelPass; i++) {
            if (!this.isFreeClaimed(i)) {
                claimableAmount++;
            }
            if (this.isGold && !this.isGoldClaimed(i)) {
                claimableAmount++;
            }
        }

        // cộng thêm 1 claim nếu isGold và đạt mốc cuối.
        if (this.isGold && this.data.levelPass == this.totalLevels - 1) {
            if (!this.isGoldClaimed(this.totalLevels)) {
                claimableAmount += 1;
            }
        }

        return claimableAmount;
    }

    public get remainForceLevel(): number {
        return this.config.condition.forceLevelTimes - this.data.currentForceLevel;
    }

    public get canForceLevel(): boolean {
        const today = this.getTodayDateString()
        if (this.data.lastForceDate !== today) {
            this.resetDailyForceLevel(today);
        }
        return (this.data.currentForceLevel < this.config.condition.forceLevelTimes);
    }

    private getTodayDateString(): string {
        const today = new Date();
        return `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;
    }

    private resetDailyForceLevel(today: string) {
        this.data.currentForceLevel = 0;
        this.data.lastForceDate = today;
        this.dataDirty = true;
    }

    private get isSessionPass(): boolean {
        return this.data.session >= this.config.condition.requestSession;
    }

    public updateSession(pass: boolean): void {
        if (this.data.isUnlock) {
            return;
        }
        if (pass && this.data.session === 0) {
            this.data.session = this.config.condition.requestSession;
        } else {
            this.data.session += 1;
        }
        this.dataDirty = true;
        this.checkUnlock();
    }

    //-------------
    // For cheat
    //-------------
    public setLevelPass(level: number): void {
        if (level < 0 || level > this.totalLevels) {
            return;
        }
        this.data.levelPass = level-1;
        this.data.pass = 0;
        this.dataDirty = true;
        this._onUpdateUi && this._onUpdateUi();
    }

    public endEventPlayPass(second: number): void {
        const duration = this.config.condition.eventDuration * SECONDS_PER_DAY
        this.data.startTime = this.timeManager.secondsNow() - duration + second;
        this.dataDirty = true;
    }

    public resetEventPlayPass(): void {
        this.data = createDefaultData();
        this.data.startTime = this.timeManager.secondsNow();
        this.dataDirty = true;
        this._onUpdateUi && this._onUpdateUi();
    }
}
