import * as ee from "../../libraries/ee/index";
import * as gm from '../../engine/gm_engine';
import {ChestView} from "../common/ChestView";
import {CrashlyticManager, crashlytics, LevelManager, SceneName, TrackingManager} from "../../manager/gm_manager";
import {ConfirmChestDialog} from "../../dialog/ConfirmChestDialog";
import {EventItemView} from "./EventItemView";
import {EventMapLayer, MapOptions} from "./EventMapLayer";
import {EventLayerController} from "./EventLayerController";
import {AlertCloseType, AlertDialog} from "../../dialog/AlertDialog";

const {ccclass, property} = cc._decorator;

@ccclass
export class MainEventLayer extends EventLayerController {
    @ee.nest(ChestView)
    private readonly _chestView: ChestView | null = null;
    private _initializeLayoutDone: boolean;

    private get chestView(): ChestView {
        return gm.retrieveNull(this._chestView);
    }

    @property({type: cc.Layout, visible: true})
    private readonly _layoutNode: cc.Layout | null = null;

    private get layoutNode(): cc.Layout {
        return gm.retrieveNull(this._layoutNode);
    }

    @property({type: cc.Node, visible: true})
    private readonly _mainFrame: cc.Node | null = null;

    private get mainFrame(): cc.Node {
        return gm.retrieveNull(this._mainFrame);
    }

    @property({type: cc.Prefab, visible: true})
    private readonly _eventMapLayerPrefab: cc.Prefab | null = null;

    private get eventMapLayerPrefab(): cc.Prefab {
        return gm.retrieveNull(this._eventMapLayerPrefab);
    }

    @property({type: cc.Prefab, visible: true})
    private readonly _eventItemPrefab: cc.Prefab | null = null;

    private get eventItemPrefab(): cc.Prefab {
        return gm.retrieveNull(this._eventItemPrefab);
    }

    @property({type: cc.Node, visible: true})
    private readonly _lockedNode: cc.Node | null = null;

    private eventMapLayer: EventMapLayer = null;

    @crashlytics
    protected onLoad(): void {
        ee.AsyncManager.getInstance().add(MainEventLayer.name, async () => {
            this.initialize();
            this.isInitialized = true;
            this.updateDisplay();
        })
    }

    @crashlytics
    protected onEnable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onEnable, `${this.uuid}`);
    }

    @crashlytics
    protected onDisable(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDisable, `${this.uuid}`);
    }

    @crashlytics
    protected onDestroy(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onDestroy, `${this.uuid}`);
    }

    private initialize() {
        this.setChest(`star`);
    }

    /**
     * Sets the chest view in the main frame by given skin.
     * @param skin The skin of the chest.
     */
    private setChest(skin: string): void {
        const dict: { [key: string]: gm.ChestType } = {
            ["free"    /**/]: gm.ChestType.Free,
            ["copper"  /**/]: gm.ChestType.Copper,
            ["silver"  /**/]: gm.ChestType.Silver,
            ["gold"    /**/]: gm.ChestType.Gold,
            ["diamond" /**/]: gm.ChestType.Diamond,
            ["star"    /**/]: gm.ChestType.Star,
        };
        const type = dict[skin];
        this.chestView.type = type;
        this.chestView.callback = () => {
            this.showChestInfoDialog(type);
        };
    }

    private showChestInfoDialog(type: gm.ChestType): void {
        const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        ConfirmChestDialog.create().then(dialog => {
            dialog.chestType = type;
            dialog.isButtonEnabled = false;
            dialog.show(dialogManager);
        });
    }

    public updateDisplay() {
        if (this.isInitialized) {
            this.updateLayout();
        }
    }

    private updateLayout() {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        if (!levelManager.isEventModeUnlocked) {
            this._lockedNode.active = true;
            return;
        }

        this._lockedNode.active = false;

        if(this._initializeLayoutDone) {
            if (this.eventMapLayer === null) {
                this.eventMapLayer = this.mainFrame.getComponentInChildren(EventMapLayer);
            }
            this.eventMapLayer &&  this.eventMapLayer.init();
            return;
        }

        for (let i = 0; i < levelManager.getEventAreas(); i++) {
            const wrapper = new cc.Node();
            this.layoutNode.node.addChild(wrapper);

            const node = cc.instantiate(this.eventItemPrefab);
            wrapper.addChild(node);

            const info = levelManager.getEventAreaInfo(i);
            const item = node.getComponent(EventItemView);

            item.eventAreaNameKey = info.name;
            item.locked = levelManager.getCurrentEventArea() !== i;
            item.callback = async () => {
                this.pushMapLayer(true, {area: i}).then((layer)=>{
                    this.eventMapLayer = layer;
                });
            };
        }

        this._initializeLayoutDone = true;

        this.layoutNode.updateLayout();
    }

    public async pushMapLayer(animated: boolean, options?: MapOptions): Promise<EventMapLayer> {
        let layer = this.mainFrame.getComponentInChildren(EventMapLayer);
        if (layer) {
            return layer;
        }
        layer = cc.instantiate(this.eventMapLayerPrefab).getComponent(EventMapLayer);
        layer.controller = this.menuController;
        layer.dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        layer.topHud = this.topHud;
        layer.playButton.setNextLevel(this.getNextLevel())
        layer.playButton.setPressedCallback(this.onPressOpenLevel.bind(this));

        this.mainFrame.addChild(layer.node);

        if (options) {
            await layer.scrollToLevel(options);
            await layer.handleDialogs(options);
        }

        if (animated) {
            this.mainFrame.opacity = 0;
            await new Promise<void>((res, rej) => {
                cc.tween(this.mainFrame)
                    .to(0.5, {opacity: 255})
                    .call(() => {
                        res();
                    })
                    .start();
            })
        }

        return layer;
    }

    getNextLevel(): number {
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        const currentEventLevel = levelManager.getEventAreaInfo(levelManager.getCurrentEventArea());
        return currentEventLevel.unlockedLevels;
    }

    onPressOpenLevel(): boolean {
        ee.ServiceLocator.resolve(TrackingManager).trackEventClick(SceneName.SceneEventMain, `btn_play_level_${this.getNextLevel()}`)
        let layer = this.mainFrame.getComponentInChildren(EventMapLayer);
        if (layer) {
            const levelManager = ee.ServiceLocator.resolve(LevelManager);
            layer.showSelectLevelDialog(levelManager.getCurrentEventArea(), this.getNextLevel()-1).then();
            return true;
        }
        const levelManager = ee.ServiceLocator.resolve(LevelManager);
        if (!levelManager.isEventModeUnlocked) {
            AlertDialog.create().then((d) => {
                d.setKey(`cant_open_event_mode`).setCloseType(AlertCloseType.CloseIcon);
                d.show(ee.ServiceLocator.resolve(ee.DialogManager));
            })
            return true
        }
        return false
    }


}
