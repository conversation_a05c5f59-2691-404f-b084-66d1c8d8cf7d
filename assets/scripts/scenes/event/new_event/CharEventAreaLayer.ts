import {AreaLayer, AreaLayerListener} from "../../map/AreaLayer";
import {AreaInfo} from "../../../manager/level/LevelManager";
import {LevelLocator, LevelLocatorImpl} from "../../map/LevelLocator";
import {ChestView} from "../../common/ChestView";
import * as gm from '../../../engine/gm_engine';
import * as ee from "../../../libraries/ee/index";
import {ConfirmChestDialog} from "../../../dialog/ConfirmChestDialog";
import {AudioManager} from "../../../manager/audio/AudioManager";
import {SoundType} from "../../../manager/audio/SoundType";

const {ccclass, property} = cc._decorator;

@ccclass
export class CharEventAreaLayer extends AreaLayer {
    @property({type: [LevelLocatorImpl], visible: true})
    private readonly _levelLocators: LevelLocator[] = [];

    @property([cc.SpriteFrame])
    private readonly characterIcons: cc.SpriteFrame[] = [];

    @ee.nest(ChestView)
    private readonly _chestView: ChestView | null = null;

    private get chestView(): ChestView {
        return gm.retrieveNull(this._chestView);
    }

    @property(cc.Sprite)
    private characterIcon: cc.Sprite = null;

    @property(cc.Node)
    private greenTick: cc.Node = null;

    listener: AreaLayerListener;
    private info?: AreaInfo;
    private isInitialized = false;

    protected onLoad(): void {
        ee.AsyncManager.getInstance().add(CharEventAreaLayer.name, async () => {
            this.updateInfo();
            this.chestView.type = gm.ChestType.Diamond;
            this.chestView.callback = () => {
                this.showChestInfoDialog(this.chestView.type);
            };
            this.isInitialized = true;
        });
    }

    private showChestInfoDialog(type: gm.ChestType): void {
        const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        ConfirmChestDialog.create().then(dialog => {
            dialog.chestType = type;
            dialog.isButtonEnabled = false;
            dialog.show(dialogManager);
        });
    }

    private updateInfo(): void {
        const info = this.info;
        if (info === undefined) {
            return;
        }

        this._levelLocators.forEach((level, idx) => {
            level.setInfo(info.levels[idx], idx < info.unlockedLevels);
            level.listener = {
                select: (_, level) => {
                    ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
                    this.listener.select && this.listener.select(this, info.index, level);
                },
            };
        });
        this.greenTick.active = info.levels[info.levels.length-1].highestStar > 0;
    }

    getLevelPosition(level: number): cc.Vec2 {
        const item = this._levelLocators[level];
        const transform = new gm.TransformHelper(item.node, this.node);
        return transform.convertTo(cc.Vec2.ZERO);
    }

    getLevelWorldPosition(level: number): cc.Vec2 {
        const item = this._levelLocators[level];
        const transform = new gm.TransformHelper(item.node, cc.director.getScene());
        return transform.convertTo(cc.Vec2.ZERO);
    }

    setInfo(info: AreaInfo): void {
        this.info = info;
        if (this.isInitialized) {
            this.updateInfo();
            this.updateDisplay();
        }
    }

    private updateDisplay() {
        let mapName = this.info.name;
        const iconDict: { [key: string]: cc.SpriteFrame } = {
            'Jaki': this.characterIcons[0],
        }
        this.characterIcon.spriteFrame = iconDict[mapName] || null;
    }

    init(): void {}

    resumeTouch(): void {}

}
