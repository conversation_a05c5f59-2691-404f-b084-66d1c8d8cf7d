import { GameServerManager } from "../../manager/game_server/GameServerManager";

export interface LeaderboardRewardConfig {
    fromRank: number;
    toRank: number;
    ruby: number;
    chest: string;
    frame: string;
}

export interface WeeklyReward {
    rank: number;
    ruby?: number;
    gold?: number;
    chest?: string;
    frame?: string;
}

export class LeaderboardReward {

    private _rewardConfig: LeaderboardRewardConfig[];

    constructor(private _gameServer: GameServerManager) {
        this._rewardConfig = [
            { fromRank: 1, toRank: 1, ruby: 5000, chest: '', frame: 'Frame_Avatar_Top_Rank_Gold' },
            { fromRank: 2, toRank: 2, ruby: 3000, chest: '', frame: 'Frame_Avatar_Top_Rank_Silver' },
            { fromRank: 3, toRank: 3, ruby: 2500, chest: '', frame: 'Frame_Avatar_Top_Rank_Bronze' },
            { fromRank: 4, toRank: 10, ruby: 1500, chest: '', frame: '' },
            { fromRank: 11, toRank: 20, ruby: 1000, chest: '', frame: '' },
            { fromRank: 21, toRank: 50, ruby: 400, chest: '', frame: '' },
        ];
    }

    public getRewardConfigByRank(rank: number): LeaderboardRewardConfig {
        return this._rewardConfig.find(config => rank >= config.fromRank && rank <= config.toRank);
    }

    public async checkAndGetWeeklyReward(): Promise<WeeklyReward | null> {
        const rewards = (await this._gameServer.getRewardInfo()).filter(reward => reward.type == 'WEEKLY');
        if (rewards.length == 0) {
            return null
        }
        return JSON.parse(rewards[0].rewardData) as WeeklyReward;
    }

    public async claimWeeklyReward(): Promise<boolean> {
        return this._gameServer.claimReward("WEEKLY");
    }

}