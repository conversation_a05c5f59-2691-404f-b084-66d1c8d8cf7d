import {RankingView} from "../leaderboard/leaderboad_v2/RankingView";
import * as ee from '../../libraries/ee/index';
import {ServiceLocator} from '../../libraries/ee/index';
import UserRankingInfos from "../leaderboard/leaderboad_v2/UserRankingInfos";
import {LeaderboardManager, LeaderBoardOption} from "../../manager/leaderboard/LeaderboardManager";
import {UserProfileManager} from "../../manager/profile/UserProfileManager";
import {LoadingProcess} from "../common/LoadingProcess";
import {TimeUtils} from "../../utils/TimeUtis";
import {BottomHud} from "../common/BottomHud";
import {MenuScenePage} from "../MenuScene";
import {AudioManager, SoundType} from "../../manager/gm_manager";
import LeaderboardUserProfile from "../leaderboard/leaderboad_v2/LeaderboardUserProfile";
import {UserProfile} from "../profile/UserProfileView";
import {TopHud} from "../common/TopHud";
import {AlertCloseType, AlertDialog} from "../../dialog/AlertDialog";
import {
    Commands,
    GameServerManager,
    JoinRequestStatus,
    TeamInfoResponse
} from "../../manager/game_server/GameServerManager";
import {IErrResponse} from "@senspark/ee-x";
import {UserProfileUtils} from "../../utils/UserProfileUtils";
import {MyTeamManager} from "../../manager/team/MyTeamManager";
import {InforTeamLayer} from "../team/InforTeamLayer";
import {TeamInfo} from "../team/TeamLayer";
import {MessageEntity, NotifyData, RequestJoin} from "../../team/chat/Manager/ChatManager";
import {ChatNetworkManager} from "../../team/chat/Manager/ChatNetworkManager";
import {MemberInfo} from "../team/MemberItem";

const {ccclass, property} = cc._decorator;
const DEFAULT_LIMIT = 50

export enum RankingCategory {
    Weekly = "WEEKLY",
    Player = "PLAYER",
    Team = "TEAM",
}

export enum RankingScope {
    World = "WORLD",
    Nation = "NATION",
}

function toRankingCategory(category: string): RankingCategory | undefined {
    return Object.values(RankingCategory).includes(category.toUpperCase() as RankingCategory)
        ? (category as RankingCategory)
        : undefined;
}

function toRankingScope(scope: string): RankingScope | undefined {
    return Object.values(RankingScope).includes(scope.toUpperCase() as RankingScope)
        ? (scope as RankingScope)
        : undefined;
}

@ccclass
export abstract class LeaderboardLayer extends cc.Component {
    @ee.nest(RankingView)
    private _topRankingView: RankingView

    @ee.nest(RankingView)
    private _rankingView: RankingView

    @property(cc.Prefab)
    private readonly profileLayerPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    private readonly inforTeamLayerPrefab: cc.Prefab = null;

    @property({type: cc.Label, visible: true})
    private _timeRemainLabel: cc.Label | null = null;

    private _currentCategory: RankingCategory = RankingCategory.Player

    private _currentScope: RankingScope = RankingScope.World

    private _leaderBoardManager: LeaderboardManager

    private _userNation: string
    private _bottomHub: BottomHud
    private _remainTimeToReset: number
    private _topHub: TopHud;
    private pressBackOriginalCallback: () => void;
    private pressBackTeamInfoCallback: () => void;
    private topLayers: cc.Node[] = [];

    private gameServerManager: GameServerManager;
    private myTeamManager: MyTeamManager;
    private infoTeamLayer: InforTeamLayer;
    private teamProfile: TeamInfo = null;

    protected onLoad() {
        this.gameServerManager = ServiceLocator.resolve(GameServerManager);
        this.myTeamManager = ServiceLocator.resolve(MyTeamManager);
        this.gameServerManager.registerBroadcastMessageListener(Commands.BROADCAST_NEW_MESSAGE, this.uuid, this.handleNewMessage.bind(this));
        this.gameServerManager.registerBroadcastMessageListener(Commands.BROADCAST_UPDATE_JOIN_REQUEST, this.uuid, this.handleUpdateJoinRequest.bind(this));
        this.gameServerManager.registerBroadcastMessageListener(Commands.BROADCAST_UPDATE_TEAM_INFO, this.uuid, this.handleUpdateTeamInfo.bind(this));
    }

    protected onEnable() {
        this._leaderBoardManager = ee.ServiceLocator.resolve(LeaderboardManager)
        this._userNation = ee.ServiceLocator.resolve(UserProfileManager).socialUser.nationCode
        this.onChangeCategory(null, this._currentCategory)
        this._rankingView.filterNationCallback = this.onChangeScope.bind(this)
        this._rankingView.onGetLeaderBoard = this.getLeaderboardData.bind(this)
        this._rankingView.onClickItem = (info: UserRankingInfos) => {
            ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress)
            if (["OPEN", "CLOSE"].includes(info.team)) {
                this.showTeamInfo(info);
            } else {
                this.showUserInfo(info);
            }
        }
        this._rankingView.onChangeNation = (scope) => {
            this._currentScope = scope;
            this.getLeaderboardData()
        }
        this._remainTimeToReset = this._leaderBoardManager.getTimeRemainingToNextRefreshWeekly()
        this.updateTime()
        // this.schedule(this.updateTime, 1)

    }

    setBottomHud(bottomHud: BottomHud) {
        this._bottomHub = bottomHud
        const originalCallback = this._bottomHub.listener.changed
        this._bottomHub.listener.changed = (sender, page) => {
            originalCallback && originalCallback(sender, page)
            if (page == MenuScenePage.LeaderBoard) {
                this._rankingView.changePage(page)

            }
            this.pressBackTeamInfoCallback && this._topHub.listener.onPressedBack();
            this.pressBackOriginalCallback && this._topHub.listener.onPressedBack()
        }
    }

    setTopHub(topHud: TopHud) {
        this._topHub = topHud
    }

    updateTime() {
        this._remainTimeToReset--;
        this._timeRemainLabel.string = TimeUtils.getRemainString(this._remainTimeToReset)
    }

    protected onDisable() {
        this.unschedule(this.updateTime)
        this.gameServerManager.unregisterBroadcastMessageListener(Commands.BROADCAST_NEW_MESSAGE, this.uuid);
        this.gameServerManager.unregisterBroadcastMessageListener(Commands.BROADCAST_UPDATE_JOIN_REQUEST, this.uuid);
        this.gameServerManager.unregisterBroadcastMessageListener(Commands.BROADCAST_UPDATE_TEAM_INFO, this.uuid);
    }

    public getLeaderboardData() {
        LoadingProcess.create().then(LoadingProcess => {
            // this._rankingContainer.active = false
            LoadingProcess.show(ee.ServiceLocator.resolve(ee.DialogManager))
            let options: LeaderBoardOption = {
                limit: DEFAULT_LIMIT,
                type: this._currentCategory,
            }
            if (this._currentScope == RankingScope.Nation) options.nation = this._userNation
            this._leaderBoardManager.fetchLeaderboard(options).then(data => {
                    // this.fetchMockLeaderboard(options).then(data => {
                    this._rankingView.listUserData = data;
                    this._topRankingView.listUserData = data;
                    // }

                }
            )
            LoadingProcess.hide()

        })

    }

    public onChangeCategory(toggle: cc.Toggle, customParam: string) {
        this._currentCategory = toRankingCategory(customParam)
        this.getLeaderboardData()
        this._currentScope = RankingScope.World
        this._rankingView.updateView(this._currentCategory, this._currentScope);
        this._topRankingView.updateView(this._currentCategory, this._currentScope);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress)
    }

    public onChangeScope(eventData: string) {
        const scope = toRankingScope(eventData)
        if (scope == this._currentScope) return
        this._currentScope = scope
        this.getLeaderboardData()
        this._rankingView.updateView(this._currentCategory, this._currentScope);
        this._topRankingView.updateView(this._currentCategory, this._currentScope);
        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress)

    }

    private async handleNewMessage(message: string): Promise<void> {
        if (this.infoTeamLayer == null) {
            return;
        }
        const msg = JSON.parse(message).msg as MessageEntity;
        if (msg.type !== "NOTIFY") {
            return;
        }
        const notify = msg.notificationMessage;
        if (notify.type === `USER_JOIN`) {
            const notifyData = JSON.parse(notify.data) as NotifyData
            const myUserId = await this.myTeamManager.getUserID();
            if (notifyData.userId === myUserId) {
                this.infoTeamLayer.updateButtons(JoinRequestStatus.APPROVED)
            }
        }
        await this.infoTeamLayer.onNotify(notify, this.teamProfile);
    }

    private async handleUpdateJoinRequest(message: string): Promise<void> {
        if (this.infoTeamLayer == null) {
            return;
        }
        const joinRequest = JSON.parse(message) as RequestJoin;
        const myUserId = await this.gameServerManager.getUId();
        const statusKey: { [key: string]: string } = {
            REJECTED: "rejected_join_request",
            CANCELLED: "cancelled_join_request",
            EXPIRED: "expired_join_request",
        };
        if ([`REJECTED`, `CANCELLED`, `EXPIRED`].includes(joinRequest.status)) {
            if (joinRequest.userId === myUserId) {
                if (this.myTeamManager.getTeamInfo().teamId == "") {
                    this.infoTeamLayer.rejectJoinRequest(statusKey[joinRequest.status]);
                }
            }
        }
    }

    private handleUpdateTeamInfo(message: string): void {
        if (this.infoTeamLayer == null) {
            return;
        }
        const teamInfo = JSON.parse(message) as TeamInfoResponse;
        //this.myTeamManager.updateTeamInfoFromResponse(teamInfo)
        if (this.infoTeamLayer.teamProfile.teamId === teamInfo.teamId) {
            this.gameServerManager.getTeamProfile(teamInfo.teamId).then((teamProfile)=>{
                this.infoTeamLayer.teamProfile = teamProfile;
            })
        }
    }

    private async showTeamInfo(info: UserRankingInfos) {
        const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        const loadingProcess = await LoadingProcess.create();
        try {
            loadingProcess.show(dialogManager);
            this.teamProfile = await this.gameServerManager.getTeamProfile(info.uid);
            //this.myTeamManager.updateTeamInfoFromProfile(this.teamProfile)
            const teamMember = await this.gameServerManager.getTeamMembers(info.uid);
            loadingProcess.hide();
            if (!this.teamProfile) {
                AlertDialog.create().then(alertDialog => {
                    alertDialog.show(dialogManager);
                    alertDialog.setKey('no_internet_notify');
                    alertDialog.setCloseType(AlertCloseType.OKButton);
                });
            } else {
                this.infoTeamLayer = cc.instantiate(this.inforTeamLayerPrefab).getComponent(InforTeamLayer);
                this.topLayers.push(this.infoTeamLayer.node);
                this.infoTeamLayer.node.parent = this.node;
                await this.infoTeamLayer.setInfo(this.teamProfile, teamMember, true);
                this.infoTeamLayer.setLayerTitle("txt_team_profile")
                this.infoTeamLayer.setListener({
                    onJoinPressed: this.onJoinPressed.bind(this),
                    onViewUserProfilePressed: this.showUserInfoById.bind(this)
                });
                await this.infoTeamLayer.showAnimation();
                this._topHub.setBackButtonVisible(true);
                this.pressBackOriginalCallback = this._topHub.listener.onPressedBack;
                this._topHub.listener.onPressedBack = () => {
                    this.infoTeamLayer.hideAnimation().then(() => {
                        this.infoTeamLayer.node.removeFromParent();
                        this.infoTeamLayer.node.destroy();
                    });
                    this.topLayers.pop();
                    this._topHub.setBackButtonVisible(false);
                    this._topHub.listener.onPressedBack = this.pressBackOriginalCallback;
                    this.pressBackOriginalCallback = null;
                }
            }
        } catch (error) {
            loadingProcess.hide();
        }
    }

    private async showUserInfo(info: UserRankingInfos) {
        await this.showUserInfoById(info.uid);
    }

    private async showUserInfoById(userId: string) {
        const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        const gameServer = ee.ServiceLocator.resolve(GameServerManager);
        const loadingProcess = await LoadingProcess.create();
        try {
            loadingProcess.show(dialogManager);
            const userData = await gameServer.getUserInfo(userId);
            const myUid = await gameServer.getUId();
            loadingProcess.hide();
            if ((userData == null || (userData as IErrResponse).errCode) && myUid.toString() != userId) {
                AlertDialog.create().then(alertDialog => {
                    alertDialog.show(dialogManager);
                    alertDialog.setKey('no_internet_notify');
                    alertDialog.setCloseType(AlertCloseType.OKButton);
                });
            } else {
                const profileLayer = cc.instantiate(this.profileLayerPrefab).getComponent(LeaderboardUserProfile);
                this.topLayers.push(profileLayer.node);
                profileLayer.node.parent = this.node;
                profileLayer.setInfo(myUid.toString() == userId ? UserProfileUtils.createLocalUserProfile() : userData as UserProfile);
                profileLayer.setActiveEditProfile(myUid.toString() == userId);
                await profileLayer.showAnimation();
                this._topHub.setBackButtonVisible(true);

                if (this.topLayers.length >= 2) {
                    this.pressBackTeamInfoCallback = this._topHub.listener.onPressedBack;
                } else {
                    this.pressBackOriginalCallback = this._topHub.listener.onPressedBack;
                }

                this._topHub.listener.onPressedBack = () => {
                    profileLayer.hideAnimation().then(() => profileLayer.node.removeFromParent());
                    this.topLayers.pop();
                    if (this.topLayers.length > 0) {
                        this._topHub.setBackButtonVisible(true);
                        this._topHub.listener.onPressedBack = this.pressBackTeamInfoCallback;
                        this.pressBackTeamInfoCallback = null;
                    } else {
                        this._topHub.setBackButtonVisible(false);
                        this._topHub.listener.onPressedBack = this.pressBackOriginalCallback;
                        this.pressBackOriginalCallback = null;
                    }
                }
            }
        } catch (error) {
            loadingProcess.hide();
        }
    }

    public async onJoinPressed(teamInfo: TeamInfo): Promise<[JoinRequestStatus, number]> {
        const [ok, error] = await this.gameServerManager.joinTeam(teamInfo.teamId);

        // Đã sử dụng nút join ở teamLeaderboard để hủy sự kiện thông báo unlock)
        this.myTeamManager.setHadShowUnlockDialog()
        this.myTeamManager.setHadShowUnlock();

        if (!error) {
            if (ok === JoinRequestStatus.PENDING) {
                ee.ServiceLocator.resolve(ChatNetworkManager).clearRoomPreview();
                this.myTeamManager.loadData();
                if (this.infoTeamLayer != null) {
                    this.infoTeamLayer.showFloating(`pending_join_request`);
                }
            }
            if (ok === JoinRequestStatus.APPROVED) {
                ServiceLocator.resolve(ChatNetworkManager).clearRoomPreview();
                this.myTeamManager.setTeamInfo(teamInfo);
                if (this.infoTeamLayer != null) {
                    this.infoTeamLayer.showFloating(`join_approved`, [`${teamInfo.teamName}`]);
                }
            }
            if (ok === JoinRequestStatus.BANNED) {
                if (this.infoTeamLayer != null) {
                    this.infoTeamLayer.showFloating(`request_banned`);
                }
            }

            this.infoTeamLayer.updateButtons(ok);
            return [ok, 0];
        } else {
            return [JoinRequestStatus.REJECTED, error];
        }
    }

    //for test
    private async generateMockUserInfo(info: UserRankingInfos): Promise<UserProfile | null> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    name: info.name,
                    picture: info.avatarId,
                    frame: info.frameId,
                    level: info.level,
                    medalCount: info.totalStar,
                    chestAcquiredCount: Math.floor(Math.random() * 100),
                    commonCardsFound: Math.floor(Math.random() * 100),
                    rareCardsFound: Math.floor(Math.random() * 10),
                    epicCardsFound: Math.floor(Math.random() * 5),
                    legendCardsFound: Math.floor(Math.random() * 2),
                    vipLevel: Math.floor(Math.random() * 10 + 1),
                    exp: Math.floor(Math.random() * 100000),
                    firstTryWins: Math.floor(Math.random() * 100),
                    levelFullClear: Math.floor(Math.random() * 100),
                    storyWinRate: Math.floor(Math.random() * 100),
                    threeStarLevels: Math.floor(Math.random() * 100),
                    unlockedLevels: Math.floor(Math.random() * 100),
                    eventFirstTryWins: Math.floor(Math.random() * 100),
                    eventLevelFullClear: Math.floor(Math.random() * 100),
                    eventThreeStarLevels: Math.floor(Math.random() * 100),
                    eventTotalUnlockedLevels: Math.floor(Math.random() * 100),
                    eventWinRate: Math.floor(Math.random() * 100),
                    starCount: Math.floor(Math.random() * 100),
                } as UserProfile);
            }, 100);
        });

    }

    //for test
    fetchMockLeaderboard(options?: LeaderBoardOption): Promise<UserRankingInfos[]> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const data: UserRankingInfos[] = [];
                for (let i = 0; i < 50; i++) {
                    data.push({
                        rank: i + 1,
                        uid: `user${i}`,
                        name: `User ${i}`,
                        avatarId: `avatar${i}`,
                        frameId: `frame${i}`,
                        nation: `nation${i % 5}`,
                        level: Math.floor(Math.random() * 100),
                        team: `team${i % 3}`,
                        totalStar: Math.floor(Math.random() * 1000)
                    });
                }
                resolve(data);
            }, 100);
        });
    }
}

