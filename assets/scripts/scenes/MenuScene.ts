import { IapShopCategory } from "./IapShopCategory";
import {IapShopLayer} from "./IapShopLayer";
import {CharacterLayer} from "./character/CharacterLayer";
import {HomeLayer} from "./HomeLayer";

export enum MenuScenePage {
    Shop = 0,
    Character = 1,
    Home = 2,
    Team = 3,
    LeaderBoard = 4,
}
// +--------+----+----+----+----+----+----+----+----+----+----+-----------+-------+--------+--------+--------+-----+
// |  Next  |    |    |    |    |    |    |    |    |    |    | Character | Event |  Pvp   | Select |  Open  |     |
// |  level |       Level bits       |       Area bits        |   event   |  bit  |  bit   | level  |  map   |     |
// |  bit   |    |    |    |    |    |    |    |    |    |    |    bit    |       |        |  bit   |  bit   |     |
// +--------+----+----+----+----+----+----+----+----+----+----+-----------+-------+--------+--------+--------+-----+
// tslint:disable: variable-name
export class MenuSceneAction {

    /** Opens the map layer and scroll to the latest level. */
    public static readonly OpenMap = 1 << 1;

    /**
     * Show the select level dialog.
     * Only effective if OpenEvent or OpenMap is enabled.
     */
    public static readonly ShowSelectLevel = 1 << 2;

    /** Opens the pvp layer. */
    public static readonly OpenPvp = 1 << 3;

    /** Opens the event layer. */
    public static readonly OpenEvent = 1 << 4;

    public static readonly OpenCharacterEvent = 1 << 5;

    public static readonly NextLevel = 1 << 16;

    /**
     * Show the select level dialog for the specified level.
     * Only effective if ShowSelectLevel is enabled.
     * @param level The desired level to open dialog.
     */
    public static createSelectLevel(area: number, level: number): number {
        // Convert zero-indexed to be one-indexed.
        return ((area + 1) << 6) | ((level + 1) << 11);
    }

    public static hasSelectLevel(mask: number): boolean {
        return ((mask >> 6) & 0x3FF) !== 0;
    }

    public static getSelectLevel(mask: number): [number, number] {
        return [((mask >> 6) & 0x1F) - 1, ((mask >> 11) & 0x1F) - 1];
    }
}
// tslint:enable: variable-name

export interface MenuController {
    showShopCategory(category: IapShopCategory): void;
    setAllButtonEnabled(enabled: boolean): void;
    changePage(page: MenuScenePage) : void;
}

export abstract class MenuScene extends cc.Component {
    public abstract controller: MenuController;

    /** Adds a post-action. */
    public abstract addAction(action: number): this;

    public abstract checkCharacterUpgradeTutorial(): void;

    public abstract get shopLayer(): IapShopLayer;

    public abstract get characterLayer(): CharacterLayer;

    public abstract get homeLayer(): HomeLayer;

    //public abstract get chestLayer(): ChestScene;
}
