import * as gm from '../../engine/gm_engine';
import BoardGem from "./BoardGem";
import {TransformHelper} from "../../engine/utils/TransformHelper";
import {ChestDialog} from "../../dialog/ChestDialog";
import * as ee from "../../libraries/ee/index";
import {ChestType} from "../../engine/items/Chest";
import {AudioManager} from "../../manager/audio/AudioManager";
import {SoundType} from "../../manager/audio/SoundType";
import {ParticleUtils} from "../../utils/ParticleUtils";

const {ccclass, property} = cc._decorator;

export interface GateListener {
    onShowChest?: () => void;
    onShowEndEvent?: () => void;
}

@ccclass
export default class HiddenTempleGate extends cc.Component {
    @property()
    public level: number = 1;

    @property({type: cc.Enum(ChestType), visible: true})
    private _chestReward: ChestType = ChestType.Copper;

    @property({type: cc.Node, visible: true})
    private _wallCover: cc.Node | null = null;

    @property({type: cc.Node, visible: true})
    private _wall: cc.Node | null = null;

    @property({type: cc.Node, visible: true})
    private _door: cc.Node | null = null;

    @property({type: [BoardGem], visible: true})
    private readonly _gems: BoardGem[] = [];

    @property({type: cc.Button, visible: true})
    private readonly _chestButton: cc.Button | null = null;

    @property({type: cc.Label, visible: true})
    private readonly _rewardText: cc.Label | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _reward: cc.Node | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _back: cc.Node | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _backDoor: cc.Node | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _particleNode: cc.Node | null = null;

    @property({type: cc.Node, visible: true})
    private readonly _particleChestNode: cc.Node | null = null;

    private get chestReward(): ChestType {
        return gm.retrieveNull(this._chestReward);
    }

    private get wallCover(): cc.Node {
        return gm.retrieveNull(this._wallCover);
    }

    private get wall(): cc.Node {
        return gm.retrieveNull(this._wall);
    }

    private get door(): cc.Node {
        return gm.retrieveNull(this._door);
    }

    private get gems(): BoardGem[] {
        return gm.retrieveNull(this._gems);
    }

    private get chestButton(): cc.Button {
        return gm.retrieveNull(this._chestButton);
    }

    private get rewardText(): cc.Label {
        return gm.retrieveNull(this._rewardText);
    }

    private get reward(): cc.Node {
        return gm.retrieveNull(this._reward);
    }

    private get back(): cc.Node {
        return gm.retrieveNull(this._back);
    }

    private get backDoor(): cc.Node {
        return gm.retrieveNull(this._backDoor);
    }

    private get particleChestNode(): cc.Node {
        return gm.retrieveNull(this._particleChestNode);
    }

    private get particleNode(): cc.Node {
        return gm.retrieveNull(this._particleNode);
    }

    private audioManager: AudioManager;

    private listener: GateListener = {};
    private onCompleted: () => void;

    private received: number = 0;

    protected onLoad() {
        this.audioManager = ee.ServiceLocator.resolve(AudioManager);
        this.chestButton.interactable = false;
        this.addParticleChest();
    }

    public addParticleChest() {
        const chestParticle = this.createChestParticle();
        this.particleChestNode.addChild(chestParticle);
        if (cc.sys.isMobile) {
            const cardParent = this.particleNode.parent;
            const worldPos = cardParent.convertToWorldSpaceAR(this.particleChestNode.getPosition());
            chestParticle.setPosition(worldPos);
        }
    }

    public removeParticleChest() {
        const childrenChest = this.particleChestNode.getComponentsInChildren(cc.ParticleSystem)
        for (const child of childrenChest) {
            child.node.destroy();
        }
        const children = this.particleNode.getComponentsInChildren(cc.ParticleSystem)
        for (const child of children) {
            child.node.destroy();
        }
    }

    public setListener(listener: GateListener) {
        this.listener = listener;
    }

    public setCompleteGateCallback(callback: () => void) {
        this.onCompleted = callback;
    }

    public resetGate() {
        this.gems.forEach((gem: BoardGem) => {
            gem.node.active = false;
        });
        this.resetDoor();
        this.received = 0;
    }

    public receiveGemFrom(from: BoardGem) {
        this.gems.forEach((gem: BoardGem) => {
            if (gem.index === from.index) {
                this.jumpFromBoardToGate(gem, from);
            }
        })
    }

    public receiveGemImmediately(from: BoardGem) {
        this.gems.forEach((gem: BoardGem) => {
            if (gem.index === from.index) {
                from.remove();
                gem.node.active = true;
                this.received += 1;
                if (this.received === this.gems.length) {
                    this.listener.onShowChest();
                    this.rotateDoorOut();
                    return;
                }
            }
        })
    }

    public zoomInForNextLevel(callback: () => void) {
        const duration = 2;

        let action = cc.tween;
        action(this.back)
            .parallel(
                action().to(1, { scale: 1}),
                action().to(1, { color: new cc.Color(255, 255, 255, 255)})
            )
            .start();

        cc.tween(this.backDoor)
            .to(1, {color: new cc.Color(255, 255, 255, 255)})
            .start();

        cc.tween(this.reward)
            .to(duration, {scale: 7})
            .start();

        cc.tween(this.wallCover)
            .to(duration, {scale: 6})
            .call(() => {
                callback();
            })
            .start();
    }

    private resetDoor() {
        this.door.stopAllActions();
        this.back.stopAllActions();
        this.backDoor.stopAllActions();

        this.wall.active = true;
        this.door.scale = 1.1;
        this.door.angle = 0;
        this.door.position = new cc.Vec3(0, 0, 0);
        this.door.active = true;
        this.wallCover.active = false;
        this.wallCover.scale = 1;
        this.back.scale = 0.4;
        this.back.color = cc.color(50, 50, 50);
        this.backDoor.color = cc.color(50, 50, 50);
        this.reward.scale = 1;
        this.chestButton.node.active = true;
        this.rewardText.node.active = true;

    }

    private rotateDoorOut() {
        this.rewardText.node.opacity = 0;
        const position = this.door.position;

        this.playSound(SoundType.TempleOpenGate);
        const openParticle = this.createGateOpenParticle();
        this.particleNode.addChild(openParticle);
        if (cc.sys.isMobile) {
            const cardParent = this.particleNode.parent;
            const worldPos = cardParent.convertToWorldSpaceAR(this.particleNode.getPosition());
            openParticle.setPosition(worldPos);
        }

        const moveOut = cc.tween(this.door)
            .to(2, {x: position.x + 500});
        const rotate = cc.tween(this.door)
            .by(2, {angle: -90})

        cc.tween(this.door)
            .to(1, {scale: 1})
            .delay(0.5)
            .call(() => {
                this.wall.active = false;
                this.wallCover.active = true;
            })
            .parallel(
                moveOut,
                rotate
            )
            .call(() => {
                this.door.active = false;
                this.zoomInForChest();

            })
            .start();
    }

    private zoomInForChest(duration: number = 1) {
        cc.tween(this.back)
            .to(duration, {scale: 0.6})
            .start();
        cc.tween(this.rewardText.node)
            .to(duration, {opacity: 255})
            .start();
        cc.tween(this.reward)
            .to(duration, {scale: 1.4})
            .start();
        cc.tween(this.wallCover)
            .to(duration, {scale: 1.5})
            .call(() => {
                this.chestButton.interactable = true;
            })
            .start();
    }

    /** Used in editor */
    private showChestDialog() {
        this.playSound(SoundType.ButtonPress);
        this.chestButton.interactable = false;
        ChestDialog.create().then(dialog => {
            const rootDialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
            dialog.chestQuantity = 1;
            dialog.chestType = this.chestReward;
            dialog.isRewardGold = false;
            dialog.show(rootDialogManager);
            dialog.onDidHide(() => {
                this.chestButton.node.active = false;
                this.rewardText.node.active = false;
                this.onCompleted();
            });
        });
    }

    private jumpFromBoardToGate(gem: BoardGem, from: BoardGem) {
        const toRotate = -gem.node.angle;
        const toScale = gem.node.scale;
        const fromScale = from.node.scale;
        const transformHelper = new TransformHelper(gem.node, from.node.parent);
        const toPos = cc.v3(transformHelper.convertTo(cc.Vec2.ZERO));

        const zoomInOut = cc.tween()
                .to(0.1, {scale: fromScale * 1.4})
                .delay(0.1)
                .to(0.05, {scale: fromScale * 0.8})
                .delay(0.05)
                .to(0.05, {scale: fromScale * 1.2})
                .delay(0.02)
                .to(0.1, {scale: fromScale * 1.1})

        const zoomAtTarget = cc.tween()
                .to(0.05, {scale: toScale * 1.04})
                .delay(0.05)
                .to(0.05, {scale: toScale * 0.9})
                .delay(0.05)
                .to(0.05, {scale: toScale * 1.02})
                .delay(0.02)
                .to(0.1, {scale: toScale})

        this.playSound(SoundType.SlotStop);

        let duration = 1;
        if (cc.sys.isMobile) {
            duration = 0.5;
        }

        const jump = cc.tween(from.node).to(duration, { position: toPos})
        const scale = cc.tween(from.node).to(duration, { scale: toScale })
        const rotate = cc.tween(from.node).to(duration, { angle: toRotate })

        cc.tween(from.node)
            .then(zoomInOut)
            .delay(0.2)
            .call(() => {
                this.playSound(SoundType.TemplePutGem);
            })
            .parallel(
                jump, scale, rotate
            )
            .call(() => {
                from.remove();
                gem.node.active = true;
                cc.tween(gem.node)
                    .then(zoomAtTarget)
                    .start();
                gem.showParticle();
                this.received += 1;
                if (this.received >= this.gems.length) {
                    this.listener.onShowChest();
                    this.rotateDoorOut();
                } else {
                    this.listener.onShowEndEvent();
                }
            })
            .start();
    }

    private createGateOpenParticle(): cc.Node {
        return ParticleUtils.createParticle({
            file: `particles/gate/gate_open`,
        })
    }

    private createChestParticle(): cc.Node {
        return ParticleUtils.createParticle({
            file: `particles/gate/chest_reward`,
        })
    }

    private playSound(soundType: SoundType): void {
        if (!this.audioManager) {
            this.audioManager = ee.ServiceLocator.resolve(AudioManager);
        }
        this.audioManager.playSound(soundType);
    }

    //---------------------------------
    // hiện thị theo tình trạng cổng.
    //---------------------------------

    public showGameEnd() {
        this.wallCover.active = true;
        this.door.active = false;
        this.chestButton.node.active = false;
        this.zoomInForChest(0);
        this.finishGame();
    }

    public showChestReward() {
        this.wall.active = false;
        this.wallCover.active = true;
        this.door.active = false;
        this.zoomInForChest(0);
    }

    public finishGame() {
        this.rewardText.getComponent(ee.LanguageComponent).key = "temple_you_won_the_game";
        const pos = this.rewardText.node.position;
        pos.y = 20;
        this.rewardText.node.position = pos;
        this.rewardText.node.active = true;
    }
}
