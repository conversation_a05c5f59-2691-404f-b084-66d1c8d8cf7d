import * as ee from '../../libraries/ee/index';
import * as gm from '../../engine/gm_engine';
import {ConfirmChestDialog} from "../../dialog/ConfirmChestDialog";

const {ccclass, property} = cc._decorator;

@ccclass
export default class GateFooter extends cc.Component {
    @property({type: [cc.Node], visible: true})
    private _steps: cc.Node[] = [];

    @property({type: [cc.Node], visible: true})
    private _checks: cc.Node[] = [];

    private get steps(): cc.Node[] {
        return gm.retrieveNull(this._steps);
    }

    private get checks(): cc.Node[] {
       return gm.retrieveNull(this._checks);
    }

    private isShowChestInfo: boolean = false;

    /** Used in editor. */
    private pressedChest(event:Event, customEventData: string) {
        if (this.isShowChestInfo) {
            return;
        }
        this.isShowChestInfo = true;
        this.showChestInfoDialog(+customEventData);
    }

    private showChestInfoDialog(type: gm.ChestType): void {
        const dialogManager = ee.ServiceLocator.resolve(ee.DialogManager);
        ConfirmChestDialog.create().then(dialog => {
            dialog.chestType = type;
            dialog.isButtonEnabled = false;
            dialog.onDidHide(()=>{
                this.isShowChestInfo = false;
            })
            dialog.show(dialogManager);
        });
    }

    public setCompleteLevel(level: number) {
        for (let i = 0; i < level && i < this.steps.length - 1; i ++) {
            this.steps[i].active = true;
        }
        for (let i = 0; i < level && i < this.checks.length; i ++) {
            this.checks[i].active = true;
        }

        for (let i = level; i < this.steps.length - 1; i ++) {
            this.steps[i].active = false;
        }
        for (let i = level; i < this.checks.length; i ++) {
            this.checks[i].active = false;
        }
    }
}
