import * as ee from '../../libraries/ee/index';
import {DialogManager} from '../../libraries/ee/index';
import {IRefreshable} from "../../manager/IRefreshable";
import {ConfigManager} from "../../manager/config/ConfigManager";
import {DataManager} from "../../manager/data/DataManager";
import {TimeManager} from "../../manager/time/TimeManager";
import {LevelManager} from "../../manager/level/LevelManager";
import UnlockTempleDialog from "./dialog/UnlockTempleDialog";
import CompletedTempleDialog from "./dialog/CompletedTempleDialog";
import EndTempleDialog from "./dialog/EndTempleDialog";
import {TimeUtils} from "../../utils/TimeUtis";

const SECONDS_PER_DAY = 24 * 3600;

const createDefaultData: () => HiddenTempleData = () => ({
    isUnLock: false,
    isCompleted: false,
    axeAmount: 0,
    startTime: 0,
    currentLevel: 0,
    levelState: LevelState.begin,
    hadShowUnlock: false,
    hadShowDialog: false,
    completedTutorial: false,
    hadShowNoMoreAxe: false,
    completedTime: 0,
    sequenceWin: 0,
    hadPreviousCompleted: false,
});

export enum LevelState {
    begin,
    showReward,
    end
}

enum Key {
    Data = 'hidden_temple_storage',
    GemData = 'hidden_temple_gems_data',
    BrokenData = 'hidden_temple_broken_data'
}

interface HiddenTempleConfig {
    levelStoryUnlock: number;
    eventDuration: number; //days
    axeReward: number;
    // part 2
    sequenceWin: number;
    unlockDelayDays: number;
}

interface HiddenTempleData {
    isUnLock: boolean;
    isCompleted: boolean;
    axeAmount: number;
    startTime: number; // timeManger.secondsNow()
    currentLevel: number; // 1-5
    levelState: LevelState;
    // for show unlock dialog
    hadShowUnlock: boolean;
    // for tutorial
    hadShowDialog: boolean;
    completedTutorial: boolean;
    hadShowNoMoreAxe: boolean;
    // for Re-unlock
    completedTime: number;
    sequenceWin: number;
    hadPreviousCompleted: boolean;
}

export interface LocationsData {
    x: number,
    y: number
}

export interface GemData {
    locations: LocationsData[]
}

@ee.service("HiddenTempleManager")
export abstract class HiddenTempleManager implements ee.Service, IRefreshable {
    public abstract destroy(): void;

    public abstract levelCompleted: boolean;

    public abstract getLastUnlockedAreaLevel(levelManager: LevelManager): [number, number];
    public abstract isUnlock(area: number, level: number): boolean;
    public abstract checkUnlock(area: number, level: number, fromWin: boolean): boolean;
    public abstract getAxeReward(area: number, level: number): number;
    public abstract completeLevel(): void;

    public abstract addAxeAmount(amount: number): void;
    public abstract setLevelState(level: number, state: LevelState): void;
    public abstract getData() : HiddenTempleData;
    public abstract getRemainString(): string;
    public abstract isEndEvent(): boolean;

    public abstract getGemData(): GemData[];
    public abstract setGemData(data: GemData[]): void;

    public abstract getBrokenData(): LocationsData[];
    public abstract setBrokenData(data: LocationsData[]): void;

    // for refreshData
    public abstract refreshData(): void;
    public abstract setRefreshCallback(callback:() => void): void;

    // for show Completed Dialog
    public abstract showCompletedDialog(dialogManager: DialogManager): Promise<void>;
    public abstract setCompleted(): void;
    public abstract isCompleted(): boolean;

    // for show End Event Dialog
    public abstract showEndDialog(dialogManager: DialogManager): Promise<void>;

    // for show unlock Dialog
    public abstract showUnlockDialog(dialogManager: DialogManager): Promise<void>;
    public abstract setHadShowUnlock(): void;
    public abstract isHadShowUnlock(): boolean;

    // for tutorial
    public abstract completeTutorial(): void;
    public abstract isTutorialComplete(): boolean;
    public abstract setHadShowDialog(): void;
    public abstract isHadShowDialog(): boolean;
    public abstract setHadShowNoMoreAxe(): void;
    public abstract isHadShowNoMoreAxe(): boolean;

    // for cheat
    public abstract setChangeAxeCallback(callback: () => void): void;
    public abstract setChangeMapCallback(callback: () => void): void;
    public abstract setBeginEventTempleCallback(callback: () => void): void;
    public abstract cheatAxeAmount(amount: number): void;
    public abstract openMap(level: number): void;
    public abstract endEvent(second: number): void;
    public abstract beginEvent(): void;
}

export class DefaultHiddenTempleManager extends HiddenTempleManager {

    private _levelCompleted: boolean = false;
    public set levelCompleted(value: boolean) {
        this._levelCompleted = value;
    }
    public get levelCompleted(): boolean {
        return this._levelCompleted;
    }

    /** Config. */
    private config: HiddenTempleConfig;

    /** Data. */
    private data: HiddenTempleData;

    /** Board Data */
    private gemData: GemData[];

    /** Broken Data */
    private brokenData: LocationsData[];

    /** Updater. */
    private dataDirty = false;
    private readonly node: cc.Node;

    /** Refresh Data */
    private onRefresh: () => void;

    /** For Cheat */
    private onChangeAxe: () => void;
    private onChangeMap: () => void;
    private onBeginEventTemple: () => void;

    public constructor(
        configManager: ConfigManager,
        private readonly dataManager: DataManager,
        private readonly timeManager: TimeManager,
        ) {
        super();

        // Config.
        this.config = configManager.getValue("hidden_temple_game_config");

        // Data.
        this.data = dataManager.getValue(Key.Data, createDefaultData());
        // Nếu đã hoàn thành ở version trước chưa có completed time.
        // Thì thực hiện lại completed
        if (this.data.isCompleted) {
            if (this.data.completedTime == null || this.data.completedTime === 0) {
                this.data.isCompleted = false;
                this.setCompleted();
            }
        }

        // BoarData
        this.gemData = dataManager.getValue(Key.GemData, []);

        // BrokenData
        this.brokenData = dataManager.getValue(Key.BrokenData, []);

        // Để tránh data lưu vết gây lỗi khi bắt đầu unlock sự kiện
        // Xóa các lưu vết khi dialog unlock chưa hiển thị hoặc tutorial chưa bắt đầu.
        if (!this.data.hadShowDialog || !this.data.hadShowDialog) {
            this.gemData = [];
            this.brokenData = [];
            this.data.axeAmount = this.config.axeReward;
            this.dataDirty = true;
        }

        // Sync handler.
        dataManager.addHandler('hidden_temple', {
            load: data => {
                this.data = data[Key.Data] || createDefaultData();
                this.gemData = data[Key.GemData] || [];
                this.brokenData = data[Key.BrokenData] || [];
                this.dataDirty = true
            },
            save: data => {
                data[Key.Data] = this.data;
                data[Key.GemData] = this.gemData;
                data[Key.BrokenData] = this.brokenData;
            },
        });

        // Data updater.
        this.node = new cc.Node();
        cc.director.getScheduler().schedule(() => {
            if (this.dataDirty) {
                this.dataDirty = false;
                this.saveData();
            }
        }, this.node, 0);
    }

    public destroy(): void {
        this.dataManager.removeHandler('hidden_temple');
        this.node.destroy();
    }

    private saveData(): void {
        this.dataManager.setValue(Key.Data, this.data);
        this.dataManager.setValue(Key.GemData, this.gemData);
        this.dataManager.setValue(Key.BrokenData, this.brokenData);

    }

    public override refreshData(): void {
        this.data = this.dataManager.getValue(Key.Data, createDefaultData());
        this.gemData = this.dataManager.getValue(Key.GemData, []);
        this.brokenData = this.dataManager.getValue(Key.BrokenData, []);
        this.onRefresh && this.onRefresh();
    }

    public setRefreshCallback(callback: () => void): void {
        this.onRefresh = callback;
    }

    public addAxeAmount(amount: number): void {
        this.data.axeAmount += amount;
        this.dataDirty = true;
    }

    public getLastUnlockedAreaLevel(levelManager: LevelManager): [number, number] {
        const lastUnlockedArea = levelManager.getLastUnlockedArea();
        const info = levelManager.getStoryAreaInfo(lastUnlockedArea);
        const lastUnlockedLevel = info.unlockedLevels;
        let area = lastUnlockedArea + 1;
        let level = lastUnlockedLevel - 1;
        if (level == 0) {
            area -= 1;
            level = 20;
        }
        return [area, level]
    }

    public isUnlock(area: number, level: number): boolean {
        if (this.data.isUnLock) {
            const remain = this.getRemainSeconds();
            return remain > 0;
        }

        let isUnlocked = false;
        if (this.data.hadPreviousCompleted) {
            isUnlocked = this.data.sequenceWin >= this.config.sequenceWin ||
                this.getRemainDays() <= 0;
        } else {
            const levelPass = this.getLevel(area, level);
            isUnlocked = levelPass >= this.config.levelStoryUnlock;
        }

        return isUnlocked;
    }

    public checkUnlock(area: number, level: number, fromWin: boolean): boolean {
        if (this.data.isUnLock) {
            const remain = this.getRemainSeconds();
            return remain > 0;
        }

        const isUnlocked = this.isUnlock(area, level);
        if (isUnlocked) {
            this.data.isUnLock = true;
            this.data.startTime = this.timeManager.secondsNow();
            this.data.isCompleted = false;
            this.data.completedTime = 0;
            this.data.currentLevel = 1;
            this.data.levelState = LevelState.begin;

            if (!fromWin) {
                this.data.axeAmount = this.config.axeReward;
            }

            this.dataDirty = true;
            return true;
        }
        return false;
    }

    public getAxeReward(): number {
        if (this.levelCompleted) {
            return 0;
        }
        if (this.data.isCompleted) {
            return 0;
        }
        if (this.data.currentLevel === 5 && this.data.levelState != LevelState.begin) {
            return 0;
        }
        return this.config.axeReward;
    }

    public completeLevel() {
        if (this.levelCompleted) {
            return;
        }
        if (this.data.hadPreviousCompleted) {
            this.data.sequenceWin += 1;
            this.dataDirty = true;
        }
    }

    public setLevelState(level: number, state: LevelState): void {
        this.data.currentLevel = level;
        this.data.levelState = state;
        this.gemData = [];
        this.brokenData = [];
        this.dataDirty = true;
    }

    public getData() : HiddenTempleData {
        return this.data;
    }

    public getGemData(): GemData[] {
        return this.gemData;
    }

    public setGemData(data: GemData[]): void {
        this.gemData = data;
        this.dataDirty = true;
    }

    public getBrokenData(): LocationsData[] {
        return this.brokenData;
    }

    public setBrokenData(data: LocationsData[]): void {
        this.brokenData = data;
        this.dataDirty = true;
    }

    public getRemainString(): string {
        const secondsRemain = this.getRemainSeconds();
        return TimeUtils.getRemainString(secondsRemain);
    }

    public isEndEvent(): boolean {
        return this.getRemainSeconds() <= 0;
    }

    private getLevel(area: number, level: number): number {
        return ((area - 1) * 20) + level;
    }

    private getRemainDays(): number {
        const duration = this.config.unlockDelayDays * SECONDS_PER_DAY;
        const remainSeconds =  this.timeManager.getRemainingTime(this.data.completedTime, duration);
        return remainSeconds / SECONDS_PER_DAY;
    }

    private getRemainSeconds(): number {
        const duration = this.config.eventDuration * SECONDS_PER_DAY
        return this.timeManager.getRemainingTime(this.data.startTime, duration)
    }

    // for show completed dialog
    public async showCompletedDialog(dialogManager: DialogManager): Promise<void> {
        this.setCompleted();
        const dialog = await CompletedTempleDialog.create();
        dialog.show(dialogManager);
        await new Promise<void>(resolve => dialog.onDidHide(() => {
            resolve();
        }));
    }

    public setCompleted() {
        if (this.data.isCompleted) {
            return;
        }
        const completedTime = this.timeManager.secondsNow();
        this.gemData = [];
        this.brokenData =[];
        this.data = createDefaultData();
        this.data.isCompleted = true;
        this.data.completedTime = completedTime;
        this.data.hadPreviousCompleted = true;
        this.dataDirty = true;
    }

    public isCompleted(): boolean {
        return this.data.isCompleted;
    }

    // for show end dialog
    public async showEndDialog(dialogManager: DialogManager): Promise<void> {
        if (this.data.isCompleted) {
            return;
        }
        this.setCompleted();
        const dialog = await EndTempleDialog.create();
        dialog.show(dialogManager);
        await new Promise<void>(resolve => dialog.onDidHide(() => {
            resolve();
        }));
    }

    // for show unlock dialog
    public async showUnlockDialog(dialogManager: DialogManager): Promise<void> {
        const dialog = await UnlockTempleDialog.create();
        dialog.show(dialogManager);
        await new Promise<void>(resolve => dialog.onDidHide(() => {
            resolve();
        }));
        this.setHadShowUnlock();
    }

    public setHadShowUnlock() {
        if (this.data.hadShowUnlock) {
            return;
        }
        this.data.hadShowUnlock = true;
        this.dataDirty = true;
    }

    public isHadShowUnlock(): boolean {
        return this.data.hadShowUnlock;
    }

    // For tutorial
    public setHadShowDialog() {
        this.data.hadShowDialog = true;
        this.dataDirty = true;
    }

    public isHadShowDialog(): boolean {
        return this.data.hadShowDialog;
    }

    public completeTutorial() {
        this.data.completedTutorial = true;
        this.dataDirty = true;
    }

    public isTutorialComplete(): boolean {
        return this.data.completedTutorial;
    }

    public setHadShowNoMoreAxe() {
        this.data.hadShowNoMoreAxe = true;
        this.dataDirty = true;
    }

    public isHadShowNoMoreAxe(): boolean {
        return this.data.hadShowNoMoreAxe;
    }

    //-------------
    // For cheat
    //-------------
    public cheatAxeAmount(amount: number): void {
        this.data.axeAmount += amount;
        this.dataDirty = true;
        this.onChangeAxe && this.onChangeAxe();
    }

    public openMap(level: number): void {
        if (level < 0 || level > 5) {
            return;
        }
        this.data.isCompleted = false;
        this.data.currentLevel = level;
        this.data.levelState = LevelState.begin;
        this.gemData = [];
        this.brokenData = [];
        this.dataDirty = true;
        this.onChangeMap && this.onChangeMap();
    }

    public endEvent(second: number): void {
        const duration = this.config.eventDuration * SECONDS_PER_DAY
        this.data.startTime = this.timeManager.secondsNow() - duration + second;
        this.dataDirty = true;
    }

    public beginEvent(): void {
        this.config.levelStoryUnlock = 3;
        this.data.isUnLock = true;
        this.data.isCompleted = false;
        this.data.currentLevel = 1;
        this.data.levelState = LevelState.begin;
        this.gemData = [];
        this.brokenData = [];
        this.data.startTime = this.timeManager.secondsNow();
        this.dataDirty = true;
        this.onBeginEventTemple && this.onBeginEventTemple();
    }

    setChangeAxeCallback(callback: () => void) {
        this.onChangeAxe = callback;
    }

    setChangeMapCallback(callback: () => void) {
        this.onChangeMap = callback;
    }

    setBeginEventTempleCallback(callback: () => void) {
        this.onBeginEventTemple = callback;
    }
}



