import * as gm from '../../engine/gm_engine';
import * as ee from "../../libraries/ee/index";
import {DialogManager} from "../../libraries/ee/index";
import GateLayer from "./GateLayer";
import BoardLayer from "./BoardLayer";
import {HiddenTempleManager, LevelState} from "./HiddenTempleManager";
import HiddenTempInfoDialog from "./HiddenTempInfoDialog";
import {AudioManager} from "../../manager/audio/AudioManager";
import {SoundType} from "../../manager/audio/SoundType";
import {HiddenTempleTutorial} from "./HiddenTempleTutorial";
import HiddenTempleTutorialView from "./HiddenTempleTutorialView";
import {BoardListener} from "./HiddenTempleBoard";
import {GateListener} from "./HiddenTempleGate";

const {ccclass, property} = cc._decorator;

@ccclass
export default class HiddenTempleLayer extends cc.Component {
    @ee.nest(GateLayer)
    private readonly _gate: GateLayer | null = null;

    @ee.nest(BoardLayer)
    private readonly _board: BoardLayer | null = null;

    @property({type: cc.Button, visible: true})
    private readonly _revealButton: cc.Button | null = null;

    @property({type: HiddenTempleTutorialView, visible: true})
    private readonly _tutorialView: HiddenTempleTutorialView | null = null;

    private get gate(): GateLayer {
        if (this._gate === null) {
            throw Error('Item not registered.');
        }
        return this._gate;
    }

    private get board(): BoardLayer {
        if (this._board === null) {
            throw Error('Item not registered.');
        }
        return this._board;
    }

    private get revealButton(): cc.Button {
        return gm.retrieveNull(this._revealButton);
    }

    private get tutorialView(): HiddenTempleTutorialView {
        return gm.retrieveNull(this._tutorialView);
    }

    private audioManager: AudioManager;
    private dialogManager: ee.DialogManager;
    private hiddenTempleManager: HiddenTempleManager;
    private currentLevel: number;
    private currentState: LevelState;

    private templeTutorial: HiddenTempleTutorial;
    private onEndHiddenTemple: () => void;
    private onDestroyCallback: () => void;

    protected onDisable() {
        this.audioManager && this.audioManager.stopAllSounds();
        this.onDestroyCallback && this.onDestroyCallback();
    }

    protected onDestroy() {
        this.onDestroyCallback && this.onDestroyCallback();
        this.hiddenTempleManager.setChangeAxeCallback(null);
        this.hiddenTempleManager.setChangeMapCallback(null);
        this.hiddenTempleManager.setRefreshCallback(null);
    }

    public initialize(callback: () => void): void {
        this.audioManager = ee.ServiceLocator.resolve(AudioManager);
        this.dialogManager = ee.ServiceLocator.resolve(DialogManager);

        this.hiddenTempleManager = ee.ServiceLocator.resolve(HiddenTempleManager);
        this.hiddenTempleManager.setChangeAxeCallback(()=>{this.onChangeAxe()});
        this.hiddenTempleManager.setChangeMapCallback(()=>{this.onChangeMap()});
        this.onEndHiddenTemple = callback;
        this.hiddenTempleManager.setRefreshCallback(() => {
            this.onEndHiddenTemple && this.onEndHiddenTemple();
        });
        this.hiddenTempleManager.setHadShowUnlock();
    }

    public initCurrent() {
        const data = this.hiddenTempleManager.getData();
        this.currentLevel = data.currentLevel === 0 ? 1 : data.currentLevel;
        this.currentState = data.levelState;
        this.templeTutorial = new HiddenTempleTutorial(this, this.dialogManager);
        this.tutorialView.hide();
        this.playGame();
    }

    public addParticleChest() {
        this.gate.addParticleChest();
    }

    public removeParticleChest() {
        this.gate.removeParticleChest();
    }

    public setOnDestroyCallback(callback: () => void): void {
        this.onDestroyCallback = callback;
    }

    private playGame(animateNext: boolean = false): void {
        this.setCurrentLevel(this.currentLevel, animateNext);

        // add board callback and listener
        this.board.setShowInfoCallback(() => {
            this.showDialogInfo().then();
        });
        const boardListener: BoardListener = {
            onLoaded: ()=> {this.onBoardLoaded();},
            onShowNoMoreAxe: (remainPos) => {this.showNoMorePickaxesTutorial(remainPos);},
            onCompleteTutorial: () => {this.completeTutorial();},
            onPickGem: (gem) => {this.gate.receiveGemFrom(gem);},
            onPickGemImmediate: (gem) => {this.gate.receiveGemImmediately(gem);},
            onAllPicked: () => { this.revealButton.node.active = false; },
            onShowEndEvent: () => {
                this.showEndEvent();
            }
        }
        this.board.currentBoard.setListener(boardListener);

        // add gate callback and listener
        this.gate.setCompleteGateCallback(() => {
            if (this.currentLevel < 5) {
                this.currentLevel += 1;
                this.hiddenTempleManager.setLevelState(this.currentLevel, LevelState.begin);
                this.currentState = LevelState.begin;
                this.playGame(true)
                this.updateAxeAmountUi();
                this.board.currentBoard.showAxeRemainFrame();
                this.showEndEvent();
            } else {
                this.hiddenTempleManager.setLevelState(this.currentLevel, LevelState.end);
                this.currentState = LevelState.end;
                this.gate.finishGame();
                this.board.finishGame();
                this.hiddenTempleManager.showCompletedDialog(this.dialogManager).then();
            }
        });
        const gateListener: GateListener = {
            onShowChest:() => {this.hiddenTempleManager.setLevelState(this.currentLevel, LevelState.showReward);},
            onShowEndEvent: () => {
                this.showEndEvent();
            }
        }
        this.gate.currentGate.setListener(gateListener);
    }

    private onBoardLoaded(): void {
        if (this.templeTutorial.checkStart()) {
            this.templeTutorial.start();
        }
        this.updateAxeAmountUi();
    }

    private updateAxeAmountUi() {
        const canReveal = this.board.canRevealAll();
        this.checkToActiveRevealAll(canReveal);
        this.board.currentBoard.canReveal = canReveal;
        this.board.currentBoard.updateAxeRemain()
    }

    public setCurrentLevel(level:number, animateNext: boolean) {
        if (this.currentState === LevelState.end) {
            this.gate.showGameEnd(5);
            this.board.showGameEnd(5);
            return;
        }
        if (this.currentState === LevelState.showReward) {
            this.gate.showChestReward(level);
            this.board.showChestReward(level);
            return;
        }
        this.gate.setCurrentGate(level, animateNext);
        this.board.setCurrentBoard(level, animateNext);
    }

    private async showDialogInfo() {
        this.audioManager.playSound(SoundType.ButtonPress);
        const dialog = await HiddenTempInfoDialog.create();
        dialog.show(this.dialogManager);
    }

    private checkToActiveRevealAll(canReveal: boolean) {
        const showRevealButton = this.board.currentBoard.canShowButtonReveal(canReveal);
        if (showRevealButton) {
            if (!this.revealButton.node.active) {
                this.showRevealButton();
            }
            return;
        }
        this.revealButton.node.active = false;
    }

    private showRevealButton() {
        const node = this.revealButton.node;
        node.active = true;
        node.opacity = 0;
        const fadeIn = cc.tween(node).to(0.1, {opacity: 255}) // cc.fadeIn(0.1);
        const zoomInOut = cc.tween(node)
                .to(0.1, {scale: 1.4})
                .delay(0.1)
                .to(0.05, {scale: 0.8})
                .delay(0.05)
                .to(0.05, {scale: 1.2})
                .delay(0.02)
                .to(0.1, {scale:  1})

        cc.tween(node)
            .delay(1)
            .parallel(fadeIn, zoomInOut)
            .call(()=>{
                this.audioManager.playSound(SoundType.TempleDig);
            })
            .start();
    }

    /** Used in editor. */
    private pressedRevealAll() {
        this.revealButton.node.active = false;
        this.board.currentBoard.revealAll();
    }

    // for Cheat
    private onChangeAxe() {
        this.updateAxeAmountUi();
    }

    private onChangeMap() {
        const data = this.hiddenTempleManager.getData();
        this.currentLevel = data.currentLevel;
        this.currentState = data.levelState;
        this.playGame();
        this.gate.currentGate.resetGate();
        this.board.currentBoard.resetBoard();
        this.updateAxeAmountUi();
    }

    // for Tutorial
    public playHiddenTempleTutorial(delay: number) {
        this.tutorialView.setOnBreakCell(()=>{
            this.breakTheCellTutorial();
        })
        this.tutorialView.show();
        cc.tween(this.node)
            .delay(delay)
            .call(()=> {
                this.board.currentBoard.playTutorial((position) => {
                    this.posThePointer(position);
                }, (posRemain) => {
                    this.posRemain(posRemain);
                });
            })
            .start();
    }

    public breakTheCellTutorial() {
        this.board.currentBoard.breakTheTutorialCell();
        this.hiddenTempleManager.setHadShowDialog()
    }

    private posThePointer(position: cc.Vec3) {
        this.tutorialView.updateRemainLabel(this.hiddenTempleManager.getData().axeAmount);
        this.tutorialView.showPointer(position);
    }

    private posRemain(posRemain: cc.Vec3) {
        this.tutorialView.showRemain(posRemain);
    }

    public completeTutorial() {
        this.tutorialView.hide();
    }

    public showNoMorePickaxesTutorial(remainPos: cc.Vec3) {
        const axeAmount = this.hiddenTempleManager.getData().axeAmount
        this.tutorialView.updateRemainLabel(axeAmount);
        this.tutorialView.showRemain(remainPos);
        if (axeAmount === 0) {
            this.tutorialView.showNoMoreAxe(remainPos);
        } else {
            this.tutorialView.hide();
        }
    }

    public showEndEvent() {
        if (this.hiddenTempleManager.isEndEvent() &&
        this.hiddenTempleManager.getData().axeAmount <= 0) {
            this.hiddenTempleManager.showEndDialog(this.dialogManager).then(()=> {
                this.onEndHiddenTemple && this.onEndHiddenTemple();
            });
        }
    }
}
