import ccclass = cc._decorator.ccclass;
import disallowMultiple = cc._decorator.disallowMultiple;
import menu = cc._decorator.menu;
import property = cc._decorator.property;
import {LevelCloudSyncer} from "../../manager/level/LevelCloudSyncer";
import {LevelLoaderManager} from "../../manager/level/LevelLoaderManager";
import FakeConfigManager from "../dependencies/FakeConfigManager";
import {ConfigKey} from "../../manager/config/ConfigManager";
import FakeCheatManager from "../dependencies/FakeCheatManager";
import {defaultServerConfig} from "../../manager/level/DefaultServerConfig";
import {GameServerManager} from "../../manager/game_server/GameServerManager";
import FakeDataManager from "../dependencies/FakeDataManager";
import * as ee_x from "@senspark/ee-x";
import {GameMode} from "../../manager/level/LevelManager";

@ccclass
@disallowMultiple
@menu("editor/LevelCloudSyncerSceneTest")
export default class LevelCloudSyncerSceneTest extends cc.Component {

    @property(cc.JsonAsset)
    private levelOverrideJson: cc.JsonAsset = null;


    protected async start() {
        ee_x.PluginManager.initializePlugins();

        const gameServerManager = new GameServerManager(defaultServerConfig, new FakeDataManager())
        const levelCloudSyncer = new LevelCloudSyncer(gameServerManager);

        const fakeConfigManager = new FakeConfigManager();
        const fakeCheatManager = new FakeCheatManager();
        fakeConfigManager.setDefaultValue(ConfigKey.LEVELS_OVERRIDE, this.levelOverrideJson.json);
        const levelLoaderManager = new LevelLoaderManager(fakeConfigManager, levelCloudSyncer, fakeCheatManager);
        const levelLoader = await levelLoaderManager.create(1, 1, GameMode.STORY);
        const node = await levelLoader.load();
        this.node.addChild(node);
    }
}