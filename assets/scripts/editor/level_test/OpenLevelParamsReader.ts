import ccclass = cc._decorator.ccclass;
import disallowMultiple = cc._decorator.disallowMultiple;
import menu = cc._decorator.menu;
import isNumber = cc.js.isNumber;

@ccclass
@disallowMultiple
@menu("editor/OpenLevelParamsReader")
export default class OpenLevelParamsReader extends cc.Component {
    public area: number = 8;
    public level: number = 2;

    protected onLoad() {
        const params = new URLSearchParams(document.location.search);
        const pArea = params.get("area");
        const pLevel = params.get("level");

        if (!pArea || !pLevel) {
            return;
        }

        const area = parseInt(pArea);
        const level = parseInt(pLevel);

        if (!isNumber(area) || isNaN(area) || !isNumber(level) || isNaN(level)) {
            return;
        }
        // console.log(`Open Area: ${pArea}, Level: ${pLevel}`);
        this.area = area - 1;
        this.level = level - 1;
    }
}