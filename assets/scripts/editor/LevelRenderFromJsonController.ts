import ccclass = cc._decorator.ccclass;
import property = cc._decorator.property;
import isNumber = cc.js.isNumber;
import menu = cc._decorator.menu;
import disallowMultiple = cc._decorator.disallowMultiple;
import LevelRenderFromJson from "./LevelRenderFromJson";
import {ILevelInfo, ILevelJsonData} from "./LevelDataJson";
import LevelToJson from "./LevelToJson";
import {GameMode} from "../manager/level/LevelManager";

const CLASS_NAME = "LevelRenderFromJsonController";
const MIN_AREA = 1;
const MAX_AREA = 12;
const MIN_LEVEL = 1;
const MAX_LEVEL = 20;

@ccclass
@disallowMultiple
@menu(`editor/${CLASS_NAME}`)
export default class LevelRenderFromJsonController extends cc.Component {
    @property(LevelRenderFromJson)
    public controller: LevelRenderFromJson = null;

    @property(cc.Button)
    public btnPrevLevel: cc.Button = null;

    @property(cc.Button)
    public btnNextLevel: cc.Button = null;

    @property(cc.Button)
    public btnPreview: cc.Button = null;

    @property(cc.EditBox)
    public editArea: cc.EditBox = null;

    @property(cc.EditBox)
    public editLevel: cc.EditBox = null;

    @property(cc.EditBox)
    public editMode: cc.EditBox = null;

    public area: number = undefined;
    public level: number = undefined;
    public mode: GameMode = undefined;

    protected onLoad() {
        this.btnPrevLevel.node.on(cc.Node.EventType.TOUCH_END, this.onBtnPrevLevelClicked, this);
        this.btnNextLevel.node.on(cc.Node.EventType.TOUCH_END, this.onBtnNextLevelClicked, this);
        this.btnPreview.node.on(cc.Node.EventType.TOUCH_END, this.onBtnPreviewClicked, this);

        const onEditDone = new cc.Component.EventHandler();
        onEditDone.target = this.node;
        onEditDone.component = CLASS_NAME;
        onEditDone.handler = "onEditDone";

        this.editArea.editingDidEnded.push(onEditDone);
        this.editLevel.editingDidEnded.push(onEditDone);

        this.setNewAreaLevel(MIN_AREA, MIN_LEVEL, GameMode.STORY);
    }

    public getLevelInfo(): ILevelInfo {
        let mode = this.mode;
        let area = this.area;
        let level = this.level;
        if (mode == undefined || area == undefined || level == undefined) {
            const pName = this.controller.node.name;
            return new LevelToJson().getLevelInfo(pName);
        }
        return {
            mode: mode,
            area: area,
            level: level
        };
    }

    public setNewAreaLevel(newArea: number, newLevel: number, newMode: GameMode) {
        if (!isNumber(newArea) || isNaN(newArea) || newArea < MIN_LEVEL || newArea > MAX_LEVEL) {
            cc.error("Area must be between 1 and 20");
            return;
        }
        if (!isNumber(newLevel) || isNaN(newLevel) || newLevel < MIN_LEVEL || newLevel > MAX_LEVEL) {
            cc.error("Level must be between 1 and 20");
            return;
        }
        if (newMode < GameMode.STORY || newMode > GameMode.CHARACTER) {
            cc.error("Mode must = 0 (Main) or 1 (Event) or 2 (Jaki)");
            return;
        }
        if (newArea != this.area || newLevel != this.level || newMode != this.mode) {
            this.area = newArea;
            this.level = newLevel;
            this.mode = newMode;
            cc.log(`Area: ${this.area}, Level: ${this.level}, Mode: ${this.mode}`);
            this.controller.renderLevelBy(this.area, this.level, this.mode);
            this.editArea.string = this.area.toString();
            this.editLevel.string = this.level.toString();
        }
    }

    public setCustomLevelJson(json:ILevelJsonData) {
        const lvName = json.name;
        const parts = new LevelToJson().getLevelInfo(lvName);
        this.mode = parts.mode;
        this.area = parts.area;
        this.level = parts.level;
        this.controller.renderLevelByJson(json);
    }

    private onBtnPrevLevelClicked() {
        let newLevel = this.level - 1;
        let newArea = this.area;
        if (newLevel < MIN_LEVEL) {
            newArea--;
            newLevel = MAX_LEVEL;
            if (newArea < MIN_AREA) {
                cc.log("Min area reached");
                return;
            }
        }
        this.setNewAreaLevel(newArea, newLevel, GameMode.STORY);
    }

    private onBtnNextLevelClicked() {
        let newLevel = this.level + 1;
        let newArea = this.area;
        if (newLevel > MAX_LEVEL) {
            newArea++;
            newLevel = MIN_LEVEL;
            if (newArea > MAX_AREA) {
                cc.log("Max area reached");
                return;
            }
        }
        this.setNewAreaLevel(newArea, newLevel, GameMode.STORY);
    }

    private onBtnPreviewClicked() {
        const url = document.location.origin
        const params = new URLSearchParams();
        params.set("scene", "level_preview");
        params.set("area", this.area.toString());
        params.set("level", this.level.toString());
        window.open(`${url}/?${params.toString()}`, "_blank");
    }

    private onEditDone() {
        const newArea = parseInt(this.editArea.string);
        const newLevel = parseInt(this.editLevel.string);
        const newMode = parseInt(this.editMode.string);
        this.setNewAreaLevel(newArea, newLevel, newMode);
    }
}