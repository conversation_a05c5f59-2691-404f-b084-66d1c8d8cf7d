import ccclass = cc._decorator.ccclass;
import disallowMultiple = cc._decorator.disallowMultiple;
import menu = cc._decorator.menu;
import executeInEditMode = cc._decorator.executeInEditMode;
import property = cc._decorator.property;
import LevelRenderFromJsonController from "./LevelRenderFromJsonController";
import LevelToJson from "./LevelToJson";
import {GameMode} from "../manager/level/LevelManager";

@ccclass
@disallowMultiple
@menu("editor/Level Editor UI")
@executeInEditMode()
export default class LevelEditorUI extends cc.Component {

    @property(LevelRenderFromJsonController)
    private controller: LevelRenderFromJsonController = null;

    protected onEnable() {
        // Editor.Ipc.sendToPackage('level-editor', 'say-hello');
        // Editor.Panel.open('level-editor');
    }

    openLevelByData(data: any) {
        if (data.mode != undefined && data.area && data.level) {
            this.openLevel(data.mode, data.area, data.level);
        } else if (data.jsonData) {
            this.openJson(data.jsonData);
        }
    }

    openLevel(mode: GameMode, area: number, level: number) {
        Editor.log('Open level:', mode, area, level);
        this.controller.setNewAreaLevel(area, level, mode);
    }

    openJson(json: string) {
        try {
            const jsonData = JSON.parse(json);
            this.controller.setCustomLevelJson(jsonData);
        } catch (e) {
            Editor.error('[openJsonFile] Error reading JSON file:', e);
        }
    }

    exportJson() {
        Editor.log('Export JSON');
        const lvInfo = this.controller.getLevelInfo();
        const rootNode = this.controller.controller.node;
        new LevelToJson().read(lvInfo, rootNode);
    }
}

interface ILevelExactData {
    mode: GameMode,
    area: number,
    level: number
}

interface IJsonData {
    url: string
}