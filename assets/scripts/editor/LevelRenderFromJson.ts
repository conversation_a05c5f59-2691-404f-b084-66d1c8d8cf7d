import ccclass = cc._decorator.ccclass;
import property = cc._decorator.property;
import executeInEditMode = cc._decorator.executeInEditMode;
import menu = cc._decorator.menu;
import disallowMultiple = cc._decorator.disallowMultiple;
import {NewLevelLoader} from "../manager/level/NewLevelLoader";
import {getBuiltInLevelResourcePath} from "../manager/level/LevelHelper";
import {BuiltInLevelLoader} from "../manager/level/BuiltInLevelLoader";
import LevelToJson from "./LevelToJson";
import {ILevelInfo, ILevelJsonData} from "./LevelDataJson";
import {GameMode} from "../manager/level/LevelManager";

@ccclass
@disallowMultiple
@executeInEditMode
@menu("editor/LevelRenderFromJson")
export default class LevelRenderFromJson extends cc.Component {
    @property(cc.JsonAsset)
    public levelData: cc.JsonAsset = null;

    protected onLoad() {
        cc.debug.setDisplayStats(false);
    }

    // protected onLoad() {
    //     this.renderLevel(this.levelData.json as ILevelJsonData)
    // }

    protected onEnable() {
        if (CC_EDITOR) {
            if (this.levelData) {
                this.node.name = this.levelData.name;
            }
            this.renderLevelByJson(this.levelData.json);
        }
    }

    public renderLevelBy(area: number, level: number, mode: GameMode) {
        const path = getBuiltInLevelResourcePath(area, level, mode);
        cc.log(`Render level ${path}`);
        const lvInfo: ILevelInfo = {mode, area, level};
        this.node.name = new LevelToJson().getLevelName(lvInfo);
        new BuiltInLevelLoader(path, {
            forEditor: true,
            rootNode: this.node
        }).load().catch(console.error);
    }

    public renderLevelByJson(json: ILevelJsonData) {
        cc.log(`Render level ${json.name}`);
        this.node.name = json.name;
        NewLevelLoader.createFromJsonData(json, {
            forEditor: true,
            rootNode: this.node
        }).load().catch(console.error);
    }
}