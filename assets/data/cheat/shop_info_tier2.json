{"ruby": [{"name": "ruby_pack_0", "offer": 0, "iap": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_pack_0", "ruby": 600}, {"name": "ruby_pack_1", "offer": 10, "iap": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_pack_1", "ruby": 1650}, {"name": "ruby_pack_2", "offer": 20, "iap": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_pack_2", "ruby": 3600}, {"name": "ruby_pack_3", "offer": 30, "iap": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_pack_3", "ruby": 7800}, {"name": "ruby_pack_4", "offer": 40, "iap": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_pack_4", "ruby": 21000}, {"name": "ruby_pack_5", "offer": 50, "iap": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_pack_5", "ruby": 45000}], "gold": [{"name": "gold_pack_1", "offer": 0, "price": 0, "quantity": 2000}, {"name": "gold_pack_2", "offer": 0, "price": 300, "quantity": 4000}, {"name": "gold_pack_3", "offer": 10, "price": 1500, "quantity": 22000}, {"name": "gold_pack_4", "offer": 25, "price": 6000, "quantity": 100000}, {"name": "gold_pack_5", "offer": 40, "price": 15000, "quantity": 280000}, {"name": "gold_pack_6", "offer": 50, "price": 30000, "quantity": 600000}], "energy": [{"name": "energy_pack_1", "offer": 0, "unlimited": 0, "price": 0, "quantity": 3}, {"name": "energy_pack_2", "offer": 0, "unlimited": 6, "price": 600, "quantity": 50}, {"name": "energy_pack_3", "offer": 0, "unlimited": 24, "price": 900, "quantity": 0}, {"name": "energy_pack_4", "offer": 0, "unlimited": 0, "price": 300, "quantity": 50}, {"name": "energy_pack_5", "offer": 25, "unlimited": 0, "price": 3000, "quantity": 625}, {"name": "energy_pack_6", "offer": 50, "unlimited": 0, "price": 30000, "quantity": 7500}], "pvpTicket": [{"name": "ticket_pack_1", "offer": 0, "price": 300, "quantity": 5}, {"name": "ticket_pack_2", "offer": 10, "price": 1500, "quantity": 28}, {"name": "ticket_pack_3", "offer": 20, "price": 3000, "quantity": 60}, {"name": "ticket_pack_4", "offer": 30, "price": 6000, "quantity": 130}, {"name": "ticket_pack_5", "offer": 40, "price": 15000, "quantity": 350}, {"name": "ticket_pack_6", "offer": 50, "price": 30000, "quantity": 750}], "offer": [{"name": "beginner_offer_pack", "tiers": [{"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.beginner0.tier2", "items": [{"type": "store", "subType": "gold", "value": 120000}, {"type": "store", "subType": "ruby", "value": 1200}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.beginner1.tier2", "items": [{"type": "store", "subType": "gold", "value": 70000}, {"type": "store", "subType": "ruby", "value": 1000}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.beginner2.tier2", "items": [{"type": "store", "subType": "gold", "value": 25000}, {"type": "store", "subType": "ruby", "value": 800}, {"type": "chest", "subType": "gold", "value": 1}]}]}, {"name": "card_offer_pack", "tiers": [{"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.card_offer0.tier2", "items": [{"type": "store", "subType": "gold", "value": 80000}, {"type": "store", "subType": "ruby", "value": 2000}, {"type": "card", "subType": "random", "value": 100}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.card_offer1.tier2", "items": [{"type": "store", "subType": "gold", "value": 40000}, {"type": "store", "subType": "ruby", "value": 1000}, {"type": "card", "subType": "random", "value": 80}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.card_offer2.tier2", "items": [{"type": "store", "subType": "gold", "value": 20000}, {"type": "store", "subType": "ruby", "value": 600}, {"type": "card", "subType": "random", "value": 60}, {"type": "chest", "subType": "gold", "value": 1}]}]}, {"name": "goldbar_offer_pack", "tiers": [{"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.gold_offer0.tier2", "items": [{"type": "store", "subType": "gold", "value": 550000}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.gold_offer1.tier2", "items": [{"type": "store", "subType": "gold", "value": 420000}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.gold_offer2.tier2", "items": [{"type": "store", "subType": "gold", "value": 310000}, {"type": "chest", "subType": "gold", "value": 1}]}]}, {"name": "ruby_offer_pack", "tiers": [{"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_offer0.tier2", "items": [{"type": "store", "subType": "ruby", "value": 7200}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_offer1.tier2", "items": [{"type": "store", "subType": "ruby", "value": 5500}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.ruby_offer2.tier2", "items": [{"type": "store", "subType": "ruby", "value": 3000}, {"type": "chest", "subType": "gold", "value": 1}]}]}, {"name": "booster_offer_pack", "tiers": [{"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.booster_offer0.tier2", "items": [{"type": "store", "subType": "gold", "value": 120000}, {"type": "store", "subType": "ruby", "value": 450}, {"type": "booster", "subType": "booster_pack", "value": 10}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.booster_offer1.tier2", "items": [{"type": "store", "subType": "gold", "value": 100000}, {"type": "store", "subType": "ruby", "value": 420}, {"type": "booster", "subType": "booster_pack", "value": 9}, {"type": "chest", "subType": "gold", "value": 1}]}, {"discount": 40, "duration": 86400, "product_id": "com.senspark.goldrush.goldminer.worldtour.iap.booster_offer2.tier2", "items": [{"type": "store", "subType": "gold", "value": 20000}, {"type": "store", "subType": "ruby", "value": 120}, {"type": "booster", "subType": "booster_pack", "value": 5}, {"type": "chest", "subType": "gold", "value": 1}]}]}], "card": [{"name": "card_pack_1", "duration": 86400, "items": [{"type": "card", "subType": "char_miner", "value": 1}, {"type": "card", "subType": "car_miner", "value": 1}, {"type": "card", "subType": "rope_miner", "value": 1}, {"type": "card", "subType": "rope_jaki", "value": 1}, {"type": "card", "subType": "claw_cactus", "value": 1}, {"type": "card", "subType": "rope_ariana", "value": 1}]}, {"name": "card_pack_2", "duration": 86400, "items": [{"type": "card", "subType": "claw_miner", "value": 1}, {"type": "card", "subType": "pet_miner", "value": 1}, {"type": "card", "subType": "claw_pirate", "value": 1}, {"type": "card", "subType": "rope_cactus", "value": 1}, {"type": "card", "subType": "pet_samurai", "value": 1}, {"type": "card", "subType": "car_doll", "value": 1}]}, {"name": "card_pack_3", "duration": 86400, "items": [{"type": "card", "subType": "car_cactus", "value": 1}, {"type": "card", "subType": "car_pirate", "value": 1}, {"type": "card", "subType": "pet_pirate", "value": 1}, {"type": "card", "subType": "car_samurai", "value": 1}, {"type": "card", "subType": "pet_fighter", "value": 1}, {"type": "card", "subType": "char_iron", "value": 1}]}, {"name": "card_pack_4", "duration": 86400, "items": [{"type": "card", "subType": "car_jaki", "value": 1}, {"type": "card", "subType": "char_pirate", "value": 1}, {"type": "card", "subType": "pet_cactus", "value": 1}, {"type": "card", "subType": "rope_fighter", "value": 1}, {"type": "card", "subType": "claw_thor", "value": 1}, {"type": "card", "subType": "car_goku", "value": 1}]}, {"name": "card_pack_5", "duration": 86400, "items": [{"type": "card", "subType": "char_cactus", "value": 1}, {"type": "card", "subType": "char_jaki", "value": 1}, {"type": "card", "subType": "car_fighter", "value": 1}, {"type": "card", "subType": "car_ariana", "value": 1}, {"type": "card", "subType": "claw_bat", "value": 1}, {"type": "card", "subType": "car_iron", "value": 1}]}, {"name": "card_pack_6", "duration": 86400, "items": [{"type": "card", "subType": "pet_jaki", "value": 1}, {"type": "card", "subType": "rope_pirate", "value": 1}, {"type": "card", "subType": "rope_samurai", "value": 1}, {"type": "card", "subType": "rope_thor", "value": 1}, {"type": "card", "subType": "rope_doll", "value": 1}, {"type": "card", "subType": "char_goku", "value": 1}]}, {"name": "card_pack_7", "duration": 86400, "items": [{"type": "card", "subType": "claw_jaki", "value": 1}, {"type": "card", "subType": "pet_ariana", "value": 1}, {"type": "card", "subType": "char_samurai", "value": 1}, {"type": "card", "subType": "pet_iron", "value": 1}, {"type": "card", "subType": "car_spider", "value": 1}, {"type": "card", "subType": "car_bat", "value": 1}]}, {"name": "card_pack_8", "duration": 86400, "items": [{"type": "card", "subType": "pet_miner", "value": 1}, {"type": "card", "subType": "claw_ariana", "value": 1}, {"type": "card", "subType": "claw_goku", "value": 1}, {"type": "card", "subType": "pet_goku", "value": 1}, {"type": "card", "subType": "pet_doll", "value": 1}, {"type": "card", "subType": "car_thor", "value": 1}]}, {"name": "card_pack_9", "duration": 86400, "items": [{"type": "card", "subType": "char_ariana", "value": 1}, {"type": "card", "subType": "claw_samurai", "value": 1}, {"type": "card", "subType": "char_bat", "value": 1}, {"type": "card", "subType": "rope_spider", "value": 1}, {"type": "card", "subType": "pet_bat", "value": 1}, {"type": "card", "subType": "rope_iron", "value": 1}]}, {"name": "card_pack_10", "duration": 86400, "items": [{"type": "card", "subType": "char_fighter", "value": 1}, {"type": "card", "subType": "rope_goku", "value": 1}, {"type": "card", "subType": "char_doll", "value": 1}, {"type": "card", "subType": "pet_thor", "value": 1}, {"type": "card", "subType": "claw_doll", "value": 1}, {"type": "card", "subType": "char_thor", "value": 1}]}, {"name": "card_pack_11", "duration": 86400, "items": [{"type": "card", "subType": "claw_fighter", "value": 1}, {"type": "card", "subType": "claw_spider", "value": 1}, {"type": "card", "subType": "pet_spider", "value": 1}, {"type": "card", "subType": "car_iron", "value": 1}, {"type": "card", "subType": "rope_bat", "value": 1}, {"type": "card", "subType": "char_spider", "value": 1}]}], "version": 1.01}