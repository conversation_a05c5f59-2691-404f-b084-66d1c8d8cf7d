[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": true, "_id": "3a2d5dc0-ab37-4c71-83c1-7c5b23762088"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 16}, {"__id__": 105}, {"__id__": 195}], "_active": true, "_components": [{"__id__": 198}, {"__id__": 199}, {"__id__": 200}, {"__id__": 201}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 640, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5anuzTuuROMZciqDxkAiUp"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 530.8735961914062, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "06Gwkx4epMdp9LghrVi7/5"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 0.1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "36d2QT/KNNN4uV364UpMVw"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 512, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}], "_active": true, "_components": [{"__id__": 14}, {"__id__": 15}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1010}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a1XNp3oBdLdLiONvpdiBKq"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 7}, {"__id__": 10}], "_active": true, "_components": [{"__id__": 12}, {"__id__": 13}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1010}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dcC9Pnq+VL65E4sCvlcTSi"}, {"__type__": "cc.Node", "_name": "BG_home", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 8}, {"__id__": 9}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1010}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "594PzyAXNGlJdPeeYYWajw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8f16ce7d-8466-4026-9f0e-402ff4104686"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "9aGrWo6sxO6JTh6Gh3y417"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 960, "_originalHeight": 640, "_id": "e6f8pEW1lKLpysBnu8qYKy"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 11}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1010}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-960, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f2JDwSVaJAkr6OpcBicKa+"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 1920, "height": 1010}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 15, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "0as2Lxdc1LJZ4KPzIIlpS1"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "e4ihcS/hJB/Y4sCGxeabOr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1010, "_id": "fcrRczlh5CfrWEwy3+sRyl"}, {"__type__": "<PERSON>.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "horizontal": true, "vertical": true, "inertia": true, "brake": 0.5, "elastic": true, "bounceDuration": 0.5, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 10}, "content": {"__id__": 10}, "scrollThreshold": 0.5, "autoPageTurningThreshold": 1500, "pageTurningEventTiming": 0.1, "pageTurningSpeed": 0.3, "pageEvents": [], "_N$sizeMode": 0, "_N$direction": 0, "_N$indicator": null, "_id": "01n0i1zodMdJ7sa9rqN3c9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 135, "_bottom": 135, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1010, "_id": "a84XKEKNdKYJAf+7mauhmh"}, {"__type__": "cc.Node", "_name": "top_hud", "_objFlags": 512, "_parent": {"__id__": 2}, "_children": [{"__id__": 17}, {"__id__": 55}, {"__id__": 74}], "_active": true, "_components": [{"__id__": 101}, {"__id__": 102}, {"__id__": 103}], "_prefab": {"__id__": 104}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 94, "g": 60, "b": 41, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 572.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3efw5o7aNM0aUJhVCF72Lg"}, {"__type__": "cc.Node", "_name": "dynamicWidget", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 18}], "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-910, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "adO8y1e1hN+o1ZGpKOQ9Ni"}, {"__type__": "cc.Node", "_name": "left<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [{"__id__": 19}, {"__id__": 38}], "_active": true, "_components": [{"__id__": 50}], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 235, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [67.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f9gXjP0wxKBbzoJczSczth"}, {"__type__": "cc.Node", "_name": "vipButton", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [{"__id__": 20}, {"__id__": 29}], "_active": true, "_components": [{"__id__": 33}, {"__id__": 34}, {"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 235, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "98roQ47eZHD5pJXDcc3ff1"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [{"__id__": 21}, {"__id__": 24}], "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "93NlZv8VlBipBkHxdZONO1"}, {"__type__": "cc.Node", "_name": "icon_vip", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 93, "height": 51}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dcudjZuclCDIhqs4FJ07sd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "499e285a-a86f-4ae4-a0ba-239232545b97"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "desphcL1RFm7UE1/4/qAj7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "3aW1rWjW5Ae5Sz706Kl1Mn", "sync": false}, {"__type__": "cc.Node", "_name": "vip_text", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 80, "g": 26, "b": 20, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 94.22, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -33, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3c3Oh+785EKKIR9AkREt68"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "VIP 10", "_N$string": "VIP 10", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "772c665b-1639-4a72-aa97-6fced35228f1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "47fi1IaKFP5o26gDVx+/H1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "89YTdKO3lE95XpCOKqUmfR", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 37, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "efqAKsTJtKJZdWFAB6TIu7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "c4E05TRXFPLqLNKYG3uuPG", "sync": false}, {"__type__": "cc.Node", "_name": "vip_notification_view", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [102.5, 42.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "94fjDUFJxIFLR29v4PTX/4"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_prefab": {"__uuid__": "ffa22def-eac5-48d8-a97b-7a98098ff125"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "058jM2WzZFKJ1yzVZ3XcVy"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 33, "_left": -25, "_right": -10, "_top": -10, "_bottom": 82.77099990844727, "_verticalCenter": 0.25, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": false, "_originalWidth": 0, "_originalHeight": 0, "_id": "b57FXmV8dGhL2ZQx7x2Ggc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "adZDWqJlNO1ZbmmVjPopRr", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9c3266d1-1811-45d9-98b7-05e28a0e5778"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "e59Ts8TN1Ot5tNiL7o6+0n"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 35}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "9c3266d1-1811-45d9-98b7-05e28a0e5778"}, "_N$pressedSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "pressedSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$hoverSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "hoverSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$disabledSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$target": {"__id__": 19}, "_id": "54U+nb+gBI87Z3KS9bCnHN"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 16}, "component": "", "_componentId": "87e359x1PxPIZRUzatTw670", "handler": "onVipButtonPressed", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 10, "_bottom": 10, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 115, "_originalHeight": 116, "_id": "6aUjXgxQNA5a4ZqhbML8ms"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "b1f2UpN5ZIl57o/TFEZmU/", "sync": false}, {"__type__": "cc.Node", "_name": "backButton", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [{"__id__": 39}], "_active": true, "_components": [{"__id__": 45}, {"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 235, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "92+Ej2NzpKN71SFT+TbjJt"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [{"__id__": 40}], "_active": true, "_components": [{"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "99RBqKZGNIWJf9kioaaqF/"}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 41}], "_prefab": {"__id__": 42}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d5QdiyrBFPQbOJ/7uQeIOe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "68baed92-5b62-4edc-8152-f336a39bdc93"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "f050fOHYFIKLANtNNZBPHH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "c4FrL8VAlDVIvzH3adMSIy", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 37, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "8bPRXq5I5O4KyPiNeaMNkh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "b8XV0Cp0tOp4wef7jxG1qF", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9c3266d1-1811-45d9-98b7-05e28a0e5778"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "a18yj9PXZOiIlHiZ42iHF2"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 47}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "9c3266d1-1811-45d9-98b7-05e28a0e5778"}, "_N$pressedSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "pressedSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$hoverSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "hoverSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$disabledSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$target": {"__id__": 38}, "_id": "be0tbIbdlHDKs94AguEKwl"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 16}, "component": "", "_componentId": "87e359x1PxPIZRUzatTw670", "handler": "onBackButtonPressed", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 10, "_bottom": 10, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 123, "_originalHeight": 124, "_id": "36bvZSzHdIO6h7lS+GksSW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "6aTagV865ILJw7ckFfjDyU", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": -135, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "6fzOd7NI1FxIfvVWK4bM95"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "219jZ6vNpPOLQBMYwrN7Vy", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 1820, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "07knGU6ThPjrgoH/RrPwwa"}, {"__type__": "ad50fwuJAdPOa5jkorQRfYl", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "leftAlign": 0, "rightAlign": 3, "_id": "a5ntrcj+lNZreGvtXsNegm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "caqESGFjdLAbj5GzQWhx8n", "sync": false}, {"__type__": "cc.Node", "_name": "dynamicWidget", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 56}], "_active": true, "_components": [{"__id__": 71}, {"__id__": 72}], "_prefab": {"__id__": 73}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [910, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "22wKn9DBREIYEGSiz0Qp1z"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>er", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [{"__id__": 57}], "_active": true, "_components": [{"__id__": 69}], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 235, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-67.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a82scrBwhJBbl6etHA8XzE"}, {"__type__": "cc.Node", "_name": "settingButton", "_objFlags": 0, "_parent": {"__id__": 56}, "_children": [{"__id__": 58}], "_active": true, "_components": [{"__id__": 64}, {"__id__": 65}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 235, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bc69Vfo9JBZ6Oce8M0ay9n"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [{"__id__": 59}], "_active": true, "_components": [{"__id__": 62}], "_prefab": {"__id__": 63}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3anqwepzpJe5U7Ei6LS9fc"}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [], "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "10N8Hu8Y9AoYKARxoT4vnw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d317314b-5739-4a83-9c5b-2960ca59ca52"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "73r9qSrpxPPLgOjT/lNXie"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "e239LKlopG475uKVSMQbcx", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 13, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "103HhVW0FFuo9fZB35ZbIn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "84A+d5pExAZoS28hEyYrxz", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9c3266d1-1811-45d9-98b7-05e28a0e5778"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "d76O51y/5Pv7CJNaqha+pq"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 66}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "9c3266d1-1811-45d9-98b7-05e28a0e5778"}, "_N$pressedSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "pressedSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$hoverSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "hoverSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$disabledSprite": {"__uuid__": "c5f4fb0d-b074-4371-b0fb-ef92c24d5f85"}, "_N$target": {"__id__": 57}, "_id": "a66ltSelJNc59QCIAQ6Af2"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 16}, "component": "", "_componentId": "87e359x1PxPIZRUzatTw670", "handler": "onSettingButtonPressed", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 10, "_bottom": 10, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 115, "_originalHeight": 116, "_id": "7cPbztcSVB7J0LyXUJiNs4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "5cW3N9iMFFGY2EltdpVCfZ", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -135, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "96Wzx2bL1Ab5g3/EC9EsLH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "2aNpvzRzVAn4srlP3b23od", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 1820, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "bfuX+tW0dJubFELXS6iZZ6"}, {"__type__": "ad50fwuJAdPOa5jkorQRfYl", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "leftAlign": 3, "rightAlign": 0, "_id": "9a9FAL785GEIxajT187905"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "b0yOUkMsdO4qjgR5W7VaV7", "sync": false}, {"__type__": "cc.Node", "_name": "dynamicWidget", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 75}], "_active": true, "_components": [{"__id__": 98}, {"__id__": 99}], "_prefab": {"__id__": 100}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "06FRTAsmtMLLCYgnckftTi"}, {"__type__": "cc.Node", "_name": "center", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [{"__id__": 76}, {"__id__": 80}, {"__id__": 84}, {"__id__": 88}, {"__id__": 92}], "_active": true, "_components": [{"__id__": 96}], "_prefab": {"__id__": 97}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1418, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9bxC3F87dL34J++s4e8pMW"}, {"__type__": "cc.Node", "_name": "level", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}, {"__id__": 78}], "_prefab": {"__id__": 79}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 329.6, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "05PUXP4GFDVrJxd/x3hCuB"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_prefab": {"__uuid__": "be1edebe-8cfc-45b1-a60d-159524fbdfe2"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "95lrbxaJNA3aQmE1sDHXTB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": -509, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "bbEURcP9lBqJ1BbKHtKbCx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "a238e+KyNNurTOiTvaZiPy", "sync": false}, {"__type__": "cc.Node", "_name": "ruby", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 81}, {"__id__": 82}], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 329.6, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-194.60000000000002, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c5zRrIXRBDx672Ae7lMt0l"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "_prefab": {"__uuid__": "83304260-54cb-4896-a6dd-f7a63768a8c9"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "f6WEqQzBRH8apA5VkQcK2L"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "94nRlVMWpK7LGwlImO8Fcm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "38wJ8U9MBD1ouGpVU0e34T", "sync": false}, {"__type__": "cc.Node", "_name": "gold", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 85}, {"__id__": 86}], "_prefab": {"__id__": 87}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 329.6, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [155, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "48gsUjzplMBYwlaQ7GOPbS"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "_prefab": {"__uuid__": "0c82a817-5b35-40d4-817e-5fefb1c615c0"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "c0z3TSCN9HK7Io8KgzWRfZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "acwfkgWX5HaKfCmOPnp4ON"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "1b4niFrD1EBLkS1YUc+7mB", "sync": false}, {"__type__": "cc.Node", "_name": "red_ticket", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 89}, {"__id__": 90}], "_prefab": {"__id__": 91}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 329.6, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [504.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8e6JQJ93ZIaaIVOYYZdJ5n"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "_prefab": {"__uuid__": "f49f57d2-f85c-41a0-a330-636db093dd1a"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "3eb/r+asVCn7MvoJQN1SMK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0.7479041916167665, "_right": 0.06526946107784426, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 312, "_originalHeight": 0, "_id": "80pJMJTfRJd7zceL0Suq70"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "98zWsn9u9E6YN5MLfr3UZN", "sync": false}, {"__type__": "cc.Node", "_name": "energy", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 93}, {"__id__": 94}], "_prefab": {"__id__": 95}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 329.6, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [507.516, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d7LgbFS/RPW4DzHXrXeXY6"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "_prefab": {"__uuid__": "8c43c142-bad4-4caf-a0bd-2754eb1869ec"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "fagynQHMFD8LRaxyPIraXF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "cbqnxNRGlMBoIXf0vj+F29"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "78ebuCK8lBl7jvQf5ULapB", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 248, "_right": 254.00000000000006, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 135, "_id": "cfecYpx2NJzqsJaqKiM/IG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "d2JGOEkJFGT4Rh7SefxTrg", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "47foYy0nNMdKx/ziA5d5V4"}, {"__type__": "ad50fwuJAdPOa5jkorQRfYl", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "leftAlign": 1, "rightAlign": 1, "_id": "85FmUYIgdDmJzdfXe8GWg7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "3ahv5V0DhG8JLG6gdBJYrw", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c5970817-f9f9-453b-80fa-9bec393d0355"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "e4wfUQ7hVKPqtbjGvnHghw"}, {"__type__": "87e359x1PxPIZRUzatTw670", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "_levelBar_prefab": {"__id__": 77}, "_rubyBar_prefab": {"__id__": 81}, "_goldBar_prefab": {"__id__": 85}, "_pvpTicketBar_prefab": {"__id__": 89}, "_energyBar_prefab": {"__id__": 93}, "_vipButton": {"__id__": 19}, "_backButton": {"__id__": 38}, "_settingButton": {"__id__": 57}, "_vipText": {"__id__": 25}, "_vipNotification_prefab": {"__id__": 30}, "_id": "4fk012Dy5CuY+14RAlMvCs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 0, "_id": "29kqpb+NJIA639SwhWY0X8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "84416462-1f59-4899-9a26-27554b2dd300"}, "fileId": "", "sync": false}, {"__type__": "cc.Node", "_name": "bottom_hud", "_objFlags": 512, "_parent": {"__id__": 2}, "_children": [{"__id__": 106}, {"__id__": 110}, {"__id__": 122}, {"__id__": 141}, {"__id__": 149}, {"__id__": 162}, {"__id__": 170}, {"__id__": 183}], "_active": true, "_components": [{"__id__": 191}, {"__id__": 192}, {"__id__": 193}], "_prefab": {"__id__": 194}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 94, "g": 60, "b": 41, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -572.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "68xD49+MtC0L4Q3DxJdbIA"}, {"__type__": "cc.Node", "_name": "blocker", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a2apOBelFO5ouZsO3wzXR3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "27wizDlGVNqpyWGtfSbl0c"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "_id": "0b7HT9KDtGr656gTg08/x0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "2fX1TJuE1JobUZKtpM+M+O", "sync": false}, {"__type__": "cc.Node", "_name": "shopButton", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 111}, {"__id__": 114}], "_active": true, "_components": [{"__id__": 118}, {"__id__": 119}, {"__id__": 120}], "_prefab": {"__id__": 121}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-768, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "12dlp4ZOVF0bcbbmzFs8/b"}, {"__type__": "cc.Node", "_name": "icon-shop", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 112}], "_prefab": {"__id__": 113}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 109}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f6WsrBHqxOe5+4rf+s0KYI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "df4a2abd-7d12-4e42-bc4e-988d75983b74"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "e71E0gkt1Ng6zNlvTkfcxk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "515ayiQrVIM6gWe+k/tDrX", "sync": false}, {"__type__": "cc.Node", "_name": "shop_notification", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 115}, {"__id__": 116}], "_prefab": {"__id__": 117}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-167, 42.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "27xQAen8ZL0JIvqS+qwGH+"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_prefab": {"__uuid__": "ffa22def-eac5-48d8-a97b-7a98098ff125"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "8dYab/Y79Jr6anlQxDlaRf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": -5, "_right": 0, "_top": -5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "d20wcpW6ZHv5QxojJQ8zIv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "d7DghG5nxNE4nF/WqxM+Zp", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "61288fb8-383a-4b66-a22e-a6d4f384c3d4"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "00dQ5JEBJGJaKB5efEvB7k"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "61288fb8-383a-4b66-a22e-a6d4f384c3d4"}, "_N$pressedSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "pressedSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "_N$hoverSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "hoverSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "_N$disabledSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "_N$target": {"__id__": 110}, "_id": "1dCMrt16BNYZw9k2x+jwq1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0, "_right": 0.8, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 385, "_originalHeight": 0, "_id": "89qPrrmsZAt5c8bWmtuQ3J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "d36UXIlkRGP7jtgVWv+hzU", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 123}, {"__id__": 126}], "_active": true, "_components": [{"__id__": 136}, {"__id__": 137}, {"__id__": 139}], "_prefab": {"__id__": 140}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 398.39999999999986, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-376.80000000000007, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "37bwXebuVM2owe3ZIwbVDY"}, {"__type__": "cc.Node", "_name": "icon-customize", "_objFlags": 0, "_parent": {"__id__": 122}, "_children": [], "_active": true, "_components": [{"__id__": 124}], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 111}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-30, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f0z98DGMhGzoM4njMaJKjC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9abee0a1-f894-4afb-aace-70ad505705fa"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "21PJqpYMJFkZJpvYOnp+oM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "cakb7z531LzqOysRD6tT2v", "sync": false}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 122}, "_children": [{"__id__": 127}, {"__id__": 130}], "_active": true, "_components": [{"__id__": 133}, {"__id__": 134}], "_prefab": {"__id__": 135}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-204.19999999999993, 41.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e0TSH2Y5VKx62nEBRaFzQX"}, {"__type__": "cc.Node", "_name": "character_notification_red", "_objFlags": 0, "_parent": {"__id__": 126}, "_children": [], "_active": true, "_components": [{"__id__": 128}], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [30, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9dCforPANH96+iD0qF5Oea"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "_prefab": {"__uuid__": "ffa22def-eac5-48d8-a97b-7a98098ff125"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "69k/rS+QJO/qZpzU+GILeA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "fexLbkKcBHUYH7pMtRxLUo", "sync": false}, {"__type__": "cc.Node", "_name": "character_notification_green", "_objFlags": 0, "_parent": {"__id__": 126}, "_children": [], "_active": true, "_components": [{"__id__": 131}], "_prefab": {"__id__": 132}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [95, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b5FKPmVtxAUY4M258IE1wY"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "_prefab": {"__uuid__": "59473712-037b-479a-a9ef-b87580ea3448"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "d1Pcg8acxATbdoqNX4nay5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "5dAt5U2lFJ74iHEc3arltA", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": -5, "_right": 0, "_top": -5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "b2GKxyaQRFVaRBWECpUx9e"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 125, "height": 60}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 5, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "69YsCFuX9AYKehDa6ujPUL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "24pTJA3mFKLbpFo6n4sflO", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "590dd14c-1c18-405d-8431-cb3170bffeb6"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "378qsvu6hFO5kzWWSY/HF8"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 138}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "590dd14c-1c18-405d-8431-cb3170bffeb6"}, "_N$pressedSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "pressedSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$hoverSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "hoverSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$disabledSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$target": {"__id__": 122}, "_id": "e7boE0ZylNBoenhyO+TFt/"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 105}, "component": "", "_componentId": "d187aVk+GpIio2xcEeQZxWr", "handler": "onResetFilter", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0.2, "_right": 0.5925, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 385, "_originalHeight": 0, "_id": "07aPr3zLdA65zNNdUwE3cW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "97WyIih/pPLqi2nJnS8qLn", "sync": false}, {"__type__": "cc.Node", "_name": "homeButton", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 142}], "_active": true, "_components": [{"__id__": 145}, {"__id__": 146}, {"__id__": 147}], "_prefab": {"__id__": 148}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 480, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3cS8wGeoZDFbUCEZmMhTkJ"}, {"__type__": "cc.Node", "_name": "icon-play", "_objFlags": 0, "_parent": {"__id__": 141}, "_children": [], "_active": true, "_components": [{"__id__": 143}], "_prefab": {"__id__": 144}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 117, "height": 108}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bazVUv5tFPlaciC4FapkrR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "07269409-4a25-4d8d-9b46-44727c24a9bd"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "5ckgt+g5RKB6saqT50b358"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "12r7jRjLtKHYiS++SRe6jX", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "16f98ab8-eef4-4c11-af9d-61f1ca58717d"}, "_N$pressedSprite": {"__uuid__": "4ef5fbbb-b736-4a19-a7f0-78bd4a9db444"}, "pressedSprite": {"__uuid__": "4ef5fbbb-b736-4a19-a7f0-78bd4a9db444"}, "_N$hoverSprite": {"__uuid__": "4ef5fbbb-b736-4a19-a7f0-78bd4a9db444"}, "hoverSprite": {"__uuid__": "4ef5fbbb-b736-4a19-a7f0-78bd4a9db444"}, "_N$disabledSprite": {"__uuid__": "4ef5fbbb-b736-4a19-a7f0-78bd4a9db444"}, "_N$target": {"__id__": 141}, "_id": "fd5AtyY2xEsrYDn43cxUfm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0.375, "_right": 0.375, "_top": 0, "_bottom": 1, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 460, "_originalHeight": 0, "_id": "fcNRn/6ElHKreywV7EMifj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "16f98ab8-eef4-4c11-af9d-61f1ca58717d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "1eJJf7bHxN+aAtwohuuFwe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "4aVhLek0tNOrMvN8NXHjQj", "sync": false}, {"__type__": "cc.Node", "_name": "teamButton", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 150}, {"__id__": 153}], "_active": true, "_components": [{"__id__": 157}, {"__id__": 159}, {"__id__": 160}], "_prefab": {"__id__": 161}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 398.39999999999986, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [376.80000000000007, 0, 0, 0, 0, 0, 1, -1, 1, -1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "24cB9YjAFJLKCKne7qrGQ1"}, {"__type__": "cc.Node", "_name": "icon-team", "_objFlags": 0, "_parent": {"__id__": 149}, "_children": [], "_active": true, "_components": [{"__id__": 151}], "_prefab": {"__id__": 152}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-33, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c7Z4mF1OdLIb6CwkHjEMcu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 150}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "513fa218-65e5-4bec-baf6-69dae1b4d3e9"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "c91OKUWZVEn6xoSf6urfmL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "2304HpoTpC8rvd6nzyZbQE", "sync": false}, {"__type__": "cc.Node", "_name": "team_notification", "_objFlags": 0, "_parent": {"__id__": 149}, "_children": [], "_active": false, "_components": [{"__id__": 154}, {"__id__": 155}], "_prefab": {"__id__": 156}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-174.19999999999993, 41.5, 0, 0, 0, 0, 1, -1, 1, -1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d8+IC+tz5ImbAMzLZFKy+l"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_prefab": {"__uuid__": "ffa22def-eac5-48d8-a97b-7a98098ff125"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "e49QOOAhJDzb48vqZr2ELY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": -5, "_right": 0, "_top": -5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 74, "_originalHeight": 0, "_id": "c1iDjDiDxObboFHIecuvsc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "cfBSu4WQ5LQri7jLVko68e", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 158}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "590dd14c-1c18-405d-8431-cb3170bffeb6"}, "_N$pressedSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "pressedSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$hoverSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "hoverSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$disabledSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$target": {"__id__": 149}, "_id": "2eVTBxT+9HtaZ7L6RmPFoo"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 105}, "component": "", "_componentId": "d187aVk+GpIio2xcEeQZxWr", "handler": "onEventButtonPressed", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0.5925, "_right": 0.2, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 385, "_originalHeight": 0, "_id": "77BC8sOqlJHquDWWHvuZd2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "590dd14c-1c18-405d-8431-cb3170bffeb6"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "75mxEFXBxDjLlOiUARaWyC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "9cHcZ71u1EvqZqn04zkNy6", "sync": false}, {"__type__": "cc.Node", "_name": "chestButton", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 163}], "_active": true, "_components": [{"__id__": 166}, {"__id__": 167}, {"__id__": 168}], "_prefab": {"__id__": 169}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [768, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8amQa8capDYKWjON3f3EIb"}, {"__type__": "cc.Node", "_name": "icon-chest", "_objFlags": 0, "_parent": {"__id__": 162}, "_children": [], "_active": true, "_components": [{"__id__": 164}], "_prefab": {"__id__": 165}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 111, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "541aPuVSNBgIdiVYh3+fPh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ea6eeee3-4f12-44d9-bb2c-692cd6d1a4e8"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "1dLb6un8hNa5KZqnv+Vrnk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "51LB3BCqNCMo1PTrF3IPJa", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "61288fb8-383a-4b66-a22e-a6d4f384c3d4"}, "_N$pressedSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "pressedSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "_N$hoverSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "hoverSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "_N$disabledSprite": {"__uuid__": "3b82a5f8-228a-41cb-b46a-872c7f82165a"}, "_N$target": {"__id__": 162}, "_id": "faeEpUID9HVqFc6df8qP0q"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0.8, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 385, "_originalHeight": 0, "_id": "41sE8amplO+oHHZA6YplzA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "61288fb8-383a-4b66-a22e-a6d4f384c3d4"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "0dA33uK6lPfY/ERVkq1aUI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "6dEkvel/RJMatvu4vW8MfU", "sync": false}, {"__type__": "cc.Node", "_name": "socialButton", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 171}, {"__id__": 174}], "_active": false, "_components": [{"__id__": 178}, {"__id__": 180}, {"__id__": 181}], "_prefab": {"__id__": 182}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 398.39999999999986, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [376.80000000000007, 0, 0, 0, 0, 0, 1, -1, 1, -1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b7lGyaQidFsJr2pXsoBgoh"}, {"__type__": "cc.Node", "_name": "icon-social", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [], "_active": true, "_components": [{"__id__": 172}], "_prefab": {"__id__": 173}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-33, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d3YELkTaxBMqwXJdxdfCAF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "217aeb58-0444-48b2-9dc0-bfe78dbbc775"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "39vXc5BdtOkZetQ9GafN5s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "2eD+cXvSZKJo3c1y0qfZ0b", "sync": false}, {"__type__": "cc.Node", "_name": "social_notification", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [], "_active": true, "_components": [{"__id__": 175}, {"__id__": 176}], "_prefab": {"__id__": 177}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-174.19999999999993, 41.5, 0, 0, 0, 0, 1, -1, 1, -1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "14Bg0MBY9MCLtTdAr/ux10"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 174}, "_enabled": true, "_prefab": {"__uuid__": "ffa22def-eac5-48d8-a97b-7a98098ff125"}, "instantiate": true, "async": true, "synchronize": false, "_syncFlag": 0, "_id": "6afCu83WdF3LF5+WKqtGD9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 174}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": -5, "_right": 0, "_top": -5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 74, "_originalHeight": 0, "_id": "15TQiMxvBJLLEiGz21P6YS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "2e5lJSj5xJX7d85Jp68+ep", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 179}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "590dd14c-1c18-405d-8431-cb3170bffeb6"}, "_N$pressedSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "pressedSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$hoverSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "hoverSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$disabledSprite": {"__uuid__": "a662ab4d-6d3c-4752-89df-1768463947bb"}, "_N$target": {"__id__": 170}, "_id": "55GZ2f/P1OyoRYjl57UW25"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 105}, "component": "", "_componentId": "d187aVk+GpIio2xcEeQZxWr", "handler": "onEventButtonPressed", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0.5925, "_right": 0.2, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 385, "_originalHeight": 0, "_id": "09asrXFpZGEpUffuY2/XXz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "590dd14c-1c18-405d-8431-cb3170bffeb6"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "4b3TUdaZhHL4ejwvqkab6g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "c3i2xfAFFKgIthmmtwI94q", "sync": false}, {"__type__": "cc.Node", "_name": "eventButton", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 184}], "_active": false, "_components": [{"__id__": 187}, {"__id__": 188}], "_prefab": {"__id__": 190}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 380, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [770, 135, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8fZKWruYZPSaCeN7+lZCBF"}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 183}, "_children": [], "_active": true, "_components": [{"__id__": 185}], "_prefab": {"__id__": 186}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 311, "height": 126}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8enws3guFNp5ULEX/wBiLB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 184}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "34fc57e0-9c48-4291-bdf3-937009e47b71"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "3547MXDJRP7JeeqmrobNLp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "dbEwLUOqZMVrtaTTL285e7", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 183}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0e08303d-5e7c-4031-aef2-2dbe7e4f73a4"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "9fh/QktE9Gw5vracetyo0S"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 183}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 189}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 183}, "_id": "91S1480B5HDqWcvCZGuELW"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 105}, "component": "", "_componentId": "d187aVk+GpIio2xcEeQZxWr", "handler": "onEventButtonPressed", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "acRqwFYs1OXJLZgui8A00h", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c5970817-f9f9-453b-80fa-9bec393d0355"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "e2A9Cx0oFDA664gA2Ae2rq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0, "_right": 0, "_top": 572.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 135, "_id": "33sTacrspLpaues6s+of7y"}, {"__type__": "d187aVk+GpIio2xcEeQZxWr", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "_blocker": {"__id__": 108}, "_shopButton": {"__id__": 110}, "_shopNotification_prefab": {"__id__": 115}, "_characterButton": {"__id__": 122}, "_characterNotificationRed_prefab": {"__id__": 128}, "_characterNotificationGreen_prefab": {"__id__": 131}, "_homeButton": {"__id__": 141}, "_teamButton": {"__id__": 149}, "_leaderboardButton": {"__id__": 162}, "_id": "f8W64DYklJo42B1Dv98kSR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "113070d6-7176-4c0f-96c1-45fc6656058f"}, "fileId": "", "sync": false}, {"__type__": "cc.Node", "_name": "blocker", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 196}, {"__id__": 197}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4dJJSP+bREaIleGtP7pi2H"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "6flgxC0TtE4KGk0oJuaISG"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "_id": "57mxwxCzlKVJr8A0lXTI0D"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_fitWidth": false, "_fitHeight": true, "_id": "79X7Dr2P1GU6mIVIli/1Yh"}, {"__type__": "76ea6WoJ21L+6TOZEyIocok", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_pageView": {"__id__": 14}, "_topHud": {"__id__": 102}, "_bottomHud": {"__id__": 193}, "_selectLevelDialogPrefab": {"__uuid__": "2e8b2f0e-2c35-4bc5-b38f-aae38036e07a"}, "_blocker": {"__id__": 197}, "_vipDialogPrefab": {"__uuid__": "1500603d-d6e3-446d-89f8-2506ad6a03da"}, "_settingDialogPrefab": {"__uuid__": "51a86a16-84b5-44f3-8acc-7c0b710baaba"}, "_id": "8cmlfscjFJ4K1aEefBMYvH"}, {"__type__": "3750eiFbFNNrb0vXZdZiizH", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": "5aYlMDUPdEn4Y8O/axxMr4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "42knMP3BBAWKOq7PehCaTo"}]