[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "2d2f792f-a40c-49bb-a189-ed176a246e49"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 229}, {"__id__": 231}, {"__id__": 8}], "_active": true, "_components": [{"__id__": 233}, {"__id__": 234}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 252, "g": 252, "b": 252, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 640, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a286bbGknJLZpRpxROV6M94"}, {"__type__": "cc.Node", "_name": "Managerr", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bcGNYYXDVKJqEdkpTnLStH"}, {"__type__": "45cdarn6VlEPpdSg2JLTYbo", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_id": "fcuozgdK9MZKNCFXooMBhr"}, {"__type__": "63414c6tWRAfbg74JZLefxD", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_id": "36ycF5Xs1Ei5P89i/yktca"}, {"__type__": "57cedCuwe1KcZ2BHoxdLoJR", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "levelView": {"__id__": 7}, "openLevelParamsReader": {"__id__": 5}, "_id": "68+bQOSCdODbxkxNtTRR0i"}, {"__type__": "bc014QsDBNOy49TPniZn9pG", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_levelEntity": {"__id__": 219}, "_levelCollection": {"__id__": 62}, "_levelProgress": {"__id__": 116}, "_levelTime": {"__id__": 153}, "_topHud": {"__id__": 26}, "_backgroundLayer": {"__id__": 9}, "_sceneView": {"__id__": 224}, "_id": "20wcY0niVCJ7VZw2bI75pa"}, {"__type__": "cc.Node", "_name": "story_level_view_v3", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 9}, {"__id__": 16}, {"__id__": 21}, {"__id__": 26}, {"__id__": 159}, {"__id__": 221}, {"__id__": 224}], "_active": true, "_components": [{"__id__": 227}, {"__id__": 7}], "_prefab": {"__id__": 228}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "971iFi1+RKLKgkylJ5bCZo"}, {"__type__": "cc.Node", "_name": "background<PERSON>ayer", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 10}], "_active": true, "_components": [{"__id__": 14}], "_prefab": {"__id__": 15}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fctw26oLBJva+UPCpdniP3"}, {"__type__": "cc.Node", "_name": "backgroundPrefab", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "12V5e7dKVAFoadyO9Ow0Da"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_prefab": {"__uuid__": "9f54e62e-4277-4a03-b76d-55659e110b08"}, "instantiate": false, "async": false, "synchronize": false, "_syncFlag": 0, "_id": "29mtbtPBxGnqQaq7/7dd9B"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1280, "_id": "26uaEh8VlOxItp1A/RBQf4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "*************-4aa0-a625-aad499bcaa85"}, "fileId": "02LpRhYF5PK59rvUI1P8MO", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a2sILYWx9DPJwdSSXo7nUQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "*************-4aa0-a625-aad499bcaa85"}, "fileId": "231akhd7hBgaKvk7VIEhZh", "sync": false}, {"__type__": "cc.Node", "_name": "movingArea", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 17}, {"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 360}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 460, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "17Axu0WitEi574ps3/AUMj"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 18}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 16}, "_id": "6d6mmTYuxHHrgHCk/R+SmV"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 8}, "component": "", "_componentId": "a7bb0Yo/shICoNHWcIKD1Al", "handler": "onMovingAreaPressed", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 0, "_right": 0, "_top": 0, "_bottom": 540, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": "b1gXGRjRRJNq60/kxIcdnD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "*************-4aa0-a625-aad499bcaa85"}, "fileId": "86pbSPSq5K1q2i2YphYt0Q", "sync": false}, {"__type__": "cc.Node", "_name": "diggingArea", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -180, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2aCYK1YsJIRZzhoC7j6YD/"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 23}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 21}, "_id": "08EInawv1OwIdoTp/fKPBA"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 8}, "component": "", "_componentId": "a7bb0Yo/shICoNHWcIKD1Al", "handler": "onDiggingAreaPressed", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 360, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": "b03SdmVOpJtqOcAl3Ypp8Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "*************-4aa0-a625-aad499bcaa85"}, "fileId": "6aveirSZtElKI8NJnibgHG", "sync": false}, {"__type__": "cc.Node", "_name": "hud", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 27}, {"__id__": 64}, {"__id__": 119}], "_active": false, "_components": [{"__id__": 156}, {"__id__": 157}], "_prefab": {"__id__": 158}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1550, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 585, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "027R2jYEVDIpzOoGtzEHao"}, {"__type__": "cc.Node", "_name": "level_collection", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [{"__id__": 28}, {"__id__": 32}, {"__id__": 35}], "_active": true, "_components": [{"__id__": 62}], "_prefab": {"__id__": 63}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-635, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0fj2B+zllBhakLnR6/KZSl"}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cduDp7oDhFg6Qr36Cj60Qe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ae9eb984-de94-478c-be17-45604a6236af"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "75PJytcSRLDq+4UjLanx5F"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 80, "_id": "c0q696aTJOVpJiMTM9RInj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "dctabMnERDqbQFb1hf/Hth", "sync": false}, {"__type__": "cc.Node", "_name": "inner", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 33}], "_prefab": {"__id__": 34}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 268, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c0nd6I4p9JGoCgloxaw3gm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 6, "_right": 6, "_top": 6, "_bottom": 6, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "e0kR5lCeJM958ExxESjduk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "c7/jTVIrpAerbIx0qQgv0m", "sync": false}, {"__type__": "cc.Node", "_name": "slots", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [{"__id__": 36}, {"__id__": 42}, {"__id__": 48}, {"__id__": 54}], "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 255, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "caAeO2oDxAvIsThg8XeIl2"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 35}, "_children": [{"__id__": 37}], "_active": true, "_components": [], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-97.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "04SESVFVZGtLaeKkSWKr8D"}, {"__type__": "cc.Node", "_name": "slot0", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 38}, {"__id__": 39}], "_prefab": {"__id__": 40}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8cUTPgfwdCIrm5xeWWlZU3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0a80b7f8-bae6-407c-afce-c4a957cd0f78"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "27l+APKxxEs7W0g+R05RtG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 120, "_id": "73KAXzGXRIsIHr0/V8zymv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "53+fpyV11EeYxC1tiDiMeF", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "4bKStwUFhIeYJ8VaayizW+", "sync": false}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 35}, "_children": [{"__id__": 43}], "_active": true, "_components": [], "_prefab": {"__id__": 47}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9atF67MYRDIoQ2GqAIU3nj"}, {"__type__": "cc.Node", "_name": "slot1", "_objFlags": 0, "_parent": {"__id__": 42}, "_children": [], "_active": true, "_components": [{"__id__": 44}, {"__id__": 45}], "_prefab": {"__id__": 46}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "abj5aPeGFLJaGnv61iULWh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0a80b7f8-bae6-407c-afce-c4a957cd0f78"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "33HiM2MbxIXYPPTvbGwyi2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 60, "_originalHeight": 60, "_id": "aeC9xOuSZKA6muhJRgU308"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "61oAm0MEdMiJ8GmIXxJ5UF", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "6db3xa3ONGOIUzTEG8qWLE", "sync": false}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 35}, "_children": [{"__id__": 49}], "_active": true, "_components": [], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dbC+fXyEJJjJjlR6z47MtQ"}, {"__type__": "cc.Node", "_name": "slot2", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 50}, {"__id__": 51}], "_prefab": {"__id__": 52}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0eR/kAls9Gfq5p+7hVncd+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0a80b7f8-bae6-407c-afce-c4a957cd0f78"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "e8qENOaxRGxKfAJW3IPP4B"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 60, "_originalHeight": 60, "_id": "57HyawdnFFrJNll0LZe+o2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "9cOO4p7dNBTaBwn2JHswMW", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "83OkUWl9dGFK6Cr27aUN2l", "sync": false}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 35}, "_children": [{"__id__": 55}], "_active": true, "_components": [], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [97.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "caDvFdWIhIOKzO6Y3X9XjL"}, {"__type__": "cc.Node", "_name": "slot3", "_objFlags": 0, "_parent": {"__id__": 54}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 57}], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5awbPae7VGwqii8QNf3a86"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0a80b7f8-bae6-407c-afce-c4a957cd0f78"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "ae4bjptg5PtYE5sll/Q/Pe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 60, "_originalHeight": 60, "_id": "75owoSdRJA3KbXdH9i9lIh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "2apzwXmBpGuYIGb9fhUTfN", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "82VokPGKVPv41nRAFbpjBS", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 255, "height": 60}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 5, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "00zkwJBdFB8JcicppHmp0Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "a4X74RdjJIhJHkvAlwqDXr", "sync": false}, {"__type__": "f6fc7mZqstDTZId5sxx6kT5", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "slots": [{"__id__": 37}, {"__id__": 43}, {"__id__": 49}, {"__id__": 55}], "_id": "0dEnGlUvNN9YLLCqRU6OGT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "37e06d57-6818-491c-b11c-53f9803ed7c0"}, "fileId": "93bFfyloxGwrLoGinnlrfa", "sync": false}, {"__type__": "cc.Node", "_name": "story_level_progress_view", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [{"__id__": 65}, {"__id__": 69}, {"__id__": 112}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 117}], "_prefab": {"__id__": 118}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1550, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [300, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "34uG6kDAxLwJrS8V0T00hm"}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1550, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1cNa71x1dHWIX/UkCc3/mx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ae9eb984-de94-478c-be17-45604a6236af"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "35LssGnXdLwbIL46ETICoH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 80, "_id": "a4QUKO6E5EHImQ0tMgT9VF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "96h9t2k2FEspqoJo0xmQl3", "sync": false}, {"__type__": "cc.Node", "_name": "inner", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [{"__id__": 70}, {"__id__": 106}], "_active": true, "_components": [{"__id__": 110}], "_prefab": {"__id__": 111}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1538, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4aFXx9DwJE4ZxINrVllkWv"}, {"__type__": "cc.Node", "_name": "progressBar", "_objFlags": 0, "_parent": {"__id__": 69}, "_children": [{"__id__": 71}, {"__id__": 75}, {"__id__": 79}, {"__id__": 89}, {"__id__": 93}, {"__id__": 97}], "_active": true, "_components": [{"__id__": 101}, {"__id__": 102}, {"__id__": 103}, {"__id__": 104}], "_prefab": {"__id__": 105}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1438, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "49jtFCwWBF8JMvQFom+SNm"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [{"__id__": 72}, {"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-719, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "85uLy0TSdO8ZlttFaDzn2A"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "65d8b526-e0ee-440a-b61b-de6d5aaa034d"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "a1A7RHkF5MC5DsGUb2q8GW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 13, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 80, "_id": "feMd1/9+hHj4MGbudzFJ9O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "49EJMl+2ZI9KVzCAhRI369", "sync": false}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [{"__id__": 76}, {"__id__": 77}], "_prefab": {"__id__": 78}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-727, 0, 0, 0, 0, 0, 1, 2, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "41L9mCzqBMgbBACdM65oOt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0afc609b-3bab-4da5-9256-166853b2e324"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "13g9ZWQhVH678Y97CICOZ/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": -32, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "b1KO4F6ZVPT6sD4vu+8jat"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "1bXEcBuAhGPa6cXkazNpoz", "sync": false}, {"__type__": "cc.Node", "_name": "progressLayer", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [{"__id__": 80}], "_active": true, "_components": [{"__id__": 87}], "_prefab": {"__id__": 88}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 883.3634000000001, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-277.31829999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "29lq6ykV5F1KR/cnCpvjNP"}, {"__type__": "cc.Node", "_name": "layer", "_objFlags": 0, "_parent": {"__id__": 79}, "_children": [{"__id__": 81}], "_active": true, "_components": [{"__id__": 85}], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 838.3634000000001, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "68S6aB6BxO4q8tibxLHuFy"}, {"__type__": "cc.Node", "_name": "progressLabel", "_objFlags": 0, "_parent": {"__id__": 80}, "_children": [], "_active": true, "_components": [{"__id__": 82}, {"__id__": 83}], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 264, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "06TaSBJpFPy72lmnMGpBwZ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "1200/1200", "_N$string": "1200/1200", "_fontSize": 50, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "48da3f4b-1744-49af-be37-94ba818ff2d7"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "8fEO3rsvBMSJs9GDs1qzZY"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": false, "_color": {"__type__": "cc.Color", "r": 53, "g": 40, "b": 40, "a": 255}, "_width": 5, "_id": "91ycMwiwJM8L/1yFeessvv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "471FlF+oNGrKFfWurdXJfL", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 20, "_right": 25, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "622qifKxdO6KibFP8MMfWN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "d22u2ro4RP361EQ2isEzDC", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0, "_right": 0.3857, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": -75, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "70I9jrFTpG3ZoHvl6Zcy9U"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "b9sNrB/q9Iv4YQNjJ3mfgj", "sync": false}, {"__type__": "cc.Node", "_name": "separator0", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [201.32000000000005, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5bR42Q6fxP4Yedn5pqobIa"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "_prefab": {"__uuid__": "fb3be667-0902-489d-a9f3-6b832b348500"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": "8aNVTELV5OQarP/WToPYIC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 37, "_left": 0, "_right": 0.36, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 1280, "_id": "972VuF1wtJnLzU+W4g2oMK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "93zCGYZaBM/Z5GgwHK0Xdy", "sync": false}, {"__type__": "cc.Node", "_name": "separator1", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [{"__id__": 94}, {"__id__": 95}], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [402.64, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ac40KmRAtAMIFIv9orBGl5"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_prefab": {"__uuid__": "fb3be667-0902-489d-a9f3-6b832b348500"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": "715b8y48hGl4Ny5QjdzyVW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 37, "_left": 0, "_right": 0.22, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 1280, "_id": "abkdI42IxBnLBy1+pOhEbK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "4701QEypdEZZijq+hVsCE0", "sync": false}, {"__type__": "cc.Node", "_name": "separator2", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [{"__id__": 98}, {"__id__": 99}], "_prefab": {"__id__": 100}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [603.96, 4, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b1tXWzxyRFm6X1wBr1Zeyf"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "_prefab": {"__uuid__": "fb3be667-0902-489d-a9f3-6b832b348500"}, "instantiate": true, "async": false, "synchronize": false, "_syncFlag": 0, "_id": "5epmrCYnRCtZz357kHpFnm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 37, "_left": 0, "_right": 0.08, "_top": -4, "_bottom": 4, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 1280, "_id": "7d3dUHyrZLwro4aAUGKLbh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "d1rtc2b4JHdoAiK0smmvc/", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "282838ae-d4f5-466e-aa10-44ea145a4b1b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "702SyQm9ZDPphO5qPFowOM"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "_N$totalLength": 1438, "_N$barSprite": {"__id__": 72}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "22sueFzcxI6Z0Z4T6lYRhW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 100, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 350, "_originalHeight": 60, "_id": "5266RhS65N7IEbgYJUO24K"}, {"__type__": "df031vx0ehPoJYm7u4xisyW", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "_id": "25nD5X7ulLU4G624Qp2MGA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "9dwrtl9bpLb636jibUz0Qf", "sync": false}, {"__type__": "cc.Node", "_name": "medal_story", "_objFlags": 0, "_parent": {"__id__": 69}, "_children": [], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-711.5999993681908, 4, 0, 0, 0, 0, 1, 0.800000011920929, 0.800000011920929, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "967/xowV5B4attHkqn/lDd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee066746-98c4-4707-b9e9-66991421de9b"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "23SUOpwX5CCK0Laf1BLzQ5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 10, "_left": 15, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 4, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "b25m8r1lpM2ZLaVL2V9QcM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "bbKGQkBA1EzZIbnRgqNAIZ", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 6, "_right": 6, "_top": 6, "_bottom": 6, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "e9uJSCgLBK/oXwT+3+ZxLC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "91+p+swKBN5LpxKHtVVCfW", "sync": false}, {"__type__": "cc.Node", "_name": "white_mask", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 113}, {"__id__": 114}], "_prefab": {"__id__": 115}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1550, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0e0W9Qn4pK+5OwN8f/TAd4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bffa609e-c115-4dc4-aa2d-2a271407ebca"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "2cIY/gWqNDxaPsi3VNfpB8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": "e37rTK16hHzaFuhR7v6GP8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "01u9XdW+tIyICwBOZbEk+I", "sync": false}, {"__type__": "131d7yWHnpLc4NCk4sYHg1H", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "_progressLayer": {"__id__": 79}, "_progressLabel": {"__id__": 82}, "_progressBar": {"__id__": 102}, "separatorPrefabs": [{"__id__": 90}, {"__id__": 94}, {"__id__": 98}], "_whiteMask": {"__id__": 112}, "_id": "f8p1KEnF9P9rqW2tFnviZS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 300, "_right": -300, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 450, "_originalHeight": 0, "_id": "72Ot5DsOdLCZYjZtfkU5Y+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "7160b300-3648-425a-a281-989992d8a12f"}, "fileId": "e4Xq1UIX1FdKO9s4ygNHmV", "sync": false}, {"__type__": "cc.Node", "_name": "level_time_view", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [{"__id__": 120}, {"__id__": 124}, {"__id__": 149}], "_active": true, "_components": [{"__id__": 153}, {"__id__": 154}], "_prefab": {"__id__": 155}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1550, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1870, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "62vL2OR/FJb7978N7/2M97"}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 121}, {"__id__": 122}], "_prefab": {"__id__": 123}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1550, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7eYuEXV9lHtYf8Yi8kjpRH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ae9eb984-de94-478c-be17-45604a6236af"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d1xtt3jsxIH4ildnQIdCif"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 80, "_id": "eaxBrBrcFDoJ2k3AhBTnXo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "96h9t2k2FEspqoJo0xmQl3", "sync": false}, {"__type__": "cc.Node", "_name": "inner", "_objFlags": 0, "_parent": {"__id__": 119}, "_children": [{"__id__": 125}, {"__id__": 143}], "_active": true, "_components": [{"__id__": 147}], "_prefab": {"__id__": 148}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1538, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b6PLbOHyRBvrqoxJ6lC/L1"}, {"__type__": "cc.Node", "_name": "progressBar", "_objFlags": 0, "_parent": {"__id__": 124}, "_children": [{"__id__": 126}, {"__id__": 130}, {"__id__": 134}], "_active": true, "_components": [{"__id__": 138}, {"__id__": 139}, {"__id__": 140}, {"__id__": 141}], "_prefab": {"__id__": 142}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1448, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [45, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bclk0+wnFOa6TEPl5J7SDq"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 125}, "_children": [], "_active": true, "_components": [{"__id__": 127}, {"__id__": 128}], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 868.8, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-724, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "130ibKN+BHS5KByr1+M52b"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "65d8b526-e0ee-440a-b61b-de6d5aaa034d"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bawLiAohFGAJUB/UlsTI/M"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 13, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 80, "_id": "fa0A853cpJ54eTr4TH9cvQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "6dY+Q8jH9HcbdwXRjek7sJ", "sync": false}, {"__type__": "cc.Node", "_name": "time<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 125}, "_children": [], "_active": true, "_components": [{"__id__": 131}, {"__id__": 132}], "_prefab": {"__id__": 133}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "90hE/FIsNLqpJQGCw0+Sf6"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "99s", "_N$string": "99s", "_fontSize": 50, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "c1eb5cc5-c797-4128-81a9-7e1abaf4255c"}, "_isSystemFontUsed": false, "_spacingX": -7, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "d9ygJAoxlCZbfqiliFHaH4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 18, "_left": 0.1, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "2dw5PiTPlJXYtN/9omUab9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "2bb7rOgNRDdKMwPMB3UnGU", "sync": false}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 125}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 136}], "_prefab": {"__id__": 137}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-732, 0, 0, 0, 0, 0, 1, 2, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0brdG84rRBqK3jbvZ2WrFc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0afc609b-3bab-4da5-9256-166853b2e324"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "a4iwbcfC5FcLDm13hfnMc3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": -32, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "53LIf5nAdI4KsJjnEXUcPA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "f3mslPCnNDrqHKaBga+jUV", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "282838ae-d4f5-466e-aa10-44ea145a4b1b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "0cWe5/u5FLH4O4iJyjYACn"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "_N$totalLength": 1448, "_N$barSprite": {"__id__": 127}, "_N$mode": 0, "_N$progress": 0.6, "_N$reverse": false, "_id": "7bPcKI1+5KA4U4j7mUnOGi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 90, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 250, "_originalHeight": 80, "_id": "f2KyxJXZ9CSrGsuupO07Fe"}, {"__type__": "df031vx0ehPoJYm7u4xisyW", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "_id": "850VE+bzdIP5HPl7dUDHbP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "2bEP/KcDlMPaGARI1ADmNG", "sync": false}, {"__type__": "cc.Node", "_name": "icon_clock", "_objFlags": 0, "_parent": {"__id__": 124}, "_children": [], "_active": true, "_components": [{"__id__": 144}, {"__id__": 145}], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 87}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-722.050000846386, 0, 0, 0, 0, 0, 1, 0.8999999761581421, 0.8999999761581421, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b246YXeKxNQK2i37L1MH3L"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6975e045-3db0-421b-b5b4-f750f8e341b9"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "7dB2lZuQ9PPI2Y+BeF3zIc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 10, "_left": 15, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "4eDlfvo7pEfKWJrABHSBd6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "4fMI/cGtRD45sHEVpEP8is", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 124}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 6, "_right": 6, "_top": 6, "_bottom": 6, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a95ZJDwxtGd7WBze/jTeY7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "3dCt9WXaRBOaqvyllAppxJ", "sync": false}, {"__type__": "cc.Node", "_name": "white_mask", "_objFlags": 0, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 150}, {"__id__": 151}], "_prefab": {"__id__": 152}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1550, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "037Nm00Z5AdKdJq4LhZMk/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bffa609e-c115-4dc4-aa2d-2a271407ebca"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "2f9Zzig2lFp5z6dQJGGJse"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 80, "_id": "74vRK5JGdBSZkdAwBFbNv7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "04GYV+ZnZHXovc55Y1XbLZ", "sync": false}, {"__type__": "a88ebPj9opECYiWLreq4sj3", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "_timeLabel": {"__id__": 131}, "_progressBar": {"__id__": 139}, "_whiteMask": {"__id__": 149}, "_id": "5aOw250MtLMadnDfVMW65v"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 1870, "_right": -1870, "_top": 600, "_bottom": 600, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 330, "_originalHeight": 80, "_id": "56FiuftiNEaqdThzN9+65H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "89012bef-6ac5-460f-8f45-05b61efb077b"}, "fileId": "10dDtLbC1F3KNEGio4yF9W", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 17, "_left": 0, "_right": 0, "_top": 55, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "68TN3Mvq1GN5HkdPADf58o"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 1550, "height": 0}, "_resize": 0, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 20, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "d1BslGh+NAc4noMk1lSiY2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "*************-4aa0-a625-aad499bcaa85"}, "fileId": "80alhjOWxFWr/351Ed1Mk4", "sync": false}, {"__type__": "cc.Node", "_name": "level_entity_view_v2", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 160}, {"__id__": 215}], "_active": true, "_components": [{"__id__": 218}, {"__id__": 219}], "_prefab": {"__id__": 220}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3588AeLEJCFIXhDzOyfSjE"}, {"__type__": "cc.Node", "_name": "entityView", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [{"__id__": 161}, {"__id__": 182}], "_active": true, "_components": [{"__id__": 212}, {"__id__": 213}], "_prefab": {"__id__": 214}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5cyoGgisZKq4zy7Br6d+Ao"}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 160}, "_children": [{"__id__": 162}, {"__id__": 168}, {"__id__": 174}], "_active": true, "_components": [{"__id__": 180}], "_prefab": {"__id__": 181}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-960, -640, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_zIndex": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "89GtDtmsZEup1CEKkG7dAK"}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": true, "_components": [{"__id__": 163}, {"__id__": 164}, {"__id__": 165}, {"__id__": 166}], "_prefab": {"__id__": 167}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_zIndex": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c7K3V/c51CF4BhZV8Ar1ps"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 12, "_left": 0, "_right": 1920, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "6euevvi85INbMuHSYf2Rru"}, {"__type__": "e9c63cOK0hA5KlykiCcz8ot", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "entityId": "border", "_id": "38s1Mdz75FVqALojCr09RG"}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -100, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 1500}, {"__type__": "cc.Vec2", "x": -100, "y": 1500}], "_id": "a0UK9G4W1P0YoVCEki9ptw"}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "movable": false, "collider": {"__id__": 165}, "_id": "27Mq2WaINGuqwGykyQEGuO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 161}, "asset": {"__uuid__": "68f95241-a691-4a2d-a329-a5bce10d1a86"}, "fileId": "fecWs2VJtElJqGkgwoWifG", "sync": false}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": true, "_components": [{"__id__": 169}, {"__id__": 170}, {"__id__": 171}, {"__id__": 172}], "_prefab": {"__id__": 173}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_zIndex": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5bsBxkUChAlYnac2hc9vnW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 20, "_left": 960, "_right": 960, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "0f9MtGartBYa6MXlowVBFV"}, {"__type__": "e9c63cOK0hA5KlykiCcz8ot", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "entityId": "border", "_id": "d08lRaxwxG74N7VVj7qgl2"}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -2000, "y": -100}, {"__type__": "cc.Vec2", "x": 2000, "y": -100}, {"__type__": "cc.Vec2", "x": 2000, "y": 0}, {"__type__": "cc.Vec2", "x": -2000, "y": 0}], "_id": "1cuY0h9JZM2qib948CR61/"}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "movable": false, "collider": {"__id__": 171}, "_id": "67oCkkRb5Nh4xA5XZFc2sj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 161}, "asset": {"__uuid__": "68f95241-a691-4a2d-a329-a5bce10d1a86"}, "fileId": "05yxr5clJPEbaiGOPZeZll", "sync": false}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": true, "_components": [{"__id__": 175}, {"__id__": 176}, {"__id__": 177}, {"__id__": 178}], "_prefab": {"__id__": 179}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1920, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_zIndex": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ccgohx/dNOFIbRd+Kzl5AG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 174}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 36, "_left": 960, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "971Aw6FSpP/LB7MZJWblfi"}, {"__type__": "e9c63cOK0hA5KlykiCcz8ot", "_name": "", "_objFlags": 0, "node": {"__id__": 174}, "_enabled": true, "entityId": "border", "_id": "74B4duiYhMGJx3Ue7nisXT"}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 174}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 100, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 1500}, {"__type__": "cc.Vec2", "x": 100, "y": 1500}], "_id": "7an4+feFFOT7pokKbV10WK"}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 174}, "_enabled": true, "movable": false, "collider": {"__id__": 177}, "_id": "fd+JV5tgZNvK0aewtW7msJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 161}, "asset": {"__uuid__": "68f95241-a691-4a2d-a329-a5bce10d1a86"}, "fileId": "79f7BMyR5FTY1bXuEviNH8", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 140, "_originalHeight": 150, "_id": "d8FQClvChPBLnIltEEEPzj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 161}, "asset": {"__uuid__": "68f95241-a691-4a2d-a329-a5bce10d1a86"}, "fileId": "6bnEGKBHJK4KYiW9FZexmO", "sync": false}, {"__type__": "cc.Node", "_name": "miner", "_objFlags": 0, "_parent": {"__id__": 160}, "_children": [{"__id__": 183}, {"__id__": 186}], "_active": true, "_components": [{"__id__": 210}], "_prefab": {"__id__": 211}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-300, 300, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "de4UjoJhBKHp7fJip1ZxJV"}, {"__type__": "cc.Node", "_name": "petPrefab", "_objFlags": 0, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 184}], "_prefab": {"__id__": 185}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-168, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "29DAKgP5dOJZLkGW/sqxg7"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 183}, "_enabled": true, "_prefab": {"__uuid__": "08efc9d2-bc39-4f8d-8760-0c5e03165a96"}, "instantiate": true, "async": false, "synchronize": true, "_syncFlag": 0, "_id": "c5TEZ/hnZH8Kr9IkxhHWtf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "f1zsHOJuxErLpLjZ15OEzh", "sync": false}, {"__type__": "cc.Node", "_name": "miner", "_objFlags": 0, "_parent": {"__id__": 182}, "_children": [{"__id__": 187}, {"__id__": 190}, {"__id__": 193}, {"__id__": 196}, {"__id__": 200}, {"__id__": 204}], "_active": true, "_components": [{"__id__": 207}, {"__id__": 208}], "_prefab": {"__id__": 209}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6bOtn1L9dP4rBEIN4mL9RM"}, {"__type__": "cc.Node", "_name": "characterPrefab", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 188}], "_prefab": {"__id__": 189}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.699999988079071, 0.699999988079071, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e4xE6nB8pCDqgk0vZRz6AL"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_prefab": {"__uuid__": "3243af2a-3e65-4a77-a9bc-ecf4ce99af61"}, "instantiate": true, "async": false, "synchronize": true, "_syncFlag": 0, "_id": "faqdd1bHpNSKOkumOm31/I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "07kN1q035PPZk6TdH8ZHHv", "sync": false}, {"__type__": "cc.Node", "_name": "ropePrefab", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 191}], "_prefab": {"__id__": 192}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 75, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3dFhTOnI5IaJGe3JlirpAg"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "_prefab": {"__uuid__": "0724814b-5175-4a71-af17-d3a89b655d5a"}, "instantiate": true, "async": false, "synchronize": true, "_syncFlag": 0, "_id": "acrblZjWpOzrbIspNUxDsV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "78EtZhXtVKpo3GLJleGSBB", "sync": false}, {"__type__": "cc.Node", "_name": "collision", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 194}], "_prefab": {"__id__": 195}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4fVRy3w8VK8YJJgAI1yU5x"}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 193}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -62.1, "y": 1}, {"__type__": "cc.Vec2", "x": 74.3, "y": 1.6}, {"__type__": "cc.Vec2", "x": 86.4, "y": 60.3}, {"__type__": "cc.Vec2", "x": 99, "y": 101}, {"__type__": "cc.Vec2", "x": 16.9, "y": 217.4}, {"__type__": "cc.Vec2", "x": -86.4, "y": 99.8}], "_id": "efKPT2K4NL37cli5v54GEp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "c0mq+Yo2xOrp0F9nWyfBgz", "sync": false}, {"__type__": "cc.Node", "_name": "throwArea", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 197}], "_prefab": {"__id__": 199}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1AlfG34JPoYNn08iFknr2"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": false, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.05, "zoomScale": 0.9, "clickEvents": [{"__id__": 198}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 196}, "_id": "8eDT9FOVRLgKkzfzEvUGmo"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 186}, "component": "", "_componentId": "21b77wUMT5ICbarJHrcB4Cm", "handler": "pressedThrowDynamite", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "f3zpj6qW1ILLdeRuwOApwm", "sync": false}, {"__type__": "cc.Node", "_name": "dynamite", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": false, "_components": [{"__id__": 201}, {"__id__": 202}], "_prefab": {"__id__": 203}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 94, "g": 60, "b": 41, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28.8, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [65, 65, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dcMxnd2jBLNo8JCl4fpVP8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "_materials": [], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "8", "_N$string": "8", "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "772c665b-1639-4a72-aa97-6fced35228f1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "603Du2EhBG3Kww3QQ8hd49"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_width": 3, "_id": "2al+jbKmZFBoGxvFmz4KEj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "439j2MV11MDIKKITa0ze6Z", "sync": false}, {"__type__": "cc.Node", "_name": "clawPrefab", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 205}], "_prefab": {"__id__": 206}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 75, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "28EDKU5u1HeKvsetxmZdxN"}, {"__type__": "b21e69KAqdBnLxKe4L8YhKB", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "_prefab": {"__uuid__": "ff746b39-d608-4b75-b830-5d540799e4d4"}, "instantiate": true, "async": false, "synchronize": true, "_syncFlag": 0, "_id": "bcEddwo1ZCF6WwjSzHOwfZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "c4Ok7++EVMtKfX01MWSo6P", "sync": false}, {"__type__": "21b77wUMT5ICbarJHrcB4Cm", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "_character_prefab": {"__id__": 188}, "_rope_prefab": {"__id__": 191}, "_claw_prefab": {"__id__": 205}, "_pet_prefab": {"__id__": 184}, "_effectPrefab": {"__uuid__": "3bf6927a-490c-4887-8234-605c842df358"}, "_id": "63nQh0cqpI4b3cEwizOFKX"}, {"__type__": "4fd909hliBHjo0S0tEPRgkg", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "movable": true, "collider": {"__id__": 194}, "_id": "60/S11PBRHi5qwRCrFoqKu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "7d97EGveVH549aTv9oiBbG", "sync": false}, {"__type__": "df0c6Ksr8BHD4YJYXInxaXC", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "_miner": {"__id__": 207}, "_pet_prefab": {"__id__": 184}, "_id": "89eWpQj+FAcLPsoCZ6zPtO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 182}, "asset": {"__uuid__": "41d64675-325e-4579-becf-c3231e2d7c69"}, "fileId": "b7HPvAUXxDZ56CP2y1UTi9", "sync": false}, {"__type__": "edc3bXgNe9MLLSuPIgz3J4k", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "_id": "f5USnXpMdLWIGmDuCiyBJh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1280, "_id": "c1/Xzo1CFCb5gzWsoCP/Le"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 159}, "asset": {"__uuid__": "0b2cc1f0-346f-49b1-8f5a-e8cbaa7da6a8"}, "fileId": "99Ej7yliNCYY2OJ3TUGegy", "sync": false}, {"__type__": "cc.Node", "_name": "debugDrawer", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 216}], "_prefab": {"__id__": 217}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "26giXNKgtKw6I77UePgnA3"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "_materials": [{"__uuid__": "6f801092-0c37-4f30-89ef-c8d960825b36"}], "_lineWidth": 3, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 255, "a": 255}, "_lineJoin": 1, "_lineCap": 1, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 20, "_id": "d2CZy1F6NGPo4Ts7IaQDl0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 159}, "asset": {"__uuid__": "0b2cc1f0-346f-49b1-8f5a-e8cbaa7da6a8"}, "fileId": "c9yVjULrZKN78q47aTf466", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1280, "_id": "f1IyUJWIBLJ6fIyIIiijdO"}, {"__type__": "e5ea3r85PdNgLTiTcltqFTa", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_debugDrawer": {"__id__": 216}, "_entityView": {"__id__": 212}, "_borderPrefab": {"__id__": 161}, "minerPrefabs": [{"__id__": 210}], "_id": "3aak4WsblOvrp+xF+tMXtD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 159}, "asset": {"__uuid__": "0b2cc1f0-346f-49b1-8f5a-e8cbaa7da6a8"}, "fileId": "a8FFxXNk5C7J67mSGDeCdi", "sync": false}, {"__type__": "cc.Node", "_name": "debugDrawer", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 222}], "_prefab": {"__id__": 223}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2a7wDgoAtFS7a0QkUQbhmf"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "node": {"__id__": 221}, "_enabled": true, "_materials": [{"__uuid__": "6f801092-0c37-4f30-89ef-c8d960825b36"}], "_lineWidth": 3, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 255, "a": 255}, "_lineJoin": 1, "_lineCap": 1, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 20, "_id": "e2/POF3URL6ZUMvMDbntjX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "*************-4aa0-a625-aad499bcaa85"}, "fileId": "91/5NtlklNTJu0rK4L/p1j", "sync": false}, {"__type__": "cc.Node", "_name": "sceneView", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 225}], "_prefab": {"__id__": 226}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "08wzyyR9ZK5K1ojTg2dw+e"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "ddhI3/4T5HZoev+7kvtKDT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "*************-4aa0-a625-aad499bcaa85"}, "fileId": "a2m69hz61CMZ2RLsJNl89E", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1280, "_id": "a4iTSCAD1GfYV/RfORPjFq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "*************-4aa0-a625-aad499bcaa85"}, "fileId": "", "sync": false}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 230}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7cow3Ee/1DS5l3FaTbDfl9"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 229}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "c72Mc81E1F8YFAs5nsrZFP"}, {"__type__": "cc.Node", "_name": "LanguageComponent", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 232}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-251, -825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a0yd+VLLlOZIGZIyjEaJaE"}, {"__type__": "923f9T6EU1Mxa5WqjBIKlfj", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "_key": "{null}", "_paramValues": [], "_id": "654XGzUe5N0rjOHEkbPe3w"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1920, "height": 1280}, "_fitWidth": false, "_fitHeight": true, "_id": "ae+/K+zotFKbsXBCU1pnvE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "91/9TSTBJLjrnJMKqbO4QL"}]