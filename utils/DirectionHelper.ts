export enum Direction {
    Left,
    Right,
}

function getSign(direction: Direction): number {
    return direction === Direction.Left ? -1 : +1;
}

export class DirectionHelper {
    public constructor(private readonly view: cc.Node) {
    }

    public getSign(): number {
        return getSign(this.getDirection());
    }

    public getDirection(): Direction {
        return this.view.scaleX > 0 ? Direction.Right : Direction.Left;
    }

    public setDirection(direction: Direction): this {
        this.view.scaleX = Math.abs(this.view.scaleX) * getSign(direction);
        return this;
    }

    public changeDirection(): this {
        this.view.scaleX = -this.view.scaleX;
        return this;
    }

    public getDistance(distance: number): number {
        return distance * getSign(this.getDirection());
    }
}