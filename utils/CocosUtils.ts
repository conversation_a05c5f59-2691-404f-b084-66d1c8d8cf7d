let _isCocos21x: boolean | undefined;

export function isCocos21x(): boolean {
    if (_isCocos21x === undefined) {
        const node = new cc.Node("isCocos21x");
        if (typeof (node as any).angle !== 'undefined') {
            _isCocos21x = true;
        } else {
            _isCocos21x = false;
        }
    }
    return _isCocos21x;
}

export class NodeAngle {
    public constructor(private readonly node: cc.Node) { }

    public get angle(): number {
        if (isCocos21x()) {
            return (this.node as any).angle;
        }
        return -this.node.rotation;
    }

    public set angle(value: number) {
        if (isCocos21x()) {
            (this.node as any).angle = value;
        } else {
            this.node.rotation = -value;
        }
    }
}

export function rotateTo(duration: number, angle: number): cc.ActionInterval {
    if (isCocos21x()) {
        return cc.rotateTo(duration, angle);
    } else {
        return cc.rotateTo(duration, -angle);
    }
}

export function retrieveNull<T>(item: T | null): T {
    if (item === null) {
        throw Error('Item not registered.');
    }
    return item;
}

export function retrieveUndefined<T>(item: T | undefined): T {
    if (item === undefined) {
        throw Error('Item not registered.');
    }
    return item;
}