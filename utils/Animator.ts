/** Can run actions. */
export class Animator {
    /** Dummy node used for action target. */
    private readonly view: cc.Node;

    /** Running actions. */
    private actions: cc.Action[];

    public constructor() {
        this.view = new cc.Node();
        this.actions = [];
    }

    /**
     * Adds the specified action.
     * @param action The action to run.
     * @return Instance to this for method chaining.
     */
    public addAction(action: cc.Action): this {
        this.actions.push(action);
        // @ts-ignore
        action.startWithTarget(this.view); // Đừng thay đổi, gọi private function
        return this;
    }

    /** Gets all running actions. */
    public getActions(): cc.Action[] {
        return this.actions;
    }

    /** Removes all running actions. */
    public removeAllActions(): void {
        this.actions = [];
    }

    /** Steps all actions by the specified amount of time. */
    public animate(delta: number): void {
        // Step all actions.
        // @ts-ignore
        this.actions.forEach(action => action.step(delta)); // Đ<PERSON>ng thay đổi, gọi private function

        // Remove done actions.
        this.actions = this.actions.filter(action => !action.isDone());
    }
}
