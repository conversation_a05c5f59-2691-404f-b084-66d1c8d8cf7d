package com.senspark.iap;

import android.app.Activity;
import android.util.Log;
import com.android.billingclient.api.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.cocos2dx.javascript.AppActivity;
import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.*;

public class IAPManager implements PurchasesUpdatedListener, BillingClientStateListener {
    private static final String TAG = "IAPManager";
    private static IAPManager instance;
    private BillingClient billingClient;
    private Activity activity;
    private List<String> productIds = new ArrayList<>();
    private Map<String, ProductDetails> productDetailsMap = new HashMap<>();
    private boolean isServiceConnected = false;
    private Gson gson = new Gson();

    // JavaScript callback methods
    private static final String JS_CALLBACK_PURCHASE_SUCCESS = "nativeIAP.onPurchaseSuccess";
    private static final String JS_CALLBACK_PURCHASE_FAILURE = "nativeIAP.onPurchaseFailure";
    private static final String JS_CALLBACK_PRODUCT_DETAILS = "nativeIAP.onProductDetailsReceived";
    private static final String JS_CALLBACK_RESTORE_COMPLETE = "nativeIAP.onRestoreComplete";
    private static final String JS_CALLBACK_INITIALIZE = "nativeIAP.onInitializeComplete";

    public static IAPManager getInstance() {
        if (instance == null) {
            instance = new IAPManager();
        }
        return instance;
    }

    private IAPManager() {
        this.activity = AppActivity.getContext();
        initializeBillingClient();
    }

    private void initializeBillingClient() {
        billingClient = BillingClient.newBuilder(activity)
                .setListener(this)
                .enablePendingPurchases()
                .build();
    }

    // Called from JavaScript
    public static void initialize(String productsJson) {
        Log.d(TAG, "Initialize called with products: " + productsJson);
        
        try {
            Type listType = new TypeToken<List<String>>(){}.getType();
            List<String> products = getInstance().gson.fromJson(productsJson, listType);
            getInstance().productIds = products;
            getInstance().startConnection();
        } catch (Exception e) {
            Log.e(TAG, "Error parsing products JSON", e);
            callJavaScript(JS_CALLBACK_INITIALIZE, "false");
        }
    }

    private void startConnection() {
        if (!billingClient.isReady()) {
            billingClient.startConnection(this);
        } else {
            onBillingSetupFinished(BillingResult.newBuilder()
                    .setResponseCode(BillingClient.BillingResponseCode.OK)
                    .build());
        }
    }

    @Override
    public void onBillingSetupFinished(BillingResult billingResult) {
        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
            isServiceConnected = true;
            Log.d(TAG, "Billing service connected successfully");
            
            // Load product details
            queryProductDetails();
            
            // Notify JavaScript
            callJavaScript(JS_CALLBACK_INITIALIZE, "true");
        } else {
            Log.e(TAG, "Billing service connection failed: " + billingResult.getDebugMessage());
            callJavaScript(JS_CALLBACK_INITIALIZE, "false");
        }
    }

    @Override
    public void onBillingServiceDisconnected() {
        isServiceConnected = false;
        Log.w(TAG, "Billing service disconnected");
    }

    private void queryProductDetails() {
        if (!isServiceConnected || productIds.isEmpty()) {
            return;
        }

        List<QueryProductDetailsParams.Product> productList = new ArrayList<>();
        for (String productId : productIds) {
            productList.add(
                QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(productId)
                    .setProductType(BillingClient.ProductType.INAPP)
                    .build()
            );
        }

        QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                .setProductList(productList)
                .build();

        billingClient.queryProductDetailsAsync(params, (billingResult, productDetailsList) -> {
            if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                Log.d(TAG, "Product details query successful");
                
                // Store product details
                productDetailsMap.clear();
                for (ProductDetails productDetails : productDetailsList) {
                    productDetailsMap.put(productDetails.getProductId(), productDetails);
                }
                
                // Convert to JSON and send to JavaScript
                try {
                    JSONArray jsonArray = new JSONArray();
                    for (ProductDetails details : productDetailsList) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("productId", details.getProductId());
                        jsonObject.put("title", details.getTitle());
                        jsonObject.put("description", details.getDescription());
                        
                        ProductDetails.OneTimePurchaseOfferDetails offerDetails = 
                            details.getOneTimePurchaseOfferDetails();
                        if (offerDetails != null) {
                            jsonObject.put("price", offerDetails.getFormattedPrice());
                            jsonObject.put("priceAmountMicros", offerDetails.getPriceAmountMicros());
                            jsonObject.put("priceCurrencyCode", offerDetails.getPriceCurrencyCode());
                        }
                        
                        jsonArray.put(jsonObject);
                    }
                    
                    callJavaScript(JS_CALLBACK_PRODUCT_DETAILS, jsonArray.toString());
                } catch (Exception e) {
                    Log.e(TAG, "Error creating product details JSON", e);
                }
            } else {
                Log.e(TAG, "Product details query failed: " + billingResult.getDebugMessage());
            }
        });
    }

    // Called from JavaScript
    public static void purchaseItem(String productId) {
        Log.d(TAG, "Purchase item called: " + productId);
        getInstance().launchBillingFlow(productId);
    }

    private void launchBillingFlow(String productId) {
        if (!isServiceConnected) {
            Log.e(TAG, "Billing service not connected");
            callJavaScript(JS_CALLBACK_PURCHASE_FAILURE, 
                productId, String.valueOf(BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE), 
                "Billing service not connected");
            return;
        }

        ProductDetails productDetails = productDetailsMap.get(productId);
        if (productDetails == null) {
            Log.e(TAG, "Product details not found for: " + productId);
            callJavaScript(JS_CALLBACK_PURCHASE_FAILURE, 
                productId, String.valueOf(BillingClient.BillingResponseCode.ITEM_UNAVAILABLE), 
                "Product not found");
            return;
        }

        BillingFlowParams.ProductDetailsParams productDetailsParams =
            BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                .build();

        BillingFlowParams billingFlowParams = BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(Arrays.asList(productDetailsParams))
                .build();

        BillingResult billingResult = billingClient.launchBillingFlow(activity, billingFlowParams);
        
        if (billingResult.getResponseCode() != BillingClient.BillingResponseCode.OK) {
            Log.e(TAG, "Failed to launch billing flow: " + billingResult.getDebugMessage());
            callJavaScript(JS_CALLBACK_PURCHASE_FAILURE, 
                productId, String.valueOf(billingResult.getResponseCode()), 
                billingResult.getDebugMessage());
        }
    }

    @Override
    public void onPurchasesUpdated(BillingResult billingResult, List<Purchase> purchases) {
        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK && purchases != null) {
            for (Purchase purchase : purchases) {
                handlePurchase(purchase);
            }
        } else if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.USER_CANCELED) {
            Log.d(TAG, "Purchase canceled by user");
            callJavaScript(JS_CALLBACK_PURCHASE_FAILURE, 
                "", String.valueOf(BillingClient.BillingResponseCode.USER_CANCELED), 
                "User canceled");
        } else {
            Log.e(TAG, "Purchase failed: " + billingResult.getDebugMessage());
            callJavaScript(JS_CALLBACK_PURCHASE_FAILURE, 
                "", String.valueOf(billingResult.getResponseCode()), 
                billingResult.getDebugMessage());
        }
    }

    private void handlePurchase(Purchase purchase) {
        Log.d(TAG, "Handling purchase: " + purchase.getProducts().get(0));
        
        try {
            // Create purchase data JSON
            JSONObject purchaseJson = new JSONObject();
            purchaseJson.put("orderId", purchase.getOrderId());
            purchaseJson.put("packageName", purchase.getPackageName());
            purchaseJson.put("productId", purchase.getProducts().get(0));
            purchaseJson.put("purchaseTime", purchase.getPurchaseTime());
            purchaseJson.put("purchaseState", purchase.getPurchaseState());
            purchaseJson.put("purchaseToken", purchase.getPurchaseToken());
            purchaseJson.put("signature", purchase.getSignature());
            purchaseJson.put("originalJson", purchase.getOriginalJson());
            
            // Acknowledge the purchase if needed
            if (!purchase.isAcknowledged()) {
                AcknowledgePurchaseParams acknowledgePurchaseParams =
                    AcknowledgePurchaseParams.newBuilder()
                        .setPurchaseToken(purchase.getPurchaseToken())
                        .build();
                        
                billingClient.acknowledgePurchase(acknowledgePurchaseParams, billingResult -> {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                        Log.d(TAG, "Purchase acknowledged successfully");
                    } else {
                        Log.e(TAG, "Failed to acknowledge purchase: " + billingResult.getDebugMessage());
                    }
                });
            }
            
            // Notify JavaScript
            callJavaScript(JS_CALLBACK_PURCHASE_SUCCESS, purchaseJson.toString());
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling purchase", e);
            callJavaScript(JS_CALLBACK_PURCHASE_FAILURE, 
                purchase.getProducts().get(0), String.valueOf(BillingClient.BillingResponseCode.ERROR), 
                "Error processing purchase");
        }
    }

    // Called from JavaScript
    public static void refreshProducts() {
        Log.d(TAG, "Refresh products called");
        getInstance().queryProductDetails();
    }

    // Called from JavaScript
    public static void restorePurchases() {
        Log.d(TAG, "Restore purchases called");
        getInstance().queryPurchases();
    }

    private void queryPurchases() {
        if (!isServiceConnected) {
            Log.e(TAG, "Billing service not connected");
            return;
        }

        QueryPurchasesParams params = QueryPurchasesParams.newBuilder()
                .setProductType(BillingClient.ProductType.INAPP)
                .build();

        billingClient.queryPurchasesAsync(params, (billingResult, purchasesList) -> {
            if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                Log.d(TAG, "Purchases query successful, found: " + purchasesList.size());
                
                try {
                    JSONArray purchasesArray = new JSONArray();
                    for (Purchase purchase : purchasesList) {
                        JSONObject purchaseJson = new JSONObject();
                        purchaseJson.put("orderId", purchase.getOrderId());
                        purchaseJson.put("packageName", purchase.getPackageName());
                        purchaseJson.put("productId", purchase.getProducts().get(0));
                        purchaseJson.put("purchaseTime", purchase.getPurchaseTime());
                        purchaseJson.put("purchaseState", purchase.getPurchaseState());
                        purchaseJson.put("purchaseToken", purchase.getPurchaseToken());
                        purchaseJson.put("signature", purchase.getSignature());
                        purchaseJson.put("originalJson", purchase.getOriginalJson());
                        
                        purchasesArray.put(purchaseJson);
                    }
                    
                    callJavaScript(JS_CALLBACK_RESTORE_COMPLETE, purchasesArray.toString());
                } catch (Exception e) {
                    Log.e(TAG, "Error creating purchases JSON", e);
                }
            } else {
                Log.e(TAG, "Purchases query failed: " + billingResult.getDebugMessage());
            }
        });
    }

    // Called from JavaScript
    public static void consumePurchase(String purchaseToken) {
        Log.d(TAG, "Consume purchase called: " + purchaseToken);
        getInstance().consumePurchaseInternal(purchaseToken);
    }

    private void consumePurchaseInternal(String purchaseToken) {
        if (!isServiceConnected) {
            Log.e(TAG, "Billing service not connected");
            return;
        }

        ConsumeParams consumeParams = ConsumeParams.newBuilder()
                .setPurchaseToken(purchaseToken)
                .build();

        billingClient.consumeAsync(consumeParams, (billingResult, purchaseToken1) -> {
            if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                Log.d(TAG, "Purchase consumed successfully");
            } else {
                Log.e(TAG, "Failed to consume purchase: " + billingResult.getDebugMessage());
            }
        });
    }

    private static void callJavaScript(String method, String... args) {
        StringBuilder jsCode = new StringBuilder();
        jsCode.append("if (typeof ").append(method).append(" === 'function') { ");
        jsCode.append(method).append("(");
        
        for (int i = 0; i < args.length; i++) {
            if (i > 0) jsCode.append(", ");
            jsCode.append("'").append(args[i].replace("'", "\\'")).append("'");
        }
        
        jsCode.append("); }");
        
        AppActivity.runOnGLThread(() -> {
            org.cocos2dx.lib.Cocos2dxJSBridge.evalString(jsCode.toString());
        });
    }
}
